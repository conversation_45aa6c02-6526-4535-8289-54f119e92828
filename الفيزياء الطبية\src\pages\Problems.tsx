
import React, { useState } from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calculator, CheckCircle, X, ChevronDown, ChevronUp, Target } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

const Problems = () => {
  const [openSections, setOpenSections] = useState<number[]>([]);

  const toggleSection = (index: number) => {
    setOpenSections((prev) =>
    prev.includes(index) ?
    prev.filter((i) => i !== index) :
    [...prev, index]
    );
  };

  const problemSets = [
  {
    category: 'التشتت المتماسك',
    categoryEn: 'Coherent Scattering',
    level: 'أساسي',
    color: 'bg-blue-50 border-blue-200',
    problems: [
    {
      id: 1,
      question: 'فوتون أشعة سينية بطاقة 30 keV يتشتت بشكل متماسك بزاوية 45°. احسب طاقة الفوتون بعد التشتت.',
      given: 'E = 30 keV, θ = 45°, تشتت متماسك',
      solution: 'في التشتت المتماسك، لا يوجد فقدان في الطاقة، لذلك E\' = E = 30 keV',
      answer: '30 keV',
      explanation: 'التشتت المتماسك يحافظ على طاقة الفوتون بغض النظر عن زاوية التشتت.'
    },
    {
      id: 2,
      question: 'مقارنة المقطع العرضي للتشتت المتماسك لعنصرين: الكربون (Z=6) والرصاص (Z=82) عند طاقة 25 keV.',
      given: 'Z₁ = 6 (كربون), Z₂ = 82 (رصاص), E = 25 keV',
      solution: 'المقطع العرضي للتشتت المتماسك يتناسب مع Z². النسبة = (82/6)² = (13.67)² ≈ 187',
      answer: 'الرصاص له مقطع عرضي أكبر بـ 187 مرة من الكربون',
      explanation: 'العناصر الثقيلة تشتت الأشعة السينية بشكل متماسك أكثر بكثير من العناصر الخفيفة.'
    }]

  },
  {
    category: 'التأثير الكهروضوئي',
    categoryEn: 'Photoelectric Effect',
    level: 'متوسط',
    color: 'bg-green-50 border-green-200',
    problems: [
    {
      id: 3,
      question: 'فوتون بطاقة 70 keV يتفاعل كهروضوئياً مع إلكترون في مدار K للرصاص (طاقة ربط K = 88 keV). هل يمكن حدوث هذا التفاعل؟',
      given: 'E = 70 keV, BE_K = 88 keV للرصاص',
      solution: 'لا يمكن حدوث التأثير الكهروضوئي لأن طاقة الفوتون (70 keV) أقل من طاقة ربط مدار K (88 keV)',
      answer: 'لا، التفاعل غير ممكن',
      explanation: 'التأثير الكهروضوئي يتطلب أن تكون طاقة الفوتون أكبر من أو تساوي طاقة ربط الإلكترون.'
    },
    {
      id: 4,
      question: 'إلكترون يطرد من مدار K للباريوم (BE_K = 37.4 keV) بواسطة فوتون طاقته 80 keV. احسب الطاقة الحركية للإلكترون المطرود.',
      given: 'E = 80 keV, BE_K = 37.4 keV',
      solution: 'الطاقة الحركية = E - BE_K = 80 - 37.4 = 42.6 keV',
      answer: '42.6 keV',
      explanation: 'الطاقة الزائدة بعد كسر الربط تتحول إلى طاقة حركية للإلكترون المطرود.'
    },
    {
      id: 5,
      question: 'قارن بين احتمالية التأثير الكهروضوئي للأنسجة الرخوة (Z_eff = 7.4) والعظام (Z_eff = 13.8) عند طاقة 30 keV.',
      given: 'Z₁ = 7.4 (أنسجة رخوة), Z₂ = 13.8 (عظام), E = 30 keV',
      solution: 'النسبة = (Z₂/Z₁)⁴·⁵ = (13.8/7.4)⁴·⁵ = (1.86)⁴·⁵ ≈ 5.2',
      answer: 'العظام لها احتمالية أكبر بـ 5.2 مرة',
      explanation: 'هذا الاختلاف الكبير في الامتصاص يخلق التباين في صور الأشعة السينية.'
    }]

  },
  {
    category: 'تشتت كومبتون',
    categoryEn: 'Compton Scattering',
    level: 'متقدم',
    color: 'bg-purple-50 border-purple-200',
    problems: [
    {
      id: 6,
      question: 'فوتون بطاقة 100 keV يتشتت كومبتون بزاوية 90°. احسب طاقة الفوتون المتشتت وطاقة الإلكترون المرتد.',
      given: 'E = 100 keV, θ = 90°, m₀c² = 511 keV',
      solution: 'E\' = E/[1 + (E/m₀c²)(1-cosθ)] = 100/[1 + (100/511)(1-0)] = 100/1.196 = 83.6 keV\nT = E - E\' = 100 - 83.6 = 16.4 keV',
      answer: 'E\' = 83.6 keV, T = 16.4 keV',
      explanation: 'في التشتت الجانبي (90°)، يفقد الفوتون جزءاً معتدلاً من طاقته للإلكترون.'
    },
    {
      id: 7,
      question: 'احسب الطول الموجي للفوتون قبل وبعد تشتت كومبتون بزاوية 180° لفوتون طاقته الأولية 60 keV.',
      given: 'E = 60 keV, θ = 180°, h = 4.136 × 10⁻¹⁵ eV·s, c = 3 × 10⁸ m/s',
      solution: 'λ = hc/E = (4.136×10⁻¹⁵ × 3×10⁸)/(60×10³) = 2.07×10⁻¹¹ m\nΔλ = (h/m₀c)(1-cosθ) = 2.43×10⁻¹² × 2 = 4.86×10⁻¹² m\nλ\' = λ + Δλ = 2.07×10⁻¹¹ + 4.86×10⁻¹² = 2.56×10⁻¹¹ m',
      answer: 'λ = 2.07×10⁻¹¹ m, λ\' = 2.56×10⁻¹¹ m',
      explanation: 'التشتت الخلفي يعطي أقصى زيادة في الطول الموجي وأقصى فقدان للطاقة.'
    }]

  },
  {
    category: 'معاملات التوهين',
    categoryEn: 'Attenuation Coefficients',
    level: 'متوسط',
    color: 'bg-red-50 border-red-200',
    problems: [
    {
      id: 8,
      question: 'حزمة أشعة سينية شدتها الأولية 1000 mR تمر عبر 5 mm من الألومينيوم (μ = 0.5 cm⁻¹). احسب الشدة النهائية.',
      given: 'I₀ = 1000 mR, x = 5 mm = 0.5 cm, μ = 0.5 cm⁻¹',
      solution: 'I = I₀ × e⁻μx = 1000 × e⁻⁽⁰·⁵ ˣ ⁰·⁵⁾ = 1000 × e⁻⁰·²⁵ = 1000 × 0.779 = 779 mR',
      answer: '779 mR',
      explanation: 'قانون بير-لامبرت يصف الانخفاض الأسي لشدة الحزمة مع سماكة المادة.'
    },
    {
      id: 9,
      question: 'إذا كان HVL للرصاص عند طاقة معينة هو 0.3 mm، احسب المعامل الخطي للتوهين.',
      given: 'HVL = 0.3 mm = 0.03 cm',
      solution: 'μ = ln(2)/HVL = 0.693/0.03 = 23.1 cm⁻¹',
      answer: 'μ = 23.1 cm⁻¹',
      explanation: 'HVL والمعامل الخطي مترابطان عكسياً - كلما قل HVL، زاد المعامل الخطي.'
    },
    {
      id: 10,
      question: 'احسب النسبة المئوية للحزمة التي تمر عبر 3 طبقات HVL من مادة ما.',
      given: 'عدد طبقات HVL = 3',
      solution: 'بعد كل HVL، تبقى 50% من الحزمة\nبعد 3 HVL: (1/2)³ = 1/8 = 0.125 = 12.5%',
      answer: '12.5%',
      explanation: 'كل طبقة HVL تقلل الحزمة إلى النصف، فثلاث طبقات تقللها إلى الثُمن.'
    }]

  },
  {
    category: 'التطبيقات المتقدمة',
    categoryEn: 'Advanced Applications',
    level: 'متقدم',
    color: 'bg-orange-50 border-orange-200',
    problems: [
    {
      id: 11,
      question: 'في فحص CT بطاقة 120 kVp (طاقة فعالة ~70 keV)، قارن بين التوهين في الأنسجة الرخوة والعظام. استخدم البيانات: μ/ρ (أنسجة رخوة) = 0.21 cm²/g، μ/ρ (عظام) = 0.48 cm²/g، كثافة الأنسجة الرخوة = 1 g/cm³، كثافة العظام = 1.85 g/cm³.',
      given: 'E_eff = 70 keV, (μ/ρ)_soft = 0.21 cm²/g, (μ/ρ)_bone = 0.48 cm²/g, ρ_soft = 1 g/cm³, ρ_bone = 1.85 g/cm³',
      solution: 'μ_soft = (μ/ρ) × ρ = 0.21 × 1 = 0.21 cm⁻¹\nμ_bone = 0.48 × 1.85 = 0.888 cm⁻¹\nالنسبة = μ_bone/μ_soft = 0.888/0.21 = 4.23',
      answer: 'العظام توهن الحزمة بـ 4.23 مرة أكثر من الأنسجة الرخوة',
      explanation: 'هذا الاختلاف في التوهين يخلق التباين الممتاز في صور CT بين العظام والأنسجة الرخوة.'
    },
    {
      id: 12,
      question: 'مريض يحتاج إلى فحص أشعة سينية للصدر. إذا كان سمك الصدر 25 cm والمعامل الخطي الفعال 0.15 cm⁻¹، احسب سماكة الرصاص المطلوبة لتوفير نفس مستوى الحماية (μ_lead = 50 cm⁻¹).',
      given: 'x_chest = 25 cm, μ_chest = 0.15 cm⁻¹, μ_lead = 50 cm⁻¹',
      solution: 'للحصول على نفس التوهين: μ_chest × x_chest = μ_lead × x_lead\n0.15 × 25 = 50 × x_lead\nx_lead = 3.75/50 = 0.075 cm = 0.75 mm',
      answer: '0.75 mm من الرصاص',
      explanation: 'كمية صغيرة من الرصاص تكافئ سماكة كبيرة من الأنسجة بسبب العدد الذري العالي للرصاص.'
    }]

  }];


  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-slate-50 to-orange-50" data-id="ifu00fegk" data-path="src/pages/Problems.tsx">
      <Navigation
        title="المشكلات والتمارين"
        titleEn="Problems & Exercises" data-id="pp6tlqra7" data-path="src/pages/Problems.tsx" />

      
      <div className="container mx-auto px-4 py-8" data-id="0wojetxnq" data-path="src/pages/Problems.tsx">
        {/* Header */}
        <Card className="mb-8" data-id="pfbc58btp" data-path="src/pages/Problems.tsx">
          <CardHeader data-id="0xx7bqbxr" data-path="src/pages/Problems.tsx">
            <div className="flex items-center gap-3" data-id="atm5plcow" data-path="src/pages/Problems.tsx">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center" data-id="htj1s6hb7" data-path="src/pages/Problems.tsx">
                <Calculator className="w-5 h-5 text-orange-600" data-id="bdugrec1h" data-path="src/pages/Problems.tsx" />
              </div>
              <div data-id="yl18o9a76" data-path="src/pages/Problems.tsx">
                <CardTitle className="text-2xl text-right" data-id="azbgty8nz" data-path="src/pages/Problems.tsx">المشكلات والتمارين</CardTitle>
                <p className="text-gray-600 text-right" data-id="f444b6az6" data-path="src/pages/Problems.tsx">
                  تمارين تطبيقية شاملة لتعزيز فهم تفاعل الأشعة السينية مع المادة
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent data-id="qcpd9skn0" data-path="src/pages/Problems.tsx">
            <div className="text-right leading-relaxed space-y-4" data-id="sudig17s2" data-path="src/pages/Problems.tsx">
              <p className="text-gray-700" data-id="onj5i0jlu" data-path="src/pages/Problems.tsx">
                تم تصميم هذه المجموعة من المسائل لتغطي جميع جوانب تفاعل الأشعة السينية مع المادة، 
                من المفاهيم الأساسية إلى التطبيقات المتقدمة في التصوير الطبي.
              </p>
              
              <Alert data-id="3tma20w9p" data-path="src/pages/Problems.tsx">
                <Target className="h-4 w-4" data-id="btdb5evbg" data-path="src/pages/Problems.tsx" />
                <AlertDescription className="text-right" data-id="uf9832r96" data-path="src/pages/Problems.tsx">
                  <strong data-id="hxvywir1p" data-path="src/pages/Problems.tsx">نصيحة للدراسة:</strong> حاول حل كل مسألة بنفسك قبل النظر إلى الحل. 
                  استخدم الآلة الحاسبة واكتب جميع الخطوات بوضوح.
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>

        {/* Problem Sets */}
        <div className="space-y-6" data-id="b6tjzvc7h" data-path="src/pages/Problems.tsx">
          {problemSets.map((set, setIndex) =>
          <Card key={setIndex} className={set.color} data-id="xyiw5evtf" data-path="src/pages/Problems.tsx">
              <CardHeader data-id="qaqpomxka" data-path="src/pages/Problems.tsx">
                <Collapsible data-id="efg5bssrp" data-path="src/pages/Problems.tsx">
                  <CollapsibleTrigger asChild data-id="chzhglnf3" data-path="src/pages/Problems.tsx">
                    <div
                    className="flex items-center justify-between w-full cursor-pointer"
                    onClick={() => toggleSection(setIndex)} data-id="t1rm7rlsc" data-path="src/pages/Problems.tsx">

                      <div className="flex items-center gap-3" data-id="j6wdbw7lc" data-path="src/pages/Problems.tsx">
                        <Badge
                        variant={set.level === 'أساسي' ? 'secondary' : set.level === 'متوسط' ? 'default' : 'destructive'} data-id="axwqli2jd" data-path="src/pages/Problems.tsx">

                          {set.level}
                        </Badge>
                        <span className="text-sm text-gray-600" data-id="kg6gylnen" data-path="src/pages/Problems.tsx">{set.problems.length} مسائل</span>
                        {openSections.includes(setIndex) ?
                      <ChevronUp className="w-4 h-4" data-id="wrn7yng3w" data-path="src/pages/Problems.tsx" /> :
                      <ChevronDown className="w-4 h-4" data-id="ahnm2iyy3" data-path="src/pages/Problems.tsx" />
                      }
                      </div>
                      <div className="text-right" data-id="cpeu7gytt" data-path="src/pages/Problems.tsx">
                        <CardTitle className="text-xl" data-id="ggfvv8k4o" data-path="src/pages/Problems.tsx">{set.category}</CardTitle>
                        <CardDescription className="italic" data-id="nknh7eytk" data-path="src/pages/Problems.tsx">{set.categoryEn}</CardDescription>
                      </div>
                    </div>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent data-id="4c23h2y40" data-path="src/pages/Problems.tsx">
                    <div className="mt-6 space-y-4" data-id="o3lb74izp" data-path="src/pages/Problems.tsx">
                      {set.problems.map((problem, problemIndex) =>
                    <Card key={problem.id} className="bg-white border-gray-200" data-id="mbpluao6s" data-path="src/pages/Problems.tsx">
                          <CardHeader data-id="omn57l92x" data-path="src/pages/Problems.tsx">
                            <div className="flex justify-between items-start" data-id="9caufvxb9" data-path="src/pages/Problems.tsx">
                              <Badge variant="outline" data-id="j7oo4vt8d" data-path="src/pages/Problems.tsx">المسألة {problem.id}</Badge>
                              <div className="text-right flex-1 mr-4" data-id="m30rz2k6z" data-path="src/pages/Problems.tsx">
                                <p className="font-medium leading-relaxed" data-id="wfxw03vjz" data-path="src/pages/Problems.tsx">
                                  {problem.question}
                                </p>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent data-id="uuet8udv6" data-path="src/pages/Problems.tsx">
                            <Collapsible data-id="j41bguk2p" data-path="src/pages/Problems.tsx">
                              <div className="space-y-4" data-id="fgpy0gron" data-path="src/pages/Problems.tsx">
                                <div className="bg-blue-50 p-3 rounded-lg" data-id="vqpfja2gs" data-path="src/pages/Problems.tsx">
                                  <p className="text-sm text-right" data-id="bpcsvr5px" data-path="src/pages/Problems.tsx">
                                    <span className="font-medium" data-id="nxa9l169s" data-path="src/pages/Problems.tsx">المعطيات: </span>
                                    {problem.given}
                                  </p>
                                </div>
                                
                                <CollapsibleTrigger asChild data-id="ry6alr313" data-path="src/pages/Problems.tsx">
                                  <Button variant="outline" className="w-full" data-id="8ttufzblx" data-path="src/pages/Problems.tsx">
                                    عرض الحل والشرح
                                  </Button>
                                </CollapsibleTrigger>
                                
                                <CollapsibleContent data-id="pbh1dw05p" data-path="src/pages/Problems.tsx">
                                  <div className="space-y-4" data-id="284z1l9ur" data-path="src/pages/Problems.tsx">
                                    <div className="bg-green-50 p-4 rounded-lg" data-id="vphct2odo" data-path="src/pages/Problems.tsx">
                                      <h4 className="font-medium mb-2 text-right flex items-center gap-2" data-id="gmdartjwq" data-path="src/pages/Problems.tsx">
                                        <CheckCircle className="w-4 h-4 text-green-600" data-id="7kstvylyr" data-path="src/pages/Problems.tsx" />
                                        الحل:
                                      </h4>
                                      <div className="text-sm text-right leading-relaxed whitespace-pre-line" data-id="lh19qls8r" data-path="src/pages/Problems.tsx">
                                        {problem.solution}
                                      </div>
                                    </div>
                                    
                                    <div className="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400" data-id="u6g9le4gi" data-path="src/pages/Problems.tsx">
                                      <h4 className="font-medium mb-2 text-right" data-id="b088kuqzc" data-path="src/pages/Problems.tsx">الإجابة النهائية:</h4>
                                      <p className="text-lg font-semibold text-right text-yellow-800" data-id="nsyr37rsb" data-path="src/pages/Problems.tsx">
                                        {problem.answer}
                                      </p>
                                    </div>
                                    
                                    <div className="bg-purple-50 p-4 rounded-lg" data-id="ckzavsxsn" data-path="src/pages/Problems.tsx">
                                      <h4 className="font-medium mb-2 text-right" data-id="v4ggn1300" data-path="src/pages/Problems.tsx">الشرح والتفسير:</h4>
                                      <p className="text-sm text-right leading-relaxed" data-id="iaux2sc8u" data-path="src/pages/Problems.tsx">
                                        {problem.explanation}
                                      </p>
                                    </div>
                                  </div>
                                </CollapsibleContent>
                              </div>
                            </Collapsible>
                          </CardContent>
                        </Card>
                    )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </CardHeader>
            </Card>
          )}
        </div>

        {/* Summary and Study Tips */}
        <Card className="mt-8" data-id="y82mov4ie" data-path="src/pages/Problems.tsx">
          <CardHeader data-id="gc0zjgkxv" data-path="src/pages/Problems.tsx">
            <CardTitle className="text-xl text-right" data-id="50wdydj8q" data-path="src/pages/Problems.tsx">نصائح لحل المسائل</CardTitle>
          </CardHeader>
          <CardContent data-id="wbhbfkmf1" data-path="src/pages/Problems.tsx">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6" data-id="tj3xlfnlh" data-path="src/pages/Problems.tsx">
              <div className="space-y-3" data-id="km4bpi0ky" data-path="src/pages/Problems.tsx">
                <h3 className="font-semibold text-right" data-id="muojihsi4" data-path="src/pages/Problems.tsx">خطوات الحل المنهجي:</h3>
                <ol className="space-y-2 text-sm text-right" data-id="tmdcbkfga" data-path="src/pages/Problems.tsx">
                  <li data-id="naajq1trb" data-path="src/pages/Problems.tsx">1. اقرأ المسألة بعناية وحدد ما هو مطلوب</li>
                  <li data-id="c73qod8gt" data-path="src/pages/Problems.tsx">2. اكتب جميع المعطيات والثوابت المعروفة</li>
                  <li data-id="ual7dfruh" data-path="src/pages/Problems.tsx">3. حدد القوانين والمعادلات المناسبة</li>
                  <li data-id="7x812ado8" data-path="src/pages/Problems.tsx">4. تحقق من وحدات القياس وحولها إذا لزم الأمر</li>
                  <li data-id="eb4gh9p1z" data-path="src/pages/Problems.tsx">5. احسب النتيجة خطوة بخطوة</li>
                  <li data-id="6vcbfkfw5" data-path="src/pages/Problems.tsx">6. تحقق من منطقية الإجابة</li>
                </ol>
              </div>
              
              <div className="space-y-3" data-id="xd5xx5rsd" data-path="src/pages/Problems.tsx">
                <h3 className="font-semibold text-right" data-id="uskb0wupa" data-path="src/pages/Problems.tsx">ثوابت مهمة يجب تذكرها:</h3>
                <ul className="space-y-2 text-sm text-right" data-id="5s1lbt890" data-path="src/pages/Problems.tsx">
                  <li data-id="262dwq2lw" data-path="src/pages/Problems.tsx">• كتلة الإلكترون الساكن: m₀c² = 511 keV</li>
                  <li data-id="irtultgwu" data-path="src/pages/Problems.tsx">• ثابت بلانك: h = 4.136 × 10⁻¹⁵ eV·s</li>
                  <li data-id="6ru9rvxb8" data-path="src/pages/Problems.tsx">• سرعة الضوء: c = 3 × 10⁸ m/s</li>
                  <li data-id="kbcto8fv3" data-path="src/pages/Problems.tsx">• الطول الموجي كومبتون: λc = 2.43 pm</li>
                  <li data-id="upzrra1l1" data-path="src/pages/Problems.tsx">• عدد أفوجادرو: NA = 6.022 × 10²³ mol⁻¹</li>
                  <li data-id="ga070xjrl" data-path="src/pages/Problems.tsx">• ln(2) = 0.693</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Problem Statistics */}
        <Card className="mt-8" data-id="mg054hjnq" data-path="src/pages/Problems.tsx">
          <CardHeader data-id="o29mwj8x8" data-path="src/pages/Problems.tsx">
            <CardTitle className="text-xl text-right" data-id="5akzzst69" data-path="src/pages/Problems.tsx">إحصائيات المسائل</CardTitle>
          </CardHeader>
          <CardContent data-id="882rxbhjl" data-path="src/pages/Problems.tsx">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4" data-id="vu5lyswt5" data-path="src/pages/Problems.tsx">
              <div className="text-center" data-id="wzql6v32h" data-path="src/pages/Problems.tsx">
                <div className="text-2xl font-bold text-blue-600" data-id="6bnhle4kz" data-path="src/pages/Problems.tsx">
                  {problemSets.reduce((sum, set) => sum + set.problems.length, 0)}
                </div>
                <p className="text-sm text-gray-600" data-id="154log0lh" data-path="src/pages/Problems.tsx">إجمالي المسائل</p>
              </div>
              <div className="text-center" data-id="unlxkosrk" data-path="src/pages/Problems.tsx">
                <div className="text-2xl font-bold text-green-600" data-id="ssh39molj" data-path="src/pages/Problems.tsx">
                  {problemSets.filter((set) => set.level === 'أساسي').reduce((sum, set) => sum + set.problems.length, 0)}
                </div>
                <p className="text-sm text-gray-600" data-id="8xqgblryn" data-path="src/pages/Problems.tsx">مسائل أساسية</p>
              </div>
              <div className="text-center" data-id="j9xq8qk4w" data-path="src/pages/Problems.tsx">
                <div className="text-2xl font-bold text-yellow-600" data-id="7knp1z1h0" data-path="src/pages/Problems.tsx">
                  {problemSets.filter((set) => set.level === 'متوسط').reduce((sum, set) => sum + set.problems.length, 0)}
                </div>
                <p className="text-sm text-gray-600" data-id="vu5ctyjap" data-path="src/pages/Problems.tsx">مسائل متوسطة</p>
              </div>
              <div className="text-center" data-id="tnfw43cig" data-path="src/pages/Problems.tsx">
                <div className="text-2xl font-bold text-red-600" data-id="4u7jntrj6" data-path="src/pages/Problems.tsx">
                  {problemSets.filter((set) => set.level === 'متقدم').reduce((sum, set) => sum + set.problems.length, 0)}
                </div>
                <p className="text-sm text-gray-600" data-id="msvcl3l9v" data-path="src/pages/Problems.tsx">مسائل متقدمة</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default Problems;