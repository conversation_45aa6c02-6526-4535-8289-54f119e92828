import { useState } from 'react';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Zap, Filter, Maximize, Minimize, Play, Pause, RefreshCw } from 'lucide-react';

interface XRaySimulationPreviewProps {
  language: 'ar' | 'en';
}

const XRaySimulationPreview = ({ language }: XRaySimulationPreviewProps) => {
  const [kVp, setKVp] = useState(80);
  const [mAs, setMAs] = useState(50);
  const [filtration, setFiltration] = useState(2.5);
  const [isPlaying, setIsPlaying] = useState(false);
  
  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };
  
  const resetSettings = () => {
    setKVp(80);
    setMAs(50);
    setFiltration(2.5);
  };
  
  const content = {
    ar: {
      title: "محاكاة الأشعة السينية",
      description: "تحكم في معلمات الأشعة السينية وشاهد التأثير",
      kvpLabel: "الجهد (kVp)",
      mAsLabel: "التيار-الزمن (mAs)",
      filtrationLabel: "الترشيح (mm Al)",
      startButton: "تشغيل المحاكاة",
      stopButton: "إيقاف المحاكاة",
      resetButton: "إعادة ضبط",
      beamQuality: "جودة الحزمة",
      patientDose: "جرعة المريض",
      imageQuality: "جودة الصورة",
      tabs: {
        preview: "معاينة",
        spectrum: "الطيف",
        dose: "الجرعة"
      }
    },
    en: {
      title: "X-Ray Simulation",
      description: "Control X-ray parameters and see the effect",
      kvpLabel: "Voltage (kVp)",
      mAsLabel: "Current-Time (mAs)",
      filtrationLabel: "Filtration (mm Al)",
      startButton: "Start Simulation",
      stopButton: "Stop Simulation",
      resetButton: "Reset",
      beamQuality: "Beam Quality",
      patientDose: "Patient Dose",
      imageQuality: "Image Quality",
      tabs: {
        preview: "Preview",
        spectrum: "Spectrum",
        dose: "Dose"
      }
    }
  };
  
  const currentContent = content[language];
  const dir = language === 'ar' ? 'rtl' : 'ltr';
  
  // Calculate simulated values based on parameters
  const beamQuality = Math.min(100, Math.round((kVp * 0.5) + (filtration * 10)));
  const patientDose = Math.max(10, Math.round((mAs * 0.8) - (filtration * 15)));
  const imageQuality = Math.min(100, Math.round((kVp * 0.3) + (mAs * 0.4) - (filtration * 5) + 40));
  
  return (
    <Card className="overflow-hidden" dir={dir}>
      <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          {currentContent.title}
        </CardTitle>
        <CardDescription className="text-blue-100">
          {currentContent.description}
        </CardDescription>
      </CardHeader>
      <CardContent className="p-6">
        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="preview">{currentContent.tabs.preview}</TabsTrigger>
            <TabsTrigger value="spectrum">{currentContent.tabs.spectrum}</TabsTrigger>
            <TabsTrigger value="dose">{currentContent.tabs.dose}</TabsTrigger>
          </TabsList>
          
          <TabsContent value="preview" className="pt-4">
            <div className="bg-gray-900 rounded-lg overflow-hidden h-48 mb-4 flex items-center justify-center relative">
              {/* Simulated X-ray beam visualization */}
              <div 
                className="absolute top-1/2 left-0 h-2 bg-blue-500 transform -translate-y-1/2"
                style={{ 
                  width: `${mAs * 0.8}%`, 
                  opacity: kVp / 150,
                  filter: `blur(${4 - filtration * 0.5}px)` 
                }}
              ></div>
              
              {/* Simulated patient */}
              <div className="w-20 h-32 bg-gray-700 rounded-lg relative overflow-hidden">
                {/* Simulated bones */}
                <div className="absolute top-1/2 left-1/2 w-8 h-24 bg-gray-500 rounded transform -translate-x-1/2 -translate-y-1/2"></div>
                
                {/* Simulated X-ray effect */}
                <div 
                  className="absolute inset-0 bg-blue-500"
                  style={{ 
                    opacity: isPlaying ? 0.1 + (kVp / 1000) : 0,
                    filter: `blur(${filtration * 0.3}px)` 
                  }}
                ></div>
              </div>
            </div>
            
            <div className="space-y-6 mt-6">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <label className="text-sm font-medium">{currentContent.kvpLabel}</label>
                  <span className="text-sm text-gray-500">{kVp} kVp</span>
                </div>
                <Slider 
                  value={[kVp]} 
                  min={40} 
                  max={150} 
                  step={1} 
                  onValueChange={(value) => setKVp(value[0])} 
                />
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <label className="text-sm font-medium">{currentContent.mAsLabel}</label>
                  <span className="text-sm text-gray-500">{mAs} mAs</span>
                </div>
                <Slider 
                  value={[mAs]} 
                  min={10} 
                  max={200} 
                  step={1} 
                  onValueChange={(value) => setMAs(value[0])} 
                />
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <label className="text-sm font-medium">{currentContent.filtrationLabel}</label>
                  <span className="text-sm text-gray-500">{filtration.toFixed(1)} mm Al</span>
                </div>
                <Slider 
                  value={[filtration]} 
                  min={0.5} 
                  max={5} 
                  step={0.1} 
                  onValueChange={(value) => setFiltration(value[0])} 
                />
              </div>
              
              <div className="flex gap-3 mt-4">
                <Button 
                  className={isPlaying ? "bg-red-600 hover:bg-red-700" : "bg-green-600 hover:bg-green-700"} 
                  onClick={togglePlay}
                >
                  {isPlaying ? (
                    <><Pause className="h-4 w-4 mr-2" /> {currentContent.stopButton}</>
                  ) : (
                    <><Play className="h-4 w-4 mr-2" /> {currentContent.startButton}</>
                  )}
                </Button>
                <Button variant="outline" onClick={resetSettings}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {currentContent.resetButton}
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="spectrum" className="pt-4">
            <div className="bg-white rounded-lg p-4 h-64 border border-gray-200 flex items-end justify-between relative">
              {/* X-axis label */}
              <div className="absolute bottom-0 left-0 right-0 text-center text-xs text-gray-500 py-1">
                Energy (keV)
              </div>
              
              {/* Y-axis label */}
              <div className="absolute top-0 bottom-8 left-0 flex items-center">
                <div className="transform -rotate-90 text-xs text-gray-500 whitespace-nowrap">
                  Relative Intensity
                </div>
              </div>
              
              {/* Spectrum bars */}
              {Array.from({ length: 20 }).map((_, i) => {
                // Calculate height based on parameters
                const energy = (i + 1) * (kVp / 20);
                let height = 0;
                
                // Characteristic radiation peaks
                const isCharacteristicPeak = (i === 11 || i === 16);
                
                if (isCharacteristicPeak) {
                  height = 120 * (mAs / 100) * (1 - (filtration * 0.05));
                } else {
                  // Bremsstrahlung radiation - triangular shape with filtration effect
                  height = (energy / kVp) * 100 * (mAs / 100);
                  
                  // Apply filtration effect (more effect on lower energies)
                  const filtrationEffect = Math.exp(-filtration * (1 - energy / kVp) * 0.5);
                  height *= filtrationEffect;
                }
                
                return (
                  <div 
                    key={i} 
                    className="w-3 bg-blue-500 mx-0.5 rounded-t"
                    style={{ 
                      height: `${Math.max(5, height)}%`,
                      opacity: isPlaying ? 1 : 0.5
                    }}
                  ></div>
                );
              })}
            </div>
          </TabsContent>
          
          <TabsContent value="dose" className="pt-4">
            <div className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">{currentContent.beamQuality}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-end gap-2">
                      <span className="text-2xl font-bold">{beamQuality}</span>
                      <span className="text-gray-500 text-sm">/ 100</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                      <div 
                        className="bg-blue-600 h-2.5 rounded-full" 
                        style={{ width: `${beamQuality}%` }}
                      ></div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">{currentContent.patientDose}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-end gap-2">
                      <span className="text-2xl font-bold">{patientDose}</span>
                      <span className="text-gray-500 text-sm">mGy</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                      <div 
                        className={`h-2.5 rounded-full ${
                          patientDose > 80 ? 'bg-red-500' : 
                          patientDose > 50 ? 'bg-amber-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(100, patientDose / 2)}%` }}
                      ></div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">{currentContent.imageQuality}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-end gap-2">
                      <span className="text-2xl font-bold">{imageQuality}</span>
                      <span className="text-gray-500 text-sm">/ 100</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                      <div 
                        className={`h-2.5 rounded-full ${
                          imageQuality > 80 ? 'bg-green-500' : 
                          imageQuality > 50 ? 'bg-amber-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${imageQuality}%` }}
                      ></div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <h4 className="text-sm font-medium mb-2">
                  {language === 'ar' ? 'العلاقة بين المعلمات' : 'Parameter Relationships'}
                </h4>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• {language === 'ar' 
                    ? `زيادة kVp تحسن جودة الحزمة وتقلل الجرعة النسبية` 
                    : `Increasing kVp improves beam quality and reduces relative dose`}
                  </li>
                  <li>• {language === 'ar' 
                    ? `زيادة mAs تزيد من الجرعة بشكل خطي` 
                    : `Increasing mAs linearly increases dose`}
                  </li>
                  <li>• {language === 'ar' 
                    ? `زيادة الترشيح تحسن جودة الحزمة وتقلل الجرعة` 
                    : `Increasing filtration improves beam quality and reduces dose`}
                  </li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default XRaySimulationPreview;