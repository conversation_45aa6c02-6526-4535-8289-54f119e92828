import { useState } from 'react';
import { motion } from 'motion/react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

const PowerSupplyDiagram = () => {
  const [selectedStage, setSelectedStage] = useState<string | null>(null);

  const stages = {
    input: {
      name: 'المدخل',
      description: 'تيار متردد 220V/50Hz من الشبكة الكهربائية',
      details: 'يتم استقبال التيار الكهربائي من الشبكة العامة بجهد 220V وتردد 50Hz',
      color: '#6b7280'
    },
    stepup: {
      name: 'المحول الرفع',
      description: 'رفع الجهد إلى المستوى المطلوب',
      details: 'محول تصاعدي يرفع الجهد من 220V إلى 40-150kV حسب المتطلبات',
      color: '#3b82f6'
    },
    rectification: {
      name: 'التعديل',
      description: 'تحويل التيار المتردد إلى مستمر',
      details: 'استخدام ديودات عالية الجهد لتعديل الموجة والحصول على تيار مستمر',
      color: '#ef4444'
    },
    filtering: {
      name: 'الترشيح',
      description: 'تنعيم التيار المستمر',
      details: 'استخدام مكثفات وملفات لتقليل التموج وتنعيم الإشارة',
      color: '#10b981'
    },
    output: {
      name: 'المخرج',
      description: 'تيار مستمر عالي الجهد مستقر',
      details: 'تيار مستمر نظيف ومستقر بجهد 40-150kV لتغذية أنبوب الأشعة السينية',
      color: '#8b5cf6'
    }
  };

  return (
    <div className="space-y-6" data-id="z2j0mhs1g" data-path="src/components/PowerSupplyDiagram.tsx">
      {/* Block Diagram */}
      <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-8 min-h-[300px]" data-id="u33w9vkzj" data-path="src/components/PowerSupplyDiagram.tsx">
        <svg
          width="100%"
          height="250"
          viewBox="0 0 900 250"
          className="max-w-full" data-id="kj7d2cf62" data-path="src/components/PowerSupplyDiagram.tsx">

          {/* Stage 1: Input */}
          <motion.g
            onClick={() => setSelectedStage(selectedStage === 'input' ? null : 'input')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="ws6znrctw" data-path="src/components/PowerSupplyDiagram.tsx">

            <rect
              x="20"
              y="80"
              width="120"
              height="80"
              rx="10"
              fill={selectedStage === 'input' ? stages.input.color : '#f9fafb'}
              stroke={selectedStage === 'input' ? stages.input.color : '#d1d5db'}
              strokeWidth="2" data-id="xs2826qxw" data-path="src/components/PowerSupplyDiagram.tsx" />

            <text x="80" y="115" textAnchor="middle" className="text-sm font-semibold fill-gray-700" data-id="r4o573tsn" data-path="src/components/PowerSupplyDiagram.tsx">
              مدخل AC
            </text>
            <text x="80" y="135" textAnchor="middle" className="text-xs fill-gray-600" data-id="h7qlqy03b" data-path="src/components/PowerSupplyDiagram.tsx">
              220V / 50Hz
            </text>
            
            {/* AC Waveform */}
            <path
              d="M40 145 Q50 140 60 145 Q70 150 80 145 Q90 140 100 145 Q110 150 120 145"
              stroke={selectedStage === 'input' ? '#ffffff' : '#6b7280'}
              strokeWidth="2"
              fill="none" data-id="3bvuz337e" data-path="src/components/PowerSupplyDiagram.tsx" />

          </motion.g>

          {/* Arrow 1 */}
          <defs data-id="3kuxnceem" data-path="src/components/PowerSupplyDiagram.tsx">
            <marker id="arrow1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto" data-id="c8b832ayg" data-path="src/components/PowerSupplyDiagram.tsx">
              <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" data-id="cyqlnsmvw" data-path="src/components/PowerSupplyDiagram.tsx" />
            </marker>
          </defs>
          <line x1="140" y1="120" x2="180" y2="120" stroke="#6b7280" strokeWidth="2" markerEnd="url(#arrow1)" data-id="imgi50w7c" data-path="src/components/PowerSupplyDiagram.tsx" />

          {/* Stage 2: Step-up Transformer */}
          <motion.g
            onClick={() => setSelectedStage(selectedStage === 'stepup' ? null : 'stepup')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="i05kam8e1" data-path="src/components/PowerSupplyDiagram.tsx">

            <rect
              x="180"
              y="80"
              width="120"
              height="80"
              rx="10"
              fill={selectedStage === 'stepup' ? stages.stepup.color : '#f9fafb'}
              stroke={selectedStage === 'stepup' ? stages.stepup.color : '#d1d5db'}
              strokeWidth="2" data-id="bz5avtam2" data-path="src/components/PowerSupplyDiagram.tsx" />

            <text x="240" y="110" textAnchor="middle" className="text-sm font-semibold fill-gray-700" data-id="k5ektd2ms" data-path="src/components/PowerSupplyDiagram.tsx">
              محول رفع
            </text>
            <text x="240" y="125" textAnchor="middle" className="text-xs fill-gray-600" data-id="81re4nnpd" data-path="src/components/PowerSupplyDiagram.tsx">
              1:500-700
            </text>
            <text x="240" y="140" textAnchor="middle" className="text-xs fill-gray-600" data-id="knhztn9ru" data-path="src/components/PowerSupplyDiagram.tsx">
              40-150kV
            </text>
            
            {/* Transformer symbol */}
            <g stroke={selectedStage === 'stepup' ? '#ffffff' : '#6b7280'} strokeWidth="2" fill="none" data-id="6t0yomklp" data-path="src/components/PowerSupplyDiagram.tsx">
              <circle cx="220" cy="150" r="8" data-id="6hv5k2i5l" data-path="src/components/PowerSupplyDiagram.tsx" />
              <circle cx="260" cy="150" r="8" data-id="2g7mcq1is" data-path="src/components/PowerSupplyDiagram.tsx" />
              <line x1="228" y1="150" x2="252" y2="150" data-id="351cgmsjy" data-path="src/components/PowerSupplyDiagram.tsx" />
            </g>
          </motion.g>

          {/* Arrow 2 */}
          <line x1="300" y1="120" x2="340" y2="120" stroke="#6b7280" strokeWidth="2" markerEnd="url(#arrow1)" data-id="o1l5zncqe" data-path="src/components/PowerSupplyDiagram.tsx" />

          {/* Stage 3: Rectification */}
          <motion.g
            onClick={() => setSelectedStage(selectedStage === 'rectification' ? null : 'rectification')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="rrqamnvbp" data-path="src/components/PowerSupplyDiagram.tsx">

            <rect
              x="340"
              y="80"
              width="120"
              height="80"
              rx="10"
              fill={selectedStage === 'rectification' ? stages.rectification.color : '#f9fafb'}
              stroke={selectedStage === 'rectification' ? stages.rectification.color : '#d1d5db'}
              strokeWidth="2" data-id="olb9u6mda" data-path="src/components/PowerSupplyDiagram.tsx" />

            <text x="400" y="110" textAnchor="middle" className="text-sm font-semibold fill-gray-700" data-id="6eeln84ic" data-path="src/components/PowerSupplyDiagram.tsx">
              معدل
            </text>
            <text x="400" y="125" textAnchor="middle" className="text-xs fill-gray-600" data-id="p69ac9p42" data-path="src/components/PowerSupplyDiagram.tsx">
              AC → DC
            </text>
            
            {/* Diode symbols */}
            <g stroke={selectedStage === 'rectification' ? '#ffffff' : '#ef4444'} strokeWidth="2" data-id="7p24psvd1" data-path="src/components/PowerSupplyDiagram.tsx">
              <polygon points="380,140 390,145 380,150" fill={selectedStage === 'rectification' ? '#ffffff' : '#ef4444'} data-id="lg4oo0y4v" data-path="src/components/PowerSupplyDiagram.tsx" />
              <line x1="390" y1="140" x2="390" y2="150" data-id="gixesm8kg" data-path="src/components/PowerSupplyDiagram.tsx" />
              <polygon points="410,140 420,145 410,150" fill={selectedStage === 'rectification' ? '#ffffff' : '#ef4444'} data-id="ddyl8gmbx" data-path="src/components/PowerSupplyDiagram.tsx" />
              <line x1="420" y1="140" x2="420" y2="150" data-id="8qyy7ubx1" data-path="src/components/PowerSupplyDiagram.tsx" />
            </g>
          </motion.g>

          {/* Arrow 3 */}
          <line x1="460" y1="120" x2="500" y2="120" stroke="#6b7280" strokeWidth="2" markerEnd="url(#arrow1)" data-id="tiix9fq6h" data-path="src/components/PowerSupplyDiagram.tsx" />

          {/* Stage 4: Filtering */}
          <motion.g
            onClick={() => setSelectedStage(selectedStage === 'filtering' ? null : 'filtering')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="rqck5cc8p" data-path="src/components/PowerSupplyDiagram.tsx">

            <rect
              x="500"
              y="80"
              width="120"
              height="80"
              rx="10"
              fill={selectedStage === 'filtering' ? stages.filtering.color : '#f9fafb'}
              stroke={selectedStage === 'filtering' ? stages.filtering.color : '#d1d5db'}
              strokeWidth="2" data-id="vv2mwguo6" data-path="src/components/PowerSupplyDiagram.tsx" />

            <text x="560" y="110" textAnchor="middle" className="text-sm font-semibold fill-gray-700" data-id="cvf8w9np3" data-path="src/components/PowerSupplyDiagram.tsx">
              مرشح
            </text>
            <text x="560" y="125" textAnchor="middle" className="text-xs fill-gray-600" data-id="g52jxdkef" data-path="src/components/PowerSupplyDiagram.tsx">
              تنعيم التيار
            </text>
            
            {/* Filter symbols */}
            <g stroke={selectedStage === 'filtering' ? '#ffffff' : '#10b981'} strokeWidth="2" data-id="pk429zj1r" data-path="src/components/PowerSupplyDiagram.tsx">
              <line x1="530" y1="140" x2="530" y2="155" data-id="wrd9r5ahc" data-path="src/components/PowerSupplyDiagram.tsx" />
              <line x1="535" y1="140" x2="535" y2="155" data-id="q1n4ctxjd" data-path="src/components/PowerSupplyDiagram.tsx" />
              <path d="M550 140 Q560 135 570 140 Q580 145 590 140" fill="none" data-id="yd776gycd" data-path="src/components/PowerSupplyDiagram.tsx" />
            </g>
          </motion.g>

          {/* Arrow 4 */}
          <line x1="620" y1="120" x2="660" y2="120" stroke="#6b7280" strokeWidth="2" markerEnd="url(#arrow1)" data-id="gdvvap6e5" data-path="src/components/PowerSupplyDiagram.tsx" />

          {/* Stage 5: Output */}
          <motion.g
            onClick={() => setSelectedStage(selectedStage === 'output' ? null : 'output')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="yqz1odpri" data-path="src/components/PowerSupplyDiagram.tsx">

            <rect
              x="660"
              y="80"
              width="120"
              height="80"
              rx="10"
              fill={selectedStage === 'output' ? stages.output.color : '#f9fafb'}
              stroke={selectedStage === 'output' ? stages.output.color : '#d1d5db'}
              strokeWidth="2" data-id="nhs7xnn00" data-path="src/components/PowerSupplyDiagram.tsx" />

            <text x="720" y="110" textAnchor="middle" className="text-sm font-semibold fill-gray-700" data-id="bhr6lk5kb" data-path="src/components/PowerSupplyDiagram.tsx">
              مخرج DC
            </text>
            <text x="720" y="125" textAnchor="middle" className="text-xs fill-gray-600" data-id="g3ata7gg9" data-path="src/components/PowerSupplyDiagram.tsx">
              عالي الجهد
            </text>
            <text x="720" y="140" textAnchor="middle" className="text-xs fill-gray-600" data-id="jq34w3lov" data-path="src/components/PowerSupplyDiagram.tsx">
              مستقر
            </text>
            
            {/* DC line */}
            <line
              x1="680"
              y1="150"
              x2="760"
              y2="150"
              stroke={selectedStage === 'output' ? '#ffffff' : '#8b5cf6'}
              strokeWidth="3" data-id="4tr1jrx4r" data-path="src/components/PowerSupplyDiagram.tsx" />

          </motion.g>

          {/* Title */}
          <text x="450" y="30" textAnchor="middle" className="text-lg font-bold fill-gray-800" data-id="of856xyd6" data-path="src/components/PowerSupplyDiagram.tsx">
            مخطط كتلي لدائرة مزود الطاقة
          </text>

          {/* Voltage Labels */}
          <text x="80" y="200" textAnchor="middle" className="text-xs fill-gray-500" data-id="cdayhdrew" data-path="src/components/PowerSupplyDiagram.tsx">220V AC</text>
          <text x="240" y="200" textAnchor="middle" className="text-xs fill-gray-500" data-id="way753x5c" data-path="src/components/PowerSupplyDiagram.tsx">40-150kV AC</text>
          <text x="400" y="200" textAnchor="middle" className="text-xs fill-gray-500" data-id="i3lmwiy73" data-path="src/components/PowerSupplyDiagram.tsx">40-150kV DC (متموج)</text>
          <text x="560" y="200" textAnchor="middle" className="text-xs fill-gray-500" data-id="vodktp8w0" data-path="src/components/PowerSupplyDiagram.tsx">40-150kV DC (منعم)</text>
          <text x="720" y="200" textAnchor="middle" className="text-xs fill-gray-500" data-id="e35wxvl6k" data-path="src/components/PowerSupplyDiagram.tsx">40-150kV DC (مستقر)</text>
        </svg>
      </div>

      {/* Waveform Display */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4" data-id="8zv4jq1uq" data-path="src/components/PowerSupplyDiagram.tsx">
        {['input', 'rectification', 'output'].map((stage) =>
        <Card key={stage} className="hover:shadow-lg transition-shadow" data-id="i1ltpwdjf" data-path="src/components/PowerSupplyDiagram.tsx">
            <CardContent className="p-4" data-id="j494a7bo5" data-path="src/components/PowerSupplyDiagram.tsx">
              <h4 className="font-medium text-gray-900 mb-2" data-id="of02lf4th" data-path="src/components/PowerSupplyDiagram.tsx">
                شكل الموجة - {stages[stage as keyof typeof stages].name}
              </h4>
              <div className="bg-gray-50 rounded p-3 h-24 flex items-center justify-center" data-id="enh32r3d8" data-path="src/components/PowerSupplyDiagram.tsx">
                <svg width="150" height="60" viewBox="0 0 150 60" data-id="g9tc1logw" data-path="src/components/PowerSupplyDiagram.tsx">
                  {stage === 'input' &&
                <path
                  d="M10 30 Q25 15 40 30 Q55 45 70 30 Q85 15 100 30 Q115 45 130 30"
                  stroke="#3b82f6"
                  strokeWidth="2"
                  fill="none" data-id="dai8p86yq" data-path="src/components/PowerSupplyDiagram.tsx" />

                }
                  {stage === 'rectification' &&
                <path
                  d="M10 30 Q25 15 40 30 L40 30 Q55 15 70 30 L70 30 Q85 15 100 30 L100 30 Q115 15 130 30"
                  stroke="#ef4444"
                  strokeWidth="2"
                  fill="none" data-id="n18sn3mmz" data-path="src/components/PowerSupplyDiagram.tsx" />

                }
                  {stage === 'output' &&
                <line
                  x1="10"
                  y1="20"
                  x2="130"
                  y2="20"
                  stroke="#8b5cf6"
                  strokeWidth="3" data-id="4lh721kkd" data-path="src/components/PowerSupplyDiagram.tsx" />

                }
                </svg>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Stage Information */}
      {selectedStage &&
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }} data-id="7h2112i7p" data-path="src/components/PowerSupplyDiagram.tsx">

          <Card data-id="b8iawfoi2" data-path="src/components/PowerSupplyDiagram.tsx">
            <CardContent className="p-6" data-id="muzmm9vp5" data-path="src/components/PowerSupplyDiagram.tsx">
              <div className="flex items-start gap-4" data-id="8bqqpk5r5" data-path="src/components/PowerSupplyDiagram.tsx">
                <div
                className="w-4 h-4 rounded-full flex-shrink-0 mt-1"
                style={{ backgroundColor: stages[selectedStage as keyof typeof stages].color }} data-id="rckfn4bq8" data-path="src/components/PowerSupplyDiagram.tsx">
              </div>
                <div className="flex-1" data-id="k732hs7ru" data-path="src/components/PowerSupplyDiagram.tsx">
                  <div className="flex items-center gap-2 mb-2" data-id="senrra2vd" data-path="src/components/PowerSupplyDiagram.tsx">
                    <h3 className="text-lg font-semibold text-gray-900" data-id="ukzm2ihos" data-path="src/components/PowerSupplyDiagram.tsx">
                      {stages[selectedStage as keyof typeof stages].name}
                    </h3>
                    <Badge variant="secondary" data-id="tsv3l3j0i" data-path="src/components/PowerSupplyDiagram.tsx">محدد</Badge>
                  </div>
                  <p className="text-gray-600 mb-3" data-id="kqoayfl9i" data-path="src/components/PowerSupplyDiagram.tsx">
                    {stages[selectedStage as keyof typeof stages].description}
                  </p>
                  <p className="text-sm text-gray-700 leading-relaxed" data-id="583yg0t9u" data-path="src/components/PowerSupplyDiagram.tsx">
                    {stages[selectedStage as keyof typeof stages].details}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      }

      {!selectedStage &&
      <div className="text-center text-gray-500 text-sm" data-id="wd2rossct" data-path="src/components/PowerSupplyDiagram.tsx">
          انقر على أي مرحلة في المخطط لعرض تفاصيلها
        </div>
      }
    </div>);

};

export default PowerSupplyDiagram;