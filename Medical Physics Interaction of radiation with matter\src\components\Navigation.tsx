import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Menu, X, ChevronDown, Home, BookOpen, Target, Atom, Waves, Zap, Users, Calculator, FileText, Key, HelpCircle, User, Cpu } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean;}>({});
  const location = useLocation();

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const navigationItems = [
  { path: '/', label: 'الصفحة الرئيسية', icon: Home },
  { path: '/learning-objectives', label: 'أهداف التعلم', icon: Target },
  {
    label: 'التفاعلات الأساسية',
    icon: Atom,
    section: 'interactions',
    children: [
    { path: '/coherent-scattering', label: 'التبعثر المتماسك', icon: Waves },
    { path: '/photoelectric-effect', label: 'التأثير الكهروضوئي', icon: Zap },
    { path: '/compton-scattering', label: 'تبعثر كومبتون', icon: Atom },
    { path: '/pair-production', label: 'إنتاج الأزواج', icon: Users }]

  },
  { path: '/attenuation-coefficients', label: 'معاملات التوهين', icon: Calculator },
  { path: '/relative-importance', label: 'الأهمية النسبية', icon: Target },
  {
    label: 'الفصول المتقدمة',
    icon: BookOpen,
    section: 'advanced',
    children: [
    { path: '/chapter9-patient-modeling', label: 'الفصل 9: نمذجة المريض والوهم', icon: User },
    { path: '/chapter10-monte-carlo', label: 'الفصل 10: محاكاة مونت كارلو', icon: Cpu }]

  },
  { path: '/key-terms', label: 'المصطلحات الرئيسية', icon: Key },
  { path: '/references', label: 'المراجع', icon: FileText },
  { path: '/problems', label: 'المشكلات', icon: HelpCircle }];


  const isActivePath = (path: string) => location.pathname === path;

  const NavItem = ({ item, isNested = false }: {item: any;isNested?: boolean;}) => {
    if (item.children) {
      const isExpanded = expandedSections[item.section];
      return (
        <div className={`${isNested ? 'mr-4' : ''}`} data-id="ja7zp9zjq" data-path="src/components/Navigation.tsx">
          <Button
            variant="ghost"
            className="w-full justify-between text-right hover:bg-blue-50 transition-colors"
            onClick={() => toggleSection(item.section)} data-id="temsxdd2c" data-path="src/components/Navigation.tsx">

            <div className="flex items-center" data-id="c1ciuifip" data-path="src/components/Navigation.tsx">
              <ChevronDown className={`h-4 w-4 ml-2 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} data-id="9f36t6edr" data-path="src/components/Navigation.tsx" />
              <span data-id="e9aue9b0q" data-path="src/components/Navigation.tsx">{item.label}</span>
            </div>
            <item.icon className="h-5 w-5" data-id="cu1w3ccnb" data-path="src/components/Navigation.tsx" />
          </Button>
          {isExpanded &&
          <div className="mr-4 mt-1 space-y-1" data-id="vydib4bwp" data-path="src/components/Navigation.tsx">
              {item.children.map((child: any) =>
            <NavItem key={child.path} item={child} isNested={true} data-id="fjdd0kma7" data-path="src/components/Navigation.tsx" />
            )}
            </div>
          }
        </div>);

    }

    return (
      <Link to={item.path} onClick={() => setIsOpen(false)} data-id="yowjustki" data-path="src/components/Navigation.tsx">
        <Button
          variant={isActivePath(item.path) ? "default" : "ghost"}
          className={`w-full justify-between text-right transition-colors ${
          isActivePath(item.path) ?
          'bg-blue-600 text-white hover:bg-blue-700' :
          'hover:bg-blue-50'} ${
          isNested ? 'mr-4 text-sm' : ''}`} data-id="5km4e5xeu" data-path="src/components/Navigation.tsx">

          <span data-id="07adm6ppa" data-path="src/components/Navigation.tsx">{item.label}</span>
          <item.icon className="h-5 w-5" data-id="j2lpr4kfg" data-path="src/components/Navigation.tsx" />
        </Button>
      </Link>);

  };

  return (
    <>
      {/* Mobile Menu Button */}
      <div className="md:hidden fixed top-4 right-4 z-50" data-id="d7eja6254" data-path="src/components/Navigation.tsx">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="bg-white shadow-lg" data-id="1zvm8dbmg" data-path="src/components/Navigation.tsx">

          {isOpen ? <X className="h-4 w-4" data-id="4uo6t7nrg" data-path="src/components/Navigation.tsx" /> : <Menu className="h-4 w-4" data-id="decfzq2he" data-path="src/components/Navigation.tsx" />}
        </Button>
      </div>

      {/* Desktop Sidebar */}
      <div className="hidden md:block fixed right-0 top-0 h-full w-72 bg-white shadow-xl border-l border-gray-200 z-40 overflow-y-auto" data-id="t0pd1m0kh" data-path="src/components/Navigation.tsx">
        <div className="p-6" data-id="gdkwl3koo" data-path="src/components/Navigation.tsx">
          <div className="mb-8" data-id="ku0ukeiec" data-path="src/components/Navigation.tsx">
            <h2 className="text-2xl font-bold text-gray-800 text-center mb-2" data-id="rleopyibh" data-path="src/components/Navigation.tsx">
              فيزياء التفاعلات الفوتونية
            </h2>
            <p className="text-sm text-gray-600 text-center" data-id="qq4l5kut7" data-path="src/components/Navigation.tsx">
              دليل شامل للتفاعلات الإشعاعية
            </p>
          </div>
          
          <nav className="space-y-2" dir="rtl" data-id="1qe3ai75z" data-path="src/components/Navigation.tsx">
            {navigationItems.map((item) =>
            <NavItem key={item.path || item.section} item={item} data-id="wtrloyfv1" data-path="src/components/Navigation.tsx" />
            )}
          </nav>
        </div>
      </div>

      {/* Mobile Sidebar */}
      {isOpen &&
      <div className="md:hidden fixed inset-0 z-40" data-id="cy96hdqjc" data-path="src/components/Navigation.tsx">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setIsOpen(false)} data-id="1iypw52tx" data-path="src/components/Navigation.tsx" />
          <div className="fixed right-0 top-0 h-full w-80 bg-white shadow-xl overflow-y-auto" data-id="0hts4iavb" data-path="src/components/Navigation.tsx">
            <div className="p-6" data-id="tc4qqx9d1" data-path="src/components/Navigation.tsx">
              <div className="mb-8" data-id="t4hjtqb5y" data-path="src/components/Navigation.tsx">
                <h2 className="text-xl font-bold text-gray-800 text-center mb-2" data-id="cixqswsax" data-path="src/components/Navigation.tsx">
                  فيزياء التفاعلات الفوتونية
                </h2>
                <p className="text-sm text-gray-600 text-center" data-id="r61gp4jf4" data-path="src/components/Navigation.tsx">
                  دليل شامل للتفاعلات الإشعاعية
                </p>
              </div>
              
              <nav className="space-y-2" dir="rtl" data-id="bjd9ihwpa" data-path="src/components/Navigation.tsx">
                {navigationItems.map((item) =>
              <NavItem key={item.path || item.section} item={item} data-id="hxg0ulm6a" data-path="src/components/Navigation.tsx" />
              )}
              </nav>
            </div>
          </div>
        </div>
      }

      {/* Content spacer for desktop */}
      <div className="hidden md:block w-72 flex-shrink-0" data-id="p8jhyq2u3" data-path="src/components/Navigation.tsx" />
    </>);

};

export default Navigation;