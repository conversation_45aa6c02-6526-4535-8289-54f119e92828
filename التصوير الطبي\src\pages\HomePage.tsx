import { motion } from 'motion/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import {
  Zap,
  CircuitBoard,
  Brain,
  BookOpen,
  ArrowRight,
  Microscope,
  Shield,
  Activity } from
'lucide-react';

const HomePage = () => {
  const features = [
  {
    icon: BookOpen,
    title: 'المفاهيم الأساسية',
    description: 'تعلم أساسيات الإشعاع المؤين وفيزياء التصوير الطبي',
    link: '/concepts',
    color: 'bg-blue-500'
  },
  {
    icon: Zap,
    title: 'أنبوب الأشعة السينية',
    description: 'استكشف تركيب وآلية عمل أنبوب الأشعة السينية بشكل تفاعلي',
    link: '/xray-tube',
    color: 'bg-purple-500'
  },
  {
    icon: CircuitBoard,
    title: 'الدوائر الكهربائية',
    description: 'فهم المخططات الكهربائية ومكونات النظام',
    link: '/circuits',
    color: 'bg-green-500'
  },
  {
    icon: Brain,
    title: 'المساعد الذكي',
    description: 'احصل على إجابات فورية لأسئلتك من المساعد الذكي',
    link: '/ai-assistant',
    color: 'bg-orange-500'
  }];


  const stats = [
  { label: 'مفاهيم تعليمية', value: '50+', icon: BookOpen },
  { label: 'رسوم توضيحية', value: '30+', icon: Activity },
  { label: 'مخططات تفاعلية', value: '15+', icon: CircuitBoard },
  { label: 'نماذج محاكاة', value: '10+', icon: Microscope }];


  return (
    <div className="min-h-screen" data-id="doz1n40s8" data-path="src/pages/HomePage.tsx">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800" data-id="z878ac0pv" data-path="src/pages/HomePage.tsx">
        <div className="absolute inset-0 bg-black/20" data-id="17gfmxbkb" data-path="src/pages/HomePage.tsx"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24" data-id="vt6nyn3eq" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center text-white" data-id="kc8ltvmk3" data-path="src/pages/HomePage.tsx">

            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 100 }}
              className="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8" data-id="qmplc54nx" data-path="src/pages/HomePage.tsx">

              <Microscope className="w-10 h-10" data-id="1q75cdhvk" data-path="src/pages/HomePage.tsx" />
            </motion.div>
            
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight" data-id="dwbjhxr4n" data-path="src/pages/HomePage.tsx">
              التصوير الطبي
              <br data-id="gnigjlfr1" data-path="src/pages/HomePage.tsx" />
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent" data-id="lf0apqdna" data-path="src/pages/HomePage.tsx">
                التفاعلي
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto" data-id="cbhh009yb" data-path="src/pages/HomePage.tsx">
              منصة تعليمية شاملة لفهم مبادئ وتقنيات أجهزة التصوير الطبي التي تستخدم الإشعاع المؤين
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center" data-id="oqp6u3pa0" data-path="src/pages/HomePage.tsx">
              <Link to="/concepts" data-id="jwe82f30q" data-path="src/pages/HomePage.tsx">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg font-semibold" data-id="104ug4s1n" data-path="src/pages/HomePage.tsx">
                  ابدأ التعلم الآن
                  <ArrowRight className="mr-2 h-5 w-5" data-id="ed5yqnrv1" data-path="src/pages/HomePage.tsx" />
                </Button>
              </Link>
              <Link to="/ai-assistant" data-id="zpk4v02f3" data-path="src/pages/HomePage.tsx">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 px-8 py-4 text-lg" data-id="37oellhqu" data-path="src/pages/HomePage.tsx">
                  المساعد الذكي
                  <Brain className="mr-2 h-5 w-5" data-id="3s6onlely" data-path="src/pages/HomePage.tsx" />
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white" data-id="nyrjnp50e" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="mnwpnf91z" data-path="src/pages/HomePage.tsx">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8" data-id="wd24dv7pa" data-path="src/pages/HomePage.tsx">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="text-center" data-id="bhuc55tex" data-path="src/pages/HomePage.tsx">

                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4" data-id="6mq82ptg2" data-path="src/pages/HomePage.tsx">
                    <Icon className="w-6 h-6 text-blue-600" data-id="fi0tovfzu" data-path="src/pages/HomePage.tsx" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2" data-id="ce7b8wr74" data-path="src/pages/HomePage.tsx">{stat.value}</div>
                  <div className="text-sm text-gray-600" data-id="j75g8316s" data-path="src/pages/HomePage.tsx">{stat.label}</div>
                </motion.div>);

            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50" data-id="eydtbeo21" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="383qi3upb" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16" data-id="yfzqn0d5o" data-path="src/pages/HomePage.tsx">

            <Badge variant="secondary" className="mb-4" data-id="9mgw69i6q" data-path="src/pages/HomePage.tsx">المحتوى التعليمي</Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4" data-id="usbiqutpm" data-path="src/pages/HomePage.tsx">
              استكشف عالم التصوير الطبي
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="eyqdo7e45" data-path="src/pages/HomePage.tsx">
              تعلم من خلال المحتوى التفاعلي والرسوم التوضيحية المتقدمة
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8" data-id="8dq6yaeah" data-path="src/pages/HomePage.tsx">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  whileHover={{ y: -5 }}
                  className="group" data-id="b848xmunh" data-path="src/pages/HomePage.tsx">

                  <Card className="h-full hover:shadow-xl transition-all duration-300 border-0 shadow-lg" data-id="5b5oa8czl" data-path="src/pages/HomePage.tsx">
                    <CardHeader className="text-center pb-4" data-id="qk7vwf0gw" data-path="src/pages/HomePage.tsx">
                      <div className={`inline-flex items-center justify-center w-16 h-16 ${feature.color} rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300`} data-id="ch15ii9pj" data-path="src/pages/HomePage.tsx">
                        <Icon className="w-8 h-8 text-white" data-id="gwz8hemp4" data-path="src/pages/HomePage.tsx" />
                      </div>
                      <CardTitle className="text-2xl font-bold text-gray-900" data-id="l5vbl88if" data-path="src/pages/HomePage.tsx">
                        {feature.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="text-center" data-id="zsk3j9py0" data-path="src/pages/HomePage.tsx">
                      <CardDescription className="text-lg mb-6 text-gray-600" data-id="fwzldgsa3" data-path="src/pages/HomePage.tsx">
                        {feature.description}
                      </CardDescription>
                      <Link to={feature.link} data-id="gxw8am5al" data-path="src/pages/HomePage.tsx">
                        <Button className="w-full group-hover:shadow-lg transition-all duration-300" data-id="z12jqazul" data-path="src/pages/HomePage.tsx">
                          استكشف الآن
                          <ArrowRight className="mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform" data-id="i0g01twt7" data-path="src/pages/HomePage.tsx" />
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                </motion.div>);

            })}
          </div>
        </div>
      </section>

      {/* Safety Notice */}
      <section className="py-16 bg-amber-50 border-t border-amber-200" data-id="t6fmzq2ec" data-path="src/pages/HomePage.tsx">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8" data-id="e1j86s93k" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
            className="text-center" data-id="evhaa3v1t" data-path="src/pages/HomePage.tsx">

            <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-100 rounded-full mb-6" data-id="2x5k4q63k" data-path="src/pages/HomePage.tsx">
              <Shield className="w-8 h-8 text-amber-600" data-id="m4txiy91w" data-path="src/pages/HomePage.tsx" />
            </div>
            <h3 className="text-2xl font-bold text-amber-900 mb-4" data-id="0ef66t79h" data-path="src/pages/HomePage.tsx">
              تنبيه السلامة الإشعاعية
            </h3>
            <p className="text-lg text-amber-800 leading-relaxed" data-id="hoxx0h990" data-path="src/pages/HomePage.tsx">
              هذا الموقع مخصص للأغراض التعليمية فقط. جميع المعلومات المقدمة حول الإشعاع المؤين 
              وأجهزة التصوير الطبي هي لأغراض التعلم والفهم النظري. يجب دائماً اتباع بروتوكولات 
              السلامة الإشعاعية واستشارة المتخصصين المؤهلين في البيئات الطبية الفعلية.
            </p>
          </motion.div>
        </div>
      </section>
    </div>);

};

export default HomePage;