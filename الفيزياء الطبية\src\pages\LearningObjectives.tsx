
import React from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Target, CheckCircle, BookOpen, Lightbulb } from 'lucide-react';

const LearningObjectives = () => {
  const objectives = [
  {
    category: 'المفاهيم الأساسية',
    categoryEn: 'Basic Concepts',
    icon: <BookOpen className="w-5 h-5" data-id="7jdh9yqei" data-path="src/pages/LearningObjectives.tsx" />,
    color: 'bg-blue-50 border-blue-200',
    items: [
    'فهم الآليات الفيزيائية الأساسية لتفاعل الأشعة السينية مع المادة',
    'التمييز بين أنواع التفاعلات المختلفة (متماسك، غير متماسك، امتصاص)',
    'شرح العلاقة بين طاقة الفوتون وآلية التفاعل المهيمنة',
    'وصف دور العدد الذري في تحديد نوع التفاعل']

  },
  {
    category: 'المعادلات والحسابات',
    categoryEn: 'Equations & Calculations',
    icon: <Target className="w-5 h-5" data-id="wfjhh2rrp" data-path="src/pages/LearningObjectives.tsx" />,
    color: 'bg-green-50 border-green-200',
    items: [
    'تطبيق معادلات كل نوع من أنواع التفاعل',
    'حساب المقاطع العرضية والمعاملات المختلفة',
    'استخدام قوانين الحفظ في تحليل التفاعلات',
    'حساب الطاقة المنقولة والمتشتتة في كل تفاعل']

  },
  {
    category: 'التطبيقات السريرية',
    categoryEn: 'Clinical Applications',
    icon: <CheckCircle className="w-5 h-5" data-id="1dzu916oo" data-path="src/pages/LearningObjectives.tsx" />,
    color: 'bg-purple-50 border-purple-200',
    items: [
    'ربط المفاهيم الفيزيائية بالتطبيقات في التصوير الطبي',
    'فهم كيفية تأثير كل تفاعل على جودة الصورة',
    'تحديد الطاقة المناسبة لكل نوع من أنواع التصوير',
    'تقييم تأثير التفاعلات على الجرعة الإشعاعية']

  },
  {
    category: 'التحليل والتقييم',
    categoryEn: 'Analysis & Evaluation',
    icon: <Lightbulb className="w-5 h-5" data-id="bet4fu425" data-path="src/pages/LearningObjectives.tsx" />,
    color: 'bg-orange-50 border-orange-200',
    items: [
    'مقارنة الأهمية النسبية للتفاعلات في ظروف مختلفة',
    'تحليل البيانات من قواعد بيانات NIST وغيرها',
    'تقييم تأثير معاملات التوهين على الاختراق والامتصاص',
    'حل مسائل متقدمة في تفاعل الإشعاع مع المادة']

  }];


  const skillsMatrix = [
  {
    skill: 'فهم المفاهيم الأساسية',
    level: 'أساسي',
    description: 'التعرف على أنواع التفاعلات ومبادئها'
  },
  {
    skill: 'تطبيق المعادلات',
    level: 'متوسط',
    description: 'استخدام الصيغ الرياضية في حل المسائل'
  },
  {
    skill: 'التحليل السريري',
    level: 'متقدم',
    description: 'ربط النظرية بالتطبيقات العملية'
  },
  {
    skill: 'التقييم النقدي',
    level: 'متقدم',
    description: 'تحليل وتقييم البيانات والنتائج'
  }];


  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-slate-50 to-purple-50" data-id="p0re903n4" data-path="src/pages/LearningObjectives.tsx">
      <Navigation
        title="أهداف التعلم"
        titleEn="Learning Objectives" data-id="258qw8fce" data-path="src/pages/LearningObjectives.tsx" />

      
      <div className="container mx-auto px-4 py-8" data-id="66f0fh2wo" data-path="src/pages/LearningObjectives.tsx">
        {/* Header */}
        <Card className="mb-8" data-id="h45oj0fbs" data-path="src/pages/LearningObjectives.tsx">
          <CardHeader data-id="ooe26ltgy" data-path="src/pages/LearningObjectives.tsx">
            <div className="flex items-center gap-3" data-id="58lsha2q9" data-path="src/pages/LearningObjectives.tsx">
              <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center" data-id="z258lqygd" data-path="src/pages/LearningObjectives.tsx">
                <Target className="w-5 h-5 text-indigo-600" data-id="f5oa66cfz" data-path="src/pages/LearningObjectives.tsx" />
              </div>
              <div data-id="4cyqcm8m7" data-path="src/pages/LearningObjectives.tsx">
                <CardTitle className="text-2xl text-right" data-id="9p8qc2oj2" data-path="src/pages/LearningObjectives.tsx">أهداف التعلم</CardTitle>
                <p className="text-gray-600 text-right" data-id="6xiae9mtp" data-path="src/pages/LearningObjectives.tsx">
                  ما يجب على الطالب تحقيقه بعد دراسة هذا الفصل
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent data-id="odks5d84n" data-path="src/pages/LearningObjectives.tsx">
            <p className="text-gray-700 text-right leading-relaxed" data-id="ua2q1x4my" data-path="src/pages/LearningObjectives.tsx">
              يهدف هذا الفصل إلى تزويد الطلاب بفهم شامل لآليات تفاعل الأشعة السينية مع المادة، 
              وتطبيق هذه المعرفة في السياق السريري والعملي.
            </p>
          </CardContent>
        </Card>

        {/* Main Objectives */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8" data-id="yh6art70s" data-path="src/pages/LearningObjectives.tsx">
          {objectives.map((category, index) =>
          <Card key={index} className={`${category.color}`} data-id="zxgv9t20r" data-path="src/pages/LearningObjectives.tsx">
              <CardHeader data-id="4j817yi7n" data-path="src/pages/LearningObjectives.tsx">
                <div className="flex items-center gap-3" data-id="sr3vnt71x" data-path="src/pages/LearningObjectives.tsx">
                  <div className="p-2 bg-white rounded-lg" data-id="510fjw2oo" data-path="src/pages/LearningObjectives.tsx">
                    {category.icon}
                  </div>
                  <div data-id="438n7q6m7" data-path="src/pages/LearningObjectives.tsx">
                    <CardTitle className="text-lg text-right" data-id="m6v2ujsam" data-path="src/pages/LearningObjectives.tsx">{category.category}</CardTitle>
                    <p className="text-sm text-gray-600 italic" data-id="hykowzhot" data-path="src/pages/LearningObjectives.tsx">{category.categoryEn}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent data-id="1481z49r6" data-path="src/pages/LearningObjectives.tsx">
                <ul className="space-y-3" data-id="gssg91d7a" data-path="src/pages/LearningObjectives.tsx">
                  {category.items.map((item, itemIndex) =>
                <li key={itemIndex} className="flex items-start gap-3 text-right" data-id="drwk3a4ur" data-path="src/pages/LearningObjectives.tsx">
                      <CheckCircle className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" data-id="aodwjhgjp" data-path="src/pages/LearningObjectives.tsx" />
                      <span className="text-sm leading-relaxed" data-id="pg1847s6f" data-path="src/pages/LearningObjectives.tsx">{item}</span>
                    </li>
                )}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Skills Matrix */}
        <Card className="mb-8" data-id="exmf6ax0j" data-path="src/pages/LearningObjectives.tsx">
          <CardHeader data-id="hyumn97ea" data-path="src/pages/LearningObjectives.tsx">
            <CardTitle className="text-xl text-right" data-id="h79phop7k" data-path="src/pages/LearningObjectives.tsx">مصفوفة المهارات المطلوبة</CardTitle>
            <p className="text-gray-600 text-right" data-id="53xkvwg7h" data-path="src/pages/LearningObjectives.tsx">توزيع المهارات حسب مستوى الصعوبة</p>
          </CardHeader>
          <CardContent data-id="emberzwqy" data-path="src/pages/LearningObjectives.tsx">
            <div className="space-y-4" data-id="55i0mqubc" data-path="src/pages/LearningObjectives.tsx">
              {skillsMatrix.map((skill, index) =>
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg" data-id="zgyxqeoyd" data-path="src/pages/LearningObjectives.tsx">
                  <div className="flex items-center gap-3" data-id="j4bpti0ds" data-path="src/pages/LearningObjectives.tsx">
                    <Badge
                    variant={skill.level === 'أساسي' ? 'secondary' : skill.level === 'متوسط' ? 'default' : 'destructive'} data-id="np3ccs97b" data-path="src/pages/LearningObjectives.tsx">

                      {skill.level}
                    </Badge>
                    <span className="text-sm text-gray-600" data-id="vxiswbvyf" data-path="src/pages/LearningObjectives.tsx">{skill.description}</span>
                  </div>
                  <h3 className="font-semibold text-right" data-id="lckybd4pq" data-path="src/pages/LearningObjectives.tsx">{skill.skill}</h3>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Assessment Criteria */}
        <Card className="mb-8" data-id="16iw21vb2" data-path="src/pages/LearningObjectives.tsx">
          <CardHeader data-id="89d977ucw" data-path="src/pages/LearningObjectives.tsx">
            <CardTitle className="text-xl text-right" data-id="n0hgc05tw" data-path="src/pages/LearningObjectives.tsx">معايير التقييم</CardTitle>
          </CardHeader>
          <CardContent data-id="2ott1l9e4" data-path="src/pages/LearningObjectives.tsx">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4" data-id="jpnr6duq5" data-path="src/pages/LearningObjectives.tsx">
              <Card className="border-green-200" data-id="2ro8ekx7e" data-path="src/pages/LearningObjectives.tsx">
                <CardHeader className="pb-3" data-id="fmw46lpr9" data-path="src/pages/LearningObjectives.tsx">
                  <CardTitle className="text-base text-right text-green-700" data-id="901lifqbv" data-path="src/pages/LearningObjectives.tsx">امتياز (90-100%)</CardTitle>
                </CardHeader>
                <CardContent data-id="5brvqaxzu" data-path="src/pages/LearningObjectives.tsx">
                  <ul className="text-sm space-y-1 text-right" data-id="vj3b3me9c" data-path="src/pages/LearningObjectives.tsx">
                    <li data-id="6yerf7oge" data-path="src/pages/LearningObjectives.tsx">• فهم عميق لجميع المفاهيم</li>
                    <li data-id="qldbweeki" data-path="src/pages/LearningObjectives.tsx">• حل مسائل معقدة بدقة</li>
                    <li data-id="gogg3mros" data-path="src/pages/LearningObjectives.tsx">• ربط النظرية بالتطبيق</li>
                    <li data-id="k25puqwbo" data-path="src/pages/LearningObjectives.tsx">• تحليل نقدي للبيانات</li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card className="border-blue-200" data-id="q113qguzp" data-path="src/pages/LearningObjectives.tsx">
                <CardHeader className="pb-3" data-id="1yfmefjtj" data-path="src/pages/LearningObjectives.tsx">
                  <CardTitle className="text-base text-right text-blue-700" data-id="n3c0zx49k" data-path="src/pages/LearningObjectives.tsx">جيد جداً (80-89%)</CardTitle>
                </CardHeader>
                <CardContent data-id="53r63yobr" data-path="src/pages/LearningObjectives.tsx">
                  <ul className="text-sm space-y-1 text-right" data-id="8ec22tevc" data-path="src/pages/LearningObjectives.tsx">
                    <li data-id="787s77iae" data-path="src/pages/LearningObjectives.tsx">• فهم جيد للمفاهيم الأساسية</li>
                    <li data-id="pplsby3k2" data-path="src/pages/LearningObjectives.tsx">• حل المسائل العادية</li>
                    <li data-id="4r6bxvmp1" data-path="src/pages/LearningObjectives.tsx">• تطبيق المعادلات بشكل صحيح</li>
                    <li data-id="rwdqsour6" data-path="src/pages/LearningObjectives.tsx">• فهم التطبيقات السريرية</li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card className="border-yellow-200" data-id="obkjaza3n" data-path="src/pages/LearningObjectives.tsx">
                <CardHeader className="pb-3" data-id="o0by62kku" data-path="src/pages/LearningObjectives.tsx">
                  <CardTitle className="text-base text-right text-yellow-700" data-id="3bfqeceyo" data-path="src/pages/LearningObjectives.tsx">مقبول (70-79%)</CardTitle>
                </CardHeader>
                <CardContent data-id="q0ess7371" data-path="src/pages/LearningObjectives.tsx">
                  <ul className="text-sm space-y-1 text-right" data-id="a5nh2bvre" data-path="src/pages/LearningObjectives.tsx">
                    <li data-id="bnsor7f04" data-path="src/pages/LearningObjectives.tsx">• فهم أساسي للمفاهيم</li>
                    <li data-id="zfs1mf4b4" data-path="src/pages/LearningObjectives.tsx">• حل مسائل بسيطة</li>
                    <li data-id="cid13kgv4" data-path="src/pages/LearningObjectives.tsx">• معرفة المعادلات الأساسية</li>
                    <li data-id="jum57evs2" data-path="src/pages/LearningObjectives.tsx">• إدراك التطبيقات العامة</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>

        {/* Study Timeline */}
        <Card data-id="crgzc1pub" data-path="src/pages/LearningObjectives.tsx">
          <CardHeader data-id="5tk6ipbe9" data-path="src/pages/LearningObjectives.tsx">
            <CardTitle className="text-xl text-right" data-id="q9ucbm8vc" data-path="src/pages/LearningObjectives.tsx">الجدول الزمني المقترح للدراسة</CardTitle>
          </CardHeader>
          <CardContent data-id="2myzxabzl" data-path="src/pages/LearningObjectives.tsx">
            <div className="space-y-4" data-id="mnfbqkfco" data-path="src/pages/LearningObjectives.tsx">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" data-id="68qvud84e" data-path="src/pages/LearningObjectives.tsx">
                <div className="bg-blue-50 p-4 rounded-lg text-center" data-id="r0x101wr4" data-path="src/pages/LearningObjectives.tsx">
                  <h3 className="font-semibold text-blue-800 mb-2" data-id="44l4s5qum" data-path="src/pages/LearningObjectives.tsx">الأسبوع الأول</h3>
                  <p className="text-sm text-blue-600" data-id="nqy3lbv8o" data-path="src/pages/LearningObjectives.tsx">المفاهيم الأساسية والتشتت المتماسك</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center" data-id="67pg49rt5" data-path="src/pages/LearningObjectives.tsx">
                  <h3 className="font-semibold text-green-800 mb-2" data-id="xzokzevn2" data-path="src/pages/LearningObjectives.tsx">الأسبوع الثاني</h3>
                  <p className="text-sm text-green-600" data-id="et2jqurfh" data-path="src/pages/LearningObjectives.tsx">التأثير الكهروضوئي وتشتت كومبتون</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg text-center" data-id="512wu58fn" data-path="src/pages/LearningObjectives.tsx">
                  <h3 className="font-semibold text-purple-800 mb-2" data-id="y0ugxt8td" data-path="src/pages/LearningObjectives.tsx">الأسبوع الثالث</h3>
                  <p className="text-sm text-purple-600" data-id="9txuvt1pu" data-path="src/pages/LearningObjectives.tsx">معاملات التوهين والتطبيقات</p>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg text-center" data-id="b7bscnx9n" data-path="src/pages/LearningObjectives.tsx">
                  <h3 className="font-semibold text-orange-800 mb-2" data-id="xdtcphumw" data-path="src/pages/LearningObjectives.tsx">الأسبوع الرابع</h3>
                  <p className="text-sm text-orange-600" data-id="23mco6hs5" data-path="src/pages/LearningObjectives.tsx">المراجعة وحل المسائل</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default LearningObjectives;