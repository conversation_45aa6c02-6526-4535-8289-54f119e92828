import { Button } from "@/components/ui/button";
import { motion } from "motion/react";
import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSwitcher from '@/components/LanguageSwitcher';

const NotFound = () => {
  const location = useLocation();
  const { dir } = useLanguage();
  const { t } = useTranslation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background" dir={dir} data-id="w3mbcv5kw" data-path="src/pages/NotFound.tsx">
      {/* Language Switcher */}
      <div className="fixed top-4 right-4 z-50" data-id="rjd4gduze" data-path="src/pages/NotFound.tsx">
        <LanguageSwitcher data-id="eb6zq4pdb" data-path="src/pages/NotFound.tsx" />
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-6 p-8" data-id="7i4qrvkf6" data-path="src/pages/NotFound.tsx">

        <motion.div
          initial={{ scale: 0.5 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }} data-id="ekp4lxeyn" data-path="src/pages/NotFound.tsx">

          <h1 className="text-8xl font-bold text-primary" data-id="ouncwrw31" data-path="src/pages/NotFound.tsx">404</h1>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="space-y-4" data-id="s0aeuvk38" data-path="src/pages/NotFound.tsx">

          <h2 className="text-2xl font-semibold tracking-tight" data-id="59ijhfpeh" data-path="src/pages/NotFound.tsx">{t('notfound.title')}</h2>
          <p className="text-muted-foreground" data-id="ohb1hnwal" data-path="src/pages/NotFound.tsx">
            {t('notfound.message')}
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }} data-id="n48w5m358" data-path="src/pages/NotFound.tsx">

          <Button asChild variant="default" size="lg" data-id="nm3uczmoo" data-path="src/pages/NotFound.tsx">
            <a href="/" data-id="240p4nu7o" data-path="src/pages/NotFound.tsx">{t('common.back_to_home')}</a>
          </Button>
        </motion.div>
      </motion.div>
    </div>);

};

export default NotFound;