import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Monitor, Camera, Zap, BarChart3, PlayCircle, BookOpen } from 'lucide-react';
import { motion } from 'motion/react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSwitcher from '@/components/LanguageSwitcher';

const Chapter11Content = () => {
  const [activeSection, setActiveSection] = useState('introduction');
  const [vrMode, setVrMode] = useState(false);
  const { dir } = useLanguage();
  const { t } = useTranslation();

  const sections = [
  {
    id: 'introduction',
    title: t('chapter11.introduction.title'),
    icon: <BookOpen className="w-5 h-5" data-id="wqdxc7uwt" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: dir === 'rtl' ? `
          تطورت أجهزة الكشف بالأشعة السينية عبر عقود من البحث والتطوير، من الأنظمة التناظرية البسيطة 
          إلى الأنظمة الرقمية المعقدة. هذا التطور شمل ثلاث مراحل رئيسية:
          
          1. **المرحلة التناظرية (1895-1980)**: بدأت مع اكتشاف رونتغن للأشعة السينية
          2. **المرحلة المحوسبة (1980-1995)**: ظهور التصوير الشعاعي المحوسب (CR)
          3. **المرحلة الرقمية (1995-الآن)**: التصوير الرقمي المباشر (DR)
        ` : `
          X-ray detection systems have evolved over decades of research and development, from simple analog systems 
          to complex digital systems. This evolution included three main phases:
          
          1. **Analog Phase (1895-1980)**: Started with Röntgen's discovery of X-rays
          2. **Computed Phase (1980-1995)**: Emergence of Computed Radiography (CR)
          3. **Digital Phase (1995-present)**: Direct Digital Radiography (DR)
        `,
      vrContent: dir === 'rtl' ? 'نموذج ثلاثي الأبعاد تفاعلي يوضح تطور أجهزة الكشف عبر الزمن' : 'Interactive 3D model showing detector evolution over time',
      equations: [
      dir === 'rtl' ? 'كفاءة الكشف = (عدد الفوتونات المكتشفة) / (عدد الفوتونات الساقطة)' : 'Detection Efficiency = (Detected Photons) / (Incident Photons)',
      dir === 'rtl' ? 'دقة التباين = (S_max - S_min) / (S_max + S_min)' : 'Contrast Resolution = (S_max - S_min) / (S_max + S_min)']

    }
  },
  {
    id: 'film-screen',
    title: t('chapter11.film_screen.title'),
    icon: <Camera className="w-5 h-5" data-id="hbl28dlfa" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: dir === 'rtl' ? `
          أنظمة الشاشة والفيلم كانت الأساس في التصوير الشعاعي لعقود طويلة. تتكون من:
          
          **مكونات النظام:**
          - **الفيلم الفوتوغرافي**: يحتوي على بلورات هاليد الفضة الحساسة للضوء
          - **الشاشة المكثفة**: تحتوي على فوسفور يحول الأشعة السينية إلى ضوء مرئي
          - **الكاسيت**: يحمي الفيلم ويضمن التلامس الجيد مع الشاشة
          
          **مبدأ العمل:**
          1. الأشعة السينية تخترق المريض وتصل إلى الشاشة المكثفة
          2. الفوسفور في الشاشة يمتص الأشعة السينية ويصدر ضوء مرئي
          3. الضوء المرئي يعرض الفيلم الفوتوغرافي
          4. الفيلم المعرض يُطور كيميائياً لإظهار الصورة
        ` : `
          Screen-film systems were the foundation of radiography for many decades. They consist of:
          
          **System Components:**
          - **Photographic Film**: Contains silver halide crystals sensitive to light
          - **Intensifying Screen**: Contains phosphor that converts X-rays to visible light
          - **Cassette**: Protects the film and ensures good contact with the screen
          
          **Working Principle:**
          1. X-rays penetrate the patient and reach the intensifying screen
          2. Phosphor in the screen absorbs X-rays and emits visible light
          3. Visible light exposes the photographic film
          4. Exposed film is chemically processed to reveal the image
        `,
      vrContent: dir === 'rtl' ? 'محاكاة ثلاثية الأبعاد لعملية تكوين الصورة في نظام الشاشة والفيلم' : '3D simulation of image formation in screen-film system',
      advantages: dir === 'rtl' ? ['تكلفة منخفضة', 'دقة مكانية عالية', 'نطاق ديناميكي واسع'] : ['Low cost', 'High spatial resolution', 'Wide dynamic range'],
      disadvantages: dir === 'rtl' ? ['معالجة كيميائية', 'تخزين فيزيائي', 'عدم إمكانية التعديل الرقمي'] : ['Chemical processing', 'Physical storage', 'No digital manipulation']
    }
  },
  {
    id: 'computed-radiography',
    title: t('chapter11.computed_radiography.title'),
    icon: <Monitor className="w-5 h-5" data-id="3ureqkjzh" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: dir === 'rtl' ? `
          التصوير الشعاعي المحوسب يستخدم الفوسفور القابل للتحفيز الضوئي (PSP) لتخزين طاقة الأشعة السينية.
          
          **مبدأ الفوسفور القابل للتحفيز الضوئي:**
          - **المادة الأساسية**: بروميد الباريوم المخلوط بالأوروبيوم (BaBrF:Eu)
          - **آلية التخزين**: الإلكترونات المثارة تُحبس في مستويات طاقة وسطية
          - **القراءة**: ليزر أحمر يحرر الإلكترونات المحبوسة فتصدر ضوء أزرق
          
          **مراحل العمل:**
          1. **التعرض**: الأشعة السينية تخزن كصورة كامنة في اللوح الفوسفوري
          2. **القراءة**: الليزر يمسح اللوح ويحول الصورة الكامنة إلى إشارة ضوئية
          3. **التحويل**: أنبوب مضاعف الضوء (PMT) يحول الضوء إلى إشارة كهربائية
          4. **الرقمنة**: محول تناظري-رقمي (ADC) يحول الإشارة إلى بيانات رقمية
        ` : `
          Computed radiography uses photostimulable phosphor (PSP) to store X-ray energy.
          
          **Photostimulable Phosphor Principle:**
          - **Base Material**: Barium bromide doped with europium (BaBrF:Eu)
          - **Storage Mechanism**: Excited electrons are trapped in intermediate energy levels
          - **Readout**: Red laser releases trapped electrons emitting blue light
          
          **Working Phases:**
          1. **Exposure**: X-rays stored as latent image in phosphor plate
          2. **Readout**: Laser scans plate converting latent image to light signal
          3. **Conversion**: Photomultiplier tube (PMT) converts light to electrical signal
          4. **Digitization**: Analog-to-digital converter (ADC) converts signal to digital data
        `,
      vrContent: dir === 'rtl' ? 'نموذج تفاعلي يوضح عملية تحفيز الفوسفور وقراءة الصورة' : 'Interactive model showing phosphor stimulation and image readout',
      technicalSpecs: {
        [dir === 'rtl' ? 'دقة مكانية' : 'Spatial Resolution']: '2.5-5 lp/mm',
        [dir === 'rtl' ? 'عمق البت' : 'Bit Depth']: '10-12 bit',
        [dir === 'rtl' ? 'كفاءة الكم' : 'Quantum Efficiency']: '15-25%',
        [dir === 'rtl' ? 'نطاق التعرض' : 'Exposure Range']: '1:10000'
      }
    }
  },
  {
    id: 'digital-radiography',
    title: t('chapter11.digital_radiography.title'),
    icon: <Zap className="w-5 h-5" data-id="n2r0vvb7g" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: dir === 'rtl' ? `
          التصوير الرقمي المباشر يحول الأشعة السينية إلى إشارة كهربائية مباشرة دون خطوات وسطية.
          
          **التحويل غير المباشر:**
          يتم على مرحلتين: أشعة سينية → ضوء → إشارة كهربائية
          
          **أ) الومضات + الثنائيات الضوئية:**
          - **المادة الومضة**: يوديد السيزيوم (CsI) أو أكسيد الغادولينيوم (Gd2O2S)
          - **ثنائيات السيليكون**: تحول الضوء إلى شحنات كهربائية
          - **مصفوفة TFT**: ترانزستورات الأغشية الرقيقة لقراءة الإشارة
          
          **ب) أجهزة CCD:**
          - تستخدم مع شاشة فوسفورية ونظام عدسات
          - دقة عالية لكن مجال رؤية محدود
          - مناسبة للتطبيقات المتخصصة
          
          **ج) أجهزة CMOS:**
          - استهلاك طاقة أقل من CCD
          - قراءة أسرع ومعالجة محلية
          - مقاومة أفضل للإشعاع
        ` : `
          Direct digital radiography converts X-rays to electrical signals directly without intermediate steps.
          
          **Indirect Conversion:**
          Two-step process: X-rays → Light → Electrical signal
          
          **a) Scintillators + Photodiodes:**
          - **Scintillator Material**: Cesium iodide (CsI) or Gadolinium oxysulfide (Gd2O2S)
          - **Silicon Photodiodes**: Convert light to electrical charges
          - **TFT Array**: Thin-film transistors for signal readout
          
          **b) CCD Devices:**
          - Used with phosphor screen and lens system
          - High resolution but limited field of view
          - Suitable for specialized applications
          
          **c) CMOS Devices:**
          - Lower power consumption than CCD
          - Faster readout and local processing
          - Better radiation resistance
        `,
      vrContent: dir === 'rtl' ? 'تصور ثلاثي الأبعاد لبنية الكاشف وعملية التحويل' : '3D visualization of detector structure and conversion process',
      comparison: {
        'CsI': {
          resolution: dir === 'rtl' ? 'عالية' : 'High',
          efficiency: '20-25%',
          structure: dir === 'rtl' ? 'عمودية' : 'Columnar'
        },
        'Gd2O2S': {
          resolution: dir === 'rtl' ? 'متوسطة' : 'Medium',
          efficiency: '15-20%',
          structure: dir === 'rtl' ? 'بودرة' : 'Powder'
        }
      }
    }
  },
  {
    id: 'direct-conversion',
    title: t('chapter11.direct_conversion.title'),
    icon: <Zap className="w-5 h-5" data-id="90nr5wiuj" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: dir === 'rtl' ? `
          **التحويل المباشر - السيلينيوم غير المتبلور:**
          
          يحول الأشعة السينية مباشرة إلى شحنات كهربائية دون مرحلة الضوء الوسطية.
          
          **مبدأ العمل:**
          1. **امتصاص الفوتون**: فوتون الأشعة السينية يُمتص في طبقة السيلينيوم
          2. **توليد الشحنات**: كل فوتون يولد آلاف أزواج الإلكترون-الثقب
          3. **الانجراف**: مجال كهربائي قوي (10 V/μm) يفصل الشحنات
          4. **الجمع**: الشحنات تُجمع في مصفوفة البكسل
          
          **مميزات السيلينيوم:**
          - **دقة مكانية عالية**: عدم انتشار الضوء الجانبي
          - **استجابة خطية**: علاقة مباشرة بين التعرض والإشارة
          - **كفاءة كم جيدة**: 40-80% في النطاق التشخيصي
          
          **التحديات:**
          - حساسية للحرارة والرطوبة
          - يتطلب جهد كهربائي عالي
          - محدود بسماكة الطبقة الحساسة
        ` : `
          **Direct Conversion - Amorphous Selenium:**
          
          Converts X-rays directly to electrical charges without intermediate light stage.
          
          **Working Principle:**
          1. **Photon Absorption**: X-ray photon absorbed in selenium layer
          2. **Charge Generation**: Each photon generates thousands of electron-hole pairs
          3. **Drift**: Strong electric field (10 V/μm) separates charges
          4. **Collection**: Charges collected in pixel array
          
          **Selenium Advantages:**
          - **High Spatial Resolution**: No lateral light spread
          - **Linear Response**: Direct relationship between exposure and signal
          - **Good Quantum Efficiency**: 40-80% in diagnostic range
          
          **Challenges:**
          - Sensitive to temperature and humidity
          - Requires high electric voltage
          - Limited by sensitive layer thickness
        `,
      vrContent: dir === 'rtl' ? 'نموذج جزيئي تفاعلي يوضح عملية توليد وانجراف الشحنات' : 'Interactive molecular model showing charge generation and drift',
      performance: {
        [dir === 'rtl' ? 'السماكة النموذجية' : 'Typical Thickness']: '100-1000 μm',
        [dir === 'rtl' ? 'الجهد المطبق' : 'Applied Voltage']: '1000-10000 V',
        [dir === 'rtl' ? 'دقة البكسل' : 'Pixel Pitch']: '50-200 μm',
        [dir === 'rtl' ? 'معدل الإطارات' : 'Frame Rate']: '1-30 fps'
      }
    }
  },
  {
    id: 'performance-metrics',
    title: t('chapter11.performance_metrics.title'),
    icon: <BarChart3 className="w-5 h-5" data-id="zgbpuyoxu" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: dir === 'rtl' ? `
          **كفاءة الكم (QE) وكفاءة الكم الاستقصائية (DQE):**
          
          **كفاءة الكم:**
          QE(E) = η_abs(E) × η_conv(E)
          
          حيث:
          - η_abs: كفاءة الامتصاص
          - η_conv: كفاءة التحويل
          
          **كفاءة الكم الاستقصائية:**
          DQE(f) = [MTF(f)]² × QE / [1 + (σ_add/σ_quantum)²]
          
          حيث:
          - MTF: دالة نقل التعديل
          - σ_add: الضوضاء المضافة
          - σ_quantum: الضوضاء الكمية
          
          **دالة نقل التعديل (MTF):**
          
          تقيس قدرة النظام على نقل التفاصيل المكانية:
          MTF(f) = |FFT[LSF(x)]|
          
          حيث LSF هي دالة الانتشار الخطي.
          
          **طيف قدرة الضوضاء (NPS):**
          NPS(f) = pixel_size² × |FFT[noise_image - mean]|²
          
          **المعايير الأساسية:**
          
          **الدقة المكانية:**
          - تُقاس بخطوط الأزواج في المليمتر (lp/mm)
          - تحدد أصغر تفصيل يمكن تمييزه
          
          **دقة التباين:**
          - القدرة على تمييز الاختلافات الطفيفة في الكثافة
          - تعتمد على نسبة الإشارة إلى الضوضاء (SNR)
          
          **النطاق الديناميكي:**
          - نسبة أقصى إشارة إلى أدنى إشارة قابلة للكشف
          - يُعبر عنه بـ dB أو نسبة عددية
        ` : `
          **Quantum Efficiency (QE) and Detective Quantum Efficiency (DQE):**
          
          **Quantum Efficiency:**
          QE(E) = η_abs(E) × η_conv(E)
          
          Where:
          - η_abs: Absorption efficiency
          - η_conv: Conversion efficiency
          
          **Detective Quantum Efficiency:**
          DQE(f) = [MTF(f)]² × QE / [1 + (σ_add/σ_quantum)²]
          
          Where:
          - MTF: Modulation Transfer Function
          - σ_add: Added noise
          - σ_quantum: Quantum noise
          
          **Modulation Transfer Function (MTF):**
          
          Measures system's ability to transfer spatial details:
          MTF(f) = |FFT[LSF(x)]|
          
          Where LSF is the Line Spread Function.
          
          **Noise Power Spectrum (NPS):**
          NPS(f) = pixel_size² × |FFT[noise_image - mean]|²
          
          **Basic Criteria:**
          
          **Spatial Resolution:**
          - Measured in line pairs per millimeter (lp/mm)
          - Determines smallest distinguishable detail
          
          **Contrast Resolution:**
          - Ability to distinguish subtle density differences
          - Depends on signal-to-noise ratio (SNR)
          
          **Dynamic Range:**
          - Ratio of maximum to minimum detectable signal
          - Expressed in dB or numerical ratio
        `,
      vrContent: dir === 'rtl' ? 'تصور تفاعلي لمنحنيات MTF وNPS وDQE' : 'Interactive visualization of MTF, NPS and DQE curves',
      benchmarks: {
        [dir === 'rtl' ? 'الأشعة العامة' : 'General Radiography']: { MTF_50: '2-4 lp/mm', DQE_0: '60-80%' },
        [dir === 'rtl' ? 'تصوير الثدي' : 'Mammography']: { MTF_50: '4-6 lp/mm', DQE_0: '40-60%' },
        [dir === 'rtl' ? 'التصوير الصدري' : 'Chest Imaging']: { MTF_50: '2-3 lp/mm', DQE_0: '50-70%' }
      }
    }
  }];

  const InteractiveVisualization = ({ section }: {section: any;}) =>
  <motion.div
    initial={{ opacity: 0, scale: 0.9 }}
    animate={{ opacity: 1, scale: 1 }}
    className="bg-gradient-to-br from-blue-50 to-purple-50 p-6 rounded-lg border" data-id="ahegngf9h" data-path="src/components/Chapter11Content.tsx">

      <div className="flex items-center justify-between mb-4" data-id="o7668iwct" data-path="src/components/Chapter11Content.tsx">
        <h3 className={`text-lg font-semibold ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="3unyou6kt" data-path="src/components/Chapter11Content.tsx">
          {vrMode ? t('chapter11.vr_mode') : t('chapter11.interactive_visualization')}
        </h3>
        <Button
        variant={vrMode ? "default" : "outline"}
        size="sm"
        onClick={() => setVrMode(!vrMode)} data-id="48jmpmuqa" data-path="src/components/Chapter11Content.tsx">

          <PlayCircle className={`w-4 h-4 ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`} data-id="vn7r79mo6" data-path="src/components/Chapter11Content.tsx" />
          {vrMode ? t('chapter11.stop_vr') : t('chapter11.start_vr')}
        </Button>
      </div>
      
      <div className="aspect-video bg-gradient-to-br from-slate-800 to-slate-900 rounded-lg flex items-center justify-center text-white" data-id="iy8sju4r1" data-path="src/components/Chapter11Content.tsx">
        {vrMode ?
      <motion.div
        initial={{ rotateY: 0 }}
        animate={{ rotateY: 360 }}
        transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
        className="text-center" data-id="jgmkb7z8g" data-path="src/components/Chapter11Content.tsx">

            <Monitor className="w-16 h-16 mx-auto mb-4" data-id="edx6jljlq" data-path="src/components/Chapter11Content.tsx" />
            <p className="text-lg" data-id="0111hl2m0" data-path="src/components/Chapter11Content.tsx">{section.content.vrContent}</p>
            <div className="mt-4 text-sm opacity-75" data-id="pg64yg4db" data-path="src/components/Chapter11Content.tsx">
              {t('chapter11.mouse_interact')}
            </div>
          </motion.div> :

      <div className="text-center" data-id="upk2b72z4" data-path="src/components/Chapter11Content.tsx">
            <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4" data-id="xvt7r8z72" data-path="src/components/Chapter11Content.tsx">
              {section.icon}
            </div>
            <p data-id="vpeszqbvy" data-path="src/components/Chapter11Content.tsx">{t('chapter11.click_start_vr')}</p>
          </div>
      }
      </div>
      
      {section.content.equations &&
    <div className="mt-4 p-4 bg-white rounded-lg" data-id="b3jnjcx9m" data-path="src/components/Chapter11Content.tsx">
          <h4 className={`font-semibold mb-2 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="jf1rly096" data-path="src/components/Chapter11Content.tsx">
            {t('chapter11.basic_equations')}
          </h4>
          {section.content.equations.map((eq: string, index: number) =>
      <div key={index} className="text-sm font-mono bg-slate-100 p-2 rounded mb-2 text-center" data-id="8rkdo1fib" data-path="src/components/Chapter11Content.tsx">
              {eq}
            </div>
      )}
        </div>
    }
    </motion.div>;

  const currentSection = sections.find((s) => s.id === activeSection);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6" dir={dir} data-id="ga3a286vn" data-path="src/components/Chapter11Content.tsx">
      <div className="max-w-7xl mx-auto" data-id="wwj1zl7ep" data-path="src/components/Chapter11Content.tsx">
        {/* Language Switcher */}
        <div className="fixed top-4 right-4 z-50" data-id="i39mea2rh" data-path="src/components/Chapter11Content.tsx">
          <LanguageSwitcher data-id="bdhdliifk" data-path="src/components/Chapter11Content.tsx" />
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8" data-id="31tga1h7o" data-path="src/components/Chapter11Content.tsx">

          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4" data-id="1ch3beqk6" data-path="src/components/Chapter11Content.tsx">
            {t('chapter11.title')}
          </h1>
          <p className="text-xl text-muted-foreground" data-id="xmouounbu" data-path="src/components/Chapter11Content.tsx">
            {t('chapter11.subtitle')}
          </p>
          <div className={`flex items-center justify-center space-x-4 mt-4 ${dir === 'rtl' ? 'rtl:space-x-reverse' : ''}`} data-id="py4gpj318" data-path="src/components/Chapter11Content.tsx">
            <Badge variant="outline" data-id="jy124ujh5" data-path="src/components/Chapter11Content.tsx">{dir === 'rtl' ? 'التصوير الطبي' : 'Medical Imaging'}</Badge>
            <Badge variant="outline" data-id="kbddzuymh" data-path="src/components/Chapter11Content.tsx">{dir === 'rtl' ? 'الفيزياء الطبية' : 'Medical Physics'}</Badge>
            <Badge variant="outline" data-id="lu88cw7s4" data-path="src/components/Chapter11Content.tsx">{dir === 'rtl' ? 'التكنولوجيا الحديثة' : 'Modern Technology'}</Badge>
          </div>
        </motion.div>

        {/* Navigation */}
        <Card className="mb-8" data-id="f6o7d1a08" data-path="src/components/Chapter11Content.tsx">
          <CardHeader data-id="smiqdxr8l" data-path="src/components/Chapter11Content.tsx">
            <CardTitle className={dir === 'rtl' ? 'text-right' : 'text-left'} data-id="1dszc0u68" data-path="src/components/Chapter11Content.tsx">
              {dir === 'rtl' ? 'محتويات الفصل' : 'Chapter Contents'}
            </CardTitle>
          </CardHeader>
          <CardContent data-id="64y6r1jdy" data-path="src/components/Chapter11Content.tsx">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2" data-id="tdlwixg7e" data-path="src/components/Chapter11Content.tsx">
              {sections.map((section) =>
              <Button
                key={section.id}
                variant={activeSection === section.id ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveSection(section.id)}
                className={`${dir === 'rtl' ? 'justify-end' : 'justify-start'}`} data-id="yp8muyiqt" data-path="src/components/Chapter11Content.tsx">

                  <span className={dir === 'rtl' ? 'mr-2' : 'ml-2'} data-id="ff3vvjcsb" data-path="src/components/Chapter11Content.tsx">{section.title}</span>
                  {section.icon}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-8" data-id="1mw8sw61b" data-path="src/components/Chapter11Content.tsx">
          {/* Theory Content */}
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }} data-id="eqejindrt" data-path="src/components/Chapter11Content.tsx">

            <Card className="h-full" data-id="fgvpmab2l" data-path="src/components/Chapter11Content.tsx">
              <CardHeader data-id="mrftxfv5t" data-path="src/components/Chapter11Content.tsx">
                <div className="flex items-center justify-between" data-id="1n0at2qdr" data-path="src/components/Chapter11Content.tsx">
                  <CardTitle className={`flex items-center space-x-3 ${dir === 'rtl' ? 'text-right rtl:space-x-reverse' : 'text-left'}`} data-id="6tzbofphf" data-path="src/components/Chapter11Content.tsx">
                    {currentSection?.icon}
                    <span data-id="ea5b7umxm" data-path="src/components/Chapter11Content.tsx">{currentSection?.title}</span>
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-6" data-id="4hm038x21" data-path="src/components/Chapter11Content.tsx">
                <div className={`prose prose-sm max-w-none ${dir === 'rtl' ? 'text-right' : 'text-left'}`} style={{ direction: dir }} data-id="asgt9xvqk" data-path="src/components/Chapter11Content.tsx">
                  <div className="whitespace-pre-line text-sm leading-relaxed" data-id="zq6jzfnlt" data-path="src/components/Chapter11Content.tsx">
                    {currentSection?.content.theory}
                  </div>
                </div>

                {/* Additional Content Sections */}
                {currentSection?.content.advantages &&
                <div className="bg-green-50 p-4 rounded-lg" data-id="15pyxioos" data-path="src/components/Chapter11Content.tsx">
                    <h4 className={`font-semibold text-green-800 mb-2 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="bra491sdw" data-path="src/components/Chapter11Content.tsx">
                      {t('chapter11.advantages')}
                    </h4>
                    <ul className="text-sm text-green-700 space-y-1" data-id="p6brdv8dq" data-path="src/components/Chapter11Content.tsx">
                      {currentSection.content.advantages.map((advantage: string, index: number) =>
                    <li key={index} className={dir === 'rtl' ? 'text-right' : 'text-left'} data-id="95tpfvslw" data-path="src/components/Chapter11Content.tsx">• {advantage}</li>
                    )}
                    </ul>
                  </div>
                }

                {currentSection?.content.disadvantages &&
                <div className="bg-red-50 p-4 rounded-lg" data-id="prnf86jmr" data-path="src/components/Chapter11Content.tsx">
                    <h4 className={`font-semibold text-red-800 mb-2 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="fv6477y0w" data-path="src/components/Chapter11Content.tsx">
                      {t('chapter11.limitations')}
                    </h4>
                    <ul className="text-sm text-red-700 space-y-1" data-id="tmv6oo91l" data-path="src/components/Chapter11Content.tsx">
                      {currentSection.content.disadvantages.map((disadvantage: string, index: number) =>
                    <li key={index} className={dir === 'rtl' ? 'text-right' : 'text-left'} data-id="x2f14h6qs" data-path="src/components/Chapter11Content.tsx">• {disadvantage}</li>
                    )}
                    </ul>
                  </div>
                }

                {currentSection?.content.technicalSpecs &&
                <div className="bg-blue-50 p-4 rounded-lg" data-id="1rnoahx9l" data-path="src/components/Chapter11Content.tsx">
                    <h4 className={`font-semibold text-blue-800 mb-3 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="pjjds29r7" data-path="src/components/Chapter11Content.tsx">
                      {t('chapter11.technical_specs')}
                    </h4>
                    <div className="grid grid-cols-2 gap-3" data-id="76uww04vx" data-path="src/components/Chapter11Content.tsx">
                      {Object.entries(currentSection.content.technicalSpecs).map(([key, value]) =>
                    <div key={key} className="text-sm" data-id="6gim7l8sx" data-path="src/components/Chapter11Content.tsx">
                          <div className={`font-medium ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="fmr7a961g" data-path="src/components/Chapter11Content.tsx">{key}</div>
                          <div className={`text-blue-600 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="f3xc3rnov" data-path="src/components/Chapter11Content.tsx">{value}</div>
                        </div>
                    )}
                    </div>
                  </div>
                }
              </CardContent>
            </Card>
          </motion.div>

          {/* Interactive Visualization */}
          <motion.div
            key={`${activeSection}-viz`}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }} data-id="0e6zjwrov" data-path="src/components/Chapter11Content.tsx">

            {currentSection && <InteractiveVisualization section={currentSection} data-id="fwycx84ed" data-path="src/components/Chapter11Content.tsx" />}
          </motion.div>
        </div>

        {/* Learning Objectives */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8" data-id="4zy2hu87n" data-path="src/components/Chapter11Content.tsx">

          <Card data-id="7f7f9gs1d" data-path="src/components/Chapter11Content.tsx">
            <CardHeader data-id="8xjo1mmpl" data-path="src/components/Chapter11Content.tsx">
              <CardTitle className={dir === 'rtl' ? 'text-right' : 'text-left'} data-id="yuqs3bd9z" data-path="src/components/Chapter11Content.tsx">
                {t('chapter11.learning_objectives.title')}
              </CardTitle>
            </CardHeader>
            <CardContent data-id="jhqutb8fk" data-path="src/components/Chapter11Content.tsx">
              <div className="grid md:grid-cols-2 gap-4" data-id="thco55sx8" data-path="src/components/Chapter11Content.tsx">
                <div data-id="eeyo14dmn" data-path="src/components/Chapter11Content.tsx">
                  <h4 className={`font-semibold mb-2 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="hsdbsyte5" data-path="src/components/Chapter11Content.tsx">
                    {t('chapter11.theoretical_knowledge')}
                  </h4>
                  <ul className={`text-sm space-y-1 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="lve5ggzhb" data-path="src/components/Chapter11Content.tsx">
                    <li data-id="7g0a48tqv" data-path="src/components/Chapter11Content.tsx">• {dir === 'rtl' ? 'فهم مبادئ عمل أجهزة الكشف المختلفة' : 'Understanding working principles of different detectors'}</li>
                    <li data-id="c9hsqllbk" data-path="src/components/Chapter11Content.tsx">• {dir === 'rtl' ? 'مقارنة مميزات وقيود كل نوع' : 'Comparing advantages and limitations of each type'}</li>
                    <li data-id="meams5l2x" data-path="src/components/Chapter11Content.tsx">• {dir === 'rtl' ? 'تحليل مقاييس الأداء والجودة' : 'Analyzing performance and quality metrics'}</li>
                  </ul>
                </div>
                <div data-id="tmq12w23i" data-path="src/components/Chapter11Content.tsx">
                  <h4 className={`font-semibold mb-2 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="ra4n4981g" data-path="src/components/Chapter11Content.tsx">
                    {t('chapter11.practical_application')}
                  </h4>
                  <ul className={`text-sm space-y-1 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="mb9i2bwgm" data-path="src/components/Chapter11Content.tsx">
                    <li data-id="k2asdbjic" data-path="src/components/Chapter11Content.tsx">• {dir === 'rtl' ? 'اختيار الكاشف المناسب للتطبيق' : 'Selecting appropriate detector for application'}</li>
                    <li data-id="c3z1buowa" data-path="src/components/Chapter11Content.tsx">• {dir === 'rtl' ? 'تقييم جودة الصور السريرية' : 'Evaluating clinical image quality'}</li>
                    <li data-id="pg92k68hn" data-path="src/components/Chapter11Content.tsx">• {dir === 'rtl' ? 'حل مشاكل الأداء والصيانة' : 'Solving performance and maintenance issues'}</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>);

};

export default Chapter11Content;