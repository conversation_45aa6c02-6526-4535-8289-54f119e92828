import React from 'react';

interface BilingualContentProps {
  ar: React.ReactNode;
  en: React.ReactNode;
  language: 'ar' | 'en';
  className?: string;
}

const BilingualContent: React.FC<BilingualContentProps> = ({ 
  ar, 
  en, 
  language, 
  className = '' 
}) => {
  return (
    <div className={`${language === 'en' ? 'en' : ''} ${className}`}>
      {language === 'ar' ? ar : en}
    </div>
  );
};

export default BilingualContent;