import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Download, RotateCcw, Sliders } from 'lucide-react';

const ImageProcessingDemo = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const originalCanvasRef = useRef<HTMLCanvasElement>(null);

  const [filter, setFilter] = useState('none');
  const [brightness, setBrightness] = useState([0]);
  const [contrast, setContrast] = useState([100]);
  const [windowLevel, setWindowLevel] = useState([128]);
  const [windowWidth, setWindowWidth] = useState([256]);
  const [noise, setNoise] = useState([0]);
  const [imageData, setImageData] = useState(null);

  const filters = {
    none: 'Original',
    blur: 'Gaussian Blur',
    sharpen: 'Sharpen',
    edge: 'Edge Detection',
    emboss: 'Emboss',
    median: 'Median Filter'
  };

  // Generate sample medical image
  useEffect(() => {
    generateSampleImage();
  }, []);

  // Apply filters when parameters change
  useEffect(() => {
    if (imageData) {
      applyProcessing();
    }
  }, [filter, brightness, contrast, windowLevel, windowWidth, noise, imageData]);

  const generateSampleImage = () => {
    const canvas = originalCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;
    const imgData = ctx.createImageData(width, height);
    const data = imgData.data;

    // Create a synthetic CT-like image
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4;

        // Create different tissue regions
        const centerX = width / 2;
        const centerY = height / 2;
        const distFromCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);

        let intensity = 50; // Background (air)

        // Outer body
        if (distFromCenter < 120) {
          intensity = 120; // Soft tissue
        }

        // Organs
        if (distFromCenter < 80) {
          intensity = 140; // Organs
        }

        // Bones
        const bone1 = Math.sqrt((x - centerX + 40) ** 2 + (y - centerY + 30) ** 2);
        const bone2 = Math.sqrt((x - centerX - 40) ** 2 + (y - centerY + 30) ** 2);
        if (bone1 < 15 || bone2 < 15) {
          intensity = 220; // Bone
        }

        // Heart
        const heart = Math.sqrt((x - centerX + 10) ** 2 + (y - centerY) ** 2);
        if (heart < 25) {
          intensity = 160; // Heart
        }

        // Lungs (air-filled)
        const lung1 = Math.sqrt((x - centerX + 50) ** 2 + (y - centerY - 20) ** 2);
        const lung2 = Math.sqrt((x - centerX - 50) ** 2 + (y - centerY - 20) ** 2);
        if (lung1 < 30 || lung2 < 30) {
          intensity = 80; // Lung
        }

        // Add some texture
        intensity += Math.random() * 20 - 10;
        intensity = Math.max(0, Math.min(255, intensity));

        data[idx] = intensity; // R
        data[idx + 1] = intensity; // G
        data[idx + 2] = intensity; // B
        data[idx + 3] = 255; // A
      }
    }

    ctx.putImageData(imgData, 0, 0);
    setImageData(imgData);
  };

  const applyProcessing = () => {
    const canvas = canvasRef.current;
    const originalCanvas = originalCanvasRef.current;
    if (!canvas || !originalCanvas || !imageData) return;

    const ctx = canvas.getContext('2d');
    const originalCtx = originalCanvas.getContext('2d');
    if (!ctx || !originalCtx) return;

    // Get original image data
    const originalData = originalCtx.getImageData(0, 0, canvas.width, canvas.height);
    const processedData = ctx.createImageData(canvas.width, canvas.height);

    // Copy original data
    for (let i = 0; i < originalData.data.length; i++) {
      processedData.data[i] = originalData.data[i];
    }

    // Apply brightness and contrast
    for (let i = 0; i < processedData.data.length; i += 4) {
      let pixel = processedData.data[i];

      // Apply brightness
      pixel += brightness[0];

      // Apply contrast
      pixel = (pixel - 128) * (contrast[0] / 100) + 128;

      // Apply windowing
      const level = windowLevel[0];
      const width = windowWidth[0];
      const minVal = level - width / 2;
      const maxVal = level + width / 2;

      if (pixel <= minVal) pixel = 0;else
      if (pixel >= maxVal) pixel = 255;else
      pixel = (pixel - minVal) / width * 255;

      // Add noise
      if (noise[0] > 0) {
        pixel += (Math.random() - 0.5) * noise[0];
      }

      pixel = Math.max(0, Math.min(255, pixel));

      processedData.data[i] = pixel; // R
      processedData.data[i + 1] = pixel; // G
      processedData.data[i + 2] = pixel; // B
    }

    // Apply filters
    if (filter !== 'none') {
      applyFilter(processedData, filter);
    }

    ctx.putImageData(processedData, 0, 0);
  };

  const applyFilter = (imageData, filterType) => {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;

    switch (filterType) {
      case 'blur':
        applyConvolution(data, width, height, [
        1 / 9, 1 / 9, 1 / 9,
        1 / 9, 1 / 9, 1 / 9,
        1 / 9, 1 / 9, 1 / 9]
        );
        break;
      case 'sharpen':
        applyConvolution(data, width, height, [
        0, -1, 0,
        -1, 5, -1,
        0, -1, 0]
        );
        break;
      case 'edge':
        applyConvolution(data, width, height, [
        -1, -1, -1,
        -1, 8, -1,
        -1, -1, -1]
        );
        break;
      case 'emboss':
        applyConvolution(data, width, height, [
        -2, -1, 0,
        -1, 1, 1,
        0, 1, 2]
        );
        break;
      case 'median':
        applyMedianFilter(data, width, height);
        break;
    }
  };

  const applyConvolution = (data, width, height, kernel) => {
    const output = new Uint8ClampedArray(data);

    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        let sum = 0;

        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const idx = ((y + ky) * width + (x + kx)) * 4;
            const kernelIdx = (ky + 1) * 3 + (kx + 1);
            sum += data[idx] * kernel[kernelIdx];
          }
        }

        const outputIdx = (y * width + x) * 4;
        const value = Math.max(0, Math.min(255, sum));
        output[outputIdx] = value;
        output[outputIdx + 1] = value;
        output[outputIdx + 2] = value;
      }
    }

    for (let i = 0; i < data.length; i++) {
      data[i] = output[i];
    }
  };

  const applyMedianFilter = (data, width, height) => {
    const output = new Uint8ClampedArray(data);

    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const neighborhood = [];

        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const idx = ((y + ky) * width + (x + kx)) * 4;
            neighborhood.push(data[idx]);
          }
        }

        neighborhood.sort((a, b) => a - b);
        const median = neighborhood[4]; // Middle value

        const outputIdx = (y * width + x) * 4;
        output[outputIdx] = median;
        output[outputIdx + 1] = median;
        output[outputIdx + 2] = median;
      }
    }

    for (let i = 0; i < data.length; i++) {
      data[i] = output[i];
    }
  };

  const resetAll = () => {
    setFilter('none');
    setBrightness([0]);
    setContrast([100]);
    setWindowLevel([128]);
    setWindowWidth([256]);
    setNoise([0]);
  };

  const downloadImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = 'processed-image.png';
    link.href = canvas.toDataURL();
    link.click();
  };

  return (
    <div className="space-y-8" data-id="akigkfanc" data-path="src/components/ImageProcessingDemo.tsx">
      {/* Image Display */}
      <div className="grid md:grid-cols-2 gap-6" data-id="4rcoy95p2" data-path="src/components/ImageProcessingDemo.tsx">
        <Card data-id="31h1pxbf8" data-path="src/components/ImageProcessingDemo.tsx">
          <CardHeader data-id="lpr4uimy2" data-path="src/components/ImageProcessingDemo.tsx">
            <CardTitle data-id="xj04dy21r" data-path="src/components/ImageProcessingDemo.tsx">Original Image</CardTitle>
            <CardDescription data-id="sx24w2r6p" data-path="src/components/ImageProcessingDemo.tsx">Synthetic CT-like medical image</CardDescription>
          </CardHeader>
          <CardContent data-id="aekx7vq4c" data-path="src/components/ImageProcessingDemo.tsx">
            <canvas
              ref={originalCanvasRef}
              width={300}
              height={300}
              className="w-full border rounded-lg bg-black" data-id="rwpuod0e0" data-path="src/components/ImageProcessingDemo.tsx" />

          </CardContent>
        </Card>

        <Card data-id="hxb56t2xe" data-path="src/components/ImageProcessingDemo.tsx">
          <CardHeader data-id="a4rw4u0q0" data-path="src/components/ImageProcessingDemo.tsx">
            <CardTitle className="flex items-center justify-between" data-id="asonncdw0" data-path="src/components/ImageProcessingDemo.tsx">
              Processed Image
              <div className="flex gap-2" data-id="hza2n5w09" data-path="src/components/ImageProcessingDemo.tsx">
                <Button size="sm" variant="outline" onClick={downloadImage} data-id="qa5sjdbip" data-path="src/components/ImageProcessingDemo.tsx">
                  <Download className="w-4 h-4" data-id="sfmyhpqxu" data-path="src/components/ImageProcessingDemo.tsx" />
                </Button>
                <Button size="sm" variant="outline" onClick={resetAll} data-id="u3p4loqom" data-path="src/components/ImageProcessingDemo.tsx">
                  <RotateCcw className="w-4 h-4" data-id="oixmqz3xu" data-path="src/components/ImageProcessingDemo.tsx" />
                </Button>
              </div>
            </CardTitle>
            <CardDescription data-id="mkrceile6" data-path="src/components/ImageProcessingDemo.tsx">Image with applied processing</CardDescription>
          </CardHeader>
          <CardContent data-id="6cg5yi2qv" data-path="src/components/ImageProcessingDemo.tsx">
            <canvas
              ref={canvasRef}
              width={300}
              height={300}
              className="w-full border rounded-lg bg-black" data-id="5958esvtu" data-path="src/components/ImageProcessingDemo.tsx" />

          </CardContent>
        </Card>
      </div>

      {/* Processing Controls */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6" data-id="lqtusqyvo" data-path="src/components/ImageProcessingDemo.tsx">
        <Card data-id="gicmqk8j3" data-path="src/components/ImageProcessingDemo.tsx">
          <CardHeader data-id="ig0kp24yb" data-path="src/components/ImageProcessingDemo.tsx">
            <CardTitle className="flex items-center gap-2" data-id="5awaekyc8" data-path="src/components/ImageProcessingDemo.tsx">
              <Sliders className="w-5 h-5" data-id="q7ws5vz42" data-path="src/components/ImageProcessingDemo.tsx" />
              Basic Adjustments
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4" data-id="hikoi7zm0" data-path="src/components/ImageProcessingDemo.tsx">
            <div data-id="mmasyr6hx" data-path="src/components/ImageProcessingDemo.tsx">
              <Label className="text-sm font-medium" data-id="pp9qksctm" data-path="src/components/ImageProcessingDemo.tsx">Brightness</Label>
              <Slider
                value={brightness}
                onValueChange={setBrightness}
                max={100}
                min={-100}
                step={5}
                className="mt-2" data-id="rdajlhgu6" data-path="src/components/ImageProcessingDemo.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="nldpq9kzv" data-path="src/components/ImageProcessingDemo.tsx">
                <span data-id="4eu49rnx3" data-path="src/components/ImageProcessingDemo.tsx">-100</span>
                <span className="font-medium" data-id="1b5k9kxdf" data-path="src/components/ImageProcessingDemo.tsx">{brightness[0]}</span>
                <span data-id="z6cfu5ne6" data-path="src/components/ImageProcessingDemo.tsx">100</span>
              </div>
            </div>

            <div data-id="wtqr0a34n" data-path="src/components/ImageProcessingDemo.tsx">
              <Label className="text-sm font-medium" data-id="671ivk9wd" data-path="src/components/ImageProcessingDemo.tsx">Contrast (%)</Label>
              <Slider
                value={contrast}
                onValueChange={setContrast}
                max={300}
                min={25}
                step={5}
                className="mt-2" data-id="i08v0i7cb" data-path="src/components/ImageProcessingDemo.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="g0okq5x8q" data-path="src/components/ImageProcessingDemo.tsx">
                <span data-id="hd5wbt3e8" data-path="src/components/ImageProcessingDemo.tsx">25</span>
                <span className="font-medium" data-id="z5v4c616w" data-path="src/components/ImageProcessingDemo.tsx">{contrast[0]}%</span>
                <span data-id="5qnz0sgpd" data-path="src/components/ImageProcessingDemo.tsx">300</span>
              </div>
            </div>

            <div data-id="tweitbci3" data-path="src/components/ImageProcessingDemo.tsx">
              <Label className="text-sm font-medium" data-id="r7pt4mau4" data-path="src/components/ImageProcessingDemo.tsx">Noise Level</Label>
              <Slider
                value={noise}
                onValueChange={setNoise}
                max={50}
                min={0}
                step={2}
                className="mt-2" data-id="uvubdesqh" data-path="src/components/ImageProcessingDemo.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="jcslftjcl" data-path="src/components/ImageProcessingDemo.tsx">
                <span data-id="uxhm4k1qm" data-path="src/components/ImageProcessingDemo.tsx">0</span>
                <span className="font-medium" data-id="8acbks1mp" data-path="src/components/ImageProcessingDemo.tsx">{noise[0]}</span>
                <span data-id="2qw150sex" data-path="src/components/ImageProcessingDemo.tsx">50</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="few7rcb2l" data-path="src/components/ImageProcessingDemo.tsx">
          <CardHeader data-id="qp81eevbw" data-path="src/components/ImageProcessingDemo.tsx">
            <CardTitle data-id="z1lf53k26" data-path="src/components/ImageProcessingDemo.tsx">Window/Level</CardTitle>
            <CardDescription data-id="fgb0lm2eh" data-path="src/components/ImageProcessingDemo.tsx">Adjust display window settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4" data-id="9b76c6dvq" data-path="src/components/ImageProcessingDemo.tsx">
            <div data-id="myklj7ur9" data-path="src/components/ImageProcessingDemo.tsx">
              <Label className="text-sm font-medium" data-id="9xumetedk" data-path="src/components/ImageProcessingDemo.tsx">Window Level</Label>
              <Slider
                value={windowLevel}
                onValueChange={setWindowLevel}
                max={255}
                min={0}
                step={5}
                className="mt-2" data-id="xjbez8ggp" data-path="src/components/ImageProcessingDemo.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="0n6nmhgyo" data-path="src/components/ImageProcessingDemo.tsx">
                <span data-id="8x7x3ptmt" data-path="src/components/ImageProcessingDemo.tsx">0</span>
                <span className="font-medium" data-id="3o1984hf0" data-path="src/components/ImageProcessingDemo.tsx">{windowLevel[0]}</span>
                <span data-id="yvgcp45gr" data-path="src/components/ImageProcessingDemo.tsx">255</span>
              </div>
            </div>

            <div data-id="nukp21kgn" data-path="src/components/ImageProcessingDemo.tsx">
              <Label className="text-sm font-medium" data-id="19497uskf" data-path="src/components/ImageProcessingDemo.tsx">Window Width</Label>
              <Slider
                value={windowWidth}
                onValueChange={setWindowWidth}
                max={512}
                min={50}
                step={10}
                className="mt-2" data-id="3un0ec3e7" data-path="src/components/ImageProcessingDemo.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="zxow0coae" data-path="src/components/ImageProcessingDemo.tsx">
                <span data-id="huddajhjl" data-path="src/components/ImageProcessingDemo.tsx">50</span>
                <span className="font-medium" data-id="nrrxcctty" data-path="src/components/ImageProcessingDemo.tsx">{windowWidth[0]}</span>
                <span data-id="mkcpxlouy" data-path="src/components/ImageProcessingDemo.tsx">512</span>
              </div>
            </div>

            <div className="pt-2" data-id="2l42xe7ei" data-path="src/components/ImageProcessingDemo.tsx">
              <div className="text-xs text-gray-600" data-id="70nt0wrpn" data-path="src/components/ImageProcessingDemo.tsx">
                <p data-id="mbis2teuy" data-path="src/components/ImageProcessingDemo.tsx">Min: {windowLevel[0] - windowWidth[0] / 2}</p>
                <p data-id="rqilfyapr" data-path="src/components/ImageProcessingDemo.tsx">Max: {windowLevel[0] + windowWidth[0] / 2}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="qto7xhuxv" data-path="src/components/ImageProcessingDemo.tsx">
          <CardHeader data-id="1acsukawc" data-path="src/components/ImageProcessingDemo.tsx">
            <CardTitle data-id="ic313sw9r" data-path="src/components/ImageProcessingDemo.tsx">Filters</CardTitle>
            <CardDescription data-id="vx0k76fnq" data-path="src/components/ImageProcessingDemo.tsx">Apply image processing filters</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4" data-id="9gcq6xyxw" data-path="src/components/ImageProcessingDemo.tsx">
            <div data-id="hy2kr6ad6" data-path="src/components/ImageProcessingDemo.tsx">
              <Label className="text-sm font-medium" data-id="v4x194lgx" data-path="src/components/ImageProcessingDemo.tsx">Filter Type</Label>
              <Select value={filter} onValueChange={setFilter} data-id="p43xkp4u9" data-path="src/components/ImageProcessingDemo.tsx">
                <SelectTrigger data-id="4pfzcbozt" data-path="src/components/ImageProcessingDemo.tsx">
                  <SelectValue data-id="h9ybpj6uf" data-path="src/components/ImageProcessingDemo.tsx" />
                </SelectTrigger>
                <SelectContent data-id="15pdtrmow" data-path="src/components/ImageProcessingDemo.tsx">
                  {Object.entries(filters).map(([key, name]) =>
                  <SelectItem key={key} value={key} data-id="nqsin7905" data-path="src/components/ImageProcessingDemo.tsx">{name}</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2" data-id="082eimq9g" data-path="src/components/ImageProcessingDemo.tsx">
              <Label className="text-sm font-medium" data-id="v66zfmoj2" data-path="src/components/ImageProcessingDemo.tsx">Filter Effects</Label>
              <div className="grid grid-cols-2 gap-2" data-id="ybzdpaejc" data-path="src/components/ImageProcessingDemo.tsx">
                <Badge variant={filter === 'blur' ? 'default' : 'outline'} className="text-xs" data-id="7wq2sxe4p" data-path="src/components/ImageProcessingDemo.tsx">
                  Noise Reduction
                </Badge>
                <Badge variant={filter === 'sharpen' ? 'default' : 'outline'} className="text-xs" data-id="o9zaeax2r" data-path="src/components/ImageProcessingDemo.tsx">
                  Edge Enhancement
                </Badge>
                <Badge variant={filter === 'edge' ? 'default' : 'outline'} className="text-xs" data-id="zc4gzjr6u" data-path="src/components/ImageProcessingDemo.tsx">
                  Feature Detection
                </Badge>
                <Badge variant={filter === 'median' ? 'default' : 'outline'} className="text-xs" data-id="3faqoviwt" data-path="src/components/ImageProcessingDemo.tsx">
                  Artifact Removal
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Information */}
      <div className="grid md:grid-cols-2 gap-8" data-id="homyprn7n" data-path="src/components/ImageProcessingDemo.tsx">
        <Card data-id="4g4org1im" data-path="src/components/ImageProcessingDemo.tsx">
          <CardHeader data-id="avc7o2fwa" data-path="src/components/ImageProcessingDemo.tsx">
            <CardTitle data-id="joltyjuri" data-path="src/components/ImageProcessingDemo.tsx">Processing Techniques</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4" data-id="pmmx4r9k2" data-path="src/components/ImageProcessingDemo.tsx">
            <div data-id="ln6bgyqey" data-path="src/components/ImageProcessingDemo.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="l7nde4ghd" data-path="src/components/ImageProcessingDemo.tsx">Spatial Filtering</h4>
              <p className="text-gray-600 text-sm" data-id="y5arp6yx2" data-path="src/components/ImageProcessingDemo.tsx">
                Convolution-based filters that modify pixel values based on neighborhood operations. 
                Useful for noise reduction, edge enhancement, and feature detection.
              </p>
            </div>
            <div data-id="o2nad430g" data-path="src/components/ImageProcessingDemo.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="9c8t4bf5a" data-path="src/components/ImageProcessingDemo.tsx">Window/Level Adjustment</h4>
              <p className="text-gray-600 text-sm" data-id="6ssweki6v" data-path="src/components/ImageProcessingDemo.tsx">
                Maps the full range of pixel values to display range for optimal visualization. 
                Critical for viewing different tissue types with appropriate contrast.
              </p>
            </div>
            <div data-id="kvvim8w6a" data-path="src/components/ImageProcessingDemo.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="svhuqr6cx" data-path="src/components/ImageProcessingDemo.tsx">Noise Management</h4>
              <p className="text-gray-600 text-sm" data-id="tmok3wnhg" data-path="src/components/ImageProcessingDemo.tsx">
                Balance between noise reduction and preservation of diagnostic information. 
                Over-processing can remove important details.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card data-id="c3p2xeq16" data-path="src/components/ImageProcessingDemo.tsx">
          <CardHeader data-id="5kim88ybk" data-path="src/components/ImageProcessingDemo.tsx">
            <CardTitle data-id="fxr7h4luw" data-path="src/components/ImageProcessingDemo.tsx">Clinical Applications</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4" data-id="39e8g9heg" data-path="src/components/ImageProcessingDemo.tsx">
            <div data-id="s9fdjdx1q" data-path="src/components/ImageProcessingDemo.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="jbepkdqrx" data-path="src/components/ImageProcessingDemo.tsx">Diagnostic Optimization</h4>
              <ul className="text-gray-600 text-sm space-y-1" data-id="946yxine0" data-path="src/components/ImageProcessingDemo.tsx">
                <li data-id="aid4ropca" data-path="src/components/ImageProcessingDemo.tsx">• Bone imaging: High contrast, edge enhancement</li>
                <li data-id="mmpim6mk6" data-path="src/components/ImageProcessingDemo.tsx">• Soft tissue: Moderate contrast, noise reduction</li>
                <li data-id="t7ymwubbk" data-path="src/components/ImageProcessingDemo.tsx">• Lung imaging: High contrast, preserved detail</li>
              </ul>
            </div>
            <div data-id="mk2msiurj" data-path="src/components/ImageProcessingDemo.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="qgi82jk8e" data-path="src/components/ImageProcessingDemo.tsx">Workflow Integration</h4>
              <ul className="text-gray-600 text-sm space-y-1" data-id="pplpihhyx" data-path="src/components/ImageProcessingDemo.tsx">
                <li data-id="pguj4s54b" data-path="src/components/ImageProcessingDemo.tsx">• Automated processing protocols</li>
                <li data-id="dz1vefhrk" data-path="src/components/ImageProcessingDemo.tsx">• Hanging protocols for display</li>
                <li data-id="g9insqsdd" data-path="src/components/ImageProcessingDemo.tsx">• Quality assurance checks</li>
              </ul>
            </div>
            <div data-id="rqma9yop6" data-path="src/components/ImageProcessingDemo.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="55swi9x2p" data-path="src/components/ImageProcessingDemo.tsx">Advanced Techniques</h4>
              <ul className="text-gray-600 text-sm space-y-1" data-id="xg01j5q2y" data-path="src/components/ImageProcessingDemo.tsx">
                <li data-id="kgr5lr0c6" data-path="src/components/ImageProcessingDemo.tsx">• Iterative reconstruction</li>
                <li data-id="go2ocr06c" data-path="src/components/ImageProcessingDemo.tsx">• Machine learning enhancement</li>
                <li data-id="l9l1kbdwq" data-path="src/components/ImageProcessingDemo.tsx">• Multi-planar reconstruction</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default ImageProcessingDemo;