import { useState } from 'react';
import { motion } from 'motion/react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

const XRayTubeDiagram = () => {
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);

  const components = {
    cathode: {
      name: 'الكاثود',
      description: 'مصدر الإلكترونات - فتيل التنجستن المُسخن',
      details: 'يحتوي على فتيل التنجستن الذي يُسخن كهربائياً لإنتاج الإلكترونات عن طريق الانبعاث الحراري',
      color: '#ef4444'
    },
    anode: {
      name: 'الأنود',
      description: 'الهدف - مصنوع من التنجستن',
      details: 'قرص دوار من التنجستن يستقبل الإلكترونات المتسارعة ويحولها إلى أشعة سينية وحرارة',
      color: '#3b82f6'
    },
    envelope: {
      name: 'الغلاف الزجاجي',
      description: 'يحتوي على فراغ عالي',
      details: 'غلاف زجاجي محكم يحتوي على فراغ عالي يسمح للإلكترونات بالحركة الحرة دون تصادمات',
      color: '#10b981'
    },
    housing: {
      name: 'الغلاف الواقي',
      description: 'حماية إشعاعية وتبريد',
      details: 'غلاف رصاصي يوفر الحماية من الإشعاع المتسرب ونظام تبريد للأنبوب',
      color: '#8b5cf6'
    }
  };

  return (
    <div className="space-y-6" data-id="t1bs90ntv" data-path="src/components/XRayTubeDiagram.tsx">
      {/* SVG Diagram */}
      <div className="relative bg-gray-50 rounded-lg p-6 min-h-[400px] flex items-center justify-center" data-id="yco5k89c4" data-path="src/components/XRayTubeDiagram.tsx">
        <svg
          width="500"
          height="300"
          viewBox="0 0 500 300"
          className="max-w-full h-auto" data-id="g4il7pa4s" data-path="src/components/XRayTubeDiagram.tsx">

          {/* Housing (Outer Shell) */}
          <motion.rect
            x="20"
            y="60"
            width="460"
            height="180"
            rx="10"
            fill={selectedComponent === 'housing' ? components.housing.color : '#e5e7eb'}
            stroke={selectedComponent === 'housing' ? components.housing.color : '#9ca3af'}
            strokeWidth="2"
            className="cursor-pointer transition-all duration-300"
            onClick={() => setSelectedComponent(selectedComponent === 'housing' ? null : 'housing')}
            whileHover={{ scale: 1.02 }} data-id="z4ehpndhc" data-path="src/components/XRayTubeDiagram.tsx" />

          
          {/* Glass Envelope */}
          <motion.ellipse
            cx="250"
            cy="150"
            rx="200"
            ry="80"
            fill={selectedComponent === 'envelope' ? components.envelope.color : '#f3f4f6'}
            stroke={selectedComponent === 'envelope' ? components.envelope.color : '#6b7280'}
            strokeWidth="2"
            className="cursor-pointer transition-all duration-300"
            onClick={() => setSelectedComponent(selectedComponent === 'envelope' ? null : 'envelope')}
            whileHover={{ scale: 1.05 }} data-id="5qzi32lqw" data-path="src/components/XRayTubeDiagram.tsx" />

          
          {/* Cathode */}
          <motion.g
            onClick={() => setSelectedComponent(selectedComponent === 'cathode' ? null : 'cathode')}
            className="cursor-pointer"
            whileHover={{ scale: 1.1 }} data-id="691fjm8iv" data-path="src/components/XRayTubeDiagram.tsx">

            <rect
              x="80"
              y="130"
              width="40"
              height="40"
              rx="5"
              fill={selectedComponent === 'cathode' ? components.cathode.color : '#fef3c7'}
              stroke={selectedComponent === 'cathode' ? components.cathode.color : '#f59e0b'}
              strokeWidth="2" data-id="43colxaqc" data-path="src/components/XRayTubeDiagram.tsx" />

            {/* Filament */}
            <circle
              cx="100"
              cy="150"
              r="8"
              fill={selectedComponent === 'cathode' ? '#dc2626' : '#f59e0b'} data-id="2jqhiqhsb" data-path="src/components/XRayTubeDiagram.tsx" />

            {/* Electron beam */}
            {selectedComponent === 'cathode' &&
            <motion.g
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }} data-id="df401fyjz" data-path="src/components/XRayTubeDiagram.tsx">

                {[...Array(5)].map((_, i) =>
              <motion.circle
                key={i}
                cx={150 + i * 40}
                cy={150}
                r="3"
                fill="#fbbf24"
                initial={{ x: -50 }}
                animate={{ x: 0 }}
                transition={{ delay: i * 0.1, repeat: Infinity, duration: 2 }} data-id="z9trvvydv" data-path="src/components/XRayTubeDiagram.tsx" />

              )}
              </motion.g>
            }
          </motion.g>
          
          {/* Anode */}
          <motion.g
            onClick={() => setSelectedComponent(selectedComponent === 'anode' ? null : 'anode')}
            className="cursor-pointer"
            whileHover={{ scale: 1.1 }} data-id="eg4znj9t5" data-path="src/components/XRayTubeDiagram.tsx">

            {/* Anode disk */}
            <circle
              cx="380"
              cy="150"
              r="35"
              fill={selectedComponent === 'anode' ? components.anode.color : '#dbeafe'}
              stroke={selectedComponent === 'anode' ? components.anode.color : '#3b82f6'}
              strokeWidth="2" data-id="lv0u7wz1k" data-path="src/components/XRayTubeDiagram.tsx" />

            {/* Target angle */}
            <path
              d="M345 130 L380 150 L345 170 Z"
              fill={selectedComponent === 'anode' ? '#1d4ed8' : '#60a5fa'} data-id="u3xgg05bd" data-path="src/components/XRayTubeDiagram.tsx" />

            {/* X-ray beam */}
            {selectedComponent === 'anode' &&
            <motion.g
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }} data-id="fhxeytfce" data-path="src/components/XRayTubeDiagram.tsx">

                {[...Array(3)].map((_, i) =>
              <motion.path
                key={i}
                d={`M380 150 L${450 + i * 10} ${120 + i * 20}`}
                stroke="#10b981"
                strokeWidth="2"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ delay: i * 0.2, duration: 1, repeat: Infinity }} data-id="k9rdcc2b7" data-path="src/components/XRayTubeDiagram.tsx" />

              )}
              </motion.g>
            }
          </motion.g>
          
          {/* Labels */}
          <text x="100" y="200" textAnchor="middle" className="text-sm font-medium fill-gray-700" data-id="b6rzaff02" data-path="src/components/XRayTubeDiagram.tsx">
            الكاثود
          </text>
          <text x="380" y="200" textAnchor="middle" className="text-sm font-medium fill-gray-700" data-id="pxal3qbi3" data-path="src/components/XRayTubeDiagram.tsx">
            الأنود
          </text>
          <text x="250" y="40" textAnchor="middle" className="text-lg font-bold fill-gray-800" data-id="g3kevgqjt" data-path="src/components/XRayTubeDiagram.tsx">
            أنبوب الأشعة السينية
          </text>
          
          {/* Voltage indicators */}
          <text x="250" y="270" textAnchor="middle" className="text-sm fill-gray-600" data-id="sp55za4jh" data-path="src/components/XRayTubeDiagram.tsx">
            فولتية عالية (40-150 kV)
          </text>
          
          {/* Direction arrow */}
          <defs data-id="0xa1l7u1r" data-path="src/components/XRayTubeDiagram.tsx">
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto" data-id="1grmhd00p" data-path="src/components/XRayTubeDiagram.tsx">

              <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" data-id="sgjvddzjc" data-path="src/components/XRayTubeDiagram.tsx" />
            </marker>
          </defs>
          <line
            x1="130"
            y1="150"
            x2="340"
            y2="150"
            stroke="#6b7280"
            strokeWidth="2"
            markerEnd="url(#arrowhead)" data-id="i7muesiqv" data-path="src/components/XRayTubeDiagram.tsx" />

        </svg>
      </div>

      {/* Component Information */}
      {selectedComponent &&
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }} data-id="1xz6xy75n" data-path="src/components/XRayTubeDiagram.tsx">

          <Card data-id="k4zwya6lm" data-path="src/components/XRayTubeDiagram.tsx">
            <CardContent className="p-6" data-id="t7wc1sh16" data-path="src/components/XRayTubeDiagram.tsx">
              <div className="flex items-start gap-4" data-id="4xik5o8op" data-path="src/components/XRayTubeDiagram.tsx">
                <div
                className="w-4 h-4 rounded-full flex-shrink-0 mt-1"
                style={{ backgroundColor: components[selectedComponent as keyof typeof components].color }} data-id="97arhgwdn" data-path="src/components/XRayTubeDiagram.tsx">
              </div>
                <div className="flex-1" data-id="2mhrqdvu3" data-path="src/components/XRayTubeDiagram.tsx">
                  <div className="flex items-center gap-2 mb-2" data-id="02da4m0hr" data-path="src/components/XRayTubeDiagram.tsx">
                    <h3 className="text-lg font-semibold text-gray-900" data-id="i167tubce" data-path="src/components/XRayTubeDiagram.tsx">
                      {components[selectedComponent as keyof typeof components].name}
                    </h3>
                    <Badge variant="secondary" data-id="sjibsppfm" data-path="src/components/XRayTubeDiagram.tsx">محدد</Badge>
                  </div>
                  <p className="text-gray-600 mb-3" data-id="zp2kro725" data-path="src/components/XRayTubeDiagram.tsx">
                    {components[selectedComponent as keyof typeof components].description}
                  </p>
                  <p className="text-sm text-gray-700 leading-relaxed" data-id="8cvdegy05" data-path="src/components/XRayTubeDiagram.tsx">
                    {components[selectedComponent as keyof typeof components].details}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      }

      {!selectedComponent &&
      <div className="text-center text-gray-500 text-sm" data-id="obf7po9ot" data-path="src/components/XRayTubeDiagram.tsx">
          انقر على أي مكون في المخطط لعرض تفاصيله
        </div>
      }
    </div>);

};

export default XRayTubeDiagram;