import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Globe } from 'lucide-react';

interface LanguageSwitcherProps {
  initialLanguage?: 'ar' | 'en';
  onLanguageChange?: (language: 'ar' | 'en') => void;
  className?: string;
}

const LanguageSwitcher = ({ 
  initialLanguage = 'ar', 
  onLanguageChange,
  className = ''
}: LanguageSwitcherProps) => {
  const [language, setLanguage] = useState<'ar' | 'en'>(initialLanguage);

  const toggleLanguage = () => {
    const newLanguage = language === 'ar' ? 'en' : 'ar';
    setLanguage(newLanguage);
    if (onLanguageChange) {
      onLanguageChange(newLanguage);
    }
  };

  return (
    <Button 
      variant="ghost" 
      size="sm" 
      onClick={toggleLanguage}
      className={`flex items-center gap-2 language-switcher ${language === 'en' ? 'en' : ''} ${className}`}
    >
      <Globe className="h-4 w-4" />
      {language === 'ar' ? 'English' : 'العربية'}
    </Button>
  );
};

export default LanguageSwitcher;