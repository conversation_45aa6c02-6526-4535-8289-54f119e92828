import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';

// Import pages
import HomePage from '@/pages/HomePage';
import NotFound from '@/pages/NotFound';
import CoherentScattering from '@/pages/CoherentScattering';
import PhotoelectricEffect from '@/pages/PhotoelectricEffect';
import ComptonScattering from '@/pages/ComptonScattering';
import PairProduction from '@/pages/PairProduction';
import AttenuationCoefficients from '@/pages/AttenuationCoefficients';
import RelativeImportance from '@/pages/RelativeImportance';
import LearningObjectives from '@/pages/LearningObjectives';
import KeyTerms from '@/pages/KeyTerms';
import References from '@/pages/References';
import Problems from '@/pages/Problems';
import Chapter9PatientModeling from '@/pages/Chapter9PatientModeling';
import Chapter10MonteCarloSimulation from '@/pages/Chapter10MonteCarloSimulation';
import Chapter11XrayDetection from '@/pages/Chapter11XrayDetection';
import Chapter12DetectorSimulation from '@/pages/Chapter12DetectorSimulation';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient} data-id="rkk3mold2" data-path="src/App.tsx">
      <TooltipProvider data-id="71lc4jj1g" data-path="src/App.tsx">
        <Router data-id="nz45wv314" data-path="src/App.tsx">
          <div className="App" data-id="e90twc3rf" data-path="src/App.tsx">
            <Routes data-id="0efq2acm1" data-path="src/App.tsx">
              <Route path="/" element={<HomePage data-id="lscx76xx9" data-path="src/App.tsx" />} data-id="zr6lmrvsq" data-path="src/App.tsx" />
              <Route path="/coherent-scattering" element={<CoherentScattering data-id="itm752uij" data-path="src/App.tsx" />} data-id="4lhejmekp" data-path="src/App.tsx" />
              <Route path="/photoelectric-effect" element={<PhotoelectricEffect data-id="0ipmdx85s" data-path="src/App.tsx" />} data-id="o324tdu30" data-path="src/App.tsx" />
              <Route path="/compton-scattering" element={<ComptonScattering data-id="nkj0rw5dv" data-path="src/App.tsx" />} data-id="x9ba77vgn" data-path="src/App.tsx" />
              <Route path="/pair-production" element={<PairProduction data-id="qdlz4bznq" data-path="src/App.tsx" />} data-id="4o5hocfyp" data-path="src/App.tsx" />
              <Route path="/attenuation-coefficients" element={<AttenuationCoefficients data-id="6stdb8t8e" data-path="src/App.tsx" />} data-id="r8egcnzyu" data-path="src/App.tsx" />
              <Route path="/relative-importance" element={<RelativeImportance data-id="itreh7oja" data-path="src/App.tsx" />} data-id="bzf4cijjt" data-path="src/App.tsx" />
              <Route path="/learning-objectives" element={<LearningObjectives data-id="040mbyi2c" data-path="src/App.tsx" />} data-id="sdn4zezya" data-path="src/App.tsx" />
              <Route path="/key-terms" element={<KeyTerms data-id="r9s70zxis" data-path="src/App.tsx" />} data-id="e1hzu4roe" data-path="src/App.tsx" />
              <Route path="/references" element={<References data-id="150bjcuzc" data-path="src/App.tsx" />} data-id="70sn8fhnu" data-path="src/App.tsx" />
              <Route path="/problems" element={<Problems data-id="lfs0jz4ab" data-path="src/App.tsx" />} data-id="k894ng1tx" data-path="src/App.tsx" />
              <Route path="/chapter-9-patient-modeling" element={<Chapter9PatientModeling data-id="jz4i7bm7j" data-path="src/App.tsx" />} data-id="5dayspmq1" data-path="src/App.tsx" />
              <Route path="/chapter-10-monte-carlo" element={<Chapter10MonteCarloSimulation data-id="etu5wnbgf" data-path="src/App.tsx" />} data-id="04y1ym6ne" data-path="src/App.tsx" />
              <Route path="/chapter-11-xray-detection" element={<Chapter11XrayDetection data-id="kx2tu5oez" data-path="src/App.tsx" />} data-id="sazdeg6o9" data-path="src/App.tsx" />
              <Route path="/chapter-12-detector-simulation" element={<Chapter12DetectorSimulation data-id="ii4vc48al" data-path="src/App.tsx" />} data-id="7c3ipzfev" data-path="src/App.tsx" />
              <Route path="*" element={<NotFound data-id="3b3twvbza" data-path="src/App.tsx" />} data-id="ltqikgf19" data-path="src/App.tsx" />
            </Routes>
          </div>
          <Toaster data-id="6j4qfbi8s" data-path="src/App.tsx" />
        </Router>
      </TooltipProvider>
    </QueryClientProvider>);

}

export default App;