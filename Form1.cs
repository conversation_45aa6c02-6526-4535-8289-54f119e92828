using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;

namespace WebAppWrapper
{
    public partial class Form1 : Form
    {
        private WebView2 webView2;

        public Form1()
        {
            InitializeComponent();
            InitializeWebView();
        }

        private void InitializeWebView()
        {
            // Create WebView2 control
            webView2 = new WebView2()
            {
                Dock = DockStyle.Fill
            };

            // Add the WebView2 control to the form
            this.Controls.Add(webView2);
        }

        private async void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                // Ensure the WebView2 control is initialized
                await webView2.EnsureCoreWebView2Async(null);

                // Navigate to the specified URL
                webView2.CoreWebView2.Navigate("https://www.example.com");

                // Optional: Set up additional event handlers
                webView2.CoreWebView2.DocumentTitleChanged += CoreWebView2_DocumentTitleChanged;
                webView2.CoreWebView2.NavigationStarting += CoreWebView2_NavigationStarting;
                webView2.CoreWebView2.NavigationCompleted += CoreWebView2_NavigationCompleted;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing WebView2: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CoreWebView2_DocumentTitleChanged(object? sender, object e)
        {
            // Update the form title with the web page title
            this.Text = $"WebAppWrapper - {webView2.CoreWebView2.DocumentTitle}";
        }

        private void CoreWebView2_NavigationStarting(object? sender, CoreWebView2NavigationStartingEventArgs e)
        {
            // Optional: Show loading indicator or update status
            this.Text = "WebAppWrapper - Loading...";
        }

        private void CoreWebView2_NavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            // Optional: Hide loading indicator or update status
            if (e.IsSuccess)
            {
                this.Text = $"WebAppWrapper - {webView2.CoreWebView2.DocumentTitle}";
            }
            else
            {
                this.Text = "WebAppWrapper - Navigation Failed";
                MessageBox.Show("Failed to load the webpage.", "Navigation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Clean up WebView2 resources
                webView2?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
