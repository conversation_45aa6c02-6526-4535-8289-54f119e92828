<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ترشيح الأشعة السينية | X-Ray Filtration</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal for Arabic, Inter for English -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Base Styles */
        :root {
            --primary-color: #4f46e5;
            --primary-light: #e0e7ff;
            --primary-dark: #312e81;
            --secondary-color: #0ea5e9;
            --secondary-light: #e0f2fe;
            --secondary-dark: #0369a1;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8fafc;
        }
        
        .en {
            font-family: 'Inter', sans-serif;
            direction: ltr;
            text-align: left;
        }
        
        /* Interactive elements */
        .interactive-control {
            transition: all 0.3s ease;
        }
        
        .interactive-control:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        /* X-ray beam visualization */
        .xray-beam {
            height: 20px;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.8) 0%, rgba(37, 99, 235, 0.6) 100%);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        
        .xray-beam::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                90deg,
                transparent,
                transparent 10px,
                rgba(255, 255, 255, 0.1) 10px,
                rgba(255, 255, 255, 0.1) 20px
            );
            animation: moveBeam 2s linear infinite;
        }
        
        @keyframes moveBeam {
            from { background-position: 0 0; }
            to { background-position: 40px 0; }
        }
        
        /* Filtration visualization */
        .filter-element {
            transition: all 0.5s ease;
        }
        
        /* Spectrum visualization */
        .spectrum-bar {
            transition: height 0.5s ease;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-4 rtl:space-x-reverse">
                <a href="landing.html" class="text-gray-600 hover:text-blue-600">
                    <i class="fas fa-arrow-right rtl:hidden"></i>
                    <i class="fas fa-arrow-left hidden rtl:inline"></i>
                    <span class="ml-2 rtl:mr-2">العودة للرئيسية</span>
                </a>
            </div>
            <div>
                <button id="language-toggle" class="text-gray-600 hover:text-blue-600 px-3 py-1 rounded-full border border-gray-200 text-sm flex items-center">
                    <i class="fas fa-globe mr-2 rtl:ml-2 rtl:mr-0"></i>
                    <span id="language-text">English</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Title Section -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div class="md:flex">
                <div class="md:w-2/3 p-8">
                    <h1 class="text-3xl font-bold text-gray-800 mb-4" id="page-title">ترشيح الأشعة السينية</h1>
                    <p class="text-gray-600 mb-4 leading-relaxed" id="page-subtitle">تحسين جودة الحزمة وتقليل الجرعة الإشعاعية</p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-sm">الفصل السادس</span>
                        <span class="bg-green-50 text-green-600 px-3 py-1 rounded-full text-sm" id="physics-tag">فيزياء الأشعة</span>
                        <span class="bg-purple-50 text-purple-600 px-3 py-1 rounded-full text-sm" id="protection-tag">الوقاية الإشعاعية</span>
                    </div>
                </div>
                <div class="md:w-1/3 bg-gradient-to-br from-blue-500 to-indigo-600 p-8 flex items-center justify-center">
                    <div class="text-center">
                        <div class="w-20 h-20 mx-auto bg-white/20 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-filter text-4xl text-white"></i>
                        </div>
                        <h3 class="text-white text-xl font-bold mb-2" id="interactive-title">محتوى تفاعلي</h3>
                        <p class="text-white/80 text-sm" id="interactive-desc">تجربة تأثير الترشيح على حزمة الأشعة السينية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Introduction Section -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center" id="intro-title">
                <i class="fas fa-info-circle text-blue-500 ml-2 rtl:ml-2 rtl:mr-0"></i>
                مقدمة عن ترشيح الأشعة السينية
            </h2>
            <p class="text-gray-700 mb-4 leading-relaxed" id="intro-p1">
                يُعد ترشيح الأشعة السينية من التقنيات الأساسية المستخدمة لتحسين جودة الصورة وتقليل الجرعة الإشعاعية للمريض. يعمل الترشيح على إزالة الفوتونات ذات الطاقة المنخفضة من حزمة الأشعة، والتي لا تساهم في تكوين الصورة ولكنها تزيد من الجرعة الإشعاعية للمريض.
            </p>
            <p class="text-gray-700 leading-relaxed" id="intro-p2">
                تتكون أنظمة ترشيح الأشعة السينية من نوعين رئيسيين: الترشيح الذاتي (الموجود في أنبوب الأشعة نفسه) والترشيح المضاف (مواد إضافية توضع في مسار الحزمة). يؤدي الترشيح المناسب إلى تحسين نسبة التباين إلى الضوضاء في الصورة وتقليل الجرعة السطحية للمريض بشكل كبير.
            </p>
        </div>

        <!-- Interactive Simulation -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div class="bg-gradient-to-r from-blue-600 to-indigo-700 p-4">
                <h2 class="text-xl font-bold text-white flex items-center" id="sim-title">
                    <i class="fas fa-laptop-code mr-2 rtl:ml-2 rtl:mr-0"></i>
                    محاكاة تفاعلية: تأثير الترشيح على حزمة الأشعة السينية
                </h2>
            </div>
            
            <div class="p-6">
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- Controls -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4" id="controls-title">التحكم في معلمات الأشعة</h3>
                        
                        <!-- kVp Control -->
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <label class="text-sm font-medium" id="kvp-label">الجهد (kVp)</label>
                                <span class="text-sm text-gray-500"><span id="kvp-value">80</span> kVp</span>
                            </div>
                            <input 
                                type="range" 
                                id="kvp-slider" 
                                min="40" 
                                max="150" 
                                value="80" 
                                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                            >
                        </div>
                        
                        <!-- Filtration Control -->
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <label class="text-sm font-medium" id="filter-label">الترشيح (mm Al)</label>
                                <span class="text-sm text-gray-500"><span id="filter-value">2.5</span> mm Al</span>
                            </div>
                            <input 
                                type="range" 
                                id="filter-slider" 
                                min="0" 
                                max="5" 
                                step="0.5" 
                                value="2.5" 
                                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                            >
                        </div>
                        
                        <!-- Filter Material Selection -->
                        <div class="space-y-2">
                            <label class="text-sm font-medium" id="material-label">مادة الترشيح</label>
                            <div class="grid grid-cols-3 gap-2">
                                <button class="interactive-control bg-blue-100 text-blue-800 px-3 py-2 rounded-lg text-sm font-medium active" id="al-filter">الألومنيوم (Al)</button>
                                <button class="interactive-control bg-gray-100 text-gray-800 px-3 py-2 rounded-lg text-sm font-medium" id="cu-filter">النحاس (Cu)</button>
                                <button class="interactive-control bg-gray-100 text-gray-800 px-3 py-2 rounded-lg text-sm font-medium" id="mo-filter">الموليبدينوم (Mo)</button>
                            </div>
                        </div>
                        
                        <!-- Results -->
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <h4 class="text-sm font-semibold text-gray-800 mb-3" id="results-title">نتائج المحاكاة</h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <p class="text-xs text-gray-500 mb-1" id="hvl-label">نصف قيمة الطبقة (HVL)</p>
                                    <p class="text-lg font-semibold text-blue-700" id="hvl-value">3.2 mm Al</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500 mb-1" id="dose-reduction-label">تخفيض الجرعة</p>
                                    <p class="text-lg font-semibold text-green-700" id="dose-reduction-value">42%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Visualization -->
                    <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4" id="visual-title">التمثيل البصري</h3>
                        
                        <!-- X-ray Source and Beam -->
                        <div class="relative h-64 mb-6">
                            <!-- X-ray Source -->
                            <div class="absolute top-4 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center">
                                <i class="fas fa-radiation text-yellow-400 text-2xl"></i>
                            </div>
                            
                            <!-- Beam Path -->
                            <div class="absolute top-20 left-1/2 transform -translate-x-1/2 w-4 h-32 bg-gradient-to-b from-blue-400/80 to-blue-600/80">
                                <!-- This will be animated with JS -->
                            </div>
                            
                            <!-- Filter Element -->
                            <div id="filter-element" class="absolute top-28 left-1/2 transform -translate-x-1/2 w-20 h-6 bg-gray-400 filter-element" style="opacity: 0.7;">
                                <!-- Thickness will be controlled by JS -->
                            </div>
                            
                            <!-- Detector -->
                            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-32 h-8 bg-gray-200 border-2 border-gray-300 rounded flex items-center justify-center">
                                <span class="text-xs text-gray-600" id="detector-label">كاشف الأشعة</span>
                            </div>
                        </div>
                        
                        <!-- Spectrum Visualization -->
                        <div class="bg-white rounded-lg p-4 border border-gray-200">
                            <h4 class="text-sm font-semibold text-gray-800 mb-3" id="spectrum-title">طيف الأشعة السينية</h4>
                            <div class="h-32 flex items-end justify-between space-x-1 rtl:space-x-reverse" id="spectrum-container">
                                <!-- Spectrum bars will be generated by JS -->
                                <!-- Example bar structure -->
                                <div class="w-2 bg-blue-500 spectrum-bar" style="height: 20%;"></div>
                                <div class="w-2 bg-blue-500 spectrum-bar" style="height: 30%;"></div>
                                <div class="w-2 bg-blue-500 spectrum-bar" style="height: 40%;"></div>
                                <!-- More bars... -->
                            </div>
                            <div class="flex justify-between mt-2">
                                <span class="text-xs text-gray-500" id="energy-min">0 keV</span>
                                <span class="text-xs text-gray-500" id="energy-max">150 keV</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Concepts -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center" id="concepts-title">
                <i class="fas fa-key text-purple-500 ml-2 rtl:ml-2 rtl:mr-0"></i>
                المفاهيم الأساسية في ترشيح الأشعة السينية
            </h2>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-purple-50 rounded-lg p-5 border border-purple-100">
                    <h3 class="font-semibold text-purple-800 mb-3" id="concept1-title">نصف قيمة الطبقة (HVL)</h3>
                    <p class="text-gray-700 text-sm" id="concept1-desc">
                        سُمك المادة اللازم لتقليل شدة الأشعة السينية إلى النصف. يُعد مقياساً مهماً لجودة الحزمة وقدرتها الاختراقية. كلما زادت قيمة HVL، كلما كانت الحزمة أكثر اختراقاً وأقل تأثراً بالامتصاص.
                    </p>
                </div>
                
                <div class="bg-blue-50 rounded-lg p-5 border border-blue-100">
                    <h3 class="font-semibold text-blue-800 mb-3" id="concept2-title">تصلب الحزمة (Beam Hardening)</h3>
                    <p class="text-gray-700 text-sm" id="concept2-desc">
                        عملية زيادة متوسط طاقة حزمة الأشعة السينية نتيجة الامتصاص التفضيلي للفوتونات ذات الطاقة المنخفضة. يؤدي الترشيح إلى "تصلب" الحزمة، مما يجعلها أكثر اختراقاً وأقل تسبباً في الجرعات السطحية.
                    </p>
                </div>
                
                <div class="bg-green-50 rounded-lg p-5 border border-green-100">
                    <h3 class="font-semibold text-green-800 mb-3" id="concept3-title">الترشيح الذاتي</h3>
                    <p class="text-gray-700 text-sm" id="concept3-desc">
                        الترشيح الناتج عن مكونات أنبوب الأشعة السينية نفسه، مثل نافذة الزجاج والزيت العازل. يوفر هذا النوع من الترشيح حداً أدنى من الحماية من الإشعاع ذي الطاقة المنخفضة.
                    </p>
                </div>
                
                <div class="bg-amber-50 rounded-lg p-5 border border-amber-100">
                    <h3 class="font-semibold text-amber-800 mb-3" id="concept4-title">الترشيح المضاف</h3>
                    <p class="text-gray-700 text-sm" id="concept4-desc">
                        مواد إضافية توضع في مسار الحزمة لتحسين جودتها، مثل الألومنيوم والنحاس. يتم اختيار مادة وسمك الترشيح بناءً على التطبيق السريري المحدد والتوازن المطلوب بين جودة الصورة والجرعة.
                    </p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="md:flex md:justify-between">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center gap-3 mb-4">
                        <div class="p-2 bg-white/10 rounded-lg">
                            <i class="fas fa-book-open text-xl"></i>
                        </div>
                        <div>
                            <h2 class="text-lg font-bold" id="footer-title">منصة التعلم الطبي</h2>
                        </div>
                    </div>
                    <p class="text-gray-400 text-sm max-w-md" id="footer-desc">
                        منصة تعليمية متخصصة في مجال الفيزياء الطبية وتقنيات التصوير بالأشعة السينية، تجمع بين المحتوى العلمي العميق والتطبيقات التفاعلية.
                    </p>
                </div>
                
                <div>
                    <h3 class="text-sm font-semibold uppercase tracking-wider mb-4" id="quick-links">روابط سريعة</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors" id="home-link">الصفحة الرئيسية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors" id="content-link">المحتوى العلمي</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors" id="simulations-link">المحاكاة التفاعلية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors" id="about-link">عن المؤلف</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-8 pt-8 border-t border-gray-700 flex flex-col md:flex-row md:justify-between md:items-center">
                <p class="text-gray-400 text-sm" id="copyright">
                    © 2024 جميع الحقوق محفوظة
                </p>
                <div class="flex space-x-6 rtl:space-x-reverse mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white">
                        <i class="fab fa-github"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript for interactivity -->
    <script>
        // DOM Elements
        const languageToggle = document.getElementById('language-toggle');
        const languageText = document.getElementById('language-text');
        const kvpSlider = document.getElementById('kvp-slider');
        const kvpValue = document.getElementById('kvp-value');
        const filterSlider = document.getElementById('filter-slider');
        const filterValue = document.getElementById('filter-value');
        const filterElement = document.getElementById('filter-element');
        const alFilter = document.getElementById('al-filter');
        const cuFilter = document.getElementById('cu-filter');
        const moFilter = document.getElementById('mo-filter');
        const hvlValue = document.getElementById('hvl-value');
        const doseReductionValue = document.getElementById('dose-reduction-value');
        const spectrumContainer = document.getElementById('spectrum-container');
        
        // Bilingual content
        const translations = {
            ar: {
                pageTitle: "ترشيح الأشعة السينية",
                pageSubtitle: "تحسين جودة الحزمة وتقليل الجرعة الإشعاعية",
                physicsTag: "فيزياء الأشعة",
                protectionTag: "الوقاية الإشعاعية",
                interactiveTitle: "محتوى تفاعلي",
                interactiveDesc: "تجربة تأثير الترشيح على حزمة الأشعة السينية",
                introTitle: "مقدمة عن ترشيح الأشعة السينية",
                introP1: "يُعد ترشيح الأشعة السينية من التقنيات الأساسية المستخدمة لتحسين جودة الصورة وتقليل الجرعة الإشعاعية للمريض. يعمل الترشيح على إزالة الفوتونات ذات الطاقة المنخفضة من حزمة الأشعة، والتي لا تساهم في تكوين الصورة ولكنها تزيد من الجرعة الإشعاعية للمريض.",
                introP2: "تتكون أنظمة ترشيح الأشعة السينية من نوعين رئيسيين: الترشيح الذاتي (الموجود في أنبوب الأشعة نفسه) والترشيح المضاف (مواد إضافية توضع في مسار الحزمة). يؤدي الترشيح المناسب إلى تحسين نسبة التباين إلى الضوضاء في الصورة وتقليل الجرعة السطحية للمريض بشكل كبير.",
                simTitle: "محاكاة تفاعلية: تأثير الترشيح على حزمة الأشعة السينية",
                controlsTitle: "التحكم في معلمات الأشعة",
                kvpLabel: "الجهد (kVp)",
                filterLabel: "الترشيح (mm Al)",
                materialLabel: "مادة الترشيح",
                resultsTitle: "نتائج المحاكاة",
                hvlLabel: "نصف قيمة الطبقة (HVL)",
                doseReductionLabel: "تخفيض الجرعة",
                visualTitle: "التمثيل البصري",
                detectorLabel: "كاشف الأشعة",
                spectrumTitle: "طيف الأشعة السينية",
                conceptsTitle: "المفاهيم الأساسية في ترشيح الأشعة السينية",
                concept1Title: "نصف قيمة الطبقة (HVL)",
                concept1Desc: "سُمك المادة اللازم لتقليل شدة الأشعة السينية إلى النصف. يُعد مقياساً مهماً لجودة الحزمة وقدرتها الاختراقية. كلما زادت قيمة HVL، كلما كانت الحزمة أكثر اختراقاً وأقل تأثراً بالامتصاص.",
                concept2Title: "تصلب الحزمة (Beam Hardening)",
                concept2Desc: "عملية زيادة متوسط طاقة حزمة الأشعة السينية نتيجة الامتصاص التفضيلي للفوتونات ذات الطاقة المنخفضة. يؤدي الترشيح إلى \"تصلب\" الحزمة، مما يجعلها أكثر اختراقاً وأقل تسبباً في الجرعات السطحية.",
                concept3Title: "الترشيح الذاتي",
                concept3Desc: "الترشيح الناتج عن مكونات أنبوب الأشعة السينية نفسه، مثل نافذة الزجاج والزيت العازل. يوفر هذا النوع من الترشيح حداً أدنى من الحماية من الإشعاع ذي الطاقة المنخفضة.",
                concept4Title: "الترشيح المضاف",
                concept4Desc: "مواد إضافية توضع في مسار الحزمة لتحسين جودتها، مثل الألومنيوم والنحاس. يتم اختيار مادة وسمك الترشيح بناءً على التطبيق السريري المحدد والتوازن المطلوب بين جودة الصورة والجرعة.",
                footerTitle: "منصة التعلم الطبي",
                footerDesc: "منصة تعليمية متخصصة في مجال الفيزياء الطبية وتقنيات التصوير بالأشعة السينية، تجمع بين المحتوى العلمي العميق والتطبيقات التفاعلية.",
                quickLinks: "روابط سريعة",
                homeLink: "الصفحة الرئيسية",
                contentLink: "المحتوى العلمي",
                simulationsLink: "المحاكاة التفاعلية",
                aboutLink: "عن المؤلف",
                copyright: "© 2024 جميع الحقوق محفوظة",
                backToHome: "العودة للرئيسية"
            },
            en: {
                pageTitle: "X-Ray Filtration",
                pageSubtitle: "Beam Quality Improvement and Dose Reduction",
                physicsTag: "Radiation Physics",
                protectionTag: "Radiation Protection",
                interactiveTitle: "Interactive Content",
                interactiveDesc: "Experience the effect of filtration on X-ray beam",
                introTitle: "Introduction to X-Ray Filtration",
                introP1: "X-ray filtration is one of the fundamental techniques used to improve image quality and reduce patient radiation dose. Filtration works by removing low-energy photons from the X-ray beam, which do not contribute to image formation but increase the radiation dose to the patient.",
                introP2: "X-ray filtration systems consist of two main types: inherent filtration (present in the X-ray tube itself) and added filtration (additional materials placed in the beam path). Appropriate filtration improves the contrast-to-noise ratio in the image and significantly reduces the surface dose to the patient.",
                simTitle: "Interactive Simulation: Effect of Filtration on X-Ray Beam",
                controlsTitle: "X-Ray Parameters Control",
                kvpLabel: "Voltage (kVp)",
                filterLabel: "Filtration (mm Al)",
                materialLabel: "Filter Material",
                resultsTitle: "Simulation Results",
                hvlLabel: "Half Value Layer (HVL)",
                doseReductionLabel: "Dose Reduction",
                visualTitle: "Visual Representation",
                detectorLabel: "X-Ray Detector",
                spectrumTitle: "X-Ray Spectrum",
                conceptsTitle: "Key Concepts in X-Ray Filtration",
                concept1Title: "Half Value Layer (HVL)",
                concept1Desc: "The thickness of material required to reduce the intensity of an X-ray beam to half its original value. It is an important measure of beam quality and penetration ability. The higher the HVL, the more penetrating the beam and the less affected by absorption.",
                concept2Title: "Beam Hardening",
                concept2Desc: "The process of increasing the average energy of an X-ray beam due to the preferential absorption of low-energy photons. Filtration 'hardens' the beam, making it more penetrating and less likely to cause surface doses.",
                concept3Title: "Inherent Filtration",
                concept3Desc: "Filtration resulting from the components of the X-ray tube itself, such as the glass window and insulating oil. This type of filtration provides a minimum level of protection from low-energy radiation.",
                concept4Title: "Added Filtration",
                concept4Desc: "Additional materials placed in the beam path to improve its quality, such as aluminum and copper. The material and thickness of filtration are chosen based on the specific clinical application and the desired balance between image quality and dose.",
                footerTitle: "Medical Learning Platform",
                footerDesc: "A specialized educational platform in the field of medical physics and X-ray imaging techniques, combining in-depth scientific content with interactive applications.",
                quickLinks: "Quick Links",
                homeLink: "Home",
                contentLink: "Scientific Content",
                simulationsLink: "Interactive Simulations",
                aboutLink: "About the Author",
                copyright: "© 2024 All Rights Reserved",
                backToHome: "Back to Home"
            }
        };
        
        // Current language
        let currentLanguage = 'ar';
        
        // Function to update UI language
        function updateLanguage(lang) {
            currentLanguage = lang;
            document.documentElement.lang = lang;
            document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
            
            // Update text content
            document.getElementById('page-title').textContent = translations[lang].pageTitle;
            document.getElementById('page-subtitle').textContent = translations[lang].pageSubtitle;
            document.getElementById('physics-tag').textContent = translations[lang].physicsTag;
            document.getElementById('protection-tag').textContent = translations[lang].protectionTag;
            document.getElementById('interactive-title').textContent = translations[lang].interactiveTitle;
            document.getElementById('interactive-desc').textContent = translations[lang].interactiveDesc;
            document.getElementById('intro-title').textContent = translations[lang].introTitle;
            document.getElementById('intro-p1').textContent = translations[lang].introP1;
            document.getElementById('intro-p2').textContent = translations[lang].introP2;
            document.getElementById('sim-title').textContent = translations[lang].simTitle;
            document.getElementById('controls-title').textContent = translations[lang].controlsTitle;
            document.getElementById('kvp-label').textContent = translations[lang].kvpLabel;
            document.getElementById('filter-label').textContent = translations[lang].filterLabel;
            document.getElementById('material-label').textContent = translations[lang].materialLabel;
            document.getElementById('results-title').textContent = translations[lang].resultsTitle;
            document.getElementById('hvl-label').textContent = translations[lang].hvlLabel;
            document.getElementById('dose-reduction-label').textContent = translations[lang].doseReductionLabel;
            document.getElementById('visual-title').textContent = translations[lang].visualTitle;
            document.getElementById('detector-label').textContent = translations[lang].detectorLabel;
            document.getElementById('spectrum-title').textContent = translations[lang].spectrumTitle;
            document.getElementById('concepts-title').textContent = translations[lang].conceptsTitle;
            document.getElementById('concept1-title').textContent = translations[lang].concept1Title;
            document.getElementById('concept1-desc').textContent = translations[lang].concept1Desc;
            document.getElementById('concept2-title').textContent = translations[lang].concept2Title;
            document.getElementById('concept2-desc').textContent = translations[lang].concept2Desc;
            document.getElementById('concept3-title').textContent = translations[lang].concept3Title;
            document.getElementById('concept3-desc').textContent = translations[lang].concept3Desc;
            document.getElementById('concept4-title').textContent = translations[lang].concept4Title;
            document.getElementById('concept4-desc').textContent = translations[lang].concept4Desc;
            document.getElementById('footer-title').textContent = translations[lang].footerTitle;
            document.getElementById('footer-desc').textContent = translations[lang].footerDesc;
            document.getElementById('quick-links').textContent = translations[lang].quickLinks;
            document.getElementById('home-link').textContent = translations[lang].homeLink;
            document.getElementById('content-link').textContent = translations[lang].contentLink;
            document.getElementById('simulations-link').textContent = translations[lang].simulationsLink;
            document.getElementById('about-link').textContent = translations[lang].aboutLink;
            document.getElementById('copyright').textContent = translations[lang].copyright;
            
            // Update language toggle button
            languageText.textContent = lang === 'ar' ? 'English' : 'العربية';
        }
        
        // Toggle language
        languageToggle.addEventListener('click', () => {
            const newLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            updateLanguage(newLanguage);
        });
        
        // Update filter material
        function updateFilterMaterial(material) {
            // Remove active class from all buttons
            alFilter.classList.remove('active', 'bg-blue-100', 'text-blue-800');
            cuFilter.classList.remove('active', 'bg-blue-100', 'text-blue-800');
            moFilter.classList.remove('active', 'bg-blue-100', 'text-blue-800');
            
            alFilter.classList.add('bg-gray-100', 'text-gray-800');
            cuFilter.classList.add('bg-gray-100', 'text-gray-800');
            moFilter.classList.add('bg-gray-100', 'text-gray-800');
            
            // Add active class to selected button
            if (material === 'al') {
                alFilter.classList.add('active', 'bg-blue-100', 'text-blue-800');
                alFilter.classList.remove('bg-gray-100', 'text-gray-800');
                filterElement.style.backgroundColor = '#A3A3A3'; // Aluminum color
            } else if (material === 'cu') {
                cuFilter.classList.add('active', 'bg-blue-100', 'text-blue-800');
                cuFilter.classList.remove('bg-gray-100', 'text-gray-800');
                filterElement.style.backgroundColor = '#B87333'; // Copper color
            } else if (material === 'mo') {
                moFilter.classList.add('active', 'bg-blue-100', 'text-blue-800');
                moFilter.classList.remove('bg-gray-100', 'text-gray-800');
                filterElement.style.backgroundColor = '#8A8A8A'; // Molybdenum color
            }
            
            updateSimulation();
        }
        
        // Add event listeners for filter material buttons
        alFilter.addEventListener('click', () => updateFilterMaterial('al'));
        cuFilter.addEventListener('click', () => updateFilterMaterial('cu'));
        moFilter.addEventListener('click', () => updateFilterMaterial('mo'));
        
        // Update kVp value display
        kvpSlider.addEventListener('input', () => {
            kvpValue.textContent = kvpSlider.value;
            updateSimulation();
        });
        
        // Update filter value display
        filterSlider.addEventListener('input', () => {
            filterValue.textContent = filterSlider.value;
            updateFilterVisualization();
            updateSimulation();
        });
        
        // Update filter visualization
        function updateFilterVisualization() {
            const filterThickness = parseFloat(filterSlider.value);
            filterElement.style.height = `${Math.max(4, filterThickness * 2)}px`;
            filterElement.style.opacity = Math.min(0.9, 0.4 + filterThickness * 0.1);
        }
        
        // Generate spectrum visualization
        function generateSpectrum() {
            // Clear existing spectrum
            spectrumContainer.innerHTML = '';
            
            const kvp = parseInt(kvpSlider.value);
            const filterThickness = parseFloat(filterSlider.value);
            const numBars = 20;
            
            // Get active filter material
            let filterMaterial = 'al';
            if (cuFilter.classList.contains('active')) filterMaterial = 'cu';
            if (moFilter.classList.contains('active')) filterMaterial = 'mo';
            
            // Material-specific attenuation factors (simplified)
            const attenuationFactor = {
                'al': 0.2,
                'cu': 0.5,
                'mo': 0.35
            };
            
            for (let i = 0; i < numBars; i++) {
                const energy = (i + 1) * (kvp / numBars);
                
                // Base height calculation (simplified bremsstrahlung spectrum)
                let height = (energy / kvp) * 100;
                
                // Add characteristic radiation peaks (simplified)
                if ((filterMaterial === 'mo' && (i === 11 || i === 13)) || 
                    (i === Math.floor(numBars * 0.7) || i === Math.floor(numBars * 0.85))) {
                    height *= 1.5;
                }
                
                // Apply filtration effect (more effect on lower energies)
                const energyFraction = energy / kvp;
                const filtrationEffect = Math.exp(-filterThickness * attenuationFactor[filterMaterial] * (1 - energyFraction));
                height *= filtrationEffect;
                
                // Create bar element
                const bar = document.createElement('div');
                bar.className = 'w-2 bg-blue-500 spectrum-bar';
                bar.style.height = `${Math.max(2, height)}%`;
                
                // Add to container
                spectrumContainer.appendChild(bar);
            }
        }
        
        // Update simulation results
        function updateSimulation() {
            const kvp = parseInt(kvpSlider.value);
            const filterThickness = parseFloat(filterSlider.value);
            
            // Get active filter material
            let filterMaterial = 'al';
            if (cuFilter.classList.contains('active')) filterMaterial = 'cu';
            if (moFilter.classList.contains('active')) filterMaterial = 'mo';
            
            // Material-specific factors (simplified calculations)
            const materialFactor = {
                'al': 1,
                'cu': 2.5,
                'mo': 1.8
            };
            
            // Calculate HVL (simplified)
            const hvl = ((kvp / 100) * 3 + filterThickness * materialFactor[filterMaterial]).toFixed(1);
            hvlValue.textContent = `${hvl} mm Al`;
            
            // Calculate dose reduction (simplified)
            const doseReduction = Math.min(90, Math.round(filterThickness * materialFactor[filterMaterial] * 15 + 5));
            doseReductionValue.textContent = `${doseReduction}%`;
            
            // Update spectrum visualization
            generateSpectrum();
        }
        
        // Initialize
        updateFilterVisualization();
        generateSpectrum();
        updateSimulation();
    </script>
    <!-- Language Switcher Script -->
    <script src="./js/language-switcher.js"></script>
    
    <!-- X-Ray Filtration Simulation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get DOM elements
            const kvpSlider = document.getElementById('kvp-slider');
            const kvpValue = document.getElementById('kvp-value');
            const filterSlider = document.getElementById('filter-slider');
            const filterValue = document.getElementById('filter-value');
            const filterElement = document.getElementById('filter-element');
            const hvlValue = document.getElementById('hvl-value');
            const doseReductionValue = document.getElementById('dose-reduction-value');
            const spectrumContainer = document.getElementById('spectrum-container');
            
            // Material buttons
            const alFilter = document.getElementById('al-filter');
            const cuFilter = document.getElementById('cu-filter');
            const moFilter = document.getElementById('mo-filter');
            
            // Initialize spectrum visualization
            initializeSpectrum();
            
            // Event listeners for sliders
            kvpSlider.addEventListener('input', updateSimulation);
            filterSlider.addEventListener('input', updateSimulation);
            
            // Event listeners for material buttons
            alFilter.addEventListener('click', function() {
                setActiveMaterial('al');
                updateSimulation();
            });
            
            cuFilter.addEventListener('click', function() {
                setActiveMaterial('cu');
                updateSimulation();
            });
            
            moFilter.addEventListener('click', function() {
                setActiveMaterial('mo');
                updateSimulation();
            });
            
            // Initialize simulation
            updateSimulation();
            
            // Function to initialize spectrum visualization
            function initializeSpectrum() {
                // Clear existing bars
                spectrumContainer.innerHTML = '';
                
                // Create 30 bars for the spectrum
                for (let i = 0; i < 30; i++) {
                    const bar = document.createElement('div');
                    bar.className = 'w-2 bg-blue-500 spectrum-bar';
                    bar.style.height = '10%';
                    spectrumContainer.appendChild(bar);
                }
            }
            
            // Function to update the active material button
            function setActiveMaterial(material) {
                alFilter.classList.remove('active', 'bg-blue-100', 'text-blue-800');
                cuFilter.classList.remove('active', 'bg-blue-100', 'text-blue-800');
                moFilter.classList.remove('active', 'bg-blue-100', 'text-blue-800');
                
                alFilter.classList.add('bg-gray-100', 'text-gray-800');
                cuFilter.classList.add('bg-gray-100', 'text-gray-800');
                moFilter.classList.add('bg-gray-100', 'text-gray-800');
                
                if (material === 'al') {
                    alFilter.classList.add('active', 'bg-blue-100', 'text-blue-800');
                    alFilter.classList.remove('bg-gray-100', 'text-gray-800');
                } else if (material === 'cu') {
                    cuFilter.classList.add('active', 'bg-blue-100', 'text-blue-800');
                    cuFilter.classList.remove('bg-gray-100', 'text-gray-800');
                } else if (material === 'mo') {
                    moFilter.classList.add('active', 'bg-blue-100', 'text-blue-800');
                    moFilter.classList.remove('bg-gray-100', 'text-gray-800');
                }
            }
            
            // Function to update the simulation based on current values
            function updateSimulation() {
                // Get current values
                const kvp = parseInt(kvpSlider.value);
                const filterThickness = parseFloat(filterSlider.value);
                
                // Update display values
                kvpValue.textContent = kvp;
                filterValue.textContent = filterThickness;
                
                // Update filter element visualization
                filterElement.style.height = (6 + filterThickness * 2) + 'px';
                
                // Determine active material
                let material = 'al';
                if (cuFilter.classList.contains('active')) {
                    material = 'cu';
                } else if (moFilter.classList.contains('active')) {
                    material = 'mo';
                }
                
                // Calculate HVL based on kVp, filter thickness and material
                let hvl = calculateHVL(kvp, filterThickness, material);
                hvlValue.textContent = hvl.toFixed(1) + ' mm Al';
                
                // Calculate dose reduction
                let doseReduction = calculateDoseReduction(filterThickness, material);
                doseReductionValue.textContent = doseReduction + '%';
                
                // Update spectrum visualization
                updateSpectrum(kvp, filterThickness, material);
            }
            
            // Function to calculate HVL based on kVp, filter thickness and material
            function calculateHVL(kvp, filterThickness, material) {
                // Simplified calculation for demonstration
                let baseHVL = kvp * 0.03;
                
                let materialFactor = 1;
                if (material === 'cu') {
                    materialFactor = 1.5;
                } else if (material === 'mo') {
                    materialFactor = 1.2;
                }
                
                return baseHVL + (filterThickness * materialFactor);
            }
            
            // Function to calculate dose reduction
            function calculateDoseReduction(filterThickness, material) {
                // Simplified calculation for demonstration
                let baseDoseReduction = filterThickness * 10;
                
                let materialFactor = 1;
                if (material === 'cu') {
                    materialFactor = 1.8;
                } else if (material === 'mo') {
                    materialFactor = 1.5;
                }
                
                return Math.min(Math.round(baseDoseReduction * materialFactor), 95);
            }
            
            // Function to update spectrum visualization
            function updateSpectrum(kvp, filterThickness, material) {
                const bars = spectrumContainer.querySelectorAll('.spectrum-bar');
                
                // Calculate spectrum shape based on kVp
                for (let i = 0; i < bars.length; i++) {
                    // Normalized position in the spectrum (0 to 1)
                    const x = i / (bars.length - 1);
                    
                    // Base height calculation (creates a typical X-ray spectrum shape)
                    let height = 100 * x * Math.exp(-4 * x * kvp / 150);
                    
                    // Apply filtration effect (reduces low energy photons)
                    let filterEffect = Math.exp(-filterThickness * (1 - x) * 2);
                    if (material === 'cu') {
                        filterEffect = Math.exp(-filterThickness * (1 - x) * 3);
                    } else if (material === 'mo') {
                        filterEffect = Math.exp(-filterThickness * (1 - x) * 2.5);
                    }
                    
                    height *= filterEffect;
                    
                    // Ensure minimum height for visibility
                    height = Math.max(height, 5);
                    
                    // Update bar height
                    bars[i].style.height = height + '%';
                }
            }
        });
    </script>
</body>
</html>