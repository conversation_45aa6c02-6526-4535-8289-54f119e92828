import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import PhotoelectricSimulation from '@/components/simulations/PhotoelectricSimulation';
import ComptonSimulation from '@/components/simulations/ComptonSimulation';
import PairProductionSimulation from '@/components/simulations/PairProductionSimulation';
import { Atom, Zap, Activity } from 'lucide-react';

const RadiationPhysics = () => {
  const [activeSimulation, setActiveSimulation] = useState('photoelectric');

  const interactions = [
  {
    id: 'photoelectric',
    title: 'Photoelectric Effect',
    description: 'Complete absorption of photon energy by inner shell electrons',
    icon: Atom,
    energyRange: '< 100 keV',
    dominance: 'Low energy, high Z materials'
  },
  {
    id: 'compton',
    title: 'Compton Scattering',
    description: 'Inelastic scattering of photons with outer shell electrons',
    icon: Activity,
    energyRange: '100 keV - 10 MeV',
    dominance: 'Intermediate energy, all materials'
  },
  {
    id: 'pair-production',
    title: 'Pair Production',
    description: 'Photon conversion to electron-positron pair near nucleus',
    icon: Zap,
    energyRange: '> 1.022 MeV',
    dominance: 'High energy, high Z materials'
  }];


  const renderSimulation = () => {
    switch (activeSimulation) {
      case 'photoelectric':
        return <PhotoelectricSimulation data-id="uvfkeht4j" data-path="src/pages/RadiationPhysics.tsx" />;
      case 'compton':
        return <ComptonSimulation data-id="tt7n82x98" data-path="src/pages/RadiationPhysics.tsx" />;
      case 'pair-production':
        return <PairProductionSimulation data-id="xqb9krefu" data-path="src/pages/RadiationPhysics.tsx" />;
      default:
        return <PhotoelectricSimulation data-id="xeuae307b" data-path="src/pages/RadiationPhysics.tsx" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50" data-id="qgjlwruot" data-path="src/pages/RadiationPhysics.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" data-id="yly664qra" data-path="src/pages/RadiationPhysics.tsx">
        {/* Header */}
        <div className="text-center mb-12" data-id="g6twl1veb" data-path="src/pages/RadiationPhysics.tsx">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4" data-id="u7wea2hai" data-path="src/pages/RadiationPhysics.tsx">
            Radiation Physics Simulations
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="u2f63dabz" data-path="src/pages/RadiationPhysics.tsx">
            Explore the fundamental interactions between radiation and matter through interactive simulations
          </p>
        </div>

        {/* Interaction Types Overview */}
        <div className="grid md:grid-cols-3 gap-6 mb-12" data-id="j6cy0z7es" data-path="src/pages/RadiationPhysics.tsx">
          {interactions.map((interaction) => {
            const Icon = interaction.icon;
            const isActive = activeSimulation === interaction.id;

            return (
              <Card
                key={interaction.id}
                className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${
                isActive ? 'ring-2 ring-blue-500 shadow-lg' : ''}`
                }
                onClick={() => setActiveSimulation(interaction.id)} data-id="s6if9a4qp" data-path="src/pages/RadiationPhysics.tsx">

                <CardHeader className="text-center" data-id="rd1fk99i2" data-path="src/pages/RadiationPhysics.tsx">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-xl flex items-center justify-center transition-colors duration-300 ${
                  isActive ? 'bg-blue-600' : 'bg-gray-100'}`
                  } data-id="u4411serj" data-path="src/pages/RadiationPhysics.tsx">
                    <Icon className={`w-8 h-8 ${isActive ? 'text-white' : 'text-gray-600'}`} data-id="xjouwsnxy" data-path="src/pages/RadiationPhysics.tsx" />
                  </div>
                  <CardTitle className="text-xl" data-id="c3a1y1cfs" data-path="src/pages/RadiationPhysics.tsx">{interaction.title}</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-3" data-id="qsh9etkmn" data-path="src/pages/RadiationPhysics.tsx">
                  <CardDescription data-id="dk7jc6933" data-path="src/pages/RadiationPhysics.tsx">{interaction.description}</CardDescription>
                  <div className="space-y-2" data-id="e47utmay7" data-path="src/pages/RadiationPhysics.tsx">
                    <Badge variant="outline" data-id="duz4g3dc8" data-path="src/pages/RadiationPhysics.tsx">{interaction.energyRange}</Badge>
                    <p className="text-sm text-gray-500" data-id="yr7ai9dar" data-path="src/pages/RadiationPhysics.tsx">{interaction.dominance}</p>
                  </div>
                </CardContent>
              </Card>);

          })}
        </div>

        {/* Main Simulation Area */}
        <div className="grid lg:grid-cols-4 gap-8" data-id="aukzami8l" data-path="src/pages/RadiationPhysics.tsx">
          {/* Simulation Controls */}
          <div className="lg:col-span-1" data-id="pxog6qnlk" data-path="src/pages/RadiationPhysics.tsx">
            <Card className="sticky top-24" data-id="5079z2u3q" data-path="src/pages/RadiationPhysics.tsx">
              <CardHeader data-id="2vmqld411" data-path="src/pages/RadiationPhysics.tsx">
                <CardTitle className="flex items-center gap-2" data-id="h6livs9as" data-path="src/pages/RadiationPhysics.tsx">
                  <Atom className="w-5 h-5" data-id="2r53x94gp" data-path="src/pages/RadiationPhysics.tsx" />
                  Simulation Controls
                </CardTitle>
              </CardHeader>
              <CardContent data-id="0dqo179ss" data-path="src/pages/RadiationPhysics.tsx">
                <Tabs value={activeSimulation} onValueChange={setActiveSimulation} data-id="7mexclu7k" data-path="src/pages/RadiationPhysics.tsx">
                  <TabsList className="grid w-full grid-cols-1 gap-2 h-auto bg-transparent" data-id="2gbc4e6ua" data-path="src/pages/RadiationPhysics.tsx">
                    <TabsTrigger
                      value="photoelectric"
                      className="w-full text-left justify-start data-[state=active]:bg-blue-600 data-[state=active]:text-white" data-id="qbvdnihvh" data-path="src/pages/RadiationPhysics.tsx">

                      Photoelectric
                    </TabsTrigger>
                    <TabsTrigger
                      value="compton"
                      className="w-full text-left justify-start data-[state=active]:bg-blue-600 data-[state=active]:text-white" data-id="b2wrvkuah" data-path="src/pages/RadiationPhysics.tsx">

                      Compton
                    </TabsTrigger>
                    <TabsTrigger
                      value="pair-production"
                      className="w-full text-left justify-start data-[state=active]:bg-blue-600 data-[state=active]:text-white" data-id="mq92j0aea" data-path="src/pages/RadiationPhysics.tsx">

                      Pair Production
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Simulation Display */}
          <div className="lg:col-span-3" data-id="fr0gqrhtx" data-path="src/pages/RadiationPhysics.tsx">
            <Card className="h-[600px]" data-id="vrk2lwbk8" data-path="src/pages/RadiationPhysics.tsx">
              <CardHeader data-id="8vfv9t0fo" data-path="src/pages/RadiationPhysics.tsx">
                <CardTitle data-id="rzyxsryms" data-path="src/pages/RadiationPhysics.tsx">
                  {interactions.find((i) => i.id === activeSimulation)?.title} Simulation
                </CardTitle>
                <CardDescription data-id="k3hxg7vhq" data-path="src/pages/RadiationPhysics.tsx">
                  Adjust parameters below to see how they affect the interaction
                </CardDescription>
              </CardHeader>
              <CardContent className="h-full" data-id="9wpnffwzl" data-path="src/pages/RadiationPhysics.tsx">
                {renderSimulation()}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Educational Content */}
        <div className="mt-16 grid md:grid-cols-2 gap-8" data-id="lp5f3emt8" data-path="src/pages/RadiationPhysics.tsx">
          <Card data-id="xptkx22fn" data-path="src/pages/RadiationPhysics.tsx">
            <CardHeader data-id="3hxc5l07q" data-path="src/pages/RadiationPhysics.tsx">
              <CardTitle data-id="oyyqp4eb6" data-path="src/pages/RadiationPhysics.tsx">Key Concepts</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4" data-id="oix7zyokg" data-path="src/pages/RadiationPhysics.tsx">
              <div data-id="ofszrxdqm" data-path="src/pages/RadiationPhysics.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="drw7qnlv6" data-path="src/pages/RadiationPhysics.tsx">Energy Dependence</h4>
                <p className="text-gray-600 text-sm" data-id="5wt6unjjo" data-path="src/pages/RadiationPhysics.tsx">
                  Different interactions dominate at different photon energies. The photoelectric effect is dominant at low energies, 
                  while Compton scattering is most important at intermediate energies, and pair production occurs at high energies.
                </p>
              </div>
              <div data-id="xlxxqihyr" data-path="src/pages/RadiationPhysics.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="d4k72p094" data-path="src/pages/RadiationPhysics.tsx">Material Dependence</h4>
                <p className="text-gray-600 text-sm" data-id="q1sgsybt7" data-path="src/pages/RadiationPhysics.tsx">
                  The atomic number (Z) of the material strongly influences which interaction is most likely. 
                  High-Z materials favor photoelectric effect and pair production.
                </p>
              </div>
              <div data-id="y9wsnt5nq" data-path="src/pages/RadiationPhysics.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="zyuqgs33t" data-path="src/pages/RadiationPhysics.tsx">Clinical Relevance</h4>
                <p className="text-gray-600 text-sm" data-id="cli25vrph" data-path="src/pages/RadiationPhysics.tsx">
                  Understanding these interactions is crucial for optimizing image quality, calculating radiation doses, 
                  and designing effective shielding in medical imaging.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card data-id="zcvmvj4ln" data-path="src/pages/RadiationPhysics.tsx">
            <CardHeader data-id="gx9td13ab" data-path="src/pages/RadiationPhysics.tsx">
              <CardTitle data-id="zch8b8nu1" data-path="src/pages/RadiationPhysics.tsx">Mathematical Relationships</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4" data-id="r7pab6s5w" data-path="src/pages/RadiationPhysics.tsx">
              <div data-id="1s8kqot8s" data-path="src/pages/RadiationPhysics.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="5iywf7jqh" data-path="src/pages/RadiationPhysics.tsx">Photoelectric Cross-Section</h4>
                <p className="text-gray-600 text-sm mb-2" data-id="xqs9u7vxi" data-path="src/pages/RadiationPhysics.tsx">σ ∝ Z⁴/E³</p>
                <p className="text-gray-600 text-sm" data-id="v0pkqr3tv" data-path="src/pages/RadiationPhysics.tsx">Strong dependence on atomic number and inverse cube of energy</p>
              </div>
              <div data-id="fcqf0qf11" data-path="src/pages/RadiationPhysics.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="xwvpg2nuq" data-path="src/pages/RadiationPhysics.tsx">Compton Cross-Section</h4>
                <p className="text-gray-600 text-sm mb-2" data-id="ye9ywmju4" data-path="src/pages/RadiationPhysics.tsx">σ ∝ Z/E</p>
                <p className="text-gray-600 text-sm" data-id="y5swtxgul" data-path="src/pages/RadiationPhysics.tsx">Linear dependence on Z and inverse dependence on energy</p>
              </div>
              <div data-id="47wzh9cfd" data-path="src/pages/RadiationPhysics.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="5bkzediz2" data-path="src/pages/RadiationPhysics.tsx">Pair Production Cross-Section</h4>
                <p className="text-gray-600 text-sm mb-2" data-id="869a4jzjx" data-path="src/pages/RadiationPhysics.tsx">σ ∝ Z² ln(E)</p>
                <p className="text-gray-600 text-sm" data-id="k6atytxni" data-path="src/pages/RadiationPhysics.tsx">Quadratic dependence on Z and logarithmic dependence on energy</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>);

};

export default RadiationPhysics;