import { useState } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import {
  Zap,
  Target,
  Thermometer,
  Settings,
  Play,
  Pause,
  RotateCcw } from
'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import XRayTubeDiagram from '@/components/XRayTubeDiagram';
import XRaySimulator from '@/components/XRaySimulator';

const XRayTubePage = () => {
  const [activeTab, setActiveTab] = useState('structure');
  const { t } = useLanguage();

  const tubeComponents = [
  {
    id: 'cathode',
    name: t('xray.cathode'),
    description: 'Source of electrons, contains heated tungsten filament',
    details: [
    'Heated tungsten filament produces electrons',
    'Focusing cup directs electrons',
    'Operating temperature: 2000-2500°C',
    'Filament life: 1000-2000 hours'],

    color: 'bg-red-500'
  },
  {
    id: 'anode',
    name: t('xray.anode'),
    description: 'Target where electrons strike to produce X-rays',
    details: [
    'Made of tungsten or molybdenum',
    'Target angle: 12-17 degrees',
    'High heat resistance capability',
    'Rotating disc distributes heat'],

    color: 'bg-blue-500'
  },
  {
    id: 'envelope',
    name: t('xray.housing'),
    description: 'Contains high vacuum for free electron movement',
    details: [
    'Borosilicate glass heat resistant',
    'High vacuum (10⁻⁶ mmHg)',
    'X-ray exit window',
    'Protection from contamination'],

    color: 'bg-green-500'
  },
  {
    id: 'housing',
    name: 'Protective Housing',
    description: 'Provides radiation protection and cooling',
    details: [
    'Lead for radiation protection',
    'Oil or air cooling system',
    'High voltage electrical insulation',
    'Operation and temperature indicators'],

    color: 'bg-purple-500'
  }];


  return (
    <div className="min-h-screen py-8" data-id="n5ggxd0ra" data-path="src/pages/XRayTubePage.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="3dccagxpb" data-path="src/pages/XRayTubePage.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12" data-id="q12sczh9y" data-path="src/pages/XRayTubePage.tsx">

          <Badge variant="secondary" className="mb-4" data-id="uvv9xwjon" data-path="src/pages/XRayTubePage.tsx">{t('xray.title')}</Badge>
          <h1 className="text-4xl font-bold text-gray-900 mb-4" data-id="skygzm3dq" data-path="src/pages/XRayTubePage.tsx">
            {t('xray.title')}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="61ptw29yh" data-path="src/pages/XRayTubePage.tsx">
            {t('xray.subtitle')}
          </p>
        </motion.div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" data-id="a0m4uny9s" data-path="src/pages/XRayTubePage.tsx">
          <TabsList className="grid w-full grid-cols-3 mb-8" data-id="80gzc07uq" data-path="src/pages/XRayTubePage.tsx">
            <TabsTrigger value="structure" data-id="8czm7v0pm" data-path="src/pages/XRayTubePage.tsx">{t('xray.overview')}</TabsTrigger>
            <TabsTrigger value="operation" data-id="5m8m8wufq" data-path="src/pages/XRayTubePage.tsx">{t('xray.components')}</TabsTrigger>
            <TabsTrigger value="simulator" data-id="jm0oq5zs6" data-path="src/pages/XRayTubePage.tsx">{t('xray.simulation')}</TabsTrigger>
          </TabsList>

          {/* Structure Tab */}
          <TabsContent value="structure" data-id="khkcd8i0t" data-path="src/pages/XRayTubePage.tsx">
            <div className="grid lg:grid-cols-2 gap-8" data-id="x357x39ag" data-path="src/pages/XRayTubePage.tsx">
              {/* Diagram */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }} data-id="kdziveb2g" data-path="src/pages/XRayTubePage.tsx">

                <Card className="h-full" data-id="dke9q32lq" data-path="src/pages/XRayTubePage.tsx">
                  <CardHeader data-id="krw2ovj0l" data-path="src/pages/XRayTubePage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="rqzlhqv6l" data-path="src/pages/XRayTubePage.tsx">
                      <Target className="w-5 h-5 text-blue-600" data-id="i8qtuizuu" data-path="src/pages/XRayTubePage.tsx" />
                      Interactive Diagram
                    </CardTitle>
                    <CardDescription data-id="iy60jxbzd" data-path="src/pages/XRayTubePage.tsx">
                      Click on components to explore their details
                    </CardDescription>
                  </CardHeader>
                  <CardContent data-id="w6d4zqvcm" data-path="src/pages/XRayTubePage.tsx">
                    <XRayTubeDiagram data-id="mnv4boxhy" data-path="src/pages/XRayTubePage.tsx" />
                  </CardContent>
                </Card>
              </motion.div>

              {/* Components */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="space-y-4" data-id="pmjppd09x" data-path="src/pages/XRayTubePage.tsx">

                {tubeComponents.map((component, index) =>
                <motion.div
                  key={component.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }} data-id="ycfxpy8zc" data-path="src/pages/XRayTubePage.tsx">

                    <Card className="hover:shadow-lg transition-shadow" data-id="dqfvke00j" data-path="src/pages/XRayTubePage.tsx">
                      <CardHeader className="pb-3" data-id="c0ikrs5el" data-path="src/pages/XRayTubePage.tsx">
                        <div className="flex items-center gap-3" data-id="tz7kady0v" data-path="src/pages/XRayTubePage.tsx">
                          <div className={`w-4 h-4 rounded-full ${component.color}`} data-id="1z2negb87" data-path="src/pages/XRayTubePage.tsx"></div>
                          <CardTitle className="text-lg" data-id="q31uwy72e" data-path="src/pages/XRayTubePage.tsx">{component.name}</CardTitle>
                        </div>
                        <CardDescription data-id="o0y7jn16c" data-path="src/pages/XRayTubePage.tsx">{component.description}</CardDescription>
                      </CardHeader>
                      <CardContent data-id="6s00afnk2" data-path="src/pages/XRayTubePage.tsx">
                        <ul className="space-y-2" data-id="b1xo1ery7" data-path="src/pages/XRayTubePage.tsx">
                          {component.details.map((detail, idx) =>
                        <li key={idx} className="flex items-start gap-2 text-sm text-gray-600" data-id="61iqf6g6c" data-path="src/pages/XRayTubePage.tsx">
                              <div className="w-1.5 h-1.5 rounded-full bg-gray-400 mt-2 flex-shrink-0" data-id="wwzampl41" data-path="src/pages/XRayTubePage.tsx"></div>
                              {detail}
                            </li>
                        )}
                        </ul>
                      </CardContent>
                    </Card>
                  </motion.div>
                )}
              </motion.div>
            </div>
          </TabsContent>

          {/* Operation Tab */}
          <TabsContent value="operation" data-id="0yw55pjam" data-path="src/pages/XRayTubePage.tsx">
            <div className="grid gap-8" data-id="m9zwzq20s" data-path="src/pages/XRayTubePage.tsx">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }} data-id="fo2bavyhz" data-path="src/pages/XRayTubePage.tsx">

                <Card data-id="v2os1yhlr" data-path="src/pages/XRayTubePage.tsx">
                  <CardHeader data-id="rff9mtf82" data-path="src/pages/XRayTubePage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="425hu600i" data-path="src/pages/XRayTubePage.tsx">
                      <Zap className="w-5 h-5 text-yellow-600" data-id="syofiz6z9" data-path="src/pages/XRayTubePage.tsx" />
                      X-Ray Production Stages
                    </CardTitle>
                  </CardHeader>
                  <CardContent data-id="8p8ogi994" data-path="src/pages/XRayTubePage.tsx">
                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6" data-id="wbd3c74zq" data-path="src/pages/XRayTubePage.tsx">
                      {[
                      {
                        step: '1',
                        title: 'Filament Heating',
                        description: 'Electric current heats tungsten filament to release electrons',
                        icon: Thermometer,
                        color: 'bg-red-100 text-red-600'
                      },
                      {
                        step: '2',
                        title: 'Electron Acceleration',
                        description: 'High voltage (40-150 kV) accelerates electrons toward anode',
                        icon: Zap,
                        color: 'bg-yellow-100 text-yellow-600'
                      },
                      {
                        step: '3',
                        title: 'Impact & Interaction',
                        description: 'Electrons strike anode and kinetic energy is converted',
                        icon: Target,
                        color: 'bg-blue-100 text-blue-600'
                      },
                      {
                        step: '4',
                        title: 'X-Ray Production',
                        description: 'X-rays produced (1%) and heat (99%) from interaction',
                        icon: Zap,
                        color: 'bg-purple-100 text-purple-600'
                      }].
                      map((step, index) => {
                        const Icon = step.icon;
                        return (
                          <motion.div
                            key={step.step}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.2 }}
                            className="text-center" data-id="xf5r4urtz" data-path="src/pages/XRayTubePage.tsx">

                            <div className={`inline-flex items-center justify-center w-16 h-16 ${step.color} rounded-full mb-4`} data-id="hbkcru8ud" data-path="src/pages/XRayTubePage.tsx">
                              <Icon className="w-8 h-8" data-id="i03byaxd4" data-path="src/pages/XRayTubePage.tsx" />
                            </div>
                            <div className="bg-gray-900 text-white text-sm font-bold w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-3" data-id="ypd4n4xgl" data-path="src/pages/XRayTubePage.tsx">
                              {step.step}
                            </div>
                            <h3 className="font-semibold text-gray-900 mb-2" data-id="7ptc51kk9" data-path="src/pages/XRayTubePage.tsx">{step.title}</h3>
                            <p className="text-sm text-gray-600" data-id="uwwvsrj1r" data-path="src/pages/XRayTubePage.tsx">{step.description}</p>
                          </motion.div>);

                      })}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Technical Specifications */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }} data-id="2evezbnkq" data-path="src/pages/XRayTubePage.tsx">

                <Card data-id="wl0xa3amr" data-path="src/pages/XRayTubePage.tsx">
                  <CardHeader data-id="j9crztz5l" data-path="src/pages/XRayTubePage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="2j9vwzwbu" data-path="src/pages/XRayTubePage.tsx">
                      <Settings className="w-5 h-5 text-gray-600" data-id="oec9qly73" data-path="src/pages/XRayTubePage.tsx" />
                      Technical Specifications
                    </CardTitle>
                  </CardHeader>
                  <CardContent data-id="nbe2kyqbq" data-path="src/pages/XRayTubePage.tsx">
                    <div className="grid md:grid-cols-3 gap-6" data-id="ow6pbqe3p" data-path="src/pages/XRayTubePage.tsx">
                      <div className="space-y-4" data-id="hi5gzqr06" data-path="src/pages/XRayTubePage.tsx">
                        <h4 className="font-semibold text-gray-900" data-id="7izmb2gsa" data-path="src/pages/XRayTubePage.tsx">Voltage & Current</h4>
                        <div className="space-y-2 text-sm" data-id="prjitaw9n" data-path="src/pages/XRayTubePage.tsx">
                          <div className="flex justify-between" data-id="pmroa144j" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="meesh6jnq" data-path="src/pages/XRayTubePage.tsx">{t('xray.voltage')}:</span>
                            <span className="font-medium" data-id="thgsdb5sj" data-path="src/pages/XRayTubePage.tsx">40-150 kV</span>
                          </div>
                          <div className="flex justify-between" data-id="5r1lq0gom" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="19ecqzb80" data-path="src/pages/XRayTubePage.tsx">{t('xray.current')}:</span>
                            <span className="font-medium" data-id="uilnuo4dn" data-path="src/pages/XRayTubePage.tsx">50-1000 mA</span>
                          </div>
                          <div className="flex justify-between" data-id="tmmfikhi0" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="bbng5a9o0" data-path="src/pages/XRayTubePage.tsx">Filament Current:</span>
                            <span className="font-medium" data-id="qxw1ypzrg" data-path="src/pages/XRayTubePage.tsx">3-5 A</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4" data-id="mwzxdtg1e" data-path="src/pages/XRayTubePage.tsx">
                        <h4 className="font-semibold text-gray-900" data-id="nj1oapgnv" data-path="src/pages/XRayTubePage.tsx">Heat & Cooling</h4>
                        <div className="space-y-2 text-sm" data-id="lr2od6oor" data-path="src/pages/XRayTubePage.tsx">
                          <div className="flex justify-between" data-id="d7mkig4da" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="fes8h6qql" data-path="src/pages/XRayTubePage.tsx">Anode Heat Capacity:</span>
                            <span className="font-medium" data-id="pcxgcvw2p" data-path="src/pages/XRayTubePage.tsx">300-2000 kJ</span>
                          </div>
                          <div className="flex justify-between" data-id="rdpwh4fmx" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="u4k0g9ki8" data-path="src/pages/XRayTubePage.tsx">Cooling Rate:</span>
                            <span className="font-medium" data-id="kqnuq2gyr" data-path="src/pages/XRayTubePage.tsx">50-200 kW</span>
                          </div>
                          <div className="flex justify-between" data-id="2glss7dgm" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="svq7v1vnp" data-path="src/pages/XRayTubePage.tsx">Operating Temperature:</span>
                            <span className="font-medium" data-id="9csvu556h" data-path="src/pages/XRayTubePage.tsx">1000-2500°C</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4" data-id="28igl5uko" data-path="src/pages/XRayTubePage.tsx">
                        <h4 className="font-semibold text-gray-900" data-id="82zt1ne32" data-path="src/pages/XRayTubePage.tsx">Physical Structure</h4>
                        <div className="space-y-2 text-sm" data-id="du640q7ir" data-path="src/pages/XRayTubePage.tsx">
                          <div className="flex justify-between" data-id="435pv6bbt" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="e8s0d248p" data-path="src/pages/XRayTubePage.tsx">Anode Angle:</span>
                            <span className="font-medium" data-id="akqxsgud1" data-path="src/pages/XRayTubePage.tsx">12-17°</span>
                          </div>
                          <div className="flex justify-between" data-id="zv1i46egh" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="yywn8mnh6" data-path="src/pages/XRayTubePage.tsx">Focal Spot Size:</span>
                            <span className="font-medium" data-id="z3414i9vo" data-path="src/pages/XRayTubePage.tsx">0.3-2.0 mm</span>
                          </div>
                          <div className="flex justify-between" data-id="udm5jdnsk" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="uy7ho1bjz" data-path="src/pages/XRayTubePage.tsx">Vacuum Level:</span>
                            <span className="font-medium" data-id="vctglhk4n" data-path="src/pages/XRayTubePage.tsx">10⁻⁶ mmHg</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </TabsContent>

          {/* Simulator Tab */}
          <TabsContent value="simulator" data-id="lvfapyprn" data-path="src/pages/XRayTubePage.tsx">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }} data-id="dptkv1655" data-path="src/pages/XRayTubePage.tsx">

              <XRaySimulator data-id="x5ukhwa0v" data-path="src/pages/XRayTubePage.tsx" />
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>
    </div>);

};

export default XRayTubePage;