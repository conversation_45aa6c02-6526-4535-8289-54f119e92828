import { useState } from 'react';
import { motion } from 'motion/react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

const CircuitDiagram = () => {
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);

  const components = {
    transformer: {
      name: 'المحول الرئيسي',
      description: 'يرفع الجهد من 220V إلى 40-150kV',
      details: 'محول تصاعدي بنسبة تحويل 1:500-700، يعمل على تردد 50/60 Hz',
      specifications: ['نسبة التحويل: 1:500-700', 'القدرة: 50-100 kW', 'التبريد: زيت أو هواء'],
      color: '#3b82f6'
    },
    rectifier: {
      name: 'المعدل',
      description: 'يحول التيار المتردد إلى مستمر',
      details: 'مجموعة من الديودات عالية الجهد لتعديل الموجة الكاملة أو النصف موجة',
      specifications: ['نوع: سيليكون أو سيلينيوم', 'جهد التحمل: 200kV', 'تيار التحمل: 1A'],
      color: '#ef4444'
    },
    filter: {
      name: 'المرشح',
      description: 'ينعم التيار المستمر',
      details: 'مكثفات وملفات لتقليل التموج في التيار المستمر إلى أقل من 5%',
      specifications: ['مكثفات: 0.1-1 μF', 'ملفات حديدية', 'تقليل التموج: &lt;5%'],
      color: '#10b981'
    },
    filament: {
      name: 'محول الفتيل',
      description: 'يوفر جهد منخفض لتسخين الفتيل',
      details: 'محول تنازلي لتوفير الجهد والتيار المناسبين لتسخين فتيل الكاثود',
      specifications: ['الجهد: 5-12V', 'التيار: 3-5A', 'نوع: محول منخفض الجهد'],
      color: '#f59e0b'
    }
  };

  return (
    <div className="space-y-6" data-id="53cbqbwc8" data-path="src/components/CircuitDiagram.tsx">
      {/* SVG Circuit Diagram */}
      <div className="relative bg-white rounded-lg p-6 border min-h-[500px] overflow-auto" data-id="bz6i3l579" data-path="src/components/CircuitDiagram.tsx">
        <svg
          width="800"
          height="450"
          viewBox="0 0 800 450"
          className="max-w-full h-auto" data-id="sx6v32n4e" data-path="src/components/CircuitDiagram.tsx">

          {/* Main Power Input */}
          <text x="20" y="30" className="text-sm font-semibold fill-gray-700" data-id="t1xtqvzyq" data-path="src/components/CircuitDiagram.tsx">
            مدخل التيار الكهربائي 220V AC
          </text>
          <line x1="20" y1="50" x2="120" y2="50" stroke="#374151" strokeWidth="2" data-id="wainnz8vl" data-path="src/components/CircuitDiagram.tsx" />
          <line x1="20" y1="70" x2="120" y2="70" stroke="#374151" strokeWidth="2" data-id="ce0h8nd9q" data-path="src/components/CircuitDiagram.tsx" />

          {/* Primary Transformer */}
          <motion.g
            onClick={() => setSelectedComponent(selectedComponent === 'transformer' ? null : 'transformer')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="uziq9iaqf" data-path="src/components/CircuitDiagram.tsx">

            <circle
              cx="160"
              cy="60"
              r="40"
              fill={selectedComponent === 'transformer' ? components.transformer.color : '#e5e7eb'}
              stroke={selectedComponent === 'transformer' ? components.transformer.color : '#9ca3af'}
              strokeWidth="2" data-id="02mucrosc" data-path="src/components/CircuitDiagram.tsx" />

            {/* Primary coil */}
            <g stroke="#374151" strokeWidth="2" fill="none" data-id="2mdvxe7ik" data-path="src/components/CircuitDiagram.tsx">
              <path d="M130 45 Q135 40 140 45 Q145 50 150 45 Q155 40 160 45" data-id="ay3fqh8oi" data-path="src/components/CircuitDiagram.tsx" />
              <path d="M130 75 Q135 80 140 75 Q145 70 150 75 Q155 80 160 75" data-id="3vvzj6jyh" data-path="src/components/CircuitDiagram.tsx" />
            </g>
            {/* Secondary coil */}
            <g stroke="#374151" strokeWidth="2" fill="none" data-id="5dzrniyl6" data-path="src/components/CircuitDiagram.tsx">
              <path d="M160 45 Q165 40 170 45 Q175 50 180 45 Q185 40 190 45" data-id="0lxuprsqi" data-path="src/components/CircuitDiagram.tsx" />
              <path d="M160 75 Q165 80 170 75 Q175 70 180 75 Q185 80 190 75" data-id="rllbxxa34" data-path="src/components/CircuitDiagram.tsx" />
            </g>
            <text x="160" y="110" textAnchor="middle" className="text-xs font-medium fill-gray-700" data-id="tdrt6tdsn" data-path="src/components/CircuitDiagram.tsx">
              المحول الرئيسي
            </text>
          </motion.g>

          {/* High Voltage Lines */}
          <line x1="200" y1="50" x2="280" y2="50" stroke="#dc2626" strokeWidth="3" data-id="b2ava2e94" data-path="src/components/CircuitDiagram.tsx" />
          <line x1="200" y1="70" x2="280" y2="70" stroke="#dc2626" strokeWidth="3" data-id="njaccjlbe" data-path="src/components/CircuitDiagram.tsx" />
          <text x="240" y="40" textAnchor="middle" className="text-xs fill-red-600 font-semibold" data-id="rkq7j8vgf" data-path="src/components/CircuitDiagram.tsx">
            40-150 kV
          </text>

          {/* Rectifier */}
          <motion.g
            onClick={() => setSelectedComponent(selectedComponent === 'rectifier' ? null : 'rectifier')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="y6m1qf55v" data-path="src/components/CircuitDiagram.tsx">

            <rect
              x="280"
              y="30"
              width="80"
              height="60"
              rx="8"
              fill={selectedComponent === 'rectifier' ? components.rectifier.color : '#fef3c7'}
              stroke={selectedComponent === 'rectifier' ? components.rectifier.color : '#f59e0b'}
              strokeWidth="2" data-id="stpsuug5a" data-path="src/components/CircuitDiagram.tsx" />

            {/* Diode symbols */}
            <g stroke="#374151" strokeWidth="2" data-id="gh9vgpsrf" data-path="src/components/CircuitDiagram.tsx">
              <polygon points="300,45 310,50 300,55" fill="#374151" data-id="cx38t8ggu" data-path="src/components/CircuitDiagram.tsx" />
              <line x1="310" y1="45" x2="310" y2="55" data-id="snvjwhacg" data-path="src/components/CircuitDiagram.tsx" />
              <polygon points="320,65 330,70 320,75" fill="#374151" data-id="562jbq5ey" data-path="src/components/CircuitDiagram.tsx" />
              <line x1="330" y1="65" x2="330" y2="75" data-id="b7kpucvh7" data-path="src/components/CircuitDiagram.tsx" />
            </g>
            <text x="320" y="110" textAnchor="middle" className="text-xs font-medium fill-gray-700" data-id="b4oa3svwe" data-path="src/components/CircuitDiagram.tsx">
              المعدل
            </text>
          </motion.g>

          {/* DC Lines */}
          <line x1="360" y1="50" x2="440" y2="50" stroke="#16a34a" strokeWidth="3" data-id="c8urvmfw2" data-path="src/components/CircuitDiagram.tsx" />
          <line x1="360" y1="70" x2="440" y2="70" stroke="#16a34a" strokeWidth="3" data-id="kz5kca424" data-path="src/components/CircuitDiagram.tsx" />
          <text x="400" y="40" textAnchor="middle" className="text-xs fill-green-600 font-semibold" data-id="4vscbtz5z" data-path="src/components/CircuitDiagram.tsx">
            DC High Voltage
          </text>

          {/* Filter */}
          <motion.g
            onClick={() => setSelectedComponent(selectedComponent === 'filter' ? null : 'filter')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="7g2wkaowb" data-path="src/components/CircuitDiagram.tsx">

            <rect
              x="440"
              y="30"
              width="80"
              height="60"
              rx="8"
              fill={selectedComponent === 'filter' ? components.filter.color : '#d1fae5'}
              stroke={selectedComponent === 'filter' ? components.filter.color : '#10b981'}
              strokeWidth="2" data-id="ox1ykwree" data-path="src/components/CircuitDiagram.tsx" />

            {/* Capacitor symbol */}
            <g stroke="#374151" strokeWidth="2" data-id="vmsx3lvkl" data-path="src/components/CircuitDiagram.tsx">
              <line x1="460" y1="45" x2="460" y2="65" data-id="dhptebaox" data-path="src/components/CircuitDiagram.tsx" />
              <line x1="465" y1="45" x2="465" y2="65" data-id="pn8jxh6vp" data-path="src/components/CircuitDiagram.tsx" />
              <path d="M475 45 Q480 40 485 45 Q490 50 495 45 Q500 40 505 45" fill="none" data-id="1vr47rfca" data-path="src/components/CircuitDiagram.tsx" />
            </g>
            <text x="480" y="110" textAnchor="middle" className="text-xs font-medium fill-gray-700" data-id="ch4shkx78" data-path="src/components/CircuitDiagram.tsx">
              المرشح
            </text>
          </motion.g>

          {/* To X-ray Tube */}
          <line x1="520" y1="50" x2="600" y2="50" stroke="#8b5cf6" strokeWidth="3" data-id="9oqwy605x" data-path="src/components/CircuitDiagram.tsx" />
          <line x1="520" y1="70" x2="600" y2="70" stroke="#8b5cf6" strokeWidth="3" data-id="xekx2x4j1" data-path="src/components/CircuitDiagram.tsx" />

          {/* X-ray Tube Representation */}
          <ellipse cx="650" cy="60" rx="60" ry="40" fill="#f3f4f6" stroke="#6b7280" strokeWidth="2" data-id="beuvr8gtc" data-path="src/components/CircuitDiagram.tsx" />
          <circle cx="620" cy="60" r="8" fill="#ef4444" data-id="n29y9a896" data-path="src/components/CircuitDiagram.tsx" />
          <circle cx="680" cy="60" r="8" fill="#3b82f6" data-id="0h7zd68ox" data-path="src/components/CircuitDiagram.tsx" />
          <text x="650" y="120" textAnchor="middle" className="text-xs font-medium fill-gray-700" data-id="74bdzcj3p" data-path="src/components/CircuitDiagram.tsx">
            أنبوب الأشعة السينية
          </text>

          {/* Filament Circuit */}
          <motion.g
            onClick={() => setSelectedComponent(selectedComponent === 'filament' ? null : 'filament')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="tbjtwkfgi" data-path="src/components/CircuitDiagram.tsx">

            <rect
              x="300"
              y="200"
              width="80"
              height="50"
              rx="8"
              fill={selectedComponent === 'filament' ? components.filament.color : '#fef3c7'}
              stroke={selectedComponent === 'filament' ? components.filament.color : '#f59e0b'}
              strokeWidth="2" data-id="7iy10ls4c" data-path="src/components/CircuitDiagram.tsx" />

            <text x="340" y="230" textAnchor="middle" className="text-xs font-medium fill-gray-700" data-id="0ohtdq67g" data-path="src/components/CircuitDiagram.tsx">
              محول الفتيل
            </text>
          </motion.g>

          {/* Filament connections */}
          <line x1="100" y1="200" x2="300" y2="200" stroke="#374151" strokeWidth="2" data-id="1qxu8yr4i" data-path="src/components/CircuitDiagram.tsx" />
          <line x1="100" y1="220" x2="300" y2="220" stroke="#374151" strokeWidth="2" data-id="1hai3lcov" data-path="src/components/CircuitDiagram.tsx" />
          <line x1="380" y1="210" x2="620" y2="210" stroke="#374151" strokeWidth="2" data-id="m8yq41b3k" data-path="src/components/CircuitDiagram.tsx" />
          <line x1="620" y1="210" x2="620" y2="68" stroke="#374151" strokeWidth="2" data-id="49tmk0dhh" data-path="src/components/CircuitDiagram.tsx" />

          <text x="100" y="190" className="text-xs fill-gray-600" data-id="jkp838iwq" data-path="src/components/CircuitDiagram.tsx">
            5-12V AC (دائرة الفتيل)
          </text>

          {/* Ground symbols */}
          <g stroke="#374151" strokeWidth="2" data-id="lu9lx3xno" data-path="src/components/CircuitDiagram.tsx">
            <line x1="650" y1="100" x2="650" y2="120" data-id="gqx9e3qv9" data-path="src/components/CircuitDiagram.tsx" />
            <line x1="640" y1="120" x2="660" y2="120" data-id="4ypkl5qni" data-path="src/components/CircuitDiagram.tsx" />
            <line x1="645" y1="125" x2="655" y2="125" data-id="h0m2b4kkx" data-path="src/components/CircuitDiagram.tsx" />
            <line x1="648" y1="130" x2="652" y2="130" data-id="etd7oe8c5" data-path="src/components/CircuitDiagram.tsx" />
          </g>

          {/* Current flow indicators */}
          {selectedComponent &&
          <motion.g
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }} data-id="jtjf4frax" data-path="src/components/CircuitDiagram.tsx">

              <defs data-id="xh78a9xr9" data-path="src/components/CircuitDiagram.tsx">
                <marker
                id="arrowhead-flow"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto" data-id="53cf97vun" data-path="src/components/CircuitDiagram.tsx">

                  <polygon points="0 0, 10 3.5, 0 7" fill="#16a34a" data-id="n6dhbcydu" data-path="src/components/CircuitDiagram.tsx" />
                </marker>
              </defs>
              <line
              x1="70"
              y1="60"
              x2="110"
              y2="60"
              stroke="#16a34a"
              strokeWidth="2"
              markerEnd="url(#arrowhead-flow)" data-id="0zp8ow9yq" data-path="src/components/CircuitDiagram.tsx" />

            </motion.g>
          }

          {/* Title */}
          <text x="400" y="400" textAnchor="middle" className="text-lg font-bold fill-gray-800" data-id="ekvse1alz" data-path="src/components/CircuitDiagram.tsx">
            المخطط الكهربائي الرئيسي لجهاز الأشعة السينية
          </text>
        </svg>
      </div>

      {/* Component Information */}
      {selectedComponent &&
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }} data-id="19ty9yco6" data-path="src/components/CircuitDiagram.tsx">

          <Card data-id="po1zvxist" data-path="src/components/CircuitDiagram.tsx">
            <CardContent className="p-6" data-id="vvk0hk9vq" data-path="src/components/CircuitDiagram.tsx">
              <div className="flex items-start gap-4" data-id="2dpo559u9" data-path="src/components/CircuitDiagram.tsx">
                <div
                className="w-4 h-4 rounded-full flex-shrink-0 mt-1"
                style={{ backgroundColor: components[selectedComponent as keyof typeof components].color }} data-id="7e8kx8vkj" data-path="src/components/CircuitDiagram.tsx">
              </div>
                <div className="flex-1" data-id="acjpg5xk4" data-path="src/components/CircuitDiagram.tsx">
                  <div className="flex items-center gap-2 mb-2" data-id="pssczrt7e" data-path="src/components/CircuitDiagram.tsx">
                    <h3 className="text-lg font-semibold text-gray-900" data-id="wrmobf0q7" data-path="src/components/CircuitDiagram.tsx">
                      {components[selectedComponent as keyof typeof components].name}
                    </h3>
                    <Badge variant="secondary" data-id="rg3fnegdw" data-path="src/components/CircuitDiagram.tsx">محدد</Badge>
                  </div>
                  <p className="text-gray-600 mb-3" data-id="4i7ybwt8j" data-path="src/components/CircuitDiagram.tsx">
                    {components[selectedComponent as keyof typeof components].description}
                  </p>
                  <p className="text-sm text-gray-700 mb-4 leading-relaxed" data-id="4d38f3urg" data-path="src/components/CircuitDiagram.tsx">
                    {components[selectedComponent as keyof typeof components].details}
                  </p>
                  <div className="space-y-2" data-id="7601c6ia6" data-path="src/components/CircuitDiagram.tsx">
                    <h4 className="font-medium text-gray-900" data-id="wnxen3q9v" data-path="src/components/CircuitDiagram.tsx">المواصفات التقنية:</h4>
                    <ul className="space-y-1" data-id="794qoorx3" data-path="src/components/CircuitDiagram.tsx">
                      {components[selectedComponent as keyof typeof components].specifications.map((spec, index) =>
                    <li key={index} className="flex items-start gap-2 text-sm text-gray-600" data-id="pia05ql7e" data-path="src/components/CircuitDiagram.tsx">
                          <div className="w-1.5 h-1.5 rounded-full bg-gray-400 mt-2 flex-shrink-0" data-id="kz4tm6p2o" data-path="src/components/CircuitDiagram.tsx"></div>
                          <span dangerouslySetInnerHTML={{ __html: spec }} data-id="6ekugdac9" data-path="src/components/CircuitDiagram.tsx"></span>
                        </li>
                    )}
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      }

      {!selectedComponent &&
      <div className="text-center text-gray-500 text-sm" data-id="fcac9e55n" data-path="src/components/CircuitDiagram.tsx">
          انقر على أي مكون في المخطط لعرض تفاصيله التقنية
        </div>
      }
    </div>);

};

export default CircuitDiagram;