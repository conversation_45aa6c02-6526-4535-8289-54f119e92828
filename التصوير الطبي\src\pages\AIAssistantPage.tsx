import { useState, useRef, useEffect } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Brain,
  Send,
  Bot,
  User,
  Lightbulb,
  HelpCircle,
  MessageSquare } from
'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const AIAssistantPage = () => {
  const [messages, setMessages] = useState<Message[]>([
  {
    id: '1',
    type: 'assistant',
    content: 'مرحباً! أنا مساعدك الذكي المختص في أجهزة التصوير الطبي. يمكنني مساعدتك في فهم مبادئ الإشعاع المؤين، أنبوب الأشعة السينية، والدوائر الكهربائية. ما الذي تود معرفته؟',
    timestamp: new Date()
  }]
  );
  const [input, setInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const quickQuestions = [
  'كيف يعمل أنبوب الأشعة السينية؟',
  'ما هو الإشعاع المؤين؟',
  'كيف تتم الحماية من الإشعاع؟',
  'ما هي مكونات الدائرة الكهربائية؟',
  'كيف يتم إنتاج الأشعة السينية؟',
  'ما هي معادلة الطاقة الإشعاعية؟'];


  const knowledgeBase = {
    'أنبوب الأشعة السينية': {
      keywords: ['أنبوب', 'اشعة', 'سينية', 'كاثود', 'انود', 'فتيل'],
      response: `أنبوب الأشعة السينية يتكون من مكونات رئيسية:

**الكاثود (المهبط):**
- فتيل التنجستن المُسخن
- ينتج الإلكترونات بالانبعاث الحراري
- درجة حرارة التشغيل: 2000-2500°C

**الأنود (المصعد):**
- هدف من التنجستن أو الموليبدينوم
- يستقبل الإلكترونات المتسارعة
- زاوية الهدف: 12-17 درجة

**آلية العمل:**
1. تسخين الفتيل لإنتاج الإلكترونات
2. تسريع الإلكترونات بجهد عالي (40-150kV)
3. اصطدام الإلكترونات بالأنود
4. إنتاج الأشعة السينية (1%) والحرارة (99%)`
    },
    'الإشعاع المؤين': {
      keywords: ['اشعاع', 'مؤين', 'تأين', 'طاقة', 'فوتون'],
      response: `الإشعاع المؤين هو نوع من الطاقة له القدرة على إزالة الإلكترونات من الذرات:

**الخصائص:**
- طاقة عالية (>12.4 eV)
- قدرة على اختراق المواد
- تفاعل مع الأنسجة البيولوجية
- إمكانية القياس والكشف

**الأنواع:**
- الأشعة السينية (X-rays)
- أشعة جاما (Gamma rays)
- جسيمات ألفا وبيتا

**التطبيقات الطبية:**
- التصوير التشخيصي
- العلاج الإشعاعي
- الطب النووي`
    },
    'الحماية الإشعاعية': {
      keywords: ['حماية', 'وقاية', 'سلامة', 'جرعة', 'alara'],
      response: `مبادئ الحماية الإشعاعية الثلاثة:

**1. التبرير (Justification):**
- الفوائد أكبر من المخاطر
- ضرورة طبية للفحص
- تقييم المؤشرات الطبية

**2. التحسين (Optimization) - مبدأ ALARA:**
- As Low As Reasonably Achievable
- تقليل الجرعة للحد الأدنى
- استخدام التقنيات المناسبة

**3. حدود الجرعة (Dose Limits):**
- حدود للعاملين: 20 mSv/سنة
- حدود للجمهور: 1 mSv/سنة
- مراقبة الجرعات المتراكمة

**وسائل الحماية:**
- الزمن: تقليل وقت التعرض
- المسافة: زيادة المسافة من المصدر
- الحاجز: استخدام الرصاص والمواد الواقية`
    },
    'الدوائر الكهربائية': {
      keywords: ['دائرة', 'كهربائية', 'محول', 'معدل', 'فولتية'],
      response: `مكونات الدائرة الكهربائية لجهاز الأشعة السينية:

**المحول الرئيسي:**
- رفع الجهد من 220V إلى 40-150kV
- نسبة التحويل: 1:500-700
- كفاءة: 95-98%

**المعدل (Rectifier):**
- تحويل AC إلى DC
- ديودات عالية الجهد
- تعديل موجة كاملة أو نصف موجة

**المرشح (Filter):**
- تنعيم التيار المستمر
- مكثفات وملفات حديدية
- تقليل التموج إلى <5%

**محول الفتيل:**
- جهد منخفض: 5-12V
- تيار: 3-5A
- تسخين فتيل الكاثود`
    }
  };

  const generateResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    // البحث في قاعدة المعرفة
    for (const [topic, data] of Object.entries(knowledgeBase)) {
      if (data.keywords.some((keyword) => input.includes(keyword))) {
        return data.response;
      }
    }

    // ردود عامة للأسئلة الشائعة
    if (input.includes('شكر') || input.includes('تسلم')) {
      return 'عفواً! سعيد لمساعدتك. هل لديك أسئلة أخرى حول التصوير الطبي؟';
    }

    if (input.includes('مساعدة') || input.includes('help')) {
      return `يمكنني مساعدتك في المواضيع التالية:

📚 **المفاهيم الأساسية:**
- الإشعاع المؤين وخصائصه
- فيزياء الأشعة السينية

🔧 **أنبوب الأشعة السينية:**
- المكونات والتركيب
- آلية العمل والتشغيل

⚡ **الدوائر الكهربائية:**
- مزود الطاقة والتحكم
- المخططات الكهربائية

🛡️ **الحماية الإشعاعية:**
- مبادئ السلامة
- طرق الوقاية

اطرح سؤالك أو اختر من الأسئلة السريعة!`;
    }

    // رد افتراضي
    return `سؤال مثير للاهتمام! دعني أساعدك بشكل عام:

للحصول على إجابة دقيقة، يمكنك:
- استخدام كلمات مفتاحية أكثر تحديداً
- زيارة صفحات المحتوى التعليمي
- طرح سؤال حول موضوع محدد

المواضيع المتاحة:
• أنبوب الأشعة السينية
• الإشعاع المؤين  
• الدوائر الكهربائية
• الحماية الإشعاعية

كيف يمكنني مساعدتك أكثر؟`;
  };

  const handleSend = async () => {
    if (!input.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: input,
      timestamp: new Date()
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setIsTyping(true);

    // محاكاة وقت التفكير
    setTimeout(() => {
      const response = generateResponse(input);
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response,
        timestamp: new Date()
      };

      setMessages((prev) => [...prev, assistantMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 2000);
  };

  const handleQuickQuestion = (question: string) => {
    setInput(question);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className="min-h-screen py-8" data-id="zmoiyr5xm" data-path="src/pages/AIAssistantPage.tsx">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8" data-id="2hljqvim5" data-path="src/pages/AIAssistantPage.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8" data-id="0e6f3zq51" data-path="src/pages/AIAssistantPage.tsx">

          <Badge variant="secondary" className="mb-4" data-id="f81uegauq" data-path="src/pages/AIAssistantPage.tsx">المساعد الذكي</Badge>
          <h1 className="text-4xl font-bold text-gray-900 mb-4" data-id="ujvu9xnj3" data-path="src/pages/AIAssistantPage.tsx">
            مساعد التصوير الطبي الذكي
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto" data-id="c9shk06v1" data-path="src/pages/AIAssistantPage.tsx">
            احصل على إجابات فورية ومفصلة لأسئلتك حول أجهزة التصوير الطبي والإشعاع المؤين
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8" data-id="9h3djou4e" data-path="src/pages/AIAssistantPage.tsx">
          {/* Chat Interface */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-2" data-id="kt3t3fk16" data-path="src/pages/AIAssistantPage.tsx">

            <Card className="h-[600px] flex flex-col" data-id="zrt1ns14z" data-path="src/pages/AIAssistantPage.tsx">
              <CardHeader data-id="enm91e7bc" data-path="src/pages/AIAssistantPage.tsx">
                <CardTitle className="flex items-center gap-2" data-id="0fb83jirh" data-path="src/pages/AIAssistantPage.tsx">
                  <Brain className="w-5 h-5 text-purple-600" data-id="m8bx9avy3" data-path="src/pages/AIAssistantPage.tsx" />
                  محادثة مع المساعد
                </CardTitle>
                <CardDescription data-id="s1v9toefz" data-path="src/pages/AIAssistantPage.tsx">
                  اطرح أسئلتك واحصل على إجابات تفصيلية
                </CardDescription>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col p-0" data-id="b3e9tsi9g" data-path="src/pages/AIAssistantPage.tsx">
                {/* Messages */}
                <ScrollArea className="flex-1 p-4" data-id="kc22adx4g" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="space-y-4" data-id="zkueoagl1" data-path="src/pages/AIAssistantPage.tsx">
                    {messages.map((message) =>
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex gap-3 ${
                      message.type === 'user' ? 'justify-end' : 'justify-start'}`
                      } data-id="gmknz2ot0" data-path="src/pages/AIAssistantPage.tsx">

                        {message.type === 'assistant' &&
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0" data-id="z6wjyie94" data-path="src/pages/AIAssistantPage.tsx">
                            <Bot className="w-4 h-4 text-purple-600" data-id="ll7rw8zgc" data-path="src/pages/AIAssistantPage.tsx" />
                          </div>
                      }
                        
                        <div
                        className={`max-w-[80%] rounded-lg px-4 py-3 ${
                        message.type === 'user' ?
                        'bg-blue-600 text-white' :
                        'bg-gray-100 text-gray-900'}`
                        } data-id="gqmforaib" data-path="src/pages/AIAssistantPage.tsx">

                          <div className="whitespace-pre-line text-sm leading-relaxed" data-id="x3ax5zufm" data-path="src/pages/AIAssistantPage.tsx">
                            {message.content}
                          </div>
                          <div
                          className={`text-xs mt-2 ${
                          message.type === 'user' ? 'text-blue-100' : 'text-gray-500'}`
                          } data-id="ai9fa7yo7" data-path="src/pages/AIAssistantPage.tsx">

                            {message.timestamp.toLocaleTimeString('ar-SA', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                          </div>
                        </div>

                        {message.type === 'user' &&
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0" data-id="w4qfa9sr7" data-path="src/pages/AIAssistantPage.tsx">
                            <User className="w-4 h-4 text-blue-600" data-id="6g0a8gim4" data-path="src/pages/AIAssistantPage.tsx" />
                          </div>
                      }
                      </motion.div>
                    )}

                    {isTyping &&
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex gap-3 justify-start" data-id="3qtq1k6jv" data-path="src/pages/AIAssistantPage.tsx">

                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0" data-id="evxtgzge2" data-path="src/pages/AIAssistantPage.tsx">
                          <Bot className="w-4 h-4 text-purple-600" data-id="4j9kwyumk" data-path="src/pages/AIAssistantPage.tsx" />
                        </div>
                        <div className="bg-gray-100 rounded-lg px-4 py-3" data-id="4omhyrokc" data-path="src/pages/AIAssistantPage.tsx">
                          <div className="flex space-x-1" data-id="csxdf4vyc" data-path="src/pages/AIAssistantPage.tsx">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" data-id="eec83ddj4" data-path="src/pages/AIAssistantPage.tsx"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} data-id="g5v2sdgln" data-path="src/pages/AIAssistantPage.tsx"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} data-id="lviwnqxwj" data-path="src/pages/AIAssistantPage.tsx"></div>
                          </div>
                        </div>
                      </motion.div>
                    }
                    <div ref={messagesEndRef} data-id="sci6iywnl" data-path="src/pages/AIAssistantPage.tsx" />
                  </div>
                </ScrollArea>

                {/* Input */}
                <div className="p-4 border-t" data-id="yc8wsd58p" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="flex gap-2" data-id="lkxs01zs7" data-path="src/pages/AIAssistantPage.tsx">
                    <Input
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder="اكتب سؤالك هنا..."
                      onKeyPress={(e) => e.key === 'Enter' && handleSend()}
                      disabled={isTyping} data-id="1e37w8y5b" data-path="src/pages/AIAssistantPage.tsx" />

                    <Button
                      onClick={handleSend}
                      disabled={!input.trim() || isTyping}
                      size="icon" data-id="79ckjrcrb" data-path="src/pages/AIAssistantPage.tsx">

                      <Send className="w-4 h-4" data-id="u0ixf6xa1" data-path="src/pages/AIAssistantPage.tsx" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Quick Questions & Help */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6" data-id="xfzriq2wy" data-path="src/pages/AIAssistantPage.tsx">

            {/* Quick Questions */}
            <Card data-id="ttw3x8ezm" data-path="src/pages/AIAssistantPage.tsx">
              <CardHeader data-id="3j0al9mt4" data-path="src/pages/AIAssistantPage.tsx">
                <CardTitle className="flex items-center gap-2 text-lg" data-id="da8yftie3" data-path="src/pages/AIAssistantPage.tsx">
                  <HelpCircle className="w-5 h-5 text-green-600" data-id="8qoo2uirh" data-path="src/pages/AIAssistantPage.tsx" />
                  أسئلة سريعة
                </CardTitle>
                <CardDescription data-id="mux91juo7" data-path="src/pages/AIAssistantPage.tsx">
                  انقر على السؤال لطرحه مباشرة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2" data-id="be1g4kpj5" data-path="src/pages/AIAssistantPage.tsx">
                {quickQuestions.map((question, index) =>
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }} data-id="qhoo38e9i" data-path="src/pages/AIAssistantPage.tsx">

                    <Button
                    variant="ghost"
                    className="w-full text-right justify-start text-sm h-auto py-3 px-3 hover:bg-blue-50"
                    onClick={() => handleQuickQuestion(question)} data-id="jxrgume70" data-path="src/pages/AIAssistantPage.tsx">

                      <MessageSquare className="w-4 h-4 ml-2 flex-shrink-0" data-id="y7zsld00r" data-path="src/pages/AIAssistantPage.tsx" />
                      <span className="text-right leading-relaxed" data-id="9g0f1whc9" data-path="src/pages/AIAssistantPage.tsx">{question}</span>
                    </Button>
                  </motion.div>
                )}
              </CardContent>
            </Card>

            {/* Tips */}
            <Card data-id="t5qpjbuvq" data-path="src/pages/AIAssistantPage.tsx">
              <CardHeader data-id="ei2e3pttu" data-path="src/pages/AIAssistantPage.tsx">
                <CardTitle className="flex items-center gap-2 text-lg" data-id="nhmxax2g5" data-path="src/pages/AIAssistantPage.tsx">
                  <Lightbulb className="w-5 h-5 text-yellow-600" data-id="7guy6vyod" data-path="src/pages/AIAssistantPage.tsx" />
                  نصائح للاستخدام
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm text-gray-600" data-id="ykk7w497k" data-path="src/pages/AIAssistantPage.tsx">
                <div className="flex items-start gap-2" data-id="96sb0r5k4" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" data-id="f8m33czt9" data-path="src/pages/AIAssistantPage.tsx"></div>
                  <span data-id="1jx0yrmp4" data-path="src/pages/AIAssistantPage.tsx">استخدم كلمات مفتاحية واضحة في أسئلتك</span>
                </div>
                <div className="flex items-start gap-2" data-id="uqnxzprna" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" data-id="5brspjvha" data-path="src/pages/AIAssistantPage.tsx"></div>
                  <span data-id="2e9moozj4" data-path="src/pages/AIAssistantPage.tsx">اطرح أسئلة محددة للحصول على إجابات دقيقة</span>
                </div>
                <div className="flex items-start gap-2" data-id="2lv8u7vbm" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" data-id="jjc46zy4i" data-path="src/pages/AIAssistantPage.tsx"></div>
                  <span data-id="c5zgkrhxm" data-path="src/pages/AIAssistantPage.tsx">يمكنك السؤال عن المعادلات والحسابات</span>
                </div>
                <div className="flex items-start gap-2" data-id="g2e06ma33" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" data-id="8pfr6hwz4" data-path="src/pages/AIAssistantPage.tsx"></div>
                  <span data-id="rqobp8cbl" data-path="src/pages/AIAssistantPage.tsx">استكشف المحتوى التفاعلي في الصفحات الأخرى</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>);

};

export default AIAssistantPage;