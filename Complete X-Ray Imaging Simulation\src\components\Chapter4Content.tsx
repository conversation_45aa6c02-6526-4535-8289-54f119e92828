import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Zap, TrendingUp, BarChart3, Thermometer, Activity, Target } from 'lucide-react';
import { motion } from 'motion/react';

const Chapter4Content = () => {
  const [openSections, setOpenSections] = useState<{[key: string]: boolean;}>({});
  const [completedSections, setCompletedSections] = useState<string[]>([]);

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const markAsCompleted = (sectionId: string) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections((prev) => [...prev, sectionId]);
    }
  };

  const sections = [
  {
    id: '4.1',
    title: 'تسارع الإلكترون والطاقة الحركية',
    icon: <Zap className="w-5 h-5" data-id="91ileg105" data-path="src/components/Chapter4Content.tsx" />,
    content: 'عندما يتم تطبيق فولتية عالية بين الكاثود والأنود، تكتسب الإلكترونات طاقة حركية: KE = eV، حيث e هو شحنة الإلكترون و V هو الجهد المطبق. هذه الطاقة تحدد الطاقة القصوى للأشعة السينية المنتجة.'
  },
  {
    id: '4.2',
    title: 'تفاعلات الإلكترون والهدف: نظرة عامة',
    icon: <Target className="w-5 h-5" data-id="hl2vntttz" data-path="src/components/Chapter4Content.tsx" />,
    content: 'عندما تصطدم الإلكترونات عالية الطاقة بالأنود، تحدث نوعان رئيسيان من التفاعلات: إشعاع الكبح (Bremsstrahlung) والإشعاع المميز (Characteristic radiation). كل نوع له خصائصه الفريدة وأهميته في التصوير.'
  },
  {
    id: '4.3',
    title: 'إشعاع Bremsstrahlung (الكبح)',
    icon: <TrendingUp className="w-5 h-5" data-id="1j9vu8i2b" data-path="src/components/Chapter4Content.tsx" />,
    subsections: [
    {
      id: '4.3.1',
      title: 'عملية التباطؤ وانبعاث الفوتونات',
      content: 'عندما يمر إلكترون سريع بالقرب من نواة ذرية، يتباطأ بسبب الجذب الكولومبي. هذا التباطؤ يؤدي إلى إصدار فوتون أشعة سينية. طاقة الفوتون تساوي الفرق في الطاقة الحركية للإلكترون.'
    },
    {
      id: '4.3.2',
      title: 'طيف الطاقة المستمر: الشكل والمحددات',
      content: 'إشعاع Bremsstrahlung ينتج طيفاً مستمراً من الطاقات من الصفر حتى الطاقة القصوى (hνmax = eV). الطيف له شكل مميز مع ذروة عند حوالي ثلث الطاقة القصوى.'
    },
    {
      id: '4.3.3',
      title: 'الاعتماد على طاقة الإلكترون (kVp) والعدد الذري المستهدف (Z)',
      content: 'كفاءة إنتاج Bremsstrahlung تزداد مع زيادة kVp والعدد الذري للهدف. الكفاءة تتناسب مع Z×E، حيث Z العدد الذري و E طاقة الإلكترون.'
    },
    {
      id: '4.3.4',
      title: 'التوزيع الزاوي لفوتونات Bremsstrahlung',
      content: 'عند طاقات منخفضة، الإشعاع موزع بانتظام. عند طاقات عالية، الإشعاع يصبح أكثر توجهاً في اتجاه حركة الإلكترون (الأمام).'
    },
    {
      id: '4.3.5',
      title: 'قانون كرامر والنماذج التجريبية',
      content: 'قانون كرامر يصف العلاقة بين طيف Bremsstrahlung والمعاملات الفيزيائية. النماذج التجريبية مثل نموذج Tucker تستخدم لتقدير الطيف في التطبيقات العملية.'
    }]

  },
  {
    id: '4.4',
    title: 'الإشعاع المميز',
    icon: <BarChart3 className="w-5 h-5" data-id="s4ep7bvc8" data-path="src/components/Chapter4Content.tsx" />,
    subsections: [
    {
      id: '4.4.1',
      title: 'تأين الغلاف الداخلي وانتقالات الإلكترونات',
      content: 'عندما يطرد إلكترون من الغلاف الداخلي (مثل غلاف K)، ينتقل إلكترون من غلاف خارجي لملء الفجوة، مما ينتج عنه إصدار فوتون أشعة سينية بطاقة مميزة.'
    },
    {
      id: '4.4.2',
      title: 'ذروات الطاقة المنفصلة وأهميتها',
      content: 'الإشعاع المميز ينتج ذروات طاقة منفصلة ومحددة تعتمد على مادة الهدف. هذه الذروات مهمة في التطبيقات المتخصصة مثل تصوير الثدي والتحليل الطيفي.'
    },
    {
      id: '4.4.3',
      title: 'الاعتماد على مادة الهدف (قانون موزلي)',
      content: 'قانون موزلي يربط تردد الإشعاع المميز بالعدد الذري: ν = R(Z-σ)²(1/n₁² - 1/n₂²)، حيث R ثابت ريدبرغ، σ ثابت الحجب، n₁ و n₂ أعداد الكم الرئيسية.'
    },
    {
      id: '4.4.4',
      title: 'إنتاج الفلورسنت وإلكترونات أوجيه',
      content: 'بعض انتقالات الإلكترونات تنتج فوتونات فلورسنت، بينما أخرى تنتج إلكترونات أوجيه. النسبة بين الاثنين تعتمد على العدد الذري ومستوى الطاقة.'
    }]

  },
  {
    id: '4.5',
    title: 'طيف طاقة الأشعة السينية المجمعة',
    icon: <Activity className="w-5 h-5" data-id="x22llbale" data-path="src/components/Chapter4Content.tsx" />,
    content: 'الطيف النهائي للأشعة السينية هو مزيج من الطيف المستمر لـ Bremsstrahlung والذروات المنفصلة للإشعاع المميز. شكل هذا الطيف يعتمد على kVp، مادة الأنود، والترشيح.'
  },
  {
    id: '4.6',
    title: 'كفاءة إنتاج الأشعة السينية',
    icon: <TrendingUp className="w-5 h-5" data-id="d4kw9txl0" data-path="src/components/Chapter4Content.tsx" />,
    subsections: [
    {
      id: '4.6.1',
      title: 'الحساب والقيم النموذجية',
      content: 'كفاءة إنتاج الأشعة السينية منخفضة جداً (أقل من 1% عند 100 kVp). الكفاءة تتناسب مع kVp وZ، وتحسب كـ: η ≈ k×Z×V، حيث k ثابت صغير.'
    },
    {
      id: '4.6.2',
      title: 'الآثار المترتبة على إنتاج الحرارة',
      content: 'أكثر من 99% من طاقة الإلكترونات تتحول إلى حرارة، مما يستلزم أنظمة تبريد فعالة وإدارة حرارية دقيقة لمنع تلف الأنود.'
    }]

  },
  {
    id: '4.7',
    title: 'إنتاج الحرارة وإدارتها عند الأنود',
    icon: <Thermometer className="w-5 h-5" data-id="jcv62tp70" data-path="src/components/Chapter4Content.tsx" />,
    subsections: [
    {
      id: '4.7.1',
      title: 'حسابات وحدة الحرارة (HU) لمولدات مختلفة',
      content: 'وحدة الحرارة (HU) تقيس الطاقة الحرارية المودعة في الأنود: HU = kVp × mA × s × rectification factor. للمولدات أحادية الطور: عامل التصحيح = 1.0، ثلاثي الطور = 1.35.'
    },
    {
      id: '4.7.2',
      title: 'مخططات السعة الحرارية والتبريد للأنود',
      content: 'مخططات التبريد تظهر كيفية تبديد الحرارة مع الوقت. معدل التبريد يتبع دالة أسية: H(t) = H₀ × e^(-t/τ)، حيث τ ثابت الوقت للتبريد.'
    }]

  }];


  const progress = completedSections.length / sections.length * 100;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6" dir="rtl" data-id="3e25dm5fl" data-path="src/components/Chapter4Content.tsx">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }} data-id="7x7l175hq" data-path="src/components/Chapter4Content.tsx">
        
        <Card className="mb-6" data-id="y36hyvlow" data-path="src/components/Chapter4Content.tsx">
          <CardHeader className="text-center" data-id="v9cck85yw" data-path="src/components/Chapter4Content.tsx">
            <CardTitle className="text-2xl font-bold text-right" data-id="v31xqohlz" data-path="src/components/Chapter4Content.tsx">
              الفصل الرابع: فيزياء توليد الأشعة السينية في أنابيب التشخيص
            </CardTitle>
            <CardDescription className="text-right" data-id="351qnuj4p" data-path="src/components/Chapter4Content.tsx">
              دراسة متعمقة لآليات توليد الأشعة السينية والعمليات الفيزيائية المرتبطة
            </CardDescription>
            <div className="mt-4" data-id="7nl1mlfe8" data-path="src/components/Chapter4Content.tsx">
              <div className="flex justify-between items-center mb-2" data-id="i7f6z3n62" data-path="src/components/Chapter4Content.tsx">
                <span className="text-sm text-muted-foreground" data-id="8e87hgqxn" data-path="src/components/Chapter4Content.tsx">التقدم</span>
                <span className="text-sm font-medium" data-id="2pl36cjej" data-path="src/components/Chapter4Content.tsx">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" data-id="umvywsx23" data-path="src/components/Chapter4Content.tsx" />
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      <Tabs defaultValue="content" className="w-full" data-id="exb6q7flp" data-path="src/components/Chapter4Content.tsx">
        <TabsList className="grid w-full grid-cols-4" data-id="uvg5bfhic" data-path="src/components/Chapter4Content.tsx">
          <TabsTrigger value="content" data-id="2l74k37zi" data-path="src/components/Chapter4Content.tsx">المحتوى</TabsTrigger>
          <TabsTrigger value="equations" data-id="tgefu5jvv" data-path="src/components/Chapter4Content.tsx">المعادلات</TabsTrigger>
          <TabsTrigger value="spectra" data-id="io6qm06ip" data-path="src/components/Chapter4Content.tsx">الأطياف</TabsTrigger>
          <TabsTrigger value="calculations" data-id="kv6ldkth8" data-path="src/components/Chapter4Content.tsx">الحسابات</TabsTrigger>
        </TabsList>
        
        <TabsContent value="content" className="space-y-4" data-id="88w3eo8dt" data-path="src/components/Chapter4Content.tsx">
          {sections.map((section, index) =>
          <motion.div
            key={section.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }} data-id="1an1i85s4" data-path="src/components/Chapter4Content.tsx">
              
              <Card className="overflow-hidden" data-id="vyszbsfi7" data-path="src/components/Chapter4Content.tsx">
                <Collapsible
                open={openSections[section.id]}
                onOpenChange={() => toggleSection(section.id)} data-id="aj87ur0v8" data-path="src/components/Chapter4Content.tsx">
                  
                  <CollapsibleTrigger asChild data-id="xzf4z6koe" data-path="src/components/Chapter4Content.tsx">
                    <CardHeader className="hover:bg-muted/50 cursor-pointer transition-colors" data-id="rky74exxt" data-path="src/components/Chapter4Content.tsx">
                      <div className="flex items-center justify-between" data-id="grzox90d2" data-path="src/components/Chapter4Content.tsx">
                        <div className="flex items-center gap-3" data-id="ar9o2wntw" data-path="src/components/Chapter4Content.tsx">
                          <div className="flex items-center gap-2" data-id="zw1hrlv6q" data-path="src/components/Chapter4Content.tsx">
                            {section.icon}
                            <Badge variant="outline" data-id="ek8xvhxi1" data-path="src/components/Chapter4Content.tsx">{section.id}</Badge>
                          </div>
                          <CardTitle className="text-lg text-right" data-id="lmzl9r35m" data-path="src/components/Chapter4Content.tsx">{section.title}</CardTitle>
                        </div>
                        <div className="flex items-center gap-2" data-id="5007ajays" data-path="src/components/Chapter4Content.tsx">
                          {completedSections.includes(section.id) &&
                        <Badge variant="default" data-id="g9wazasvf" data-path="src/components/Chapter4Content.tsx">مكتمل</Badge>
                        }
                          {openSections[section.id] ?
                        <ChevronDown className="w-4 h-4" data-id="3dcvujiso" data-path="src/components/Chapter4Content.tsx" /> :

                        <ChevronRight className="w-4 h-4" data-id="55umwx4qy" data-path="src/components/Chapter4Content.tsx" />
                        }
                        </div>
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent data-id="vrtmity0y" data-path="src/components/Chapter4Content.tsx">
                    <CardContent className="pt-0" data-id="ojiozg06v" data-path="src/components/Chapter4Content.tsx">
                      {section.subsections ?
                    <div className="space-y-4" data-id="icglujtaf" data-path="src/components/Chapter4Content.tsx">
                          {section.subsections.map((subsection) =>
                      <Card key={subsection.id} className="border-l-4 border-l-primary" data-id="x1gy8ki45" data-path="src/components/Chapter4Content.tsx">
                              <CardHeader className="pb-2" data-id="dvpin9y8b" data-path="src/components/Chapter4Content.tsx">
                                <div className="flex items-center gap-2" data-id="e4708667b" data-path="src/components/Chapter4Content.tsx">
                                  <Badge variant="secondary" data-id="7a1lh8ymt" data-path="src/components/Chapter4Content.tsx">{subsection.id}</Badge>
                                  <CardTitle className="text-base text-right" data-id="gg0y7jd20" data-path="src/components/Chapter4Content.tsx">
                                    {subsection.title}
                                  </CardTitle>
                                </div>
                              </CardHeader>
                              <CardContent data-id="gjrr3eujb" data-path="src/components/Chapter4Content.tsx">
                                <p className="text-muted-foreground text-right leading-relaxed" data-id="pl4q6np40" data-path="src/components/Chapter4Content.tsx">
                                  {subsection.content}
                                </p>
                              </CardContent>
                            </Card>
                      )}
                        </div> :

                    <p className="text-muted-foreground text-right leading-relaxed" data-id="h8sul7goq" data-path="src/components/Chapter4Content.tsx">
                          {section.content}
                        </p>
                    }
                      
                      <div className="mt-4 flex justify-start" data-id="ci3rul05q" data-path="src/components/Chapter4Content.tsx">
                        <Button
                        onClick={() => markAsCompleted(section.id)}
                        disabled={completedSections.includes(section.id)}
                        size="sm" data-id="a5pwgrlpx" data-path="src/components/Chapter4Content.tsx">
                          {completedSections.includes(section.id) ? 'مكتمل' : 'وضع علامة كمكتمل'}
                        </Button>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            </motion.div>
          )}
        </TabsContent>

        <TabsContent value="equations" className="space-y-4" data-id="iswd5qe0a" data-path="src/components/Chapter4Content.tsx">
          <Card data-id="h3rott3zj" data-path="src/components/Chapter4Content.tsx">
            <CardHeader data-id="4zv232am3" data-path="src/components/Chapter4Content.tsx">
              <CardTitle className="text-right" data-id="o9qydjeq0" data-path="src/components/Chapter4Content.tsx">المعادلات الفيزيائية الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6" data-id="jlymxf44l" data-path="src/components/Chapter4Content.tsx">
              <div className="bg-muted p-4 rounded-lg" data-id="30wqfnzkk" data-path="src/components/Chapter4Content.tsx">
                <h4 className="font-semibold mb-2 text-right" data-id="wpkuld2bp" data-path="src/components/Chapter4Content.tsx">الطاقة الحركية للإلكترون:</h4>
                <div className="font-mono text-center bg-white p-3 rounded border" data-id="penqn32xp" data-path="src/components/Chapter4Content.tsx">
                  KE = eV = hνmax
                </div>
              </div>
              
              <div className="bg-muted p-4 rounded-lg" data-id="gdjd6buno" data-path="src/components/Chapter4Content.tsx">
                <h4 className="font-semibold mb-2 text-right" data-id="rfv06rlra" data-path="src/components/Chapter4Content.tsx">كفاءة إنتاج الأشعة السينية:</h4>
                <div className="font-mono text-center bg-white p-3 rounded border" data-id="t7tqktdvb" data-path="src/components/Chapter4Content.tsx">
                  η ≈ k × Z × V (%)
                </div>
                <p className="text-sm text-muted-foreground mt-2 text-right" data-id="game6j14u" data-path="src/components/Chapter4Content.tsx">
                  حيث k ≈ 10⁻⁹ للتنغستن
                </p>
              </div>
              
              <div className="bg-muted p-4 rounded-lg" data-id="vgszj4wu7" data-path="src/components/Chapter4Content.tsx">
                <h4 className="font-semibold mb-2 text-right" data-id="bedpgo2rh" data-path="src/components/Chapter4Content.tsx">قانون موزلي للإشعاع المميز:</h4>
                <div className="font-mono text-center bg-white p-3 rounded border" data-id="97db4um4h" data-path="src/components/Chapter4Content.tsx">
                  ν = R(Z-σ)²(1/n₁² - 1/n₂²)
                </div>
              </div>
              
              <div className="bg-muted p-4 rounded-lg" data-id="g4nwg7f07" data-path="src/components/Chapter4Content.tsx">
                <h4 className="font-semibold mb-2 text-right" data-id="6jd3v0mys" data-path="src/components/Chapter4Content.tsx">وحدة الحرارة (HU):</h4>
                <div className="font-mono text-center bg-white p-3 rounded border" data-id="mknxw4w7n" data-path="src/components/Chapter4Content.tsx">
                  HU = kVp × mA × s × تصحيح المولد
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="spectra" className="space-y-4" data-id="svw5ozun2" data-path="src/components/Chapter4Content.tsx">
          <Card data-id="w6zlfm806" data-path="src/components/Chapter4Content.tsx">
            <CardHeader data-id="7vj6v176o" data-path="src/components/Chapter4Content.tsx">
              <CardTitle className="text-right" data-id="b6ktdi8wi" data-path="src/components/Chapter4Content.tsx">خصائص الأطياف</CardTitle>
            </CardHeader>
            <CardContent data-id="lfdbpyh21" data-path="src/components/Chapter4Content.tsx">
              <div className="grid md:grid-cols-2 gap-4" data-id="pxnz94jx6" data-path="src/components/Chapter4Content.tsx">
                <Card data-id="oeromrfd6" data-path="src/components/Chapter4Content.tsx">
                  <CardHeader data-id="0t017cmft" data-path="src/components/Chapter4Content.tsx">
                    <CardTitle className="text-base text-right" data-id="blnzwsmg8" data-path="src/components/Chapter4Content.tsx">إشعاع Bremsstrahlung</CardTitle>
                  </CardHeader>
                  <CardContent data-id="d9vr52unb" data-path="src/components/Chapter4Content.tsx">
                    <ul className="text-sm space-y-2 text-right" data-id="7r1s53ad0" data-path="src/components/Chapter4Content.tsx">
                      <li data-id="boj8corxz" data-path="src/components/Chapter4Content.tsx">• طيف مستمر من 0 إلى hνmax</li>
                      <li data-id="abci8su0t" data-path="src/components/Chapter4Content.tsx">• ذروة عند ~E/3</li>
                      <li data-id="zzc47joa9" data-path="src/components/Chapter4Content.tsx">• يزداد مع kVp و Z</li>
                      <li data-id="7xfojh36l" data-path="src/components/Chapter4Content.tsx">• يشكل معظم الإشعاع المنتج</li>
                    </ul>
                  </CardContent>
                </Card>
                
                <Card data-id="53o8234ov" data-path="src/components/Chapter4Content.tsx">
                  <CardHeader data-id="uzs1ps6p4" data-path="src/components/Chapter4Content.tsx">
                    <CardTitle className="text-base text-right" data-id="o7eqtvdoo" data-path="src/components/Chapter4Content.tsx">الإشعاع المميز</CardTitle>
                  </CardHeader>
                  <CardContent data-id="i2amxy5wl" data-path="src/components/Chapter4Content.tsx">
                    <ul className="text-sm space-y-2 text-right" data-id="xgsrn1woc" data-path="src/components/Chapter4Content.tsx">
                      <li data-id="75iv3rlgj" data-path="src/components/Chapter4Content.tsx">• ذروات طاقة منفصلة</li>
                      <li data-id="vdr6sms0i" data-path="src/components/Chapter4Content.tsx">• يعتمد على مادة الهدف</li>
                      <li data-id="fhk7wn32o" data-path="src/components/Chapter4Content.tsx">• Kα, Kβ, Lα خطوط رئيسية</li>
                      <li data-id="860kh9yxl" data-path="src/components/Chapter4Content.tsx">• مهم في التطبيقات المتخصصة</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calculations" className="space-y-4" data-id="fckgckjpn" data-path="src/components/Chapter4Content.tsx">
          <Card data-id="j9bpfel18" data-path="src/components/Chapter4Content.tsx">
            <CardHeader data-id="mxwdw9nkn" data-path="src/components/Chapter4Content.tsx">
              <CardTitle className="text-right" data-id="7yqly75qq" data-path="src/components/Chapter4Content.tsx">أمثلة حسابية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4" data-id="lkcht6swz" data-path="src/components/Chapter4Content.tsx">
              <div className="bg-blue-50 p-4 rounded-lg" data-id="v60ertw6a" data-path="src/components/Chapter4Content.tsx">
                <h4 className="font-semibold mb-2 text-right" data-id="fs5cpxirg" data-path="src/components/Chapter4Content.tsx">مثال 1: حساب الطاقة القصوى</h4>
                <p className="text-sm text-right mb-2" data-id="luenyg0m7" data-path="src/components/Chapter4Content.tsx">عند kVp = 120 kV، الطاقة القصوى للفوتونات:</p>
                <div className="font-mono bg-white p-2 rounded border text-center" data-id="b0u16xxmr" data-path="src/components/Chapter4Content.tsx">
                  Emax = 120 keV
                </div>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg" data-id="kwwbzf02v" data-path="src/components/Chapter4Content.tsx">
                <h4 className="font-semibold mb-2 text-right" data-id="4u7aiolwg" data-path="src/components/Chapter4Content.tsx">مثال 2: حساب كفاءة الإنتاج</h4>
                <p className="text-sm text-right mb-2" data-id="t4meos2py" data-path="src/components/Chapter4Content.tsx">للتنغستن (Z=74) عند 120 kVp:</p>
                <div className="font-mono bg-white p-2 rounded border text-center" data-id="4lswpwkat" data-path="src/components/Chapter4Content.tsx">
                  η = 10⁻⁹ × 74 × 120 ≈ 0.9%
                </div>
              </div>
              
              <div className="bg-orange-50 p-4 rounded-lg" data-id="cofliuggq" data-path="src/components/Chapter4Content.tsx">
                <h4 className="font-semibold mb-2 text-right" data-id="ebmgbqqoq" data-path="src/components/Chapter4Content.tsx">مثال 3: حساب وحدة الحرارة</h4>
                <p className="text-sm text-right mb-2" data-id="s0qeado7j" data-path="src/components/Chapter4Content.tsx">لتعرض 100 kVp, 200 mA, 0.5 s:</p>
                <div className="font-mono bg-white p-2 rounded border text-center" data-id="ps1v6d22d" data-path="src/components/Chapter4Content.tsx">
                  HU = 100 × 200 × 0.5 = 10,000 HU
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card className="mt-8" data-id="nct3eewvw" data-path="src/components/Chapter4Content.tsx">
        <CardHeader data-id="27ri0m1cy" data-path="src/components/Chapter4Content.tsx">
          <CardTitle className="text-right" data-id="c1p559xn8" data-path="src/components/Chapter4Content.tsx">أهداف التعلم</CardTitle>
        </CardHeader>
        <CardContent data-id="86rkqr1vu" data-path="src/components/Chapter4Content.tsx">
          <ul className="list-disc list-inside space-y-2 text-right" data-id="md2ssjq23" data-path="src/components/Chapter4Content.tsx">
            <li data-id="oahwonzj9" data-path="src/components/Chapter4Content.tsx">فهم آليات إنتاج إشعاع Bremsstrahlung والإشعاع المميز</li>
            <li data-id="y2x8doa5y" data-path="src/components/Chapter4Content.tsx">تعلم خصائص الأطياف وكيفية تشكيلها</li>
            <li data-id="2hzqemwd0" data-path="src/components/Chapter4Content.tsx">إتقان حسابات الكفاءة ووحدة الحرارة</li>
            <li data-id="dbxj2s3pd" data-path="src/components/Chapter4Content.tsx">فهم إدارة الحرارة في أنابيب الأشعة السينية</li>
            <li data-id="nk9wbuokm" data-path="src/components/Chapter4Content.tsx">ربط النظرية بالتطبيقات العملية</li>
          </ul>
        </CardContent>
      </Card>
    </div>);

};

export default Chapter4Content;