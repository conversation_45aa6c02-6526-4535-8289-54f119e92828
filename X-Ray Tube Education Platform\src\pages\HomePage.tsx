import { motion } from 'motion/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import {
  Zap,
  CircuitBoard,
  Brain,
  BookOpen,
  ArrowRight,
  Microscope,
  Shield,
  Activity } from
'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const HomePage = () => {
  const { t } = useLanguage();

  const features = [
  {
    icon: BookOpen,
    title: t('home.feature1.title'),
    description: t('home.feature1.desc'),
    link: '/concepts',
    color: 'bg-blue-500'
  },
  {
    icon: Zap,
    title: t('home.feature2.title'),
    description: t('home.feature2.desc'),
    link: '/xray-tube',
    color: 'bg-purple-500'
  },
  {
    icon: CircuitBoard,
    title: t('home.feature3.title'),
    description: t('home.feature3.desc'),
    link: '/circuits',
    color: 'bg-green-500'
  },
  {
    icon: Brain,
    title: t('home.feature4.title'),
    description: t('home.feature4.desc'),
    link: '/ai-assistant',
    color: 'bg-orange-500'
  }];


  const stats = [
  { label: 'Learning Concepts', value: '50+', icon: BookOpen },
  { label: 'Illustrations', value: '30+', icon: Activity },
  { label: 'Interactive Diagrams', value: '15+', icon: CircuitBoard },
  { label: 'Simulation Models', value: '10+', icon: Microscope }];


  return (
    <div className="min-h-screen" data-id="mlogck0n8" data-path="src/pages/HomePage.tsx">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800" data-id="uf8hjrzr7" data-path="src/pages/HomePage.tsx">
        <div className="absolute inset-0 bg-black/20" data-id="31urv2xl3" data-path="src/pages/HomePage.tsx"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24" data-id="fdo5ubt1w" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center text-white" data-id="jkuyi5l2b" data-path="src/pages/HomePage.tsx">

            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 100 }}
              className="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8" data-id="zkkl9ouk0" data-path="src/pages/HomePage.tsx">

              <Microscope className="w-10 h-10" data-id="w55r62f8w" data-path="src/pages/HomePage.tsx" />
            </motion.div>
            
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight" data-id="sac1w4j2q" data-path="src/pages/HomePage.tsx">
              {t('home.title')}
            </h1>
            
            <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto" data-id="j3w31k0qy" data-path="src/pages/HomePage.tsx">
              {t('home.subtitle')}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center" data-id="qess02gdx" data-path="src/pages/HomePage.tsx">
              <Link to="/concepts" data-id="yqrrna715" data-path="src/pages/HomePage.tsx">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg font-semibold" data-id="4vvcd0bkm" data-path="src/pages/HomePage.tsx">
                  {t('home.getStarted')}
                  <ArrowRight className="ml-2 h-5 w-5" data-id="zv7xg2tfw" data-path="src/pages/HomePage.tsx" />
                </Button>
              </Link>
              <Link to="/ai-assistant" data-id="af3k0e98a" data-path="src/pages/HomePage.tsx">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 px-8 py-4 text-lg" data-id="uo068pjyh" data-path="src/pages/HomePage.tsx">
                  {t('home.exploreFeatures')}
                  <Brain className="ml-2 h-5 w-5" data-id="p0ae7cfah" data-path="src/pages/HomePage.tsx" />
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white" data-id="o5dc4tin7" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="sctwjj934" data-path="src/pages/HomePage.tsx">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8" data-id="bx0xd0von" data-path="src/pages/HomePage.tsx">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="text-center" data-id="kxed2lqbx" data-path="src/pages/HomePage.tsx">

                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4" data-id="ikxv9gzlf" data-path="src/pages/HomePage.tsx">
                    <Icon className="w-6 h-6 text-blue-600" data-id="82o340svl" data-path="src/pages/HomePage.tsx" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2" data-id="wjze6wu9p" data-path="src/pages/HomePage.tsx">{stat.value}</div>
                  <div className="text-sm text-gray-600" data-id="s2jqd7chm" data-path="src/pages/HomePage.tsx">{stat.label}</div>
                </motion.div>);

            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50" data-id="3ybsagy8u" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="5zvoy340h" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16" data-id="d8hlz9gr0" data-path="src/pages/HomePage.tsx">

            <Badge variant="secondary" className="mb-4" data-id="7o3b0tszj" data-path="src/pages/HomePage.tsx">{t('home.featuresTitle')}</Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4" data-id="syp3gw2o1" data-path="src/pages/HomePage.tsx">
              Explore X-Ray Technology
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="rv8a3imxo" data-path="src/pages/HomePage.tsx">
              Learn through interactive content and advanced visualizations
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8" data-id="yfbdxya6j" data-path="src/pages/HomePage.tsx">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  whileHover={{ y: -5 }}
                  className="group" data-id="djewbx8h0" data-path="src/pages/HomePage.tsx">

                  <Card className="h-full hover:shadow-xl transition-all duration-300 border-0 shadow-lg" data-id="iwc9hkhgx" data-path="src/pages/HomePage.tsx">
                    <CardHeader className="text-center pb-4" data-id="lzw5ttu9p" data-path="src/pages/HomePage.tsx">
                      <div className={`inline-flex items-center justify-center w-16 h-16 ${feature.color} rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300`} data-id="rkev7mc5j" data-path="src/pages/HomePage.tsx">
                        <Icon className="w-8 h-8 text-white" data-id="ud9crefv2" data-path="src/pages/HomePage.tsx" />
                      </div>
                      <CardTitle className="text-2xl font-bold text-gray-900" data-id="hsdyrbdvb" data-path="src/pages/HomePage.tsx">
                        {feature.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="text-center" data-id="9wktt3pe1" data-path="src/pages/HomePage.tsx">
                      <CardDescription className="text-lg mb-6 text-gray-600" data-id="5u0b77var" data-path="src/pages/HomePage.tsx">
                        {feature.description}
                      </CardDescription>
                      <Link to={feature.link} data-id="itedovlwq" data-path="src/pages/HomePage.tsx">
                        <Button className="w-full group-hover:shadow-lg transition-all duration-300" data-id="wvh4rvv53" data-path="src/pages/HomePage.tsx">
                          Explore Now
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" data-id="7d906xtgg" data-path="src/pages/HomePage.tsx" />
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                </motion.div>);

            })}
          </div>
        </div>
      </section>

      {/* Safety Notice */}
      <section className="py-16 bg-amber-50 border-t border-amber-200" data-id="flimxcn6o" data-path="src/pages/HomePage.tsx">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8" data-id="d1ghfai9y" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
            className="text-center" data-id="7dk0rxehy" data-path="src/pages/HomePage.tsx">

            <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-100 rounded-full mb-6" data-id="j2houmtsy" data-path="src/pages/HomePage.tsx">
              <Shield className="w-8 h-8 text-amber-600" data-id="2dilbunoc" data-path="src/pages/HomePage.tsx" />
            </div>
            <h3 className="text-2xl font-bold text-amber-900 mb-4" data-id="8n9xc0269" data-path="src/pages/HomePage.tsx">
              Radiation Safety Notice
            </h3>
            <p className="text-lg text-amber-800 leading-relaxed" data-id="1tpronfi9" data-path="src/pages/HomePage.tsx">
              This website is for educational purposes only. All information provided about ionizing radiation 
              and medical imaging equipment is for learning and theoretical understanding. Always follow 
              radiation safety protocols and consult qualified professionals in actual medical environments.
            </p>
          </motion.div>
        </div>
      </section>
    </div>);

};

export default HomePage;