import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import ImageProcessingDemo from '@/components/ImageProcessingDemo';
import ArtifactSimulator from '@/components/ArtifactSimulator';
import { Image, Sliders, AlertTriangle } from 'lucide-react';

const ImageQuality = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50" data-id="42gctbr03" data-path="src/pages/ImageQuality.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" data-id="ya04x22ff" data-path="src/pages/ImageQuality.tsx">
        {/* <PERSON><PERSON> */}
        <div className="text-center mb-12" data-id="t18xaltdr" data-path="src/pages/ImageQuality.tsx">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4" data-id="6dblf45cb" data-path="src/pages/ImageQuality.tsx">
            Image Quality & Artifacts
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="rr3zux53z" data-path="src/pages/ImageQuality.tsx">
            Understand image quality factors and explore common artifacts in medical imaging
          </p>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="processing" className="space-y-8" data-id="67jnbe7br" data-path="src/pages/ImageQuality.tsx">
          <TabsList className="grid w-full grid-cols-2 lg:w-96 mx-auto" data-id="e5gsm1rj7" data-path="src/pages/ImageQuality.tsx">
            <TabsTrigger value="processing" className="flex items-center gap-2" data-id="g4x4e5395" data-path="src/pages/ImageQuality.tsx">
              <Sliders className="w-4 h-4" data-id="hg6sbnpdz" data-path="src/pages/ImageQuality.tsx" />
              Image Processing
            </TabsTrigger>
            <TabsTrigger value="artifacts" className="flex items-center gap-2" data-id="kobljnsgr" data-path="src/pages/ImageQuality.tsx">
              <AlertTriangle className="w-4 h-4" data-id="zj5ht43b1" data-path="src/pages/ImageQuality.tsx" />
              Artifacts
            </TabsTrigger>
          </TabsList>

          <TabsContent value="processing" data-id="5pj2hfjnq" data-path="src/pages/ImageQuality.tsx">
            <ImageProcessingDemo data-id="6acsnwzg8" data-path="src/pages/ImageQuality.tsx" />
          </TabsContent>

          <TabsContent value="artifacts" data-id="fpwn33plq" data-path="src/pages/ImageQuality.tsx">
            <ArtifactSimulator data-id="e7o8k2ciu" data-path="src/pages/ImageQuality.tsx" />
          </TabsContent>
        </Tabs>

        {/* Image Quality Factors */}
        <div className="mt-16 grid md:grid-cols-2 lg:grid-cols-4 gap-6" data-id="9vnnxmuum" data-path="src/pages/ImageQuality.tsx">
          <Card data-id="dlzch19he" data-path="src/pages/ImageQuality.tsx">
            <CardHeader data-id="8tbf90sjp" data-path="src/pages/ImageQuality.tsx">
              <CardTitle className="text-lg" data-id="lgfatx09t" data-path="src/pages/ImageQuality.tsx">Contrast</CardTitle>
            </CardHeader>
            <CardContent data-id="f9crys0sr" data-path="src/pages/ImageQuality.tsx">
              <div className="space-y-3" data-id="lcswnq7is" data-path="src/pages/ImageQuality.tsx">
                <div className="h-4 bg-gradient-to-r from-black to-white rounded" data-id="ejp4tr51a" data-path="src/pages/ImageQuality.tsx"></div>
                <p className="text-sm text-gray-600" data-id="i2jz0slr2" data-path="src/pages/ImageQuality.tsx">
                  Difference in signal intensity between tissues or structures
                </p>
                <div className="space-y-1" data-id="ilxo5346e" data-path="src/pages/ImageQuality.tsx">
                  <Badge variant="outline" className="text-xs" data-id="yhfhmf8oq" data-path="src/pages/ImageQuality.tsx">Subject Contrast</Badge>
                  <Badge variant="outline" className="text-xs" data-id="qx9tz3p6q" data-path="src/pages/ImageQuality.tsx">Image Contrast</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card data-id="hda5mqh9f" data-path="src/pages/ImageQuality.tsx">
            <CardHeader data-id="mpx0uofvu" data-path="src/pages/ImageQuality.tsx">
              <CardTitle className="text-lg" data-id="wh6mvl7l6" data-path="src/pages/ImageQuality.tsx">Resolution</CardTitle>
            </CardHeader>
            <CardContent data-id="t5te1wivo" data-path="src/pages/ImageQuality.tsx">
              <div className="space-y-3" data-id="qggw61z8s" data-path="src/pages/ImageQuality.tsx">
                <div className="grid grid-cols-4 gap-1" data-id="5t86pchmf" data-path="src/pages/ImageQuality.tsx">
                  {[...Array(16)].map((_, i) =>
                  <div key={i} className="h-2 bg-gray-800 rounded-sm" data-id="gg6n6t6po" data-path="src/pages/ImageQuality.tsx"></div>
                  )}
                </div>
                <p className="text-sm text-gray-600" data-id="kijtbicdh" data-path="src/pages/ImageQuality.tsx">
                  Ability to distinguish between closely spaced objects
                </p>
                <div className="space-y-1" data-id="oir2swb6s" data-path="src/pages/ImageQuality.tsx">
                  <Badge variant="outline" className="text-xs" data-id="usqz9oxrd" data-path="src/pages/ImageQuality.tsx">Spatial Resolution</Badge>
                  <Badge variant="outline" className="text-xs" data-id="uig9rv9nh" data-path="src/pages/ImageQuality.tsx">Temporal Resolution</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card data-id="yx2qasy5u" data-path="src/pages/ImageQuality.tsx">
            <CardHeader data-id="q1vj1ft91" data-path="src/pages/ImageQuality.tsx">
              <CardTitle className="text-lg" data-id="sh140xay9" data-path="src/pages/ImageQuality.tsx">Noise</CardTitle>
            </CardHeader>
            <CardContent data-id="62n6o0ehn" data-path="src/pages/ImageQuality.tsx">
              <div className="space-y-3" data-id="ot06slpzf" data-path="src/pages/ImageQuality.tsx">
                <div className="h-4 bg-gray-300 rounded relative overflow-hidden" data-id="xku834rrp" data-path="src/pages/ImageQuality.tsx">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-500 to-transparent opacity-30 animate-pulse" data-id="i7wnxktii" data-path="src/pages/ImageQuality.tsx"></div>
                </div>
                <p className="text-sm text-gray-600" data-id="lcjgzpwgm" data-path="src/pages/ImageQuality.tsx">
                  Random variations in pixel values that degrade image quality
                </p>
                <div className="space-y-1" data-id="sslvly487" data-path="src/pages/ImageQuality.tsx">
                  <Badge variant="outline" className="text-xs" data-id="bto5mkufj" data-path="src/pages/ImageQuality.tsx">Statistical Noise</Badge>
                  <Badge variant="outline" className="text-xs" data-id="zio053v5d" data-path="src/pages/ImageQuality.tsx">Electronic Noise</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card data-id="8nqg6rl6e" data-path="src/pages/ImageQuality.tsx">
            <CardHeader data-id="vcrbd3ddv" data-path="src/pages/ImageQuality.tsx">
              <CardTitle className="text-lg" data-id="qa9xo2aft" data-path="src/pages/ImageQuality.tsx">Artifacts</CardTitle>
            </CardHeader>
            <CardContent data-id="3by1m9fvu" data-path="src/pages/ImageQuality.tsx">
              <div className="space-y-3" data-id="3zcg9razr" data-path="src/pages/ImageQuality.tsx">
                <div className="h-4 bg-red-100 rounded relative" data-id="pwmdzamdx" data-path="src/pages/ImageQuality.tsx">
                  <div className="absolute inset-0 bg-red-500 w-1/3 rounded-l opacity-50" data-id="npl3x6bca" data-path="src/pages/ImageQuality.tsx"></div>
                </div>
                <p className="text-sm text-gray-600" data-id="zqks0gi15" data-path="src/pages/ImageQuality.tsx">
                  Unwanted features that can compromise diagnostic quality
                </p>
                <div className="space-y-1" data-id="vrg55jysp" data-path="src/pages/ImageQuality.tsx">
                  <Badge variant="outline" className="text-xs" data-id="5nnyqwrnc" data-path="src/pages/ImageQuality.tsx">Motion Artifacts</Badge>
                  <Badge variant="outline" className="text-xs" data-id="edneln3oz" data-path="src/pages/ImageQuality.tsx">Beam Hardening</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Educational Content */}
        <div className="mt-16 grid md:grid-cols-3 gap-8" data-id="oq3kuyfsc" data-path="src/pages/ImageQuality.tsx">
          <Card data-id="sbzaj9amp" data-path="src/pages/ImageQuality.tsx">
            <CardHeader data-id="8bnionl8z" data-path="src/pages/ImageQuality.tsx">
              <CardTitle className="flex items-center gap-2" data-id="afc9e1j3i" data-path="src/pages/ImageQuality.tsx">
                <Image className="w-5 h-5" data-id="6rlpqdc0f" data-path="src/pages/ImageQuality.tsx" />
                Image Quality Metrics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4" data-id="x3roct57z" data-path="src/pages/ImageQuality.tsx">
              <div data-id="080wp9f48" data-path="src/pages/ImageQuality.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="0yf8i2i1t" data-path="src/pages/ImageQuality.tsx">Signal-to-Noise Ratio (SNR)</h4>
                <p className="text-gray-600 text-sm" data-id="hj2wkcqj7" data-path="src/pages/ImageQuality.tsx">
                  Ratio of signal strength to noise level. Higher SNR indicates better image quality.
                </p>
              </div>
              <div data-id="f6z1jl5g9" data-path="src/pages/ImageQuality.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="4z401vrsa" data-path="src/pages/ImageQuality.tsx">Contrast-to-Noise Ratio (CNR)</h4>
                <p className="text-gray-600 text-sm" data-id="fbd1rpty8" data-path="src/pages/ImageQuality.tsx">
                  Measure of contrast visibility in the presence of noise. Critical for lesion detection.
                </p>
              </div>
              <div data-id="0pesloefn" data-path="src/pages/ImageQuality.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="1kiioowx2" data-path="src/pages/ImageQuality.tsx">Modulation Transfer Function (MTF)</h4>
                <p className="text-gray-600 text-sm" data-id="7hj0atxqb" data-path="src/pages/ImageQuality.tsx">
                  Quantitative measure of spatial resolution performance as a function of spatial frequency.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card data-id="126od0qcp" data-path="src/pages/ImageQuality.tsx">
            <CardHeader data-id="mqapn6816" data-path="src/pages/ImageQuality.tsx">
              <CardTitle data-id="bd8jzjh8x" data-path="src/pages/ImageQuality.tsx">Optimization Strategies</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4" data-id="mjf9jmcb1" data-path="src/pages/ImageQuality.tsx">
              <div data-id="hs7941kyd" data-path="src/pages/ImageQuality.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="g707j4nic" data-path="src/pages/ImageQuality.tsx">Technique Optimization</h4>
                <p className="text-gray-600 text-sm" data-id="4iwoyo0de" data-path="src/pages/ImageQuality.tsx">
                  Adjust kVp, mAs, and filtration to optimize contrast and minimize dose while maintaining image quality.
                </p>
              </div>
              <div data-id="52963lf6x" data-path="src/pages/ImageQuality.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="lqpxuk3yc" data-path="src/pages/ImageQuality.tsx">Post-Processing</h4>
                <p className="text-gray-600 text-sm" data-id="zu30sw4in" data-path="src/pages/ImageQuality.tsx">
                  Apply appropriate filters, windowing, and enhancement techniques to improve diagnostic utility.
                </p>
              </div>
              <div data-id="s4cn3yzc5" data-path="src/pages/ImageQuality.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="h8j0ao1o1" data-path="src/pages/ImageQuality.tsx">Protocol Standardization</h4>
                <p className="text-gray-600 text-sm" data-id="87gjdxlg7" data-path="src/pages/ImageQuality.tsx">
                  Establish consistent imaging protocols to ensure reproducible image quality across patients and time.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card data-id="bozgzccky" data-path="src/pages/ImageQuality.tsx">
            <CardHeader data-id="498p39jcs" data-path="src/pages/ImageQuality.tsx">
              <CardTitle data-id="lk7rs69jw" data-path="src/pages/ImageQuality.tsx">Quality Assurance</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4" data-id="fzm7zlhs7" data-path="src/pages/ImageQuality.tsx">
              <div data-id="mjrg8e5ru" data-path="src/pages/ImageQuality.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="z6pofave0" data-path="src/pages/ImageQuality.tsx">Phantom Testing</h4>
                <p className="text-gray-600 text-sm" data-id="gqk37mba1" data-path="src/pages/ImageQuality.tsx">
                  Regular testing with standardized phantoms to monitor system performance and image quality metrics.
                </p>
              </div>
              <div data-id="hktde4te8" data-path="src/pages/ImageQuality.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="9nzw4zo6a" data-path="src/pages/ImageQuality.tsx">Calibration</h4>
                <p className="text-gray-600 text-sm" data-id="p87pvd5ww" data-path="src/pages/ImageQuality.tsx">
                  Ensure proper calibration of imaging systems to maintain consistent image quality and dose delivery.
                </p>
              </div>
              <div data-id="chbu4szgv" data-path="src/pages/ImageQuality.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="xhljntscr" data-path="src/pages/ImageQuality.tsx">Monitor Performance</h4>
                <p className="text-gray-600 text-sm" data-id="2qjr6ie6o" data-path="src/pages/ImageQuality.tsx">
                  Regular evaluation of display monitors to ensure optimal image presentation for diagnosis.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Trade-offs */}
        <div className="mt-16" data-id="oxoktsqjg" data-path="src/pages/ImageQuality.tsx">
          <Card data-id="opnfmzr5n" data-path="src/pages/ImageQuality.tsx">
            <CardHeader data-id="15co9h679" data-path="src/pages/ImageQuality.tsx">
              <CardTitle data-id="5cm7useis" data-path="src/pages/ImageQuality.tsx">Image Quality Trade-offs</CardTitle>
              <CardDescription data-id="uaee29nd2" data-path="src/pages/ImageQuality.tsx">
                Understanding the relationships between different image quality factors
              </CardDescription>
            </CardHeader>
            <CardContent data-id="oyvh5sgq" data-path="src/pages/ImageQuality.tsx">
              <div className="grid md:grid-cols-2 gap-8" data-id="u70hnmrn9" data-path="src/pages/ImageQuality.tsx">
                <div data-id="f7rqi0bby" data-path="src/pages/ImageQuality.tsx">
                  <h4 className="font-semibold text-gray-900 mb-4" data-id="fyzctqygn" data-path="src/pages/ImageQuality.tsx">Fundamental Trade-offs</h4>
                  <div className="space-y-3" data-id="z3r68ss6s" data-path="src/pages/ImageQuality.tsx">
                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg" data-id="u77ros5lg" data-path="src/pages/ImageQuality.tsx">
                      <span className="text-sm" data-id="rbrte6bgk" data-path="src/pages/ImageQuality.tsx">Contrast ↔ Noise</span>
                      <Badge variant="outline" data-id="itqhs239d" data-path="src/pages/ImageQuality.tsx">Inversely related</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg" data-id="awuu99wjf" data-path="src/pages/ImageQuality.tsx">
                      <span className="text-sm" data-id="17mxxpr24" data-path="src/pages/ImageQuality.tsx">Resolution ↔ Noise</span>
                      <Badge variant="outline" data-id="thijd20w8" data-path="src/pages/ImageQuality.tsx">Inversely related</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg" data-id="907309hpr" data-path="src/pages/ImageQuality.tsx">
                      <span className="text-sm" data-id="d67n4n5x0" data-path="src/pages/ImageQuality.tsx">Speed ↔ Quality</span>
                      <Badge variant="outline" data-id="5ow7qqk2s" data-path="src/pages/ImageQuality.tsx">Inversely related</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg" data-id="vs4yjcaje" data-path="src/pages/ImageQuality.tsx">
                      <span className="text-sm" data-id="o2du3enft" data-path="src/pages/ImageQuality.tsx">Dose ↔ Noise</span>
                      <Badge variant="outline" data-id="os48tu0ih" data-path="src/pages/ImageQuality.tsx">Inversely related</Badge>
                    </div>
                  </div>
                </div>
                
                <div data-id="2zccpu0pq" data-path="src/pages/ImageQuality.tsx">
                  <h4 className="font-semibold text-gray-900 mb-4" data-id="b2t6c4g7y" data-path="src/pages/ImageQuality.tsx">Optimization Strategies</h4>
                  <ul className="space-y-2 text-sm text-gray-600" data-id="fz1wlr797" data-path="src/pages/ImageQuality.tsx">
                    <li data-id="4z1cixqhr" data-path="src/pages/ImageQuality.tsx">• <strong data-id="0sxkcy2sp" data-path="src/pages/ImageQuality.tsx">Higher kVp:</strong> Reduces contrast but improves penetration and reduces dose</li>
                    <li data-id="l1rfs66gx" data-path="src/pages/ImageQuality.tsx">• <strong data-id="tpfqh8tqy" data-path="src/pages/ImageQuality.tsx">Higher mAs:</strong> Reduces noise but increases dose and heat loading</li>
                    <li data-id="ecdv8nj41" data-path="src/pages/ImageQuality.tsx">• <strong data-id="6ou9d45x8" data-path="src/pages/ImageQuality.tsx">Smaller pixel size:</strong> Improves resolution but increases noise</li>
                    <li data-id="902e3rjqk" data-path="src/pages/ImageQuality.tsx">• <strong data-id="f8p18vs2d" data-path="src/pages/ImageQuality.tsx">Thicker sections:</strong> Reduces noise but decreases z-axis resolution</li>
                    <li data-id="gfgd7r3aj" data-path="src/pages/ImageQuality.tsx">• <strong data-id="nof0c8vl5" data-path="src/pages/ImageQuality.tsx">Longer acquisition:</strong> Reduces noise but increases motion artifacts</li>
                    <li data-id="fo9xyiuf5" data-path="src/pages/ImageQuality.tsx">• <strong data-id="9h3jp36ct" data-path="src/pages/ImageQuality.tsx">Post-processing:</strong> Can improve apparent quality but may mask pathology</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>);

};

export default ImageQuality;