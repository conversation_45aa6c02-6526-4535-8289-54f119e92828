import { useState } from 'react';
import { motion } from 'motion/react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

const XRayTubeDiagram = () => {
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);

  const components = {
    cathode: {
      name: 'الكاثود',
      description: 'مصدر الإلكترونات - فتيل التنجستن المُسخن',
      details: 'يحتوي على فتيل التنجستن الذي يُسخن كهربائياً لإنتاج الإلكترونات عن طريق الانبعاث الحراري',
      color: '#ef4444'
    },
    anode: {
      name: 'الأنود',
      description: 'الهدف - مصنوع من التنجستن',
      details: 'قرص دوار من التنجستن يستقبل الإلكترونات المتسارعة ويحولها إلى أشعة سينية وحرارة',
      color: '#3b82f6'
    },
    envelope: {
      name: 'الغلاف الزجاجي',
      description: 'يحتوي على فراغ عالي',
      details: 'غلاف زجاجي محكم يحتوي على فراغ عالي يسمح للإلكترونات بالحركة الحرة دون تصادمات',
      color: '#10b981'
    },
    housing: {
      name: 'الغلاف الواقي',
      description: 'حماية إشعاعية وتبريد',
      details: 'غلاف رصاصي يوفر الحماية من الإشعاع المتسرب ونظام تبريد للأنبوب',
      color: '#8b5cf6'
    }
  };

  return (
    <div className="space-y-6" data-id="2zdk1y327" data-path="src/components/XRayTubeDiagram.tsx">
      {/* SVG Diagram */}
      <div className="relative bg-gray-50 rounded-lg p-6 min-h-[400px] flex items-center justify-center" data-id="9z2eanr3t" data-path="src/components/XRayTubeDiagram.tsx">
        <svg
          width="500"
          height="300"
          viewBox="0 0 500 300"
          className="max-w-full h-auto" data-id="x2lqvu1ep" data-path="src/components/XRayTubeDiagram.tsx">

          {/* Housing (Outer Shell) */}
          <motion.rect
            x="20"
            y="60"
            width="460"
            height="180"
            rx="10"
            fill={selectedComponent === 'housing' ? components.housing.color : '#e5e7eb'}
            stroke={selectedComponent === 'housing' ? components.housing.color : '#9ca3af'}
            strokeWidth="2"
            className="cursor-pointer transition-all duration-300"
            onClick={() => setSelectedComponent(selectedComponent === 'housing' ? null : 'housing')}
            whileHover={{ scale: 1.02 }} data-id="z5vwnf489" data-path="src/components/XRayTubeDiagram.tsx" />

          
          {/* Glass Envelope */}
          <motion.ellipse
            cx="250"
            cy="150"
            rx="200"
            ry="80"
            fill={selectedComponent === 'envelope' ? components.envelope.color : '#f3f4f6'}
            stroke={selectedComponent === 'envelope' ? components.envelope.color : '#6b7280'}
            strokeWidth="2"
            className="cursor-pointer transition-all duration-300"
            onClick={() => setSelectedComponent(selectedComponent === 'envelope' ? null : 'envelope')}
            whileHover={{ scale: 1.05 }} data-id="bgonheq2s" data-path="src/components/XRayTubeDiagram.tsx" />

          
          {/* Cathode */}
          <motion.g
            onClick={() => setSelectedComponent(selectedComponent === 'cathode' ? null : 'cathode')}
            className="cursor-pointer"
            whileHover={{ scale: 1.1 }} data-id="twgr0h298" data-path="src/components/XRayTubeDiagram.tsx">

            <rect
              x="80"
              y="130"
              width="40"
              height="40"
              rx="5"
              fill={selectedComponent === 'cathode' ? components.cathode.color : '#fef3c7'}
              stroke={selectedComponent === 'cathode' ? components.cathode.color : '#f59e0b'}
              strokeWidth="2" data-id="2qtm2a4ec" data-path="src/components/XRayTubeDiagram.tsx" />

            {/* Filament */}
            <circle
              cx="100"
              cy="150"
              r="8"
              fill={selectedComponent === 'cathode' ? '#dc2626' : '#f59e0b'} data-id="nmoqcyu51" data-path="src/components/XRayTubeDiagram.tsx" />

            {/* Electron beam */}
            {selectedComponent === 'cathode' &&
            <motion.g
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }} data-id="e3sp8igtg" data-path="src/components/XRayTubeDiagram.tsx">

                {[...Array(5)].map((_, i) =>
              <motion.circle
                key={i}
                cx={150 + i * 40}
                cy={150}
                r="3"
                fill="#fbbf24"
                initial={{ x: -50 }}
                animate={{ x: 0 }}
                transition={{ delay: i * 0.1, repeat: Infinity, duration: 2 }} data-id="mwzr02x81" data-path="src/components/XRayTubeDiagram.tsx" />

              )}
              </motion.g>
            }
          </motion.g>
          
          {/* Anode */}
          <motion.g
            onClick={() => setSelectedComponent(selectedComponent === 'anode' ? null : 'anode')}
            className="cursor-pointer"
            whileHover={{ scale: 1.1 }} data-id="5itbzmsbi" data-path="src/components/XRayTubeDiagram.tsx">

            {/* Anode disk */}
            <circle
              cx="380"
              cy="150"
              r="35"
              fill={selectedComponent === 'anode' ? components.anode.color : '#dbeafe'}
              stroke={selectedComponent === 'anode' ? components.anode.color : '#3b82f6'}
              strokeWidth="2" data-id="re6pe4frs" data-path="src/components/XRayTubeDiagram.tsx" />

            {/* Target angle */}
            <path
              d="M345 130 L380 150 L345 170 Z"
              fill={selectedComponent === 'anode' ? '#1d4ed8' : '#60a5fa'} data-id="n93w9x2qz" data-path="src/components/XRayTubeDiagram.tsx" />

            {/* X-ray beam */}
            {selectedComponent === 'anode' &&
            <motion.g
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }} data-id="ztlkrhpc8" data-path="src/components/XRayTubeDiagram.tsx">

                {[...Array(3)].map((_, i) =>
              <motion.path
                key={i}
                d={`M380 150 L${450 + i * 10} ${120 + i * 20}`}
                stroke="#10b981"
                strokeWidth="2"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ delay: i * 0.2, duration: 1, repeat: Infinity }} data-id="53nljy8sm" data-path="src/components/XRayTubeDiagram.tsx" />

              )}
              </motion.g>
            }
          </motion.g>
          
          {/* Labels */}
          <text x="100" y="200" textAnchor="middle" className="text-sm font-medium fill-gray-700" data-id="pc076vk6v" data-path="src/components/XRayTubeDiagram.tsx">
            الكاثود
          </text>
          <text x="380" y="200" textAnchor="middle" className="text-sm font-medium fill-gray-700" data-id="cho492rcc" data-path="src/components/XRayTubeDiagram.tsx">
            الأنود
          </text>
          <text x="250" y="40" textAnchor="middle" className="text-lg font-bold fill-gray-800" data-id="bvonpbne6" data-path="src/components/XRayTubeDiagram.tsx">
            أنبوب الأشعة السينية
          </text>
          
          {/* Voltage indicators */}
          <text x="250" y="270" textAnchor="middle" className="text-sm fill-gray-600" data-id="j3f6qdw6y" data-path="src/components/XRayTubeDiagram.tsx">
            فولتية عالية (40-150 kV)
          </text>
          
          {/* Direction arrow */}
          <defs data-id="py7d8jh4a" data-path="src/components/XRayTubeDiagram.tsx">
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto" data-id="vwe007h58" data-path="src/components/XRayTubeDiagram.tsx">

              <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" data-id="nn0kccmvo" data-path="src/components/XRayTubeDiagram.tsx" />
            </marker>
          </defs>
          <line
            x1="130"
            y1="150"
            x2="340"
            y2="150"
            stroke="#6b7280"
            strokeWidth="2"
            markerEnd="url(#arrowhead)" data-id="ne1a0fmsz" data-path="src/components/XRayTubeDiagram.tsx" />

        </svg>
      </div>

      {/* Component Information */}
      {selectedComponent &&
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }} data-id="cvr7r3109" data-path="src/components/XRayTubeDiagram.tsx">

          <Card data-id="86us5jvsl" data-path="src/components/XRayTubeDiagram.tsx">
            <CardContent className="p-6" data-id="e8wkxjj1q" data-path="src/components/XRayTubeDiagram.tsx">
              <div className="flex items-start gap-4" data-id="7ugmd3sva" data-path="src/components/XRayTubeDiagram.tsx">
                <div
                className="w-4 h-4 rounded-full flex-shrink-0 mt-1"
                style={{ backgroundColor: components[selectedComponent as keyof typeof components].color }} data-id="vieoc2oa3" data-path="src/components/XRayTubeDiagram.tsx">
              </div>
                <div className="flex-1" data-id="93ksqxdme" data-path="src/components/XRayTubeDiagram.tsx">
                  <div className="flex items-center gap-2 mb-2" data-id="ow8q2rdk5" data-path="src/components/XRayTubeDiagram.tsx">
                    <h3 className="text-lg font-semibold text-gray-900" data-id="vax5w0vwb" data-path="src/components/XRayTubeDiagram.tsx">
                      {components[selectedComponent as keyof typeof components].name}
                    </h3>
                    <Badge variant="secondary" data-id="nxlddbx5n" data-path="src/components/XRayTubeDiagram.tsx">محدد</Badge>
                  </div>
                  <p className="text-gray-600 mb-3" data-id="kiaik84bj" data-path="src/components/XRayTubeDiagram.tsx">
                    {components[selectedComponent as keyof typeof components].description}
                  </p>
                  <p className="text-sm text-gray-700 leading-relaxed" data-id="6xb1fdiws" data-path="src/components/XRayTubeDiagram.tsx">
                    {components[selectedComponent as keyof typeof components].details}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      }

      {!selectedComponent &&
      <div className="text-center text-gray-500 text-sm" data-id="yq0qggye9" data-path="src/components/XRayTubeDiagram.tsx">
          انقر على أي مكون في المخطط لعرض تفاصيله
        </div>
      }
    </div>);

};

export default XRayTubeDiagram;