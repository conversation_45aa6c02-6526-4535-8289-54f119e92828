<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية | Advanced X-Ray Imaging Simulation</title>
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Google Fonts - Tajawal for Arabic, Inter for English -->
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <!-- Custom CSS -->
  <style>
    /* Base Styles */
    :root {
      --primary-color: #4f46e5;
      --primary-light: #e0e7ff;
      --primary-dark: #312e81;
      --secondary-color: #0ea5e9;
      --secondary-light: #e0f2fe;
      --secondary-dark: #0369a1;
    }
    
    body {
      font-family: 'Tajawal', sans-serif;
    }
    
    /* RTL/LTR Support */
    [dir="rtl"] {
      font-family: 'Tajawal', sans-serif;
    }
    
    [dir="ltr"] {
      font-family: 'Inter', sans-serif;
    }
    
    /* Fallback content */
    .fallback-content {
      display: none;
      text-align: center;
      padding: 2rem;
    }
  </style>
  <link rel="stylesheet" href="./src/styles/main.css">
</head>

<body>
  <!-- React App Root -->
  <div id="root"></div>
  
  <!-- Fallback content in case React doesn't load -->
  <div class="fallback-content" id="fallback">
    <div class="bg-white p-8 rounded-lg shadow-md max-w-md mx-auto">
      <div class="text-center mb-6">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-info-circle text-blue-600 text-2xl"></i>
        </div>
        <h2 class="text-xl font-bold text-gray-800 mb-2">جاري تحميل التطبيق</h2>
        <p class="text-gray-600">إذا استمرت هذه الشاشة، يمكنك الوصول إلى النسخة الثابتة من الموقع.</p>
      </div>
      <a href="landing.html" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-md">
        الانتقال إلى الصفحة الرئيسية
      </a>
    </div>
  </div>
  
  <script>
    // Show fallback content if React doesn't load within 5 seconds
    setTimeout(function() {
      if (document.getElementById('root').children.length === 0) {
        document.getElementById('fallback').style.display = 'block';
      }
    }, 5000);
  </script>
  
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>