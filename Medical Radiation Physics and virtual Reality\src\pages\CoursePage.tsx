import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { BookOpen, Monitor, Cpu, Zap, Users, Microscope, Search, TrendingUp } from 'lucide-react';
import { motion } from 'motion/react';

const CoursePage = () => {
  const [selectedChapter, setSelectedChapter] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  const part4Chapters = [
  {
    id: 'ch11',
    title: 'الفصل الحادي عشر: أجهزة الكشف بالأشعة السينية',
    description: 'دراسة شاملة لأنواع أجهزة الكشف المختلفة ومبادئ عملها',
    icon: <Monitor className="w-6 h-6" data-id="5fwmekdus" data-path="src/pages/CoursePage.tsx" />,
    topics: ['أنظمة الشاشة والفيلم', 'التصوير الشعاعي المحوسب', 'التصوير الرقمي', 'مقاييس الأداء'],
    vrFeatures: ['نماذج ثلاثية الأبعاد للكاشفات', 'محاكاة تفاعلية لعمليات الكشف']
  },
  {
    id: 'ch12',
    title: 'الفصل الثاني عشر: محاكاة استجابة الكاشف',
    description: 'نمذجة متقدمة لتكوين الصور وتحليل الأنظمة الخطية',
    icon: <Cpu className="w-6 h-6" data-id="kbkd7yj7r" data-path="src/pages/CoursePage.tsx" />,
    topics: ['نمذجة ترسب الطاقة', 'محاكاة انتشار الضوء', 'تحليل الأنظمة الخطية', 'معالجة الصور'],
    vrFeatures: ['تجارب افتراضية لمحاكاة الكاشف', 'تصور ثلاثي الأبعاد لانتشار الضوء']
  }];


  const part5Chapters = [
  {
    id: 'ch13',
    title: 'الفصل الثالث عشر: تطبيقات المحاكاة المتقدمة',
    description: 'تصميم وتحسين الأنظمة الإشعاعية باستخدام المحاكاة',
    icon: <Zap className="w-6 h-6" data-id="xik9qikwl" data-path="src/pages/CoursePage.tsx" />,
    topics: ['تحسين معلمات المصدر', 'تقييم مواد جديدة', 'تصميم الشبكات', 'دراسات حالة'],
    vrFeatures: ['بيئة افتراضية لتصميم الأنظمة', 'تجارب تفاعلية للتحسين']
  },
  {
    id: 'ch14',
    title: 'الفصل الرابع عشر: تقدير جرعة المريض',
    description: 'حساب وتحسين الجرعات الإشعاعية للمرضى',
    icon: <Users className="w-6 h-6" data-id="h50cb0hcp" data-path="src/pages/CoursePage.tsx" />,
    topics: ['حساب جرعات الأعضاء', 'تأثير معلمات التصوير', 'قياس الجرعات للأطفال', 'التحسين السريري'],
    vrFeatures: ['نماذج ثلاثية الأبعاد للجسم البشري', 'محاكاة توزيع الجرعة بصرياً']
  },
  {
    id: 'ch15',
    title: 'الفصل الخامس عشر: تقييم جودة الصورة',
    description: 'محاكاة وتحسين معايير جودة الصور الطبية',
    icon: <Microscope className="w-6 h-6" data-id="eq5mcd7kv" data-path="src/pages/CoursePage.tsx" />,
    topics: ['مقاييس جودة الصورة', 'تأثير التشتت', 'معالجة الصور', 'تقييم قائم على المهام'],
    vrFeatures: ['مقارنة تفاعلية لجودة الصور', 'تصور ثلاثي الأبعاد للتشتت']
  },
  {
    id: 'ch16',
    title: 'الفصل السادس عشر: التحقق والتحقق من الصحة',
    description: 'ضمان دقة وموثوقية نتائج المحاكاة',
    icon: <Search className="w-6 h-6" data-id="jb7tqk64k" data-path="src/pages/CoursePage.tsx" />,
    topics: ['تعريفات VVUQ', 'التحقق من الكود', 'تجارب التحقق', 'تحليل الحساسية'],
    vrFeatures: ['بيئة تجريبية افتراضية', 'مقارنة تفاعلية للنتائج']
  },
  {
    id: 'ch17',
    title: 'الفصل السابع عشر: الاتجاهات المستقبلية',
    description: 'التقنيات الناشئة والذكاء الاصطناعي في المحاكاة',
    icon: <TrendingUp className="w-6 h-6" data-id="dhdx92prv" data-path="src/pages/CoursePage.tsx" />,
    topics: ['الذكاء الاصطناعي', 'الحوسبة عالية الأداء', 'المنصات السحابية', 'التوائم الرقمية'],
    vrFeatures: ['استكشاف مستقبل التكنولوجيا', 'نماذج تفاعلية للتوائم الرقمية']
  }];


  const ChapterCard = ({ chapter, partTitle }: {chapter: any;partTitle: string;}) =>
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="h-full" data-id="9pcbrbssd" data-path="src/pages/CoursePage.tsx">

      <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group" onClick={() => setSelectedChapter(chapter.id)} data-id="9ol8f0rwb" data-path="src/pages/CoursePage.tsx">
        <CardHeader data-id="rj9kpodu6" data-path="src/pages/CoursePage.tsx">
          <div className="flex items-start justify-between" data-id="5w1w86ofe" data-path="src/pages/CoursePage.tsx">
            <div className="flex items-center space-x-3 rtl:space-x-reverse" data-id="t8orda86g" data-path="src/pages/CoursePage.tsx">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white group-hover:from-purple-600 group-hover:to-blue-500 transition-all" data-id="oaljvtvxc" data-path="src/pages/CoursePage.tsx">
                {chapter.icon}
              </div>
              <div data-id="papak9n8a" data-path="src/pages/CoursePage.tsx">
                <CardTitle className="text-right text-lg leading-tight" data-id="yrfo8my3v" data-path="src/pages/CoursePage.tsx">{chapter.title}</CardTitle>
                <Badge variant="outline" className="mt-1" data-id="6eei2gyyn" data-path="src/pages/CoursePage.tsx">{partTitle}</Badge>
              </div>
            </div>
          </div>
          <CardDescription className="text-right mt-2" data-id="uotaxs6uh" data-path="src/pages/CoursePage.tsx">{chapter.description}</CardDescription>
        </CardHeader>
        <CardContent data-id="gjt4t9k83" data-path="src/pages/CoursePage.tsx">
          <div className="space-y-4" data-id="04056tuqe" data-path="src/pages/CoursePage.tsx">
            <div data-id="oyc5vncjj" data-path="src/pages/CoursePage.tsx">
              <h4 className="font-semibold text-right text-sm mb-2" data-id="wi1typr5b" data-path="src/pages/CoursePage.tsx">المواضيع الرئيسية:</h4>
              <div className="flex flex-wrap gap-1 justify-end" data-id="nzpdp6wrq" data-path="src/pages/CoursePage.tsx">
                {chapter.topics.map((topic: string, index: number) =>
              <Badge key={index} variant="secondary" className="text-xs" data-id="abp3bnt7b" data-path="src/pages/CoursePage.tsx">{topic}</Badge>
              )}
              </div>
            </div>
            <div data-id="krlivf7ve" data-path="src/pages/CoursePage.tsx">
              <h4 className="font-semibold text-right text-sm mb-2" data-id="63u44old9" data-path="src/pages/CoursePage.tsx">مميزات الواقع الافتراضي:</h4>
              <div className="space-y-1" data-id="s0dkvg4so" data-path="src/pages/CoursePage.tsx">
                {chapter.vrFeatures.map((feature: string, index: number) =>
              <div key={index} className="flex items-center justify-end space-x-2 rtl:space-x-reverse text-xs text-muted-foreground" data-id="c36igly5z" data-path="src/pages/CoursePage.tsx">
                    <span data-id="atbe8yao7" data-path="src/pages/CoursePage.tsx">{feature}</span>
                    <div className="w-2 h-2 bg-green-500 rounded-full" data-id="lrf6nhzkx" data-path="src/pages/CoursePage.tsx"></div>
                  </div>
              )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>;


  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6" dir="rtl" data-id="u1az6dhsv" data-path="src/pages/CoursePage.tsx">
      <div className="max-w-7xl mx-auto" data-id="fwx9phie8" data-path="src/pages/CoursePage.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8" data-id="3jbdndyxg" data-path="src/pages/CoursePage.tsx">

          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4" data-id="eixzewjne" data-path="src/pages/CoursePage.tsx">
            نمذجة الكشف بالأشعة السينية والتطبيقات المتقدمة
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto" data-id="qagl0sk79" data-path="src/pages/CoursePage.tsx">
            دورة شاملة في محاكاة التصوير بالأشعة السينية مع تقنيات الواقع الافتراضي التفاعلية
          </p>
          <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mt-4" data-id="u9gghycjf" data-path="src/pages/CoursePage.tsx">
            <div className="flex items-center space-x-2 rtl:space-x-reverse" data-id="vec0kw945" data-path="src/pages/CoursePage.tsx">
              <BookOpen className="w-5 h-5 text-blue-600" data-id="11xeg9oi8" data-path="src/pages/CoursePage.tsx" />
              <span className="text-sm font-medium" data-id="rlmxulm3g" data-path="src/pages/CoursePage.tsx">7 فصول متخصصة</span>
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse" data-id="8q2hojga9" data-path="src/pages/CoursePage.tsx">
              <Monitor className="w-5 h-5 text-purple-600" data-id="cz9bj9r1u" data-path="src/pages/CoursePage.tsx" />
              <span className="text-sm font-medium" data-id="wqh9bpaf8" data-path="src/pages/CoursePage.tsx">محتوى تفاعلي بالواقع الافتراضي</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <Card className="mb-8" data-id="q7niryfla" data-path="src/pages/CoursePage.tsx">
          <CardHeader data-id="6hofvema7" data-path="src/pages/CoursePage.tsx">
            <CardTitle className="text-right" data-id="sf1ga3ygl" data-path="src/pages/CoursePage.tsx">تقدم الدورة</CardTitle>
          </CardHeader>
          <CardContent data-id="mgtm6bzft" data-path="src/pages/CoursePage.tsx">
            <div className="space-y-2" data-id="k1kzj2gz0" data-path="src/pages/CoursePage.tsx">
              <div className="flex justify-between text-sm" data-id="alt1rnwtg" data-path="src/pages/CoursePage.tsx">
                <span data-id="vqu0c9kzd" data-path="src/pages/CoursePage.tsx">{progress}% مكتمل</span>
                <span data-id="z78rmmvw4" data-path="src/pages/CoursePage.tsx">التقدم الإجمالي</span>
              </div>
              <Progress value={progress} className="w-full" data-id="90eh256ff" data-path="src/pages/CoursePage.tsx" />
            </div>
          </CardContent>
        </Card>

        {/* Course Content */}
        <Tabs defaultValue="part4" className="w-full" data-id="od474h08c" data-path="src/pages/CoursePage.tsx">
          <TabsList className="grid w-full grid-cols-2 mb-8" data-id="itr1yxp5k" data-path="src/pages/CoursePage.tsx">
            <TabsTrigger value="part4" className="text-lg" data-id="umo4sjzk9" data-path="src/pages/CoursePage.tsx">
              الجزء الرابع: نمذجة الكشف وتكوين الصور
            </TabsTrigger>
            <TabsTrigger value="part5" className="text-lg" data-id="vhhjjxe9d" data-path="src/pages/CoursePage.tsx">
              الجزء الخامس: التطبيقات المتقدمة والاتجاهات المستقبلية
            </TabsTrigger>
          </TabsList>

          <TabsContent value="part4" className="space-y-6" data-id="nc1r91h6h" data-path="src/pages/CoursePage.tsx">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="grid md:grid-cols-2 gap-6" data-id="57030qz9a" data-path="src/pages/CoursePage.tsx">

              {part4Chapters.map((chapter) =>
              <ChapterCard key={chapter.id} chapter={chapter} partTitle="الجزء الرابع" data-id="1utfkty10" data-path="src/pages/CoursePage.tsx" />
              )}
            </motion.div>
          </TabsContent>

          <TabsContent value="part5" className="space-y-6" data-id="kto7yse7f" data-path="src/pages/CoursePage.tsx">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-6" data-id="eulrfnbjt" data-path="src/pages/CoursePage.tsx">

              {part5Chapters.map((chapter) =>
              <ChapterCard key={chapter.id} chapter={chapter} partTitle="الجزء الخامس" data-id="qtiayowcf" data-path="src/pages/CoursePage.tsx" />
              )}
            </motion.div>
          </TabsContent>
        </Tabs>

        {/* VR Features Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-12" data-id="hrlv2bo1v" data-path="src/pages/CoursePage.tsx">

          <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white" data-id="a9bhy8gnq" data-path="src/pages/CoursePage.tsx">
            <CardHeader data-id="64e86i7iv" data-path="src/pages/CoursePage.tsx">
              <CardTitle className="text-right text-2xl" data-id="iczvz2us0" data-path="src/pages/CoursePage.tsx">تجربة الواقع الافتراضي</CardTitle>
              <CardDescription className="text-blue-100 text-right" data-id="2hyu1w9at" data-path="src/pages/CoursePage.tsx">
                استكشف عالم التصوير بالأشعة السينية من خلال تقنيات الواقع الافتراضي المتطورة
              </CardDescription>
            </CardHeader>
            <CardContent data-id="pbd474fow" data-path="src/pages/CoursePage.tsx">
              <div className="grid md:grid-cols-3 gap-6" data-id="6hxbg0kmo" data-path="src/pages/CoursePage.tsx">
                <div className="text-center" data-id="rn6fcrlq2" data-path="src/pages/CoursePage.tsx">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3" data-id="jjjxoh02j" data-path="src/pages/CoursePage.tsx">
                    <Monitor className="w-8 h-8" data-id="fttskyp7y" data-path="src/pages/CoursePage.tsx" />
                  </div>
                  <h3 className="font-semibold mb-2" data-id="6iztnxi34" data-path="src/pages/CoursePage.tsx">نماذج ثلاثية الأبعاد</h3>
                  <p className="text-sm text-blue-100" data-id="br8fnwqr1" data-path="src/pages/CoursePage.tsx">تفاعل مع نماذج دقيقة لأجهزة الأشعة السينية</p>
                </div>
                <div className="text-center" data-id="e7ao725pw" data-path="src/pages/CoursePage.tsx">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3" data-id="fu4r8fza4" data-path="src/pages/CoursePage.tsx">
                    <Cpu className="w-8 h-8" data-id="vi9glbx5f" data-path="src/pages/CoursePage.tsx" />
                  </div>
                  <h3 className="font-semibold mb-2" data-id="2qvprhyo5" data-path="src/pages/CoursePage.tsx">محاكاة تفاعلية</h3>
                  <p className="text-sm text-blue-100" data-id="ka2pne31h" data-path="src/pages/CoursePage.tsx">جرب المحاكاة في بيئة افتراضية واقعية</p>
                </div>
                <div className="text-center" data-id="ybgueu0ha" data-path="src/pages/CoursePage.tsx">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3" data-id="7vj2prkyv" data-path="src/pages/CoursePage.tsx">
                    <Search className="w-8 h-8" data-id="uaz24n6bk" data-path="src/pages/CoursePage.tsx" />
                  </div>
                  <h3 className="font-semibold mb-2" data-id="caglwhzl5" data-path="src/pages/CoursePage.tsx">تحليل مرئي</h3>
                  <p className="text-sm text-blue-100" data-id="bmh1uxfkv" data-path="src/pages/CoursePage.tsx">تصور البيانات والنتائج بشكل تفاعلي</p>
                </div>
              </div>
              <div className="text-center mt-6" data-id="rm2om9830" data-path="src/pages/CoursePage.tsx">
                <Button variant="secondary" size="lg" className="text-blue-600" data-id="14y8dson9" data-path="src/pages/CoursePage.tsx">
                  ابدأ التجربة الافتراضية
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>);

};

export default CoursePage;