import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TooltipProvider } from '@/components/ui/tooltip';
import { Toaster } from '@/components/ui/toaster';
import HomePage from '@/pages/HomePage';
import XRayTubePage from '@/pages/XRayTubePage';
import CircuitDiagramPage from '@/pages/CircuitDiagramPage';
import ConceptsPage from '@/pages/ConceptsPage';
import AIAssistantPage from '@/pages/AIAssistantPage';
import NotFound from '@/pages/NotFound';
import Navbar from '@/components/Navbar';
import './App.css';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient} data-id="qihxank35" data-path="src/App.tsx">
      <TooltipProvider data-id="6d39h84w8" data-path="src/App.tsx">
        <Router data-id="udq8siwoi" data-path="src/App.tsx">
          <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50" data-id="6jmxeptnt" data-path="src/App.tsx">
            <Navbar data-id="5x2079oig" data-path="src/App.tsx" />
            <main data-id="0le12e4t8" data-path="src/App.tsx">
              <Routes data-id="hnv5q7maq" data-path="src/App.tsx">
                <Route path="/" element={<HomePage data-id="frg8qjjo3" data-path="src/App.tsx" />} data-id="bigm9fhqw" data-path="src/App.tsx" />
                <Route path="/xray-tube" element={<XRayTubePage data-id="o2ey2w3xf" data-path="src/App.tsx" />} data-id="t0tz4os4k" data-path="src/App.tsx" />
                <Route path="/circuits" element={<CircuitDiagramPage data-id="cdtqifcpc" data-path="src/App.tsx" />} data-id="v7yk2jajg" data-path="src/App.tsx" />
                <Route path="/concepts" element={<ConceptsPage data-id="oysaxo20u" data-path="src/App.tsx" />} data-id="6dtbnrvh4" data-path="src/App.tsx" />
                <Route path="/ai-assistant" element={<AIAssistantPage data-id="7c8mmngzz" data-path="src/App.tsx" />} data-id="f6y09tgkp" data-path="src/App.tsx" />
                <Route path="*" element={<NotFound data-id="wm0ede318" data-path="src/App.tsx" />} data-id="qlwcyt6gv" data-path="src/App.tsx" />
              </Routes>
            </main>
          </div>
          <Toaster data-id="160h2r093" data-path="src/App.tsx" />
        </Router>
      </TooltipProvider>
    </QueryClientProvider>);

}

export default App;