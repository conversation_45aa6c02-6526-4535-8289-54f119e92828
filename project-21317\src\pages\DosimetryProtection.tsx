import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import DoseCalculator from '@/components/DoseCalculator';
import ShieldingCalculator from '@/components/ShieldingCalculator';
import { Shield, Calculator, Activity } from 'lucide-react';

const DosimetryProtection = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50" data-id="80abx69e3" data-path="src/pages/DosimetryProtection.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" data-id="99kemmdlr" data-path="src/pages/DosimetryProtection.tsx">
        {/* <PERSON>er */}
        <div className="text-center mb-12" data-id="dlsgyltex" data-path="src/pages/DosimetryProtection.tsx">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4" data-id="45ta17v88" data-path="src/pages/DosimetryProtection.tsx">
            Radiation Dosimetry & Protection
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="gbvqj3eh7" data-path="src/pages/DosimetryProtection.tsx">
            Calculate radiation doses and design effective shielding for medical imaging applications
          </p>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="dose-calculator" className="space-y-8" data-id="aie950xap" data-path="src/pages/DosimetryProtection.tsx">
          <TabsList className="grid w-full grid-cols-2 lg:w-96 mx-auto" data-id="fi21sgo8o" data-path="src/pages/DosimetryProtection.tsx">
            <TabsTrigger value="dose-calculator" className="flex items-center gap-2" data-id="d1q6phduc" data-path="src/pages/DosimetryProtection.tsx">
              <Calculator className="w-4 h-4" data-id="mvzcr0ta2" data-path="src/pages/DosimetryProtection.tsx" />
              Dose Calculator
            </TabsTrigger>
            <TabsTrigger value="shielding" className="flex items-center gap-2" data-id="pmhvw9i3n" data-path="src/pages/DosimetryProtection.tsx">
              <Shield className="w-4 h-4" data-id="hqp2vubtj" data-path="src/pages/DosimetryProtection.tsx" />
              Shielding Design
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dose-calculator" data-id="uydjxj4rp" data-path="src/pages/DosimetryProtection.tsx">
            <DoseCalculator data-id="8uyz67r21" data-path="src/pages/DosimetryProtection.tsx" />
          </TabsContent>

          <TabsContent value="shielding" data-id="4ouv6bbzr" data-path="src/pages/DosimetryProtection.tsx">
            <ShieldingCalculator data-id="gc98cr5jv" data-path="src/pages/DosimetryProtection.tsx" />
          </TabsContent>
        </Tabs>

        {/* Educational Content */}
        <div className="mt-16 grid md:grid-cols-3 gap-8" data-id="vu13j0p7d" data-path="src/pages/DosimetryProtection.tsx">
          <Card data-id="zmiiyzadn" data-path="src/pages/DosimetryProtection.tsx">
            <CardHeader data-id="rnkqu0fk4" data-path="src/pages/DosimetryProtection.tsx">
              <CardTitle className="flex items-center gap-2" data-id="v866tcjbx" data-path="src/pages/DosimetryProtection.tsx">
                <Activity className="w-5 h-5" data-id="u1e024830" data-path="src/pages/DosimetryProtection.tsx" />
                Radiation Quantities
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4" data-id="dw1ptdsqs" data-path="src/pages/DosimetryProtection.tsx">
              <div data-id="oabouhtoh" data-path="src/pages/DosimetryProtection.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="ahi3b5sdc" data-path="src/pages/DosimetryProtection.tsx">Absorbed Dose (D)</h4>
                <p className="text-gray-600 text-sm" data-id="2z0t0zp0j" data-path="src/pages/DosimetryProtection.tsx">
                  Energy deposited per unit mass of material. Unit: Gray (Gy) = J/kg
                </p>
              </div>
              <div data-id="hxjs1jttv" data-path="src/pages/DosimetryProtection.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="9az8vom5s" data-path="src/pages/DosimetryProtection.tsx">Equivalent Dose (H)</h4>
                <p className="text-gray-600 text-sm" data-id="hdegmefof" data-path="src/pages/DosimetryProtection.tsx">
                  Absorbed dose weighted by radiation type. Unit: Sievert (Sv) = Gy × wR
                </p>
              </div>
              <div data-id="3gqyz3xsr" data-path="src/pages/DosimetryProtection.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="yar6l88kt" data-path="src/pages/DosimetryProtection.tsx">Effective Dose (E)</h4>
                <p className="text-gray-600 text-sm" data-id="ti4dz9eys" data-path="src/pages/DosimetryProtection.tsx">
                  Equivalent dose weighted by tissue sensitivity. Unit: Sievert (Sv)
                </p>
              </div>
            </CardContent>
          </Card>

          <Card data-id="mjkb6s0sj" data-path="src/pages/DosimetryProtection.tsx">
            <CardHeader data-id="syyr2xv2c" data-path="src/pages/DosimetryProtection.tsx">
              <CardTitle data-id="prm3375un" data-path="src/pages/DosimetryProtection.tsx">Dose Limits</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4" data-id="uevpqt7rx" data-path="src/pages/DosimetryProtection.tsx">
              <div data-id="ddj9b77qb" data-path="src/pages/DosimetryProtection.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="qc9gy77eg" data-path="src/pages/DosimetryProtection.tsx">Occupational Workers</h4>
                <ul className="text-gray-600 text-sm space-y-1" data-id="zko3kwfs1" data-path="src/pages/DosimetryProtection.tsx">
                  <li data-id="el9owvvry" data-path="src/pages/DosimetryProtection.tsx">• Effective dose: 20 mSv/year</li>
                  <li data-id="nuwtxogpb" data-path="src/pages/DosimetryProtection.tsx">• Eye lens: 20 mSv/year</li>
                  <li data-id="zo756tycp" data-path="src/pages/DosimetryProtection.tsx">• Skin: 500 mSv/year</li>
                  <li data-id="v8yop5zu0" data-path="src/pages/DosimetryProtection.tsx">• Hands/feet: 500 mSv/year</li>
                </ul>
              </div>
              <div data-id="2sjaldshx" data-path="src/pages/DosimetryProtection.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="dxn8t15jk" data-path="src/pages/DosimetryProtection.tsx">General Public</h4>
                <ul className="text-gray-600 text-sm space-y-1" data-id="0n0f4lg07" data-path="src/pages/DosimetryProtection.tsx">
                  <li data-id="st9fbuf62" data-path="src/pages/DosimetryProtection.tsx">• Effective dose: 1 mSv/year</li>
                  <li data-id="ggkcimhut" data-path="src/pages/DosimetryProtection.tsx">• Eye lens: 15 mSv/year</li>
                  <li data-id="zz6rgu7dy" data-path="src/pages/DosimetryProtection.tsx">• Skin: 50 mSv/year</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card data-id="zdkzbjkjz" data-path="src/pages/DosimetryProtection.tsx">
            <CardHeader data-id="zp1ysndh3" data-path="src/pages/DosimetryProtection.tsx">
              <CardTitle data-id="agfz48xt6" data-path="src/pages/DosimetryProtection.tsx">ALARA Principle</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4" data-id="1902j34fj" data-path="src/pages/DosimetryProtection.tsx">
              <div data-id="vdacmuq3k" data-path="src/pages/DosimetryProtection.tsx">
                <h4 className="font-semibold text-gray-900 mb-2" data-id="fisncp1wh" data-path="src/pages/DosimetryProtection.tsx">As Low As Reasonably Achievable</h4>
                <p className="text-gray-600 text-sm mb-3" data-id="4faue2ul4" data-path="src/pages/DosimetryProtection.tsx">
                  Optimize radiation protection through three fundamental principles:
                </p>
                <ul className="text-gray-600 text-sm space-y-2" data-id="isux6av5f" data-path="src/pages/DosimetryProtection.tsx">
                  <li data-id="judkudwdi" data-path="src/pages/DosimetryProtection.tsx"><strong data-id="6to9jw5pa" data-path="src/pages/DosimetryProtection.tsx">Time:</strong> Minimize exposure duration</li>
                  <li data-id="qud6rn3ie" data-path="src/pages/DosimetryProtection.tsx"><strong data-id="2t2fuud5d" data-path="src/pages/DosimetryProtection.tsx">Distance:</strong> Maximize distance from source (inverse square law)</li>
                  <li data-id="zq589cx1r" data-path="src/pages/DosimetryProtection.tsx"><strong data-id="ntjrk2dto" data-path="src/pages/DosimetryProtection.tsx">Shielding:</strong> Use appropriate materials to attenuate radiation</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Key Formulas */}
        <div className="mt-16" data-id="kqmkpw3jp" data-path="src/pages/DosimetryProtection.tsx">
          <Card data-id="amhqsf4or" data-path="src/pages/DosimetryProtection.tsx">
            <CardHeader data-id="qml12hl3f" data-path="src/pages/DosimetryProtection.tsx">
              <CardTitle data-id="1f76fqidx" data-path="src/pages/DosimetryProtection.tsx">Key Formulas & Relationships</CardTitle>
            </CardHeader>
            <CardContent data-id="nyt67tgmo" data-path="src/pages/DosimetryProtection.tsx">
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6" data-id="e1eeb64jn" data-path="src/pages/DosimetryProtection.tsx">
                <div data-id="70c7ug11k" data-path="src/pages/DosimetryProtection.tsx">
                  <h4 className="font-semibold text-gray-900 mb-2" data-id="gi5gpw5lt" data-path="src/pages/DosimetryProtection.tsx">Inverse Square Law</h4>
                  <p className="text-gray-600 text-sm mb-1" data-id="hn2jjnpf9" data-path="src/pages/DosimetryProtection.tsx">I₂ = I₁ × (r₁/r₂)²</p>
                  <p className="text-xs text-gray-500" data-id="yc33f2u30" data-path="src/pages/DosimetryProtection.tsx">Intensity decreases with square of distance</p>
                </div>
                <div data-id="5zr8azk8l" data-path="src/pages/DosimetryProtection.tsx">
                  <h4 className="font-semibold text-gray-900 mb-2" data-id="l4gd8gbhh" data-path="src/pages/DosimetryProtection.tsx">Beer-Lambert Law</h4>
                  <p className="text-gray-600 text-sm mb-1" data-id="mbone8ro6" data-path="src/pages/DosimetryProtection.tsx">I = I₀ × e^(-μx)</p>
                  <p className="text-xs text-gray-500" data-id="adxo1j9da" data-path="src/pages/DosimetryProtection.tsx">Exponential attenuation through matter</p>
                </div>
                <div data-id="59lzy69b6" data-path="src/pages/DosimetryProtection.tsx">
                  <h4 className="font-semibold text-gray-900 mb-2" data-id="hy307otvq" data-path="src/pages/DosimetryProtection.tsx">Half-Value Layer</h4>
                  <p className="text-gray-600 text-sm mb-1" data-id="ncrhkd0cd" data-path="src/pages/DosimetryProtection.tsx">HVL = ln(2)/μ</p>
                  <p className="text-xs text-gray-500" data-id="7pwdeep4d" data-path="src/pages/DosimetryProtection.tsx">Thickness reducing intensity by 50%</p>
                </div>
                <div data-id="8fkuhexxo" data-path="src/pages/DosimetryProtection.tsx">
                  <h4 className="font-semibold text-gray-900 mb-2" data-id="ps51u1lc2" data-path="src/pages/DosimetryProtection.tsx">Effective Dose</h4>
                  <p className="text-gray-600 text-sm mb-1" data-id="th6amegm7" data-path="src/pages/DosimetryProtection.tsx">E = Σ wT × HT</p>
                  <p className="text-xs text-gray-500" data-id="650zjj3yw" data-path="src/pages/DosimetryProtection.tsx">Sum of tissue-weighted equivalent doses</p>
                </div>
                <div data-id="b3cuaiuif" data-path="src/pages/DosimetryProtection.tsx">
                  <h4 className="font-semibold text-gray-900 mb-2" data-id="9a5l2owkb" data-path="src/pages/DosimetryProtection.tsx">Equivalent Dose</h4>
                  <p className="text-gray-600 text-sm mb-1" data-id="nhsi92zzv" data-path="src/pages/DosimetryProtection.tsx">H = D × wR</p>
                  <p className="text-xs text-gray-500" data-id="34ylwpvdl" data-path="src/pages/DosimetryProtection.tsx">Absorbed dose × radiation weighting factor</p>
                </div>
                <div data-id="e5scxs5r0" data-path="src/pages/DosimetryProtection.tsx">
                  <h4 className="font-semibold text-gray-900 mb-2" data-id="xfnijdq5p" data-path="src/pages/DosimetryProtection.tsx">Dose Rate</h4>
                  <p className="text-gray-600 text-sm mb-1" data-id="xrc1dt6lk" data-path="src/pages/DosimetryProtection.tsx">Ḋ = D/t</p>
                  <p className="text-xs text-gray-500" data-id="cyj1znwzo" data-path="src/pages/DosimetryProtection.tsx">Dose delivered per unit time</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>);

};

export default DosimetryProtection;