import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Users,
  Target,
  Award,
  BookOpen,
  Code,
  Lightbulb,
  Mail,
  Github,
  Globe,
  Heart } from
'lucide-react';

const About = () => {
  const features = [
  {
    icon: Target,
    title: 'Interactive Learning',
    description: 'Hands-on simulations that make complex physics concepts accessible and engaging'
  },
  {
    icon: Code,
    title: 'Modern Technology',
    description: 'Built with cutting-edge web technologies for smooth, responsive interactions'
  },
  {
    icon: Users,
    title: 'Multi-Audience',
    description: 'Designed for students, residents, professionals, and educators at all levels'
  },
  {
    icon: Award,
    title: 'Evidence-Based',
    description: 'Content based on established physics principles and clinical best practices'
  }];


  const teamMembers = [
  {
    name: 'Development Team',
    role: 'Educational Technology Specialists',
    description: 'Experienced developers focused on creating engaging educational tools for medical physics',
    expertise: ['Web Development', 'Medical Physics', 'Educational Design', 'User Experience']
  }];


  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-50" data-id="kaxpq053l" data-path="src/pages/About.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" data-id="rt1qun59d" data-path="src/pages/About.tsx">
        {/* Header */}
        <div className="text-center mb-16" data-id="2sqlb2fjt" data-path="src/pages/About.tsx">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6" data-id="m4seqxjl7" data-path="src/pages/About.tsx">
            About MedPhys Sim
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed" data-id="b6sr4skvn" data-path="src/pages/About.tsx">
            An innovative educational platform dedicated to making medical radiation physics 
            accessible, interactive, and engaging through cutting-edge web-based simulations 
            and comprehensive learning resources.
          </p>
        </div>

        {/* Mission Statement */}
        <div className="mb-16" data-id="rpv889y9v" data-path="src/pages/About.tsx">
          <Card className="border-none shadow-lg bg-gradient-to-r from-blue-600 to-indigo-600 text-white" data-id="x1nqmu3g8" data-path="src/pages/About.tsx">
            <CardContent className="p-8 text-center" data-id="aypn1e533" data-path="src/pages/About.tsx">
              <Lightbulb className="w-16 h-16 mx-auto mb-6 opacity-80" data-id="q5ktr82z0" data-path="src/pages/About.tsx" />
              <h2 className="text-3xl font-bold mb-4" data-id="k6rhtsq0h" data-path="src/pages/About.tsx">Our Mission</h2>
              <p className="text-xl leading-relaxed max-w-4xl mx-auto" data-id="mrzmpy0vs" data-path="src/pages/About.tsx">
                To revolutionize medical physics education by providing interactive, 
                accessible, and comprehensive learning tools that bridge the gap between 
                theoretical knowledge and practical understanding, empowering the next 
                generation of medical physics professionals.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Key Features */}
        <div className="mb-16" data-id="rrc352v5c" data-path="src/pages/About.tsx">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12" data-id="tr6bidvng" data-path="src/pages/About.tsx">
            Platform Features
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6" data-id="k3yp5psjd" data-path="src/pages/About.tsx">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow" data-id="4t6y25q8x" data-path="src/pages/About.tsx">
                  <CardHeader data-id="qra1gtnle" data-path="src/pages/About.tsx">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center" data-id="athvatu8q" data-path="src/pages/About.tsx">
                      <Icon className="w-8 h-8 text-white" data-id="cczgz5zhv" data-path="src/pages/About.tsx" />
                    </div>
                    <CardTitle className="text-xl" data-id="u0i65yvhu" data-path="src/pages/About.tsx">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent data-id="jeeo39yo2" data-path="src/pages/About.tsx">
                    <p className="text-gray-600" data-id="p48q54zov" data-path="src/pages/About.tsx">{feature.description}</p>
                  </CardContent>
                </Card>);

            })}
          </div>
        </div>

        {/* Educational Philosophy */}
        <div className="mb-16" data-id="ny3u2udrf" data-path="src/pages/About.tsx">
          <Card data-id="qtguzdb5l" data-path="src/pages/About.tsx">
            <CardHeader data-id="t0g4gx43q" data-path="src/pages/About.tsx">
              <CardTitle className="text-2xl text-center" data-id="ezxw2gi57" data-path="src/pages/About.tsx">Educational Philosophy</CardTitle>
            </CardHeader>
            <CardContent data-id="hj67ddhds" data-path="src/pages/About.tsx">
              <div className="grid md:grid-cols-3 gap-8" data-id="0lyopvvkz" data-path="src/pages/About.tsx">
                <div className="text-center" data-id="i9p8dhks0" data-path="src/pages/About.tsx">
                  <div className="w-12 h-12 mx-auto mb-4 bg-green-100 rounded-lg flex items-center justify-center" data-id="imdp45v7z" data-path="src/pages/About.tsx">
                    <BookOpen className="w-6 h-6 text-green-600" data-id="twwqpnlc5" data-path="src/pages/About.tsx" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2" data-id="24197tj46" data-path="src/pages/About.tsx">Learn by Doing</h3>
                  <p className="text-gray-600 text-sm" data-id="kq6eysywk" data-path="src/pages/About.tsx">
                    Interactive simulations allow students to manipulate parameters and 
                    observe real-time changes, reinforcing theoretical concepts through practice.
                  </p>
                </div>
                <div className="text-center" data-id="z4rpmnn2b" data-path="src/pages/About.tsx">
                  <div className="w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-lg flex items-center justify-center" data-id="7vp1r12oc" data-path="src/pages/About.tsx">
                    <Target className="w-6 h-6 text-blue-600" data-id="k6gzkdacr" data-path="src/pages/About.tsx" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2" data-id="s7qctnoh4" data-path="src/pages/About.tsx">Progressive Learning</h3>
                  <p className="text-gray-600 text-sm" data-id="49vw0bo4c" data-path="src/pages/About.tsx">
                    Content is structured to build knowledge progressively, from basic concepts 
                    to advanced applications, ensuring solid foundation at each level.
                  </p>
                </div>
                <div className="text-center" data-id="ig5ma6pwd" data-path="src/pages/About.tsx">
                  <div className="w-12 h-12 mx-auto mb-4 bg-purple-100 rounded-lg flex items-center justify-center" data-id="duf2jvekt" data-path="src/pages/About.tsx">
                    <Users className="w-6 h-6 text-purple-600" data-id="b3zs7vgkx" data-path="src/pages/About.tsx" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2" data-id="91yejkusx" data-path="src/pages/About.tsx">Collaborative Learning</h3>
                  <p className="text-gray-600 text-sm" data-id="pckxoxkkl" data-path="src/pages/About.tsx">
                    Resources designed to support both individual study and collaborative 
                    learning environments, enhancing the educational experience.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Target Audiences */}
        <div className="mb-16" data-id="lgv0ec5qz" data-path="src/pages/About.tsx">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12" data-id="ysalhhow3" data-path="src/pages/About.tsx">
            Who We Serve
          </h2>
          <div className="grid md:grid-cols-2 gap-8" data-id="u3iw5ww3w" data-path="src/pages/About.tsx">
            <Card data-id="gtci4d0xj" data-path="src/pages/About.tsx">
              <CardHeader data-id="rur0895ig" data-path="src/pages/About.tsx">
                <CardTitle className="flex items-center gap-2" data-id="kucffht2o" data-path="src/pages/About.tsx">
                  <Users className="w-5 h-5" data-id="c7owihx72" data-path="src/pages/About.tsx" />
                  Students & Trainees
                </CardTitle>
              </CardHeader>
              <CardContent data-id="ga75lnwva" data-path="src/pages/About.tsx">
                <ul className="space-y-2 text-gray-600" data-id="ouvxecly2" data-path="src/pages/About.tsx">
                  <li data-id="y1pd32rt2" data-path="src/pages/About.tsx">• Undergraduate physics and engineering students</li>
                  <li data-id="8lsnjcj83" data-path="src/pages/About.tsx">• Graduate students in medical physics programs</li>
                  <li data-id="t7nm1aak3" data-path="src/pages/About.tsx">• Radiology and radiation oncology residents</li>
                  <li data-id="85g7pc1fl" data-path="src/pages/About.tsx">• Medical physics residency trainees</li>
                  <li data-id="47c7suavo" data-path="src/pages/About.tsx">• Radiologic technology students</li>
                </ul>
              </CardContent>
            </Card>

            <Card data-id="rdewdyg2l" data-path="src/pages/About.tsx">
              <CardHeader data-id="xfv1rc8wo" data-path="src/pages/About.tsx">
                <CardTitle className="flex items-center gap-2" data-id="cev79b521" data-path="src/pages/About.tsx">
                  <Award className="w-5 h-5" data-id="kxnmjn874" data-path="src/pages/About.tsx" />
                  Professionals & Educators
                </CardTitle>
              </CardHeader>
              <CardContent data-id="4nucq5z8y" data-path="src/pages/About.tsx">
                <ul className="space-y-2 text-gray-600" data-id="9xb9a8hi3" data-path="src/pages/About.tsx">
                  <li data-id="242koeaa0" data-path="src/pages/About.tsx">• Medical physicists seeking continuing education</li>
                  <li data-id="0chq4b7ct" data-path="src/pages/About.tsx">• Radiology and radiation oncology professionals</li>
                  <li data-id="xa50xiqwa" data-path="src/pages/About.tsx">• Medical physics educators and instructors</li>
                  <li data-id="lkty7l88a" data-path="src/pages/About.tsx">• Healthcare professionals in imaging departments</li>
                  <li data-id="k2zsf0oxb" data-path="src/pages/About.tsx">• Researchers in medical physics and imaging</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Development Team */}
        <div className="mb-16" data-id="tzpssyzr0" data-path="src/pages/About.tsx">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12" data-id="m342lek4c" data-path="src/pages/About.tsx">
            Development Team
          </h2>
          <div className="max-w-4xl mx-auto" data-id="pbn5z05kj" data-path="src/pages/About.tsx">
            {teamMembers.map((member, index) =>
            <Card key={index} className="mb-6" data-id="7wnew7ulj" data-path="src/pages/About.tsx">
                <CardHeader data-id="652lh7udy" data-path="src/pages/About.tsx">
                  <CardTitle className="text-xl" data-id="wtf7bbkhd" data-path="src/pages/About.tsx">{member.name}</CardTitle>
                  <CardDescription className="text-lg" data-id="5hzdryq0x" data-path="src/pages/About.tsx">{member.role}</CardDescription>
                </CardHeader>
                <CardContent data-id="dv4sqd45f" data-path="src/pages/About.tsx">
                  <p className="text-gray-600 mb-4" data-id="sdf2qvcqd" data-path="src/pages/About.tsx">{member.description}</p>
                  <div className="flex flex-wrap gap-2" data-id="14d2j8zhw" data-path="src/pages/About.tsx">
                    {member.expertise.map((skill, skillIndex) =>
                  <Badge key={skillIndex} variant="secondary" data-id="4d9ng2ur4" data-path="src/pages/About.tsx">
                        {skill}
                      </Badge>
                  )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Technical Implementation */}
        <div className="mb-16" data-id="3qmq72ge9" data-path="src/pages/About.tsx">
          <Card data-id="ehu097ooh" data-path="src/pages/About.tsx">
            <CardHeader data-id="a4ujy9be1" data-path="src/pages/About.tsx">
              <CardTitle className="text-2xl text-center flex items-center justify-center gap-2" data-id="nhm2k9l5x" data-path="src/pages/About.tsx">
                <Code className="w-6 h-6" data-id="ganouv6t6" data-path="src/pages/About.tsx" />
                Technical Implementation
              </CardTitle>
            </CardHeader>
            <CardContent data-id="18pzqrlsu" data-path="src/pages/About.tsx">
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6" data-id="08yhe1gp0" data-path="src/pages/About.tsx">
                <div data-id="x27048l1x" data-path="src/pages/About.tsx">
                  <h3 className="font-semibold text-gray-900 mb-3" data-id="ng8pifc95" data-path="src/pages/About.tsx">Frontend Technologies</h3>
                  <ul className="space-y-1 text-sm text-gray-600" data-id="9pce94gwu" data-path="src/pages/About.tsx">
                    <li data-id="3u4d38gvp" data-path="src/pages/About.tsx">• React 18 with TypeScript</li>
                    <li data-id="2jq6xutic" data-path="src/pages/About.tsx">• Modern CSS with Tailwind</li>
                    <li data-id="su1uavpzv" data-path="src/pages/About.tsx">• Canvas API for visualizations</li>
                    <li data-id="tr13yrzrr" data-path="src/pages/About.tsx">• Responsive design principles</li>
                    <li data-id="0ggc9vmue" data-path="src/pages/About.tsx">• Accessibility compliance (WCAG)</li>
                  </ul>
                </div>
                <div data-id="tcprtyaqh" data-path="src/pages/About.tsx">
                  <h3 className="font-semibold text-gray-900 mb-3" data-id="pqx2kygiu" data-path="src/pages/About.tsx">Simulation Engine</h3>
                  <ul className="space-y-1 text-sm text-gray-600" data-id="o41j02zm4" data-path="src/pages/About.tsx">
                    <li data-id="jen0qlb3z" data-path="src/pages/About.tsx">• Real-time physics calculations</li>
                    <li data-id="edeqd87jd" data-path="src/pages/About.tsx">• Interactive parameter controls</li>
                    <li data-id="ni0oib4m1" data-path="src/pages/About.tsx">• Optimized rendering algorithms</li>
                    <li data-id="iyy7cde4o" data-path="src/pages/About.tsx">• Cross-platform compatibility</li>
                    <li data-id="sb451uabs" data-path="src/pages/About.tsx">• Performance optimization</li>
                  </ul>
                </div>
                <div data-id="lqislnazk" data-path="src/pages/About.tsx">
                  <h3 className="font-semibold text-gray-900 mb-3" data-id="1csd6obqr" data-path="src/pages/About.tsx">User Experience</h3>
                  <ul className="space-y-1 text-sm text-gray-600" data-id="zq01gwq9f" data-path="src/pages/About.tsx">
                    <li data-id="w94igqb7u" data-path="src/pages/About.tsx">• Intuitive interface design</li>
                    <li data-id="l1ckjt997" data-path="src/pages/About.tsx">• Mobile-responsive layouts</li>
                    <li data-id="bzeaexkcr" data-path="src/pages/About.tsx">• Progressive disclosure</li>
                    <li data-id="3kjd9a4h2" data-path="src/pages/About.tsx">• Clear visual feedback</li>
                    <li data-id="mngvs28ap" data-path="src/pages/About.tsx">• Educational workflows</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Future Development */}
        <div className="mb-16" data-id="bw0b7ty0j" data-path="src/pages/About.tsx">
          <Card data-id="i9j5f9p3s" data-path="src/pages/About.tsx">
            <CardHeader data-id="d8p1iar39" data-path="src/pages/About.tsx">
              <CardTitle className="text-2xl text-center" data-id="wf27yzpou" data-path="src/pages/About.tsx">Future Development</CardTitle>
              <CardDescription className="text-center" data-id="ro9ys770d" data-path="src/pages/About.tsx">
                Exciting features and improvements planned for upcoming releases
              </CardDescription>
            </CardHeader>
            <CardContent data-id="k8yco2qma" data-path="src/pages/About.tsx">
              <div className="grid md:grid-cols-2 gap-8" data-id="96rub4esg" data-path="src/pages/About.tsx">
                <div data-id="n3f5700tf" data-path="src/pages/About.tsx">
                  <h3 className="font-semibold text-gray-900 mb-3" data-id="b3hav6iwm" data-path="src/pages/About.tsx">Enhanced Simulations</h3>
                  <ul className="space-y-2 text-gray-600 text-sm" data-id="4koqaa7qm" data-path="src/pages/About.tsx">
                    <li data-id="uuplp6kno" data-path="src/pages/About.tsx">• Advanced MRI pulse sequence simulations</li>
                    <li data-id="vfdqefqnu" data-path="src/pages/About.tsx">• 3D visualization of radiation interactions</li>
                    <li data-id="d4ms4p6ie" data-path="src/pages/About.tsx">• Monte Carlo simulation integration</li>
                    <li data-id="jglxirsqe" data-path="src/pages/About.tsx">• Real-time dose calculations</li>
                    <li data-id="p2qexor4o" data-path="src/pages/About.tsx">• Multi-modal imaging comparisons</li>
                  </ul>
                </div>
                <div data-id="cgx59x94p" data-path="src/pages/About.tsx">
                  <h3 className="font-semibold text-gray-900 mb-3" data-id="ui1avb8eq" data-path="src/pages/About.tsx">Educational Features</h3>
                  <ul className="space-y-2 text-gray-600 text-sm" data-id="i350zn0bm" data-path="src/pages/About.tsx">
                    <li data-id="hplt8ht5e" data-path="src/pages/About.tsx">• Adaptive learning pathways</li>
                    <li data-id="zw7qgvn96" data-path="src/pages/About.tsx">• Progress tracking and analytics</li>
                    <li data-id="roslhwv78" data-path="src/pages/About.tsx">• Collaborative learning tools</li>
                    <li data-id="jj349d6fg" data-path="src/pages/About.tsx">• Extended quiz and assessment system</li>
                    <li data-id="zocqilsfp" data-path="src/pages/About.tsx">• Integration with LMS platforms</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contact Information */}
        <div className="mb-16" data-id="4wz3r8e8m" data-path="src/pages/About.tsx">
          <Card className="bg-gradient-to-r from-gray-50 to-blue-50" data-id="hunib6exp" data-path="src/pages/About.tsx">
            <CardHeader data-id="r2j2f472h" data-path="src/pages/About.tsx">
              <CardTitle className="text-2xl text-center" data-id="xgov71ui9" data-path="src/pages/About.tsx">Get Involved</CardTitle>
              <CardDescription className="text-center" data-id="zu1wgzoir" data-path="src/pages/About.tsx">
                We welcome feedback, suggestions, and collaboration opportunities
              </CardDescription>
            </CardHeader>
            <CardContent data-id="esofiyvde" data-path="src/pages/About.tsx">
              <div className="grid md:grid-cols-3 gap-6 text-center" data-id="5f8mq53k0" data-path="src/pages/About.tsx">
                <div data-id="3s6dpy5vl" data-path="src/pages/About.tsx">
                  <div className="w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-lg flex items-center justify-center" data-id="rwv9n1qno" data-path="src/pages/About.tsx">
                    <Mail className="w-6 h-6 text-blue-600" data-id="5557fapcn" data-path="src/pages/About.tsx" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2" data-id="ojj5qrzlp" data-path="src/pages/About.tsx">Feedback</h3>
                  <p className="text-gray-600 text-sm" data-id="yrjkiiwun" data-path="src/pages/About.tsx">
                    Share your thoughts and suggestions for improving the platform
                  </p>
                </div>
                <div data-id="opedgadcl" data-path="src/pages/About.tsx">
                  <div className="w-12 h-12 mx-auto mb-4 bg-green-100 rounded-lg flex items-center justify-center" data-id="x7sr9vfdg" data-path="src/pages/About.tsx">
                    <Heart className="w-6 h-6 text-green-600" data-id="q64vjh86z" data-path="src/pages/About.tsx" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2" data-id="dqs4vo0kl" data-path="src/pages/About.tsx">Contribute</h3>
                  <p className="text-gray-600 text-sm" data-id="ieok4pxua" data-path="src/pages/About.tsx">
                    Help us expand the content and improve the educational experience
                  </p>
                </div>
                <div data-id="ynv622d31" data-path="src/pages/About.tsx">
                  <div className="w-12 h-12 mx-auto mb-4 bg-purple-100 rounded-lg flex items-center justify-center" data-id="m01o4zr5h" data-path="src/pages/About.tsx">
                    <Users className="w-6 h-6 text-purple-600" data-id="u8ezpt1p2" data-path="src/pages/About.tsx" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2" data-id="07p23w8k9" data-path="src/pages/About.tsx">Collaborate</h3>
                  <p className="text-gray-600 text-sm" data-id="15388ihyo" data-path="src/pages/About.tsx">
                    Partner with us on educational initiatives and research projects
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Acknowledgments */}
        <div className="text-center" data-id="e7hzyinya" data-path="src/pages/About.tsx">
          <Card data-id="t4id5w3lj" data-path="src/pages/About.tsx">
            <CardHeader data-id="wqo8cocpz" data-path="src/pages/About.tsx">
              <CardTitle data-id="ibbw3aksa" data-path="src/pages/About.tsx">Acknowledgments</CardTitle>
            </CardHeader>
            <CardContent data-id="jbdq1b3hz" data-path="src/pages/About.tsx">
              <p className="text-gray-600 mb-4" data-id="c50uhdbrh" data-path="src/pages/About.tsx">
                This platform is built upon the foundational work of countless researchers, 
                educators, and professionals in the field of medical physics. We acknowledge 
                the contributions of professional organizations, academic institutions, and 
                the open-source community that make projects like this possible.
              </p>
              <div className="flex flex-wrap justify-center gap-4" data-id="i3lkc2xea" data-path="src/pages/About.tsx">
                <Badge variant="outline" data-id="nhh0c8sug" data-path="src/pages/About.tsx">AAPM</Badge>
                <Badge variant="outline" data-id="mnyblv7b9" data-path="src/pages/About.tsx">ICRP</Badge>
                <Badge variant="outline" data-id="6n36wxksm" data-path="src/pages/About.tsx">NCRP</Badge>
                <Badge variant="outline" data-id="81ndj53zs" data-path="src/pages/About.tsx">IAEA</Badge>
                <Badge variant="outline" data-id="1npeao7f8" data-path="src/pages/About.tsx">Open Source Community</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>);

};

export default About;