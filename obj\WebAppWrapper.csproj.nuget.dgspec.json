{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية\\WebAppWrapper.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية\\WebAppWrapper.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية\\WebAppWrapper.csproj", "projectName": "WebAppWrapper", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية\\WebAppWrapper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Web.WebView2": {"target": "Package", "version": "[1.0.2210.55, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.14, 6.0.14]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.14, 6.0.14]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.14, 6.0.14]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.14, 6.0.14]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.201\\RuntimeIdentifierGraph.json"}}}}}