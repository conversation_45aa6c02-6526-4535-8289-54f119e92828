import React from 'react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';
import { Globe } from 'lucide-react';

const LanguageSwitcher: React.FC = () => {
  const { language, setLanguage } = useLanguage();

  const toggleLanguage = () => {
    setLanguage(language === 'ar' ? 'en' : 'ar');
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleLanguage}
      className="flex items-center space-x-2 rtl:space-x-reverse"
      aria-label={`Switch to ${language === 'ar' ? 'English' : 'Arabic'}`} data-id="7f1jvba6r" data-path="src/components/LanguageSwitcher.tsx">

      <Globe className="w-4 h-4" data-id="y1lfdk3pr" data-path="src/components/LanguageSwitcher.tsx" />
      <span className="font-medium" data-id="qah5sek3t" data-path="src/components/LanguageSwitcher.tsx">
        {language === 'ar' ? 'English' : 'العربية'}
      </span>
    </Button>);

};

export default LanguageSwitcher;