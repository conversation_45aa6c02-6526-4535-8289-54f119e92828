import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BookOpen, Monitor, Cpu, Zap, Users, Microscope, Search, TrendingUp, Play, ArrowLeft } from 'lucide-react';
import { motion } from 'motion/react';
import { Link } from 'react-router-dom';

const HomePage = () => {
  const features = [
  {
    icon: <Monitor className="w-8 h-8" data-id="7bu0guvib" data-path="src/pages/HomePage.tsx" />,
    title: 'نماذج ثلاثية الأبعاد',
    description: 'تفاعل مع نماذج دقيقة لأجهزة الأشعة السينية والكاشفات'
  },
  {
    icon: <Cpu className="w-8 h-8" data-id="13oxjw6zg" data-path="src/pages/HomePage.tsx" />,
    title: 'محاكاة Monte Carlo',
    description: 'تجارب افتراضية متقدمة لفهم الفيزياء الطبية'
  },
  {
    icon: <Zap className="w-8 h-8" data-id="wpawguwf4" data-path="src/pages/HomePage.tsx" />,
    title: 'تحليل الأنظمة الخطية',
    description: 'أدوات تحليلية لتقييم أداء أجهزة الكشف'
  },
  {
    icon: <Search className="w-8 h-8" data-id="nftpp8w50" data-path="src/pages/HomePage.tsx" />,
    title: 'تصور البيانات التفاعلي',
    description: 'رسوم بيانية وتصورات ديناميكية للمفاهيم المعقدة'
  }];


  const courseHighlights = [
  {
    title: 'الجزء الرابع: نمذجة الكشف بالأشعة السينية',
    chapters: ['أجهزة الكشف بالأشعة السينية', 'محاكاة استجابة الكاشف وتكوين الصورة'],
    icon: <Monitor className="w-6 h-6" data-id="6cwra4wp6" data-path="src/pages/HomePage.tsx" />,
    color: 'from-blue-500 to-cyan-500'
  },
  {
    title: 'الجزء الخامس: التطبيقات المتقدمة',
    chapters: ['تطبيقات المحاكاة', 'تقدير جرعة المريض', 'تقييم جودة الصورة', 'التحقق والتحقق من الصحة', 'الاتجاهات المستقبلية'],
    icon: <TrendingUp className="w-6 h-6" data-id="gka1729oz" data-path="src/pages/HomePage.tsx" />,
    color: 'from-purple-500 to-pink-500'
  }];


  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50" dir="rtl" data-id="bp9kzht54" data-path="src/pages/HomePage.tsx">
      {/* Hero Section */}
      <section className="relative py-20 px-6" data-id="5w2k7a2wb" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto text-center" data-id="wwa3m0kpx" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }} data-id="xjzxeg5dv" data-path="src/pages/HomePage.tsx">

            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6" data-id="41ky0tf5e" data-path="src/pages/HomePage.tsx">
              محاكاة التصوير بالأشعة السينية
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto" data-id="ov7q7nn1f" data-path="src/pages/HomePage.tsx">
              استكشف عالم الفيزياء الطبية من خلال محاكاة متقدمة وتقنيات الواقع الافتراضي التفاعلية
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12" data-id="wrq43bc1s" data-path="src/pages/HomePage.tsx">
              <Link to="/course" data-id="fjhguwvsh" data-path="src/pages/HomePage.tsx">
                <Button size="lg" className="text-lg px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700" data-id="yhfyqqndv" data-path="src/pages/HomePage.tsx">
                  <Play className="w-6 h-6 ml-2" data-id="xohrmknb0" data-path="src/pages/HomePage.tsx" />
                  ابدأ الدورة الآن
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="text-lg px-8 py-4" data-id="u7d4e33vj" data-path="src/pages/HomePage.tsx">
                <BookOpen className="w-6 h-6 ml-2" data-id="icirnd5e6" data-path="src/pages/HomePage.tsx" />
                دليل المستخدم
              </Button>
            </div>

            <div className="flex flex-wrap items-center justify-center gap-4 text-sm" data-id="71mmm6ozt" data-path="src/pages/HomePage.tsx">
              <Badge variant="secondary" className="px-4 py-2" data-id="k6s4emzzc" data-path="src/pages/HomePage.tsx">
                <Users className="w-4 h-4 ml-1" data-id="rr9ecc8cm" data-path="src/pages/HomePage.tsx" />
                للطلاب والمهنيين
              </Badge>
              <Badge variant="secondary" className="px-4 py-2" data-id="p76z2dl4i" data-path="src/pages/HomePage.tsx">
                <Monitor className="w-4 h-4 ml-1" data-id="yetf472gh" data-path="src/pages/HomePage.tsx" />
                محتوى تفاعلي
              </Badge>
              <Badge variant="secondary" className="px-4 py-2" data-id="fh570xuem" data-path="src/pages/HomePage.tsx">
                <Microscope className="w-4 h-4 ml-1" data-id="f5y0m0wg1" data-path="src/pages/HomePage.tsx" />
                أساس علمي متين
              </Badge>
            </div>
          </motion.div>
        </div>

        {/* Floating Animation Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none" data-id="neahxglr8" data-path="src/pages/HomePage.tsx">
          <motion.div
            animate={{
              y: [0, -20, 0],
              rotate: [0, 5, 0]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-20 left-20 w-20 h-20 bg-blue-200 rounded-full opacity-20" data-id="bg3934brg" data-path="src/pages/HomePage.tsx" />

          <motion.div
            animate={{
              y: [0, 15, 0],
              rotate: [0, -5, 0]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
            className="absolute top-40 right-32 w-16 h-16 bg-purple-200 rounded-full opacity-20" data-id="7tnsnugl4" data-path="src/pages/HomePage.tsx" />

          <motion.div
            animate={{
              y: [0, -10, 0],
              rotate: [0, 3, 0]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute bottom-40 left-40 w-24 h-24 bg-pink-200 rounded-full opacity-20" data-id="xgmrcqmgl" data-path="src/pages/HomePage.tsx" />

        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6" data-id="cvk1p1i2i" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto" data-id="tmgiya3xc" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16" data-id="96xsp66wi" data-path="src/pages/HomePage.tsx">

            <h2 className="text-4xl font-bold mb-4" data-id="32tl3gmmc" data-path="src/pages/HomePage.tsx">مميزات الدورة التفاعلية</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto" data-id="huu5342w6" data-path="src/pages/HomePage.tsx">
              تجربة تعليمية ثورية تجمع بين المحتوى الأكاديمي المتقدم وتقنيات الواقع الافتراضي
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8" data-id="gaaxmwia5" data-path="src/pages/HomePage.tsx">
            {features.map((feature, index) =>
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }} data-id="qms77v9ib" data-path="src/pages/HomePage.tsx">

                <Card className="h-full hover:shadow-lg transition-all duration-300 group" data-id="0txopsyfo" data-path="src/pages/HomePage.tsx">
                  <CardHeader className="text-center" data-id="6h7qex1yp" data-path="src/pages/HomePage.tsx">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform" data-id="wlnd45t1e" data-path="src/pages/HomePage.tsx">
                      {feature.icon}
                    </div>
                    <CardTitle className="text-right" data-id="wyu097y8r" data-path="src/pages/HomePage.tsx">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent data-id="1q24ijfyb" data-path="src/pages/HomePage.tsx">
                    <CardDescription className="text-right" data-id="t8gffrmpl" data-path="src/pages/HomePage.tsx">{feature.description}</CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </section>

      {/* Course Overview */}
      <section className="py-20 px-6 bg-gradient-to-r from-blue-50 to-purple-50" data-id="8hfgnktns" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto" data-id="unwatwels" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16" data-id="45knmsd0e" data-path="src/pages/HomePage.tsx">

            <h2 className="text-4xl font-bold mb-4" data-id="antq5pkr1" data-path="src/pages/HomePage.tsx">محتوى الدورة</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto" data-id="i21zdd8q0" data-path="src/pages/HomePage.tsx">
              سبعة فصول شاملة تغطي جميع جوانب محاكاة التصوير بالأشعة السينية
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12" data-id="mqihq6xme" data-path="src/pages/HomePage.tsx">
            {courseHighlights.map((part, index) =>
            <motion.div
              key={index}
              initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }} data-id="emq0mf22r" data-path="src/pages/HomePage.tsx">

                <Card className="h-full hover:shadow-xl transition-all duration-300" data-id="dkqtb0n36" data-path="src/pages/HomePage.tsx">
                  <CardHeader data-id="owjbu6mu7" data-path="src/pages/HomePage.tsx">
                    <div className={`w-12 h-12 bg-gradient-to-r ${part.color} rounded-lg flex items-center justify-center text-white mb-4`} data-id="g7njo5ji4" data-path="src/pages/HomePage.tsx">
                      {part.icon}
                    </div>
                    <CardTitle className="text-right text-xl mb-4" data-id="ry0zxyam4" data-path="src/pages/HomePage.tsx">{part.title}</CardTitle>
                  </CardHeader>
                  <CardContent data-id="36vifzjyv" data-path="src/pages/HomePage.tsx">
                    <div className="space-y-3" data-id="9pvhc5zav" data-path="src/pages/HomePage.tsx">
                      {part.chapters.map((chapter, chapterIndex) =>
                    <div key={chapterIndex} className="flex items-center justify-end space-x-3 rtl:space-x-reverse" data-id="23ibn9v8i" data-path="src/pages/HomePage.tsx">
                          <span className="text-sm" data-id="jtqc8jlw5" data-path="src/pages/HomePage.tsx">{chapter}</span>
                          <div className="w-2 h-2 bg-blue-500 rounded-full" data-id="iksulzhce" data-path="src/pages/HomePage.tsx"></div>
                        </div>
                    )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </section>

      {/* VR Experience Section */}
      <section className="py-20 px-6" data-id="crypen5il" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto" data-id="zlrsmpfse" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }} data-id="03s8v277q" data-path="src/pages/HomePage.tsx">

            <Card className="bg-gradient-to-r from-slate-900 to-blue-900 text-white overflow-hidden" data-id="52irrlm4e" data-path="src/pages/HomePage.tsx">
              <CardContent className="p-12" data-id="d3f96scvp" data-path="src/pages/HomePage.tsx">
                <div className="grid lg:grid-cols-2 gap-12 items-center" data-id="v4613xcof" data-path="src/pages/HomePage.tsx">
                  <div className="text-right" data-id="c4w86tg3z" data-path="src/pages/HomePage.tsx">
                    <h2 className="text-4xl font-bold mb-6" data-id="595gtp0kv" data-path="src/pages/HomePage.tsx">تجربة الواقع الافتراضي</h2>
                    <p className="text-xl mb-8 text-blue-100" data-id="s50ub7aft" data-path="src/pages/HomePage.tsx">
                      ادخل إلى عالم ثلاثي الأبعاد واستكشف المفاهيم المعقدة بطريقة تفاعلية وممتعة
                    </p>
                    
                    <div className="space-y-4 mb-8" data-id="ptlyiaqyd" data-path="src/pages/HomePage.tsx">
                      <div className="flex items-center justify-end space-x-3 rtl:space-x-reverse" data-id="syf0o4gs1" data-path="src/pages/HomePage.tsx">
                        <span data-id="wst8ji8or" data-path="src/pages/HomePage.tsx">نماذج ثلاثية الأبعاد للأجهزة الطبية</span>
                        <div className="w-3 h-3 bg-green-400 rounded-full" data-id="3wuoqjfn9" data-path="src/pages/HomePage.tsx"></div>
                      </div>
                      <div className="flex items-center justify-end space-x-3 rtl:space-x-reverse" data-id="yz2wn48fx" data-path="src/pages/HomePage.tsx">
                        <span data-id="ydoed7c4g" data-path="src/pages/HomePage.tsx">محاكاة فيزيائية دقيقة ومتقدمة</span>
                        <div className="w-3 h-3 bg-green-400 rounded-full" data-id="jknrbsr3p" data-path="src/pages/HomePage.tsx"></div>
                      </div>
                      <div className="flex items-center justify-end space-x-3 rtl:space-x-reverse" data-id="g9yzcaqdf" data-path="src/pages/HomePage.tsx">
                        <span data-id="s4zsylb3e" data-path="src/pages/HomePage.tsx">تجارب تفاعلية لا محدودة</span>
                        <div className="w-3 h-3 bg-green-400 rounded-full" data-id="mlchpor8z" data-path="src/pages/HomePage.tsx"></div>
                      </div>
                      <div className="flex items-center justify-end space-x-3 rtl:space-x-reverse" data-id="v7t9g8837" data-path="src/pages/HomePage.tsx">
                        <span data-id="hvh26owpf" data-path="src/pages/HomePage.tsx">تصور البيانات بشكل مبتكر</span>
                        <div className="w-3 h-3 bg-green-400 rounded-full" data-id="n8x75q0ap" data-path="src/pages/HomePage.tsx"></div>
                      </div>
                    </div>

                    <Link to="/course" data-id="62jxus3wx" data-path="src/pages/HomePage.tsx">
                      <Button size="lg" variant="secondary" className="text-slate-900" data-id="62dc4l87n" data-path="src/pages/HomePage.tsx">
                        <ArrowLeft className="w-5 h-5 ml-2" data-id="qrmbhz70w" data-path="src/pages/HomePage.tsx" />
                        اكتشف التجربة
                      </Button>
                    </Link>
                  </div>
                  
                  <div className="relative" data-id="o01vgj8x4" data-path="src/pages/HomePage.tsx">
                    <motion.div
                      animate={{
                        rotateY: [0, 360]
                      }}
                      transition={{
                        duration: 20,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      className="w-80 h-80 mx-auto" data-id="lj1dltt96" data-path="src/pages/HomePage.tsx">

                      <div className="w-full h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-6xl" data-id="tuxog0n1m" data-path="src/pages/HomePage.tsx">
                        🥽
                      </div>
                    </motion.div>
                    
                    {/* Orbiting elements */}
                    <motion.div
                      animate={{
                        rotate: [0, 360]
                      }}
                      transition={{
                        duration: 15,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      className="absolute inset-0" data-id="oe4ok9exy" data-path="src/pages/HomePage.tsx">

                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-yellow-400 rounded-full" data-id="sdvmg3aes" data-path="src/pages/HomePage.tsx"></div>
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-400 rounded-full" data-id="bvdfxwrnq" data-path="src/pages/HomePage.tsx"></div>
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-red-400 rounded-full" data-id="h0jm5irx6" data-path="src/pages/HomePage.tsx"></div>
                      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-400 rounded-full" data-id="v4aechoqo" data-path="src/pages/HomePage.tsx"></div>
                    </motion.div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6 bg-gradient-to-r from-blue-600 to-purple-600" data-id="55nh9v2qd" data-path="src/pages/HomePage.tsx">
        <div className="max-w-4xl mx-auto text-center text-white" data-id="tbl27leow" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }} data-id="h018xs8qn" data-path="src/pages/HomePage.tsx">

            <h2 className="text-4xl font-bold mb-6" data-id="6g0cftk0m" data-path="src/pages/HomePage.tsx">ابدأ رحلتك التعليمية اليوم</h2>
            <p className="text-xl mb-8 text-blue-100" data-id="w0zq3rkby" data-path="src/pages/HomePage.tsx">
              انضم إلى آلاف الطلاب والمهنيين الذين يستكشفون المستقبل في الفيزياء الطبية
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4" data-id="dd6hgukqo" data-path="src/pages/HomePage.tsx">
              <Link to="/course" data-id="z1uc4it61" data-path="src/pages/HomePage.tsx">
                <Button size="lg" variant="secondary" className="text-blue-600 px-8 py-4" data-id="g2z02de72" data-path="src/pages/HomePage.tsx">
                  <Play className="w-6 h-6 ml-2" data-id="p927g0l0b" data-path="src/pages/HomePage.tsx" />
                  ابدأ الدورة المجانية
                </Button>
              </Link>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4" data-id="zpdbb3xot" data-path="src/pages/HomePage.tsx">
                <BookOpen className="w-6 h-6 ml-2" data-id="x8lnmslss" data-path="src/pages/HomePage.tsx" />
                تحميل المنهج
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>);

};

export default HomePage;