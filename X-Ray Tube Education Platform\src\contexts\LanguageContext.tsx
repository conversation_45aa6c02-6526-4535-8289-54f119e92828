import React, { createContext, useContext, useState, ReactNode } from 'react';

export type Language = 'en' | 'es';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Translation dictionaries
const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.concepts': 'Concepts',
    'nav.xrayTube': 'X-Ray Tube',
    'nav.circuit': 'Circuit Diagram',
    'nav.assistant': 'AI Assistant',
    'nav.language': 'Language',

    // Home Page
    'home.title': 'X-Ray Tube Educational Platform',
    'home.subtitle': 'Master the fundamentals of X-ray tube technology through interactive learning',
    'home.getStarted': 'Get Started',
    'home.exploreFeatures': 'Explore Features',
    'home.featuresTitle': 'Features',
    'home.feature1.title': 'Interactive Diagrams',
    'home.feature1.desc': 'Explore detailed X-ray tube components with interactive visualizations',
    'home.feature2.title': 'Circuit Analysis',
    'home.feature2.desc': 'Understand power supply circuits and electrical components',
    'home.feature3.title': 'Real-time Simulation',
    'home.feature3.desc': 'Adjust parameters and see immediate effects on X-ray production',
    'home.feature4.title': 'AI Assistant',
    'home.feature4.desc': 'Get instant answers to your X-ray technology questions',

    // Concepts Page
    'concepts.title': 'X-Ray Tube Concepts',
    'concepts.subtitle': 'Learn the fundamental principles behind X-ray tube operation',
    'concepts.basics': 'Basics',
    'concepts.advanced': 'Advanced',
    'concepts.physics': 'Physics',
    'concepts.progress': 'Learning Progress',
    'concepts.basicConcepts': 'Basic Concepts',
    'concepts.electronFlow': 'Electron Flow',
    'concepts.electronFlowDesc': 'Understanding how electrons travel from cathode to anode in the X-ray tube.',
    'concepts.xrayProduction': 'X-Ray Production',
    'concepts.xrayProductionDesc': 'Learn about Bremsstrahlung and characteristic X-ray production mechanisms.',
    'concepts.heatManagement': 'Heat Management',
    'concepts.heatManagementDesc': 'Critical cooling systems and thermal management in X-ray tubes.',
    'concepts.advancedConcepts': 'Advanced Concepts',
    'concepts.kvpEffects': 'kVp Effects',
    'concepts.kvpEffectsDesc': 'How tube voltage affects X-ray beam quality and quantity.',
    'concepts.masEffects': 'mAs Effects',
    'concepts.masEffectsDesc': 'Understanding the relationship between tube current, time, and X-ray quantity.',
    'concepts.beamFiltration': 'Beam Filtration',
    'concepts.beamFiltrationDesc': 'How filters modify X-ray beam characteristics for optimal imaging.',

    // X-Ray Tube Page
    'xray.title': 'X-Ray Tube Components',
    'xray.subtitle': 'Interactive exploration of X-ray tube anatomy and operation',
    'xray.overview': 'Overview',
    'xray.simulation': 'Simulation',
    'xray.components': 'Components',
    'xray.cathode': 'Cathode',
    'xray.anode': 'Anode',
    'xray.housing': 'Housing',
    'xray.coolant': 'Coolant',
    'xray.voltage': 'Tube Voltage (kVp)',
    'xray.current': 'Tube Current (mA)',
    'xray.rotationSpeed': 'Anode Rotation (RPM)',
    'xray.startSim': 'Start Simulation',
    'xray.stopSim': 'Stop Simulation',
    'xray.resetSim': 'Reset Simulation',
    'xray.simStatus': 'Simulation Status',
    'xray.heatGeneration': 'Heat Generation',
    'xray.xrayOutput': 'X-Ray Output',

    // Circuit Diagram Page
    'circuit.title': 'Circuit Diagrams',
    'circuit.subtitle': 'Understanding X-ray generator electrical circuits and power supply systems',
    'circuit.xrayCircuit': 'X-Ray Circuit',
    'circuit.powerSupply': 'Power Supply',
    'circuit.control': 'Control',
    'circuit.mainCircuit': 'Main X-Ray Circuit',
    'circuit.powerSupplySystem': 'Power Supply System',
    'circuit.highVoltage': 'High Voltage',
    'circuit.lowVoltage': 'Low Voltage',
    'circuit.transformer': 'Transformer',
    'circuit.rectifier': 'Rectifier',
    'circuit.smoothing': 'Smoothing',

    // AI Assistant Page
    'ai.title': 'AI Assistant',
    'ai.subtitle': 'Get instant answers to your X-ray technology questions',
    'ai.placeholder': 'Ask me anything about X-ray tubes, circuits, or imaging technology...',
    'ai.send': 'Send',
    'ai.thinking': 'Thinking...',
    'ai.welcome': 'Hello! I\'m your X-ray technology assistant. I can help you understand X-ray tubes, circuits, imaging principles, and more. What would you like to learn about?',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.close': 'Close',
    'common.save': 'Save',
    'common.cancel': 'Cancel'
  },
  es: {
    // Navigation
    'nav.home': 'Inicio',
    'nav.concepts': 'Conceptos',
    'nav.xrayTube': 'Tubo de Rayos X',
    'nav.circuit': 'Diagrama de Circuito',
    'nav.assistant': 'Asistente IA',
    'nav.language': 'Idioma',

    // Home Page
    'home.title': 'Plataforma Educativa de Tubos de Rayos X',
    'home.subtitle': 'Domina los fundamentos de la tecnología de tubos de rayos X a través del aprendizaje interactivo',
    'home.getStarted': 'Comenzar',
    'home.exploreFeatures': 'Explorar Características',
    'home.featuresTitle': 'Características',
    'home.feature1.title': 'Diagramas Interactivos',
    'home.feature1.desc': 'Explora componentes detallados del tubo de rayos X con visualizaciones interactivas',
    'home.feature2.title': 'Análisis de Circuitos',
    'home.feature2.desc': 'Comprende los circuitos de alimentación y componentes eléctricos',
    'home.feature3.title': 'Simulación en Tiempo Real',
    'home.feature3.desc': 'Ajusta parámetros y observa efectos inmediatos en la producción de rayos X',
    'home.feature4.title': 'Asistente IA',
    'home.feature4.desc': 'Obtén respuestas instantáneas a tus preguntas sobre tecnología de rayos X',

    // Concepts Page
    'concepts.title': 'Conceptos de Tubos de Rayos X',
    'concepts.subtitle': 'Aprende los principios fundamentales detrás del funcionamiento de tubos de rayos X',
    'concepts.basics': 'Básico',
    'concepts.advanced': 'Avanzado',
    'concepts.physics': 'Física',
    'concepts.progress': 'Progreso de Aprendizaje',
    'concepts.basicConcepts': 'Conceptos Básicos',
    'concepts.electronFlow': 'Flujo de Electrones',
    'concepts.electronFlowDesc': 'Comprende cómo viajan los electrones del cátodo al ánodo en el tubo de rayos X.',
    'concepts.xrayProduction': 'Producción de Rayos X',
    'concepts.xrayProductionDesc': 'Aprende sobre los mecanismos de producción de rayos X Bremsstrahlung y característicos.',
    'concepts.heatManagement': 'Gestión del Calor',
    'concepts.heatManagementDesc': 'Sistemas críticos de enfriamiento y gestión térmica en tubos de rayos X.',
    'concepts.advancedConcepts': 'Conceptos Avanzados',
    'concepts.kvpEffects': 'Efectos de kVp',
    'concepts.kvpEffectsDesc': 'Cómo el voltaje del tubo afecta la calidad y cantidad del haz de rayos X.',
    'concepts.masEffects': 'Efectos de mAs',
    'concepts.masEffectsDesc': 'Comprende la relación entre corriente del tubo, tiempo y cantidad de rayos X.',
    'concepts.beamFiltration': 'Filtración del Haz',
    'concepts.beamFiltrationDesc': 'Cómo los filtros modifican las características del haz de rayos X para imágenes óptimas.',

    // X-Ray Tube Page
    'xray.title': 'Componentes del Tubo de Rayos X',
    'xray.subtitle': 'Exploración interactiva de la anatomía y operación del tubo de rayos X',
    'xray.overview': 'Resumen',
    'xray.simulation': 'Simulación',
    'xray.components': 'Componentes',
    'xray.cathode': 'Cátodo',
    'xray.anode': 'Ánodo',
    'xray.housing': 'Carcasa',
    'xray.coolant': 'Refrigerante',
    'xray.voltage': 'Voltaje del Tubo (kVp)',
    'xray.current': 'Corriente del Tubo (mA)',
    'xray.rotationSpeed': 'Rotación del Ánodo (RPM)',
    'xray.startSim': 'Iniciar Simulación',
    'xray.stopSim': 'Detener Simulación',
    'xray.resetSim': 'Reiniciar Simulación',
    'xray.simStatus': 'Estado de Simulación',
    'xray.heatGeneration': 'Generación de Calor',
    'xray.xrayOutput': 'Salida de Rayos X',

    // Circuit Diagram Page
    'circuit.title': 'Diagramas de Circuito',
    'circuit.subtitle': 'Comprende los circuitos eléctricos del generador de rayos X y sistemas de alimentación',
    'circuit.xrayCircuit': 'Circuito de Rayos X',
    'circuit.powerSupply': 'Fuente de Alimentación',
    'circuit.control': 'Control',
    'circuit.mainCircuit': 'Circuito Principal de Rayos X',
    'circuit.powerSupplySystem': 'Sistema de Fuente de Alimentación',
    'circuit.highVoltage': 'Alto Voltaje',
    'circuit.lowVoltage': 'Bajo Voltaje',
    'circuit.transformer': 'Transformador',
    'circuit.rectifier': 'Rectificador',
    'circuit.smoothing': 'Suavizado',

    // AI Assistant Page
    'ai.title': 'Asistente IA',
    'ai.subtitle': 'Obtén respuestas instantáneas a tus preguntas sobre tecnología de rayos X',
    'ai.placeholder': 'Pregúntame cualquier cosa sobre tubos de rayos X, circuitos o tecnología de imágenes...',
    'ai.send': 'Enviar',
    'ai.thinking': 'Pensando...',
    'ai.welcome': '¡Hola! Soy tu asistente de tecnología de rayos X. Puedo ayudarte a entender tubos de rayos X, circuitos, principios de imágenes y más. ¿Qué te gustaría aprender?',

    // Common
    'common.loading': 'Cargando...',
    'common.error': 'Error',
    'common.back': 'Atrás',
    'common.next': 'Siguiente',
    'common.close': 'Cerrar',
    'common.save': 'Guardar',
    'common.cancel': 'Cancelar'
  }
};

export const LanguageProvider: React.FC<{children: ReactNode;}> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('en');

  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }} data-id="i127cp6ey" data-path="src/contexts/LanguageContext.tsx">
      {children}
    </LanguageContext.Provider>);

};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};