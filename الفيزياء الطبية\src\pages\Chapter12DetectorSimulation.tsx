import React from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ChevronDown,
  ChevronUp,
  Cpu,
  Zap,
  Lightbulb,
  BarChart3,
  Camera,
  Grid3X3,
  Layers,
  Target,
  Book,
  FileText,
  HelpCircle,
  Activity,
  Settings,
  Atom,
  Eye } from
'lucide-react';

const Chapter12DetectorSimulation = () => {
  const [openSections, setOpenSections] = React.useState<Record<string, boolean>>({});

  const toggleSection = (section: string) => {
    setOpenSections((prev) => ({ ...prev, [section]: !prev[section] }));
  };

  const learningObjectives = [
  "فهم طرق نمذجة ترسب الطاقة في مواد الكاشف المختلفة",
  "تعلم محاكاة انتشار ضوء الومض والتداخل البصري",
  "إتقان نمذجة جمع الشحنات والضوضاء الإلكترونية",
  "استيعاب تحليل الأنظمة الخطية المتتالية",
  "تطبيق تقنيات محاكاة تكوين صور الإسقاط الشعاعي",
  "دمج شبكات مكافحة التشتت في عمليات المحاكاة"];


  const keyTerms = [
  { term: "محاكاة مونت كارلو", definition: "تقنية رقمية لمحاكاة العمليات الفيزيائية العشوائية" },
  { term: "تحليل الأنظمة المتتالية", definition: "طريقة لتحليل أداء النظام من خلال سلسلة من المراحل" },
  { term: "انتشار ضوء الومض", definition: "توزيع الضوء المنبعث من نقطة واحدة في الكاشف" },
  { term: "التداخل البصري", definition: "انتشار الضوء بين البكسلات المجاورة" },
  { term: "ضوضاء كم الفوتونات", definition: "التقلبات الإحصائية في عدد الفوتونات المكتشفة" },
  { term: "ضوضاء إلكترونية", definition: "الضوضاء الناتجة عن دوائر القراءة الإلكترونية" },
  { term: "دالة انتشار النقطة", definition: "استجابة النظام لإشارة نقطية" },
  { term: "معامل الشبكة", definition: "النسبة بين ارتفاع الشبكة وعرض الفراغات بينها" }];


  const problems = [
  {
    id: 1,
    question: "احسب كفاءة امتصاص الأشعة السينية لطبقة CsI بسمك 500 ميكرومتر عند طاقة 60 keV.",
    solution: "باستخدام معامل التوهين الشامل μ = 2.1 cm⁻¹ لـ CsI عند 60 keV: كفاءة الامتصاص = 1 - exp(-μt) = 1 - exp(-2.1 × 0.05) = 1 - exp(-0.105) = 0.10 أو 10%"
  },
  {
    id: 2,
    question: "ما تأثير زيادة سمك طبقة الومض على دالة انتشار النقطة؟",
    solution: "زيادة سمك طبقة الومض يؤدي إلى: 1) زيادة انتشار الضوء الجانبي، 2) تدهور الدقة المكانية، 3) زيادة كفاءة امتصاص الأشعة، 4) تحسن نسبة الإشارة إلى الضوضاء."
  },
  {
    id: 3,
    question: "كيف تؤثر شبكة مكافحة التشتت بنسبة 10:1 على جودة الصورة؟",
    solution: "شبكة بنسبة 10:1 تقلل التشتت بحوالي 80-90%، تحسن التباين بنسبة 2-3 أضعاف، لكنها تزيد الجرعة المطلوبة بحوالي 2-3 أضعاف وقد تقلل الدقة المكانية قليلاً."
  }];


  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50" data-id="axsq84u51" data-path="src/pages/Chapter12DetectorSimulation.tsx">
      <Navigation data-id="4eyw9v7om" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
      <div className="container mx-auto px-4 py-8 max-w-4xl" data-id="ycgoi4trn" data-path="src/pages/Chapter12DetectorSimulation.tsx">
        <div className="text-center mb-12" data-id="e657lsg1t" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <div className="inline-flex items-center gap-3 bg-white px-6 py-3 rounded-full shadow-lg mb-6" data-id="ohfbgc58f" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <Cpu className="h-8 w-8 text-purple-600" data-id="55sd5ogpc" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
            <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent" data-id="r0brjar6n" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              الفصل الثاني عشر
            </span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4" data-id="rp7l9ylve" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            محاكاة استجابة الكاشف وتكوين الصورة
          </h1>
          <p className="text-xl text-gray-600 mb-6" data-id="75ujjobxb" data-path="src/pages/Chapter12DetectorSimulation.tsx">النمذجة الحاسوبية والمحاكاة</p>
          <div className="flex flex-wrap justify-center gap-3" data-id="a7ksnb9hd" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <Badge variant="secondary" className="px-4 py-2" data-id="35s9t2rav" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <Cpu className="h-4 w-4 mr-2" data-id="ghiedwnby" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              محاكاة رقمية
            </Badge>
            <Badge variant="secondary" className="px-4 py-2" data-id="0lyrse51a" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <Lightbulb className="h-4 w-4 mr-2" data-id="4kk7t3lqb" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              انتشار الضوء
            </Badge>
            <Badge variant="secondary" className="px-4 py-2" data-id="v8ii4olq2" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <Grid3X3 className="h-4 w-4 mr-2" data-id="jz2mrztgn" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              شبكات التشتت
            </Badge>
          </div>
        </div>

        {/* Learning Objectives */}
        <Card className="mb-8 border-l-4 border-l-purple-500" data-id="cvzh89uui" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <CardHeader data-id="igijt46e1" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CardTitle className="flex items-center gap-3" data-id="63yfb1d5c" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <Target className="h-6 w-6 text-purple-600" data-id="abh3dd8no" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              أهداف التعلم
            </CardTitle>
          </CardHeader>
          <CardContent data-id="tmof4r3ab" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <ul className="space-y-3" data-id="fbky20gsx" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              {learningObjectives.map((objective, index) =>
              <li key={index} className="flex items-start gap-3" data-id="ml58tln04" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="h-2 w-2 bg-purple-500 rounded-full mt-3 flex-shrink-0" data-id="at6tznbmt" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                  <span className="text-gray-700" data-id="zcvceqdue" data-path="src/pages/Chapter12DetectorSimulation.tsx">{objective}</span>
                </li>
              )}
            </ul>
          </CardContent>
        </Card>

        {/* Section 12.1: Energy Deposition Modeling */}
        <Card className="mb-6" data-id="kcdhd2p6h" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['energy-deposition']} onOpenChange={() => toggleSection('energy-deposition')} data-id="wng1hgw6s" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="sil2j0hpf" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="pxl262brb" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="u2qllmxw3" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="y5qjv7dt8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Atom className="h-6 w-6 text-red-600" data-id="60uds02hb" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="soffcfu7l" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.1 نمذجة ترسب الطاقة في مواد الكاشف</span>
                  </div>
                  {openSections['energy-deposition'] ? <ChevronUp data-id="d69vt31aj" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="z4zukfhdg" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="stn1tvtbo" data-path="src/pages/Chapter12DetectorSimulation.tsx">محاكاة التفاعلات والترسب</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="z1kz912fk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="0kg7bxi3h" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <Alert data-id="07ou8kmqc" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Atom className="h-4 w-4" data-id="hjs1s8f1d" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                  <AlertDescription data-id="qrrpx7e77" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    نمذجة ترسب الطاقة أساسية لفهم استجابة الكاشف وتحسين تصميمه.
                  </AlertDescription>
                </Alert>

                <div className="grid md:grid-cols-2 gap-6" data-id="3em1zwexl" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div data-id="0l4w0s0mr" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="gq9zg6fwg" data-path="src/pages/Chapter12DetectorSimulation.tsx">طرق المحاكاة:</h4>
                    <ul className="space-y-2 text-gray-700" data-id="giucpds02" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="3mj40rhbe" data-path="src/pages/Chapter12DetectorSimulation.tsx">• محاكاة مونت كارلو للتفاعلات</li>
                      <li data-id="ox4txtq5h" data-path="src/pages/Chapter12DetectorSimulation.tsx">• نمذجة النقل الإلكتروني</li>
                      <li data-id="qmsopttqu" data-path="src/pages/Chapter12DetectorSimulation.tsx">• حساب ترسب الطاقة المحلي</li>
                      <li data-id="lw4gcaoa7" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تتبع المسارات الثانوية</li>
                    </ul>
                  </div>
                  <div data-id="atr6zzd46" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="6z2tdzgz6" data-path="src/pages/Chapter12DetectorSimulation.tsx">العوامل المؤثرة:</h4>
                    <ul className="space-y-2 text-gray-700" data-id="8f2d1z4pg" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="pdrb7relk" data-path="src/pages/Chapter12DetectorSimulation.tsx">• طاقة الأشعة السينية الساقطة</li>
                      <li data-id="medfwu2uf" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تركيب المادة وكثافتها</li>
                      <li data-id="6a4mjljrx" data-path="src/pages/Chapter12DetectorSimulation.tsx">• هندسة الكاشف وسمكه</li>
                      <li data-id="4ny0ejzkt" data-path="src/pages/Chapter12DetectorSimulation.tsx">• وجود طبقات متعددة</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-red-50 p-4 rounded-lg" data-id="3kzdjy1cc" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-red-800 mb-2" data-id="8xvubantj" data-path="src/pages/Chapter12DetectorSimulation.tsx">أنواع التفاعلات المنمذجة:</h4>
                  <div className="grid md:grid-cols-3 gap-4 text-red-700" data-id="d9l9sw12i" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <div data-id="r6ecnnb5j" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold" data-id="5pf9q8y40" data-path="src/pages/Chapter12DetectorSimulation.tsx">التأثير الكهروضوئي:</h5>
                      <p className="text-sm" data-id="52zq1nl13" data-path="src/pages/Chapter12DetectorSimulation.tsx">امتصاص كامل للفوتون</p>
                    </div>
                    <div data-id="mgukmsirt" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold" data-id="xdbl9vaq6" data-path="src/pages/Chapter12DetectorSimulation.tsx">تشتت كومبتون:</h5>
                      <p className="text-sm" data-id="3bop0zk6n" data-path="src/pages/Chapter12DetectorSimulation.tsx">امتصاص جزئي مع تشتت</p>
                    </div>
                    <div data-id="3ph3a239m" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold" data-id="7l5uxlvkw" data-path="src/pages/Chapter12DetectorSimulation.tsx">التشتت المترابط:</h5>
                      <p className="text-sm" data-id="zebr31y9h" data-path="src/pages/Chapter12DetectorSimulation.tsx">تغيير اتجاه بدون فقدان طاقة</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.2: Light Propagation Simulation */}
        <Card className="mb-6" data-id="qjf2up3g6" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['light-propagation']} onOpenChange={() => toggleSection('light-propagation')} data-id="x716y3z7t" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="0tba44zm7" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="zyu345i0y" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="tqbg59yge" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="4twasvnik" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Lightbulb className="h-6 w-6 text-yellow-600" data-id="cpan6c1gm" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="t7sjkujqt" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.2 محاكاة انتشار ضوء الومض والتداخل البصري</span>
                  </div>
                  {openSections['light-propagation'] ? <ChevronUp data-id="9tovd22qt" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="i369g4fb7" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="dxs879vpc" data-path="src/pages/Chapter12DetectorSimulation.tsx">نمذجة الضوء والتداخل البصري</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="1mf6dlric" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="0zps3ef3y" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="y8ag2n8v0" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Card data-id="rq0b97y36" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="veaar4b0a" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg flex items-center gap-2" data-id="sz9nxvphf" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <Eye className="h-5 w-5 text-blue-600" data-id="aj7grppbx" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                        انتشار الضوء
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4" data-id="yr13k2dul" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold text-gray-800" data-id="u3tcxf0im" data-path="src/pages/Chapter12DetectorSimulation.tsx">المعاملات الرئيسية:</h5>
                      <ul className="space-y-2 text-gray-700" data-id="x9gcih4l6" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="94iaa9gbx" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معامل الانكسار للمادة</li>
                        <li data-id="w7av8okpm" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معامل الامتصاص البصري</li>
                        <li data-id="8wzhbcigp" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معامل التشتت</li>
                        <li data-id="6c3f7ekbm" data-path="src/pages/Chapter12DetectorSimulation.tsx">• عامل الانعكاس في الحدود</li>
                      </ul>
                      
                      <h5 className="font-semibold text-gray-800" data-id="iueudc5zy" data-path="src/pages/Chapter12DetectorSimulation.tsx">تقنيات المحاكاة:</h5>
                      <ul className="space-y-1 text-gray-700" data-id="za2srtgjh" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="1meanjl9t" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تتبع الأشعة الضوئية</li>
                        <li data-id="t4rlnsaox" data-path="src/pages/Chapter12DetectorSimulation.tsx">• طريقة مونت كارلو البصرية</li>
                        <li data-id="pns07a01g" data-path="src/pages/Chapter12DetectorSimulation.tsx">• نمذجة الانتشار</li>
                        <li data-id="i180s5v7l" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معادلات النقل الإشعاعي</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card data-id="7prct5qeo" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="0kvnnr3yq" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg flex items-center gap-2" data-id="ehyc7zjel" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <Layers className="h-5 w-5 text-green-600" data-id="k7dwfbwye" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                        التداخل البصري
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4" data-id="miur3sm7u" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold text-gray-800" data-id="0otr4g0mh" data-path="src/pages/Chapter12DetectorSimulation.tsx">مصادر التداخل:</h5>
                      <ul className="space-y-2 text-gray-700" data-id="ma28n58uz" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="l189oc0gf" data-path="src/pages/Chapter12DetectorSimulation.tsx">• انتشار الضوء الجانبي</li>
                        <li data-id="9otejc6wq" data-path="src/pages/Chapter12DetectorSimulation.tsx">• انعكاسات الحدود</li>
                        <li data-id="ds6jmfq8h" data-path="src/pages/Chapter12DetectorSimulation.tsx">• عيوب البنية البلورية</li>
                        <li data-id="vp36x16iy" data-path="src/pages/Chapter12DetectorSimulation.tsx">• طبقات الحماية</li>
                      </ul>
                      
                      <h5 className="font-semibold text-gray-800" data-id="88q8vrusr" data-path="src/pages/Chapter12DetectorSimulation.tsx">التأثيرات على الأداء:</h5>
                      <ul className="space-y-1 text-gray-700" data-id="abrlxq2yl" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="jbtkwbtue" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تدهور الدقة المكانية</li>
                        <li data-id="sssrj11i0" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تقليل التباين</li>
                        <li data-id="jvl9ctcuq" data-path="src/pages/Chapter12DetectorSimulation.tsx">• زيادة الضوضاء المتعلقة</li>
                        <li data-id="e9ujqguj5" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تأثير على MTF</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <div className="bg-yellow-50 p-4 rounded-lg" data-id="jrrypamqm" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-yellow-800 mb-2" data-id="tfj2sej1j" data-path="src/pages/Chapter12DetectorSimulation.tsx">دالة انتشار النقطة (PSF):</h4>
                  <p className="text-yellow-700" data-id="1gp4wd5a5" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    تصف كيفية انتشار الضوء من نقطة واحدة في الكاشف، وتحدد الدقة المكانية النهائية للنظام.
                  </p>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.3: Charge Collection and Electronic Noise */}
        <Card className="mb-6" data-id="v1tmcxynn" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['charge-collection']} onOpenChange={() => toggleSection('charge-collection')} data-id="3c27g4f01" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="0htn0csgt" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="82uhrw2l9" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="f2yt1vthm" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="1e2soxtsj" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Zap className="h-6 w-6 text-blue-600" data-id="j3k9ly0hk" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="rk1fr4s1x" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.3 نمذجة جمع الشحنات والضوضاء الإلكترونية</span>
                  </div>
                  {openSections['charge-collection'] ? <ChevronUp data-id="1fy1lav4z" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="d02jwqs1d" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="n9imev5em" data-path="src/pages/Chapter12DetectorSimulation.tsx">الشحنات والضوضاء الإلكترونية</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="fpn7xqfb7" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="x746rkful" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <div className="grid gap-6" data-id="3vcjep4fc" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="grid md:grid-cols-2 gap-4" data-id="h1fgs1lc6" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <div data-id="y6yygivy4" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="lur8mz1c3" data-path="src/pages/Chapter12DetectorSimulation.tsx">عملية جمع الشحنات:</h4>
                      <ul className="space-y-2 text-gray-700" data-id="exmn9pfb8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="zto85w9he" data-path="src/pages/Chapter12DetectorSimulation.tsx">• توليد أزواج الإلكترون-الثقب</li>
                        <li data-id="81k872qwx" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الانتشار والانسياق الكهربائي</li>
                        <li data-id="qtwjzfv0g" data-path="src/pages/Chapter12DetectorSimulation.tsx">• إعادة التركيب والالتقاط</li>
                        <li data-id="b2b0z0wvu" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التحكم بالمجال الكهربائي</li>
                      </ul>
                    </div>
                    <div data-id="or2accrgw" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="l6fmr43lc" data-path="src/pages/Chapter12DetectorSimulation.tsx">مصادر الضوضاء:</h4>
                      <ul className="space-y-2 text-gray-700" data-id="2l66167jl" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="sze0erjul" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء كم الفوتونات</li>
                        <li data-id="1bi8lwdaw" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الضوضاء الحرارية</li>
                        <li data-id="sqtzgbh2h" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء التيار المظلم</li>
                        <li data-id="orssnsbfj" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء دوائر القراءة</li>
                      </ul>
                    </div>
                  </div>

                  <Card data-id="jw4frkyjy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="qnupy1df6" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg" data-id="pmua1qhnq" data-path="src/pages/Chapter12DetectorSimulation.tsx">نماذج الضوضاء</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4" data-id="bkw1nel0t" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="grid md:grid-cols-2 gap-4" data-id="2b5u5gc0n" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <div data-id="3v61amfg1" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="07vhx3fjv" data-path="src/pages/Chapter12DetectorSimulation.tsx">الضوضاء الأولية:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="n0zeajvwj" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="cha8q72hy" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء بواسون للفوتونات</li>
                            <li data-id="7abyey2st" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تقلبات طاقة التفاعل</li>
                            <li data-id="nwo37rh5f" data-path="src/pages/Chapter12DetectorSimulation.tsx">• كفاءة التحويل المتغيرة</li>
                          </ul>
                        </div>
                        <div data-id="5eb56e3rx" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="ma58j6oev" data-path="src/pages/Chapter12DetectorSimulation.tsx">الضوضاء الثانوية:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="19p4c3lah" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="5qz5oyhb6" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تقلبات التضخيم</li>
                            <li data-id="r9enixckh" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء دوائر القراءe</li>
                            <li data-id="tslqi1ubm" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التداخل الكهرومغناطيسي</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg" data-id="p8ot6l35g" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-blue-800 mb-2" data-id="dil094kyo" data-path="src/pages/Chapter12DetectorSimulation.tsx">عامل فانو (Fano Factor):</h4>
                  <p className="text-blue-700" data-id="imrcqitl3" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    يصف التقلبات الإحصائية في عدد حاملات الشحنة المولدة، وهو أقل من الوحدة للمواد شبه الموصلة.
                  </p>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.4: Cascaded Linear Systems Analysis */}
        <Card className="mb-6" data-id="f95iti0wm" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['cascaded-systems']} onOpenChange={() => toggleSection('cascaded-systems')} data-id="jss4rs21x" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="x6zo5tzx3" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="onl4df8fb" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="1nnluuyky" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="a5w0eyqty" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <BarChart3 className="h-6 w-6 text-green-600" data-id="b34pmq9rw" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="515s8j04u" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.4 تحليل الأنظمة الخطية المتتالية لنمذجة الكاشف</span>
                  </div>
                  {openSections['cascaded-systems'] ? <ChevronUp data-id="enkj62hoz" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="6cxl4927o" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="azcbhp3mm" data-path="src/pages/Chapter12DetectorSimulation.tsx">نمذجة النظام كسلسلة من المراحل</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="n046fj130" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="mxp8m1buk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <Alert data-id="vlymyt038" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Activity className="h-4 w-4" data-id="pwgterz34" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                  <AlertDescription data-id="bbruv02m8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    تحليل الأنظمة المتتالية يسمح بنمذجة دقيقة لأداء الكاشف من خلال تقسيمه لمراحل منفصلة.
                  </AlertDescription>
                </Alert>

                <div className="grid gap-6" data-id="bx3aynxwx" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Card data-id="wydejg07r" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="oq06yjqeo" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg" data-id="7jetmd1wj" data-path="src/pages/Chapter12DetectorSimulation.tsx">مراحل النظام</CardTitle>
                    </CardHeader>
                    <CardContent data-id="am1ze9nr5" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="grid md:grid-cols-2 gap-4" data-id="peqlnopzr" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <div data-id="au47duavj" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="yzaw5qqvb" data-path="src/pages/Chapter12DetectorSimulation.tsx">المرحلة الأولى - الامتصاص:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="8e5yzuf7c" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="evhpodpfb" data-path="src/pages/Chapter12DetectorSimulation.tsx">• امتصاص الأشعة السينية</li>
                            <li data-id="ysi7yap08" data-path="src/pages/Chapter12DetectorSimulation.tsx">• كفاءة الكم الأولية</li>
                            <li data-id="s5m73lc2s" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء بواسون</li>
                          </ul>
                        </div>
                        <div data-id="89s29z4po" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="3mhh48wu9" data-path="src/pages/Chapter12DetectorSimulation.tsx">المرحلة الثانية - التحويل:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="wn89midle" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="doi8mgoo5" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تحويل الطاقة إلى ضوء/شحنة</li>
                            <li data-id="yv0je19lz" data-path="src/pages/Chapter12DetectorSimulation.tsx">• كسب التحويل</li>
                            <li data-id="6xkaxc0f9" data-path="src/pages/Chapter12DetectorSimulation.tsx">• انتشار الإشارة</li>
                          </ul>
                        </div>
                        <div data-id="ymmuvo0bk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="dttptbrvr" data-path="src/pages/Chapter12DetectorSimulation.tsx">المرحلة الثالثة - الكشف:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="nqwkaa87k" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="m0enju9j8" data-path="src/pages/Chapter12DetectorSimulation.tsx">• جمع الضوء/الشحنة</li>
                            <li data-id="kquimb1tz" data-path="src/pages/Chapter12DetectorSimulation.tsx">• كفاءة الجمع</li>
                            <li data-id="6gtyrjhjf" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء إضافية</li>
                          </ul>
                        </div>
                        <div data-id="h9n1mf121" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="3ol1gnlcc" data-path="src/pages/Chapter12DetectorSimulation.tsx">المرحلة النهائية - المعالجة:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="5vkybz4vy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="k75tgk2p2" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التضخيم الإلكتروني</li>
                            <li data-id="cakewfupi" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الرقمنة</li>
                            <li data-id="zsdnovijp" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معالجة الإشارة</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card data-id="yrjbhabd7" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="z2e9gufox" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg" data-id="cis7lal3p" data-path="src/pages/Chapter12DetectorSimulation.tsx">المعادلات الأساسية</CardTitle>
                    </CardHeader>
                    <CardContent data-id="4w7hgxbp8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="space-y-4" data-id="bmwoyn70n" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <div className="bg-gray-50 p-4 rounded-lg" data-id="ei1rifrmy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold mb-2" data-id="nqbfwj1av" data-path="src/pages/Chapter12DetectorSimulation.tsx">كفاءة الكم الاستقصائية الإجمالية:</h5>
                          <p className="font-mono" data-id="0mki5dzjo" data-path="src/pages/Chapter12DetectorSimulation.tsx">DQE = η₀ × T₁² × T₂² × ... × Tₙ²</p>
                          <p className="text-sm text-gray-600 mt-1" data-id="uoutaiaz7" data-path="src/pages/Chapter12DetectorSimulation.tsx">حيث η₀ هي كفاءة الكم الأولية و Tᵢ هي نقل كل مرحلة</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg" data-id="idz2hybp7" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold mb-2" data-id="6iy1bhjl7" data-path="src/pages/Chapter12DetectorSimulation.tsx">دالة نقل التعديل الإجمالية:</h5>
                          <p className="font-mono" data-id="st1yyu6zj" data-path="src/pages/Chapter12DetectorSimulation.tsx">MTF_total = MTF₁ × MTF₂ × ... × MTFₙ</p>
                          <p className="text-sm text-gray-600 mt-1" data-id="g4hmfi2mx" data-path="src/pages/Chapter12DetectorSimulation.tsx">حاصل ضرب دوال النقل لكل مرحلة</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.5: Radiographic Projection Image Formation */}
        <Card className="mb-6" data-id="1j44i4p0g" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['image-formation']} onOpenChange={() => toggleSection('image-formation')} data-id="7x8g6orai" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="xjk6apei5" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="tqk5gzrpm" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="ob3svoo3u" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="w4ihtwnvi" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Camera className="h-6 w-6 text-indigo-600" data-id="a440uedf3" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="0u90jq33v" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.5 محاكاة تكوين صورة الإسقاط الشعاعي</span>
                  </div>
                  {openSections['image-formation'] ? <ChevronUp data-id="k6dsj9srg" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="aaez00do9" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="51adruulf" data-path="src/pages/Chapter12DetectorSimulation.tsx">من الجسم إلى الصورة النهائية</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="pj4fol7z9" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="07opycxuq" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="5os9v29om" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div data-id="f3hga1vwe" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="xpur26494" data-path="src/pages/Chapter12DetectorSimulation.tsx">مراحل تكوين الصورة:</h4>
                    <ol className="space-y-2 text-gray-700 list-decimal list-inside" data-id="0q8e1otsl" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="qhqye0hcc" data-path="src/pages/Chapter12DetectorSimulation.tsx">توليد حزمة الأشعة السينية</li>
                      <li data-id="0r39y2tsu" data-path="src/pages/Chapter12DetectorSimulation.tsx">التفاعل مع الجسم المفحوص</li>
                      <li data-id="9i38u701n" data-path="src/pages/Chapter12DetectorSimulation.tsx">تشكيل الإسقاط الأولي</li>
                      <li data-id="prwpxtjvb" data-path="src/pages/Chapter12DetectorSimulation.tsx">الكشف وتكوين الإشارة</li>
                      <li data-id="24bnrex30" data-path="src/pages/Chapter12DetectorSimulation.tsx">معالجة وتحسين الصورة</li>
                    </ol>
                  </div>
                  <div data-id="l5beoaazd" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="2v918gner" data-path="src/pages/Chapter12DetectorSimulation.tsx">العوامل المؤثرة:</h4>
                    <ul className="space-y-2 text-gray-700" data-id="igw1hy1vk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="45670yux6" data-path="src/pages/Chapter12DetectorSimulation.tsx">• هندسة النظام</li>
                      <li data-id="cm4id3eqc" data-path="src/pages/Chapter12DetectorSimulation.tsx">• خصائص حزمة الأشعة</li>
                      <li data-id="eusiyfvgx" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تركيب الجسم المفحوص</li>
                      <li data-id="i1os074qp" data-path="src/pages/Chapter12DetectorSimulation.tsx">• خصائص الكاشف</li>
                      <li data-id="mth5munmo" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الحركة أثناء التصوير</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-indigo-50 p-4 rounded-lg" data-id="u66kalvac" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-indigo-800 mb-2" data-id="hv3q92jlm" data-path="src/pages/Chapter12DetectorSimulation.tsx">معادلة تكوين الصورة الأساسية:</h4>
                  <p className="font-mono text-indigo-700" data-id="5u2pknm9i" data-path="src/pages/Chapter12DetectorSimulation.tsx">I(x,y) = I₀ × exp(-∫μ(x,y,z)dz) + S(x,y)</p>
                  <p className="text-indigo-600 text-sm mt-2" data-id="5njf7upkd" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    حيث I₀ هي الشدة الأولية، μ معامل التوهين، وS الإشعاع المتشتت
                  </p>
                </div>

                <Card data-id="jru50ufan" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <CardHeader data-id="ywa8f2n8k" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardTitle className="text-lg" data-id="i1rgd8o6o" data-path="src/pages/Chapter12DetectorSimulation.tsx">محاكاة التشتت</CardTitle>
                  </CardHeader>
                  <CardContent data-id="p6u53wufb" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <div className="grid md:grid-cols-2 gap-4" data-id="xe2rvt80p" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div data-id="n9idt4yre" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <h5 className="font-semibold text-gray-800 mb-2" data-id="xyn74a9bd" data-path="src/pages/Chapter12DetectorSimulation.tsx">أنواع التشتت:</h5>
                        <ul className="space-y-1 text-gray-700" data-id="tcqdcxod4" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <li data-id="r6gkth9i2" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التشتت الأولي من الجسم</li>
                          <li data-id="ro2xd7ilg" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التشتت المتعدد</li>
                          <li data-id="sh8ssgnvf" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التشتت من الطاولة والدعامات</li>
                        </ul>
                      </div>
                      <div data-id="3dhhlkr4i" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <h5 className="font-semibold text-gray-800 mb-2" data-id="gzm354f73" data-path="src/pages/Chapter12DetectorSimulation.tsx">التأثيرات:</h5>
                        <ul className="space-y-1 text-gray-700" data-id="p5sxm1hp3" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <li data-id="licj5g30u" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تقليل التباين</li>
                          <li data-id="78e9g53nr" data-path="src/pages/Chapter12DetectorSimulation.tsx">• إضافة ضوضاء منظمة</li>
                          <li data-id="f32j66y8s" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تدهور جودة الصورة</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.6: Anti-scatter Grids */}
        <Card className="mb-6" data-id="mq8880uv2" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['anti-scatter']} onOpenChange={() => toggleSection('anti-scatter')} data-id="jtns16a2q" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="6p0t40jz2" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="3juxn2ppn" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="zvf5gpm6d" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="hdvxeege7" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Grid3X3 className="h-6 w-6 text-orange-600" data-id="jtz0fw8m2" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="n00x7qrje" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.6 دمج شبكات مكافحة التشتت في عمليات المحاكاة</span>
                  </div>
                  {openSections['anti-scatter'] ? <ChevronUp data-id="sfpudji8c" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="dhjvzks0t" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="iexcu4czu" data-path="src/pages/Chapter12DetectorSimulation.tsx">تصميم ونمذجة الشبكات</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="ylogvn83d" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="fl51usbbt" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <div className="grid gap-6" data-id="h7vx2zjpt" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Card data-id="buowcvyw8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="k3ztt66qy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg flex items-center gap-2" data-id="zxkjvlyxx" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <Settings className="h-5 w-5 text-blue-600" data-id="2c9ogbhbu" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                        12.6.1 معلمات تصميم الشبكة
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="t4ndnypao" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="grid md:grid-cols-3 gap-4" data-id="gjatp88zi" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <div data-id="ixwn7je72" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="tp6gh3ic8" data-path="src/pages/Chapter12DetectorSimulation.tsx">النسبة (Ratio):</h5>
                          <ul className="space-y-1 text-gray-700" data-id="cm38kd505" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="sxzrd7ljb" data-path="src/pages/Chapter12DetectorSimulation.tsx">• نسبة الارتفاع إلى العرض</li>
                            <li data-id="hu1lh792v" data-path="src/pages/Chapter12DetectorSimulation.tsx">• القيم الشائعة: 6:1 إلى 16:1</li>
                            <li data-id="hj40vjme7" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تحدد قدرة رفض التشتت</li>
                          </ul>
                        </div>
                        <div data-id="bo92k30zv" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="pks01ppqq" data-path="src/pages/Chapter12DetectorSimulation.tsx">التردد (Frequency):</h5>
                          <ul className="space-y-1 text-gray-700" data-id="47p954qr1" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="yx73xzdy2" data-path="src/pages/Chapter12DetectorSimulation.tsx">• عدد الخطوط لكل بوصة</li>
                            <li data-id="rnjp1mns1" data-path="src/pages/Chapter12DetectorSimulation.tsx">• القيم الشائعة: 40-200 خط/بوصة</li>
                            <li data-id="9or1o8t9r" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تؤثر على ظهور خطوط الشبكة</li>
                          </ul>
                        </div>
                        <div data-id="sk0hj9wm9" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="0rdyxx3r1" data-path="src/pages/Chapter12DetectorSimulation.tsx">المادة:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="985jtxsi1" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="km1bf8szx" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الرصاص (تقليدي)</li>
                            <li data-id="tqbqiy0dt" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التنغستن (عالي الأداء)</li>
                            <li data-id="wyrxhcq26" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ألياف الكربون (منخفض الجرعة)</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card data-id="q2w5anqty" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="s31xloa9k" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg flex items-center gap-2" data-id="40k9dzapa" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <BarChart3 className="h-5 w-5 text-green-600" data-id="bt8n3bmfr" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                        12.6.2 نمذجة التوهين الشبكي ورفض التشتت
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="7m131vwwi" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="space-y-4" data-id="6iuoqzj4f" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <div className="grid md:grid-cols-2 gap-4" data-id="qmv1q47zg" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <div data-id="h98c1yeyf" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <h5 className="font-semibold text-gray-800 mb-2" data-id="7s6r59cxb" data-path="src/pages/Chapter12DetectorSimulation.tsx">معاملات الأداء:</h5>
                            <ul className="space-y-1 text-gray-700" data-id="bzpu9udj4" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                              <li data-id="eomo4d4v0" data-path="src/pages/Chapter12DetectorSimulation.tsx">• عامل تحسين التباين (CIF)</li>
                              <li data-id="e5k53vdjf" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معامل البريموس (Bucky factor)</li>
                              <li data-id="qpdv0stju" data-path="src/pages/Chapter12DetectorSimulation.tsx">• نسبة رفض التشتت (SPR)</li>
                              <li data-id="yu16hpnpr" data-path="src/pages/Chapter12DetectorSimulation.tsx">• عامل الاختيارية</li>
                            </ul>
                          </div>
                          <div data-id="i7rei57bc" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <h5 className="font-semibold text-gray-800 mb-2" data-id="pgz5q1ain" data-path="src/pages/Chapter12DetectorSimulation.tsx">العيوب والقيود:</h5>
                            <ul className="space-y-1 text-gray-700" data-id="7zp0qpacm" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                              <li data-id="um8rg2gnj" data-path="src/pages/Chapter12DetectorSimulation.tsx">• زيادة الجرعة المطلوبة</li>
                              <li data-id="66w4d9h42" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ظهور خطوط الشبكة</li>
                              <li data-id="tuzkkezc0" data-path="src/pages/Chapter12DetectorSimulation.tsx">• قطع الإشعاع الأولي</li>
                              <li data-id="tgfywuu8h" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تحديد زاوية الحزمة</li>
                            </ul>
                          </div>
                        </div>

                        <div className="bg-orange-50 p-4 rounded-lg" data-id="iie6e3w10" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-orange-800 mb-2" data-id="6f09ki9kk" data-path="src/pages/Chapter12DetectorSimulation.tsx">معادلات الأداء:</h5>
                          <div className="space-y-2" data-id="qeuxijcg2" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <p className="font-mono text-orange-700" data-id="ffhayic0o" data-path="src/pages/Chapter12DetectorSimulation.tsx">CIF = (C_with_grid / C_without_grid)</p>
                            <p className="font-mono text-orange-700" data-id="c7cwx919f" data-path="src/pages/Chapter12DetectorSimulation.tsx">Bucky Factor = (Exposure_with_grid / Exposure_without_grid)</p>
                            <p className="text-orange-600 text-sm" data-id="5q5od15jb" data-path="src/pages/Chapter12DetectorSimulation.tsx">حيث C هو التباين والـ Exposure هو التعرض المطلوب</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.7: Post-acquisition Processing */}
        <Card className="mb-6" data-id="u0fhfg1wk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['post-processing']} onOpenChange={() => toggleSection('post-processing')} data-id="s0yh2fux6" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="mh9zgd6qe" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="pscj6ho1k" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="shmb5jslq" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="dmjzvqi49" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Settings className="h-6 w-6 text-teal-600" data-id="2xwdcesxf" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="gmu3jcj7u" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.7 خطوات معالجة الصور بعد الاستحواذ</span>
                  </div>
                  {openSections['post-processing'] ? <ChevronUp data-id="i4ts16l7p" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="pn7zclpti" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="fjn4ppmzu" data-path="src/pages/Chapter12DetectorSimulation.tsx">نظرة عامة موجزة على المعالجة</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="mrumozapb" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="r9z0dzfne" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <Alert data-id="bqzhxik9g" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Settings className="h-4 w-4" data-id="94dvkp4sw" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                  <AlertDescription data-id="hflso4xys" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    معالجة الصور بعد الاستحواذ تحسن جودة الصورة وتحضرها للعرض والتشخيص.
                  </AlertDescription>
                </Alert>

                <div className="grid md:grid-cols-2 gap-6" data-id="fhjin3jwf" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div data-id="fotib3thk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="3ra05s88x" data-path="src/pages/Chapter12DetectorSimulation.tsx">خطوات المعالجة الأساسية:</h4>
                    <ol className="space-y-2 text-gray-700 list-decimal list-inside" data-id="mr03bq1w3" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="ryr7i264g" data-path="src/pages/Chapter12DetectorSimulation.tsx">تصحيح البكسلات المعيبة</li>
                      <li data-id="7ulbxpwox" data-path="src/pages/Chapter12DetectorSimulation.tsx">تطبيق تصحيح الكسب والإزاحة</li>
                      <li data-id="38kn6f8ik" data-path="src/pages/Chapter12DetectorSimulation.tsx">إزالة الضوضاء</li>
                      <li data-id="p0tk2hjbh" data-path="src/pages/Chapter12DetectorSimulation.tsx">تحسين التباين</li>
                      <li data-id="t9gfykun7" data-path="src/pages/Chapter12DetectorSimulation.tsx">توضيح الحواف</li>
                      <li data-id="ueflb4l5f" data-path="src/pages/Chapter12DetectorSimulation.tsx">ضبط النافذة والمستوى</li>
                    </ol>
                  </div>
                  <div data-id="88j9hrefa" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="cm2yw38jf" data-path="src/pages/Chapter12DetectorSimulation.tsx">تقنيات متقدمة:</h4>
                    <ul className="space-y-2 text-gray-700" data-id="54tof3285" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="ipx3oiskk" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تقليل الضوضاء التكيفي</li>
                      <li data-id="6tua2dvbi" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تحسين التباين متعدد المقاييس</li>
                      <li data-id="rypftl0i6" data-path="src/pages/Chapter12DetectorSimulation.tsx">• إزالة تشويه الشبكة</li>
                      <li data-id="zdgigm5tw" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التصحيح الهندسي</li>
                      <li data-id="81ihskv8r" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معايرة الطيف الرقمي</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-teal-50 p-4 rounded-lg" data-id="y3kbsyaaw" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-teal-800 mb-2" data-id="hoia579dp" data-path="src/pages/Chapter12DetectorSimulation.tsx">اعتبارات مهمة:</h4>
                  <ul className="text-teal-700 space-y-1" data-id="bos5os0ov" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <li data-id="q9iqp3rvy" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الحفاظ على المعلومات التشخيصية</li>
                    <li data-id="llj9fkwwo" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تجنب الإفراط في المعالجة</li>
                    <li data-id="bdcdn16mj" data-path="src/pages/Chapter12DetectorSimulation.tsx">• مراعاة متطلبات التشخيص المختلفة</li>
                    <li data-id="hs2f5b7o5" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضمان الاتساق بين الصور</li>
                  </ul>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Key Terms */}
        <Card className="mb-8 border-l-4 border-l-purple-500" data-id="cx81bkw0r" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <CardHeader data-id="9ne92o9of" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CardTitle className="flex items-center gap-3" data-id="uj7dozlrg" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <Book className="h-6 w-6 text-purple-600" data-id="ovhl72p0u" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              المصطلحات الرئيسية
            </CardTitle>
          </CardHeader>
          <CardContent data-id="u7voc16fv" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <div className="grid md:grid-cols-2 gap-4" data-id="aud2u72yj" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              {keyTerms.map((item, index) =>
              <div key={index} className="bg-gray-50 p-4 rounded-lg" data-id="oqfb78278" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-gray-800 mb-2" data-id="1yxr9yqto" data-path="src/pages/Chapter12DetectorSimulation.tsx">{item.term}</h4>
                  <p className="text-gray-700 text-sm" data-id="vu32x2hjf" data-path="src/pages/Chapter12DetectorSimulation.tsx">{item.definition}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Problems Section */}
        <Card className="mb-8 border-l-4 border-l-orange-500" data-id="hv9x0bxpv" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <CardHeader data-id="c1hyex0no" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CardTitle className="flex items-center gap-3" data-id="3n65rykev" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <HelpCircle className="h-6 w-6 text-orange-600" data-id="ajsjcxwws" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              مشكلات وتمارين
            </CardTitle>
          </CardHeader>
          <CardContent data-id="awqq3v03z" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <div className="space-y-6" data-id="728mo519b" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              {problems.map((problem) =>
              <Collapsible key={problem.id} data-id="wgnvl69aa" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <CollapsibleTrigger asChild data-id="dd4pe3nwz" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <div className="cursor-pointer bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors" data-id="kqwflksda" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="flex items-center justify-between" data-id="iy88w3k9a" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <h4 className="font-semibold text-gray-800" data-id="fgoytuhp7" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          المشكلة {problem.id}
                        </h4>
                        <ChevronDown className="h-4 w-4" data-id="4n9sv4kda" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                      </div>
                      <p className="text-gray-700 mt-2" data-id="ful5hoj5i" data-path="src/pages/Chapter12DetectorSimulation.tsx">{problem.question}</p>
                    </div>
                  </CollapsibleTrigger>
                  <CollapsibleContent data-id="1ebqmr6py" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <div className="mt-4 p-4 bg-green-50 rounded-lg border-r-4 border-r-green-500" data-id="1c2p5rnjq" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold text-green-800 mb-2" data-id="wm089bz3t" data-path="src/pages/Chapter12DetectorSimulation.tsx">الحل:</h5>
                      <p className="text-green-700" data-id="lsaepifgx" data-path="src/pages/Chapter12DetectorSimulation.tsx">{problem.solution}</p>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              )}
            </div>
          </CardContent>
        </Card>

        {/* References */}
        <Card className="border-l-4 border-l-green-500" data-id="hdw8hsozh" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <CardHeader data-id="4nv9btb3s" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CardTitle className="flex items-center gap-3" data-id="yyy0rg1fk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <FileText className="h-6 w-6 text-green-600" data-id="ixhj0tvqe" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              المراجع
            </CardTitle>
          </CardHeader>
          <CardContent data-id="3tn19r8tx" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <ol className="space-y-2 text-gray-700 list-decimal list-inside" data-id="j9874kx4d" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <li data-id="rnfapj6io" data-path="src/pages/Chapter12DetectorSimulation.tsx">Boone, J. M., and Seibert, J. A. "An accurate method for computer-generating tungsten anode x-ray spectra." Medical Physics, 24(11), 1661-1670, 1997.</li>
              <li data-id="3gejplukn" data-path="src/pages/Chapter12DetectorSimulation.tsx">Cunningham, I. A. "Applied linear-systems theory." Handbook of Medical Imaging, Vol. 1, SPIE Press, 2000.</li>
              <li data-id="nsvkm2q59" data-path="src/pages/Chapter12DetectorSimulation.tsx">Zhao, W., and Rowlands, J. A. "X-ray imaging using amorphous selenium." Physics in Medicine & Biology, 40(11), 1846-1864, 1995.</li>
              <li data-id="7ksh1s6zc" data-path="src/pages/Chapter12DetectorSimulation.tsx">Swank, R. K. "Absorption and noise in x-ray phosphors." Journal of Applied Physics, 44(9), 4199-4203, 1973.</li>
              <li data-id="gjvisgcjt" data-path="src/pages/Chapter12DetectorSimulation.tsx">Lubberts, G. "Random noise produced by x-ray fluorescent screens." Journal of the Optical Society of America, 58(11), 1475-1483, 1968.</li>
              <li data-id="lrdv9mmy6" data-path="src/pages/Chapter12DetectorSimulation.tsx">Barrett, H. H., and Swindell, W. "Radiological Imaging: The Theory of Image Formation, Detection, and Processing." Academic Press, 1996.</li>
            </ol>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default Chapter12DetectorSimulation;