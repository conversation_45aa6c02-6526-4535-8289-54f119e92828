import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { ChevronRight, Zap, Filter, Calculator, BookOpen, Users, Award } from 'lucide-react';

interface ConceptCardProps {
  title: string;
  definition: string;
  importance: string;
  icon: React.ReactNode;
}

const ConceptCard: React.FC<ConceptCardProps> = ({ title, definition, importance, icon }) =>
<Card className="h-full transition-all duration-300 hover:shadow-lg hover:scale-105" data-id="5o97vn675" data-path="src/components/XRayFilteringEducation.tsx">
    <CardHeader data-id="ekou0poe4" data-path="src/components/XRayFilteringEducation.tsx">
      <div className="flex items-center gap-3" data-id="07n4fk9lv" data-path="src/components/XRayFilteringEducation.tsx">
        <div className="p-2 rounded-lg bg-blue-100 text-blue-600" data-id="0mfs2pvu0" data-path="src/components/XRayFilteringEducation.tsx">
          {icon}
        </div>
        <CardTitle className="text-lg" data-id="nz2ai2drc" data-path="src/components/XRayFilteringEducation.tsx">{title}</CardTitle>
      </div>
    </CardHeader>
    <CardContent data-id="kfkiupqst" data-path="src/components/XRayFilteringEducation.tsx">
      <CardDescription className="mb-3 text-gray-700 leading-relaxed" data-id="wkxz3ibf4" data-path="src/components/XRayFilteringEducation.tsx">
        {definition}
      </CardDescription>
      <div className="p-3 bg-green-50 rounded-lg border-r-4 border-green-400" data-id="0dxugtync" data-path="src/components/XRayFilteringEducation.tsx">
        <p className="text-sm font-medium text-green-800" data-id="d46i56d40" data-path="src/components/XRayFilteringEducation.tsx">الأهمية السريرية:</p>
        <p className="text-sm text-green-700 mt-1" data-id="905c8owow" data-path="src/components/XRayFilteringEducation.tsx">{importance}</p>
      </div>
    </CardContent>
  </Card>;


interface FormulaCardProps {
  title: string;
  formula: string;
  description: string;
}

const FormulaCard: React.FC<FormulaCardProps> = ({ title, formula, description }) =>
<Card className="transition-all duration-300 hover:shadow-md" data-id="bsb02wx1b" data-path="src/components/XRayFilteringEducation.tsx">
    <CardHeader data-id="hnxsgeldx" data-path="src/components/XRayFilteringEducation.tsx">
      <CardTitle className="text-lg flex items-center gap-2" data-id="lcfzsv3pa" data-path="src/components/XRayFilteringEducation.tsx">
        <Calculator className="w-5 h-5 text-blue-600" data-id="nx6svhfd0" data-path="src/components/XRayFilteringEducation.tsx" />
        {title}
      </CardTitle>
    </CardHeader>
    <CardContent data-id="q77tflecb" data-path="src/components/XRayFilteringEducation.tsx">
      <div className="bg-gray-50 p-4 rounded-lg font-mono text-lg text-center mb-3" data-id="1pro403jn" data-path="src/components/XRayFilteringEducation.tsx">
        {formula}
      </div>
      <p className="text-gray-700 text-sm leading-relaxed" data-id="zh90unj3s" data-path="src/components/XRayFilteringEducation.tsx">{description}</p>
    </CardContent>
  </Card>;


const XRayFilteringEducation: React.FC = () => {
  const [currentSection, setCurrentSection] = useState('basics');
  const [progress, setProgress] = useState(15);

  const sections = [
  { id: 'basics', title: 'الأساسيات', icon: <BookOpen className="w-4 h-4" data-id="xojvuhyip" data-path="src/components/XRayFilteringEducation.tsx" /> },
  { id: 'hvl-tvl', title: 'HVL & TVL', icon: <Calculator className="w-4 h-4" data-id="zwmb8s342" data-path="src/components/XRayFilteringEducation.tsx" /> },
  { id: 'beam-hardening', title: 'تقوية الشعاع', icon: <Zap className="w-4 h-4" data-id="zejm7zwx0" data-path="src/components/XRayFilteringEducation.tsx" /> },
  { id: 'filter-types', title: 'أنواع المرشحات', icon: <Filter className="w-4 h-4" data-id="zprzg9yr0" data-path="src/components/XRayFilteringEducation.tsx" /> },
  { id: 'clinical', title: 'التطبيقات السريرية', icon: <Users className="w-4 h-4" data-id="hm2042rvz" data-path="src/components/XRayFilteringEducation.tsx" /> },
  { id: 'assessment', title: 'التقييم', icon: <Award className="w-4 h-4" data-id="wbsxfz4f7" data-path="src/components/XRayFilteringEducation.tsx" /> }];


  const handleSectionChange = (sectionId: string) => {
    setCurrentSection(sectionId);
    const sectionIndex = sections.findIndex((s) => s.id === sectionId);
    setProgress((sectionIndex + 1) * (100 / sections.length));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50" dir="rtl" data-id="dex1nfkgn" data-path="src/components/XRayFilteringEducation.tsx">
      {/* Header */}
      <header className="bg-white shadow-sm border-b" data-id="x6110bodg" data-path="src/components/XRayFilteringEducation.tsx">
        <div className="max-w-7xl mx-auto px-4 py-6" data-id="wu2cqcdri" data-path="src/components/XRayFilteringEducation.tsx">
          <div className="flex items-center justify-between" data-id="697p1d6tm" data-path="src/components/XRayFilteringEducation.tsx">
            <div data-id="h9fdph4in" data-path="src/components/XRayFilteringEducation.tsx">
              <h1 className="text-3xl font-bold text-gray-900" data-id="fumhj08bp" data-path="src/components/XRayFilteringEducation.tsx">
                ترشيح شعاع الأشعة السينية والجودة
              </h1>
              <p className="text-gray-600 mt-2" data-id="qmzpj1a4i" data-path="src/components/XRayFilteringEducation.tsx">دليل شامل للمفاهيم الأساسية والتطبيقات السريرية</p>
            </div>
            <div className="text-left" data-id="zzxzcuzfi" data-path="src/components/XRayFilteringEducation.tsx">
              <div className="text-sm text-gray-500 mb-1" data-id="5dxl5mdvy" data-path="src/components/XRayFilteringEducation.tsx">التقدم</div>
              <Progress value={progress} className="w-32" data-id="9zvsz4o60" data-path="src/components/XRayFilteringEducation.tsx" />
              <div className="text-xs text-gray-400 mt-1" data-id="sw166gric" data-path="src/components/XRayFilteringEducation.tsx">{Math.round(progress)}%</div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-8" data-id="0t5ujfl6y" data-path="src/components/XRayFilteringEducation.tsx">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8" data-id="wgdv3izv7" data-path="src/components/XRayFilteringEducation.tsx">
          {/* Navigation Sidebar */}
          <div className="lg:col-span-1" data-id="rq75xbmyd" data-path="src/components/XRayFilteringEducation.tsx">
            <Card className="sticky top-8" data-id="nmousctla" data-path="src/components/XRayFilteringEducation.tsx">
              <CardHeader data-id="tdc02z5mc" data-path="src/components/XRayFilteringEducation.tsx">
                <CardTitle className="text-lg flex items-center gap-2" data-id="fsl7busq0" data-path="src/components/XRayFilteringEducation.tsx">
                  <BookOpen className="w-5 h-5 text-blue-600" data-id="o3zmdh12s" data-path="src/components/XRayFilteringEducation.tsx" />
                  المحتويات
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2" data-id="rul7xpewd" data-path="src/components/XRayFilteringEducation.tsx">
                <nav className="space-y-1" data-id="igqy9nfnv" data-path="src/components/XRayFilteringEducation.tsx">
                  {sections.map((section) =>
                  <Button
                    key={section.id}
                    variant={currentSection === section.id ? "default" : "ghost"}
                    className="w-full justify-start text-right"
                    onClick={() => handleSectionChange(section.id)} data-id="1fa5tmbkh" data-path="src/components/XRayFilteringEducation.tsx">

                      {section.icon}
                      <span className="mr-2" data-id="c6bfbn5ia" data-path="src/components/XRayFilteringEducation.tsx">{section.title}</span>
                      {currentSection === section.id && <ChevronRight className="w-4 h-4 mr-auto" data-id="jwh7jdmgz" data-path="src/components/XRayFilteringEducation.tsx" />}
                    </Button>
                  )}
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3" data-id="l5piveaub" data-path="src/components/XRayFilteringEducation.tsx">
            <Tabs value={currentSection} onValueChange={handleSectionChange} data-id="puobbulgc" data-path="src/components/XRayFilteringEducation.tsx">
              
              {/* الأساسيات */}
              <TabsContent value="basics" className="space-y-6" data-id="1jpza73fk" data-path="src/components/XRayFilteringEducation.tsx">
                <Card data-id="nl5vv8nnb" data-path="src/components/XRayFilteringEducation.tsx">
                  <CardHeader data-id="k3m4jz3hh" data-path="src/components/XRayFilteringEducation.tsx">
                    <CardTitle className="text-2xl flex items-center gap-3" data-id="2upa6538p" data-path="src/components/XRayFilteringEducation.tsx">
                      <div className="p-2 rounded-lg bg-blue-100 text-blue-600" data-id="cj3ljdxrr" data-path="src/components/XRayFilteringEducation.tsx">
                        <BookOpen className="w-6 h-6" data-id="9lo4hdvu5" data-path="src/components/XRayFilteringEducation.tsx" />
                      </div>
                      6.1 تحديد جودة شعاع الأشعة السينية
                    </CardTitle>
                    <CardDescription data-id="2hsogv0w9" data-path="src/components/XRayFilteringEducation.tsx">
                      فهم قابلية النفاذ والتوزيع الطيفي لأشعة السين
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6" data-id="4crcsrhsk" data-path="src/components/XRayFilteringEducation.tsx">
                    <div className="grid md:grid-cols-2 gap-6" data-id="sj05csops" data-path="src/components/XRayFilteringEducation.tsx">
                      <ConceptCard
                        title="قابلية النفاذ (Penetrability)"
                        definition="قدرة شعاع الأشعة السينية على اختراق المواد المختلفة، وتعتمد على طاقة الفوتونات وكثافة المادة المخترقة."
                        importance="تحديد الجرعة المناسبة وجودة الصورة للحصول على تباين مثالي"
                        icon={<Zap className="w-5 h-5" data-id="jgz0norhz" data-path="src/components/XRayFilteringEducation.tsx" />} data-id="do19yoyfi" data-path="src/components/XRayFilteringEducation.tsx" />

                      <ConceptCard
                        title="التوزيع الطيفي (Spectral Distribution)"
                        definition="توزيع طاقات الفوتونات في شعاع الأشعة السينية، والذي يؤثر على جودة الصورة والجرعة المُعطاة للمريض."
                        importance="تحسين التباين وتقليل الجرعة الإشعاعية غير الضرورية"
                        icon={<Filter className="w-5 h-5" data-id="cnawm1rr2" data-path="src/components/XRayFilteringEducation.tsx" />} data-id="q0xlltvm2" data-path="src/components/XRayFilteringEducation.tsx" />

                    </div>
                    
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border" data-id="gz1ip2ykz" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="font-semibold text-lg mb-3 text-blue-900" data-id="illlavd5u" data-path="src/components/XRayFilteringEducation.tsx">العوامل المؤثرة على جودة الشعاع:</h3>
                      <div className="grid md:grid-cols-2 gap-4" data-id="egk2mtyqf" data-path="src/components/XRayFilteringEducation.tsx">
                        <div className="space-y-2" data-id="dkyu8ml7k" data-path="src/components/XRayFilteringEducation.tsx">
                          <Badge variant="secondary" className="mb-2" data-id="h3en2279t" data-path="src/components/XRayFilteringEducation.tsx">العوامل الفيزيائية</Badge>
                          <ul className="text-sm space-y-1 text-gray-700" data-id="b29mml2fl" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="518p54i28" data-path="src/components/XRayFilteringEducation.tsx">• الجهد الكهربائي (kVp)</li>
                            <li data-id="xctlry7pw" data-path="src/components/XRayFilteringEducation.tsx">• التيار والزمن (mAs)</li>
                            <li data-id="627w5nbri" data-path="src/components/XRayFilteringEducation.tsx">• مادة الهدف (Target Material)</li>
                            <li data-id="xrkhh2h0t" data-path="src/components/XRayFilteringEducation.tsx">• زاوية الهدف</li>
                          </ul>
                        </div>
                        <div className="space-y-2" data-id="oj8gjho02" data-path="src/components/XRayFilteringEducation.tsx">
                          <Badge variant="secondary" className="mb-2" data-id="91c7s4ko1" data-path="src/components/XRayFilteringEducation.tsx">عوامل الترشيح</Badge>
                          <ul className="text-sm space-y-1 text-gray-700" data-id="kyvpxdvuo" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="n5xyvyunq" data-path="src/components/XRayFilteringEducation.tsx">• الترشيح المتأصل</li>
                            <li data-id="3win15egd" data-path="src/components/XRayFilteringEducation.tsx">• الترشيح الإضافي</li>
                            <li data-id="qgxzqxpgb" data-path="src/components/XRayFilteringEducation.tsx">• نوع مادة المرشح</li>
                            <li data-id="k55k0dmcd" data-path="src/components/XRayFilteringEducation.tsx">• سماكة المرشح</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* HVL & TVL */}
              <TabsContent value="hvl-tvl" className="space-y-6" data-id="nhvkrarxs" data-path="src/components/XRayFilteringEducation.tsx">
                <Card data-id="160lfwy7i" data-path="src/components/XRayFilteringEducation.tsx">
                  <CardHeader data-id="uc1ld739j" data-path="src/components/XRayFilteringEducation.tsx">
                    <CardTitle className="text-2xl flex items-center gap-3" data-id="vnr095lpf" data-path="src/components/XRayFilteringEducation.tsx">
                      <div className="p-2 rounded-lg bg-green-100 text-green-600" data-id="yy1qop32v" data-path="src/components/XRayFilteringEducation.tsx">
                        <Calculator className="w-6 h-6" data-id="4u6s29agx" data-path="src/components/XRayFilteringEducation.tsx" />
                      </div>
                      6.2 طبقة نصف القيمة (HVL) وطبقة القيمة العاشرة (TVL)
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6" data-id="kepi7boz4" data-path="src/components/XRayFilteringEducation.tsx">
                    <div className="grid md:grid-cols-2 gap-6" data-id="2yxs1ly28" data-path="src/components/XRayFilteringEducation.tsx">
                      <ConceptCard
                        title="طبقة نصف القيمة (HVL)"
                        definition="سماكة المادة المطلوبة لتقليل شدة شعاع الأشعة السينية إلى النصف من قيمتها الأصلية."
                        importance="مؤشر مهم لقياس جودة وقوة نفاذ الشعاع، ويُستخدم في حسابات الحماية الإشعاعية"
                        icon={<Calculator className="w-5 h-5" data-id="xehboheug" data-path="src/components/XRayFilteringEducation.tsx" />} data-id="46zat9fnz" data-path="src/components/XRayFilteringEducation.tsx" />

                      <ConceptCard
                        title="طبقة القيمة العاشرة (TVL)"
                        definition="سماكة المادة المطلوبة لتقليل شدة شعاع الأشعة السينية إلى عُشر قيمتها الأصلية."
                        importance="يُستخدم في تصميم الحماية الإشعاعية للمنشآت الطبية"
                        icon={<Filter className="w-5 h-5" data-id="ksllwzwbv" data-path="src/components/XRayFilteringEducation.tsx" />} data-id="608knhl72" data-path="src/components/XRayFilteringEducation.tsx" />

                    </div>

                    <div className="grid md:grid-cols-2 gap-6" data-id="px39ixtyf" data-path="src/components/XRayFilteringEducation.tsx">
                      <FormulaCard
                        title="معادلة التوهين الأسّي"
                        formula="I = I₀ × e^(-μx)"
                        description="حيث I هي الشدة بعد التوهين، I₀ هي الشدة الأولية، μ هو معامل التوهين الخطي، x هي سماكة المادة" data-id="oaesuuztq" data-path="src/components/XRayFilteringEducation.tsx" />

                      <FormulaCard
                        title="العلاقة بين HVL و TVL"
                        formula="TVL = 3.32 × HVL"
                        description="العلاقة الرياضية الثابتة بين طبقة نصف القيمة وطبقة القيمة العاشرة" data-id="4f355g9i0" data-path="src/components/XRayFilteringEducation.tsx" />

                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 p-6 rounded-lg" data-id="nkx5wp0t7" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="font-semibold text-lg mb-3 text-yellow-800" data-id="w1yjuhomp" data-path="src/components/XRayFilteringEducation.tsx">القياس والتطبيق السريري:</h3>
                      <div className="grid md:grid-cols-3 gap-4" data-id="zlxks5wms" data-path="src/components/XRayFilteringEducation.tsx">
                        <div data-id="gwf7mn2oy" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-yellow-700 mb-2" data-id="xealb4p08" data-path="src/components/XRayFilteringEducation.tsx">معدات القياس</h4>
                          <ul className="text-sm text-yellow-600 space-y-1" data-id="3p2gtl4np" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="ao0ra5kng" data-path="src/components/XRayFilteringEducation.tsx">• مقاييس الأيونية</li>
                            <li data-id="z5g4h0lcc" data-path="src/components/XRayFilteringEducation.tsx">• مرشحات ألمنيوم</li>
                            <li data-id="szcds676i" data-path="src/components/XRayFilteringEducation.tsx">• أجهزة القياس الرقمية</li>
                          </ul>
                        </div>
                        <div data-id="1fy5s77p0" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-yellow-700 mb-2" data-id="56se0clkp" data-path="src/components/XRayFilteringEducation.tsx">التطبيقات</h4>
                          <ul className="text-sm text-yellow-600 space-y-1" data-id="wwquxi2tv" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="v6yi2pevl" data-path="src/components/XRayFilteringEducation.tsx">• مراقبة جودة الأجهزة</li>
                            <li data-id="8808hpgxw" data-path="src/components/XRayFilteringEducation.tsx">• حسابات الجرعة</li>
                            <li data-id="qh848870a" data-path="src/components/XRayFilteringEducation.tsx">• تصميم الحماية</li>
                          </ul>
                        </div>
                        <div data-id="3ltox5apx" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-yellow-700 mb-2" data-id="kt78sufrj" data-path="src/components/XRayFilteringEducation.tsx">القيم المرجعية</h4>
                          <ul className="text-sm text-yellow-600 space-y-1" data-id="awzwet81t" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="2sr6fqia8" data-path="src/components/XRayFilteringEducation.tsx">• 80 kVp: 2.3-2.5 mm Al</li>
                            <li data-id="ukz81c23o" data-path="src/components/XRayFilteringEducation.tsx">• 100 kVp: 3.2-3.6 mm Al</li>
                            <li data-id="ze2l6ofzx" data-path="src/components/XRayFilteringEducation.tsx">• 120 kVp: 4.1-4.7 mm Al</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* تقوية الشعاع */}
              <TabsContent value="beam-hardening" className="space-y-6" data-id="xk8kobu4m" data-path="src/components/XRayFilteringEducation.tsx">
                <Card data-id="k4s6k0pag" data-path="src/components/XRayFilteringEducation.tsx">
                  <CardHeader data-id="q82axqd4q" data-path="src/components/XRayFilteringEducation.tsx">
                    <CardTitle className="text-2xl flex items-center gap-3" data-id="2m04mztuc" data-path="src/components/XRayFilteringEducation.tsx">
                      <div className="p-2 rounded-lg bg-purple-100 text-purple-600" data-id="ies3frdoq" data-path="src/components/XRayFilteringEducation.tsx">
                        <Zap className="w-6 h-6" data-id="70yzwhys2" data-path="src/components/XRayFilteringEducation.tsx" />
                      </div>
                      6.3 تقوية العارضة (Beam Hardening)
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6" data-id="q7ad1d2oz" data-path="src/components/XRayFilteringEducation.tsx">
                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg border" data-id="hqxvr0ey5" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="font-semibold text-lg mb-4 text-purple-900" data-id="k5gyat06f" data-path="src/components/XRayFilteringEducation.tsx">المبدأ الأساسي:</h3>
                      <p className="text-gray-700 leading-relaxed mb-4" data-id="ztcfa91qg" data-path="src/components/XRayFilteringEducation.tsx">
                        تقوية الشعاع هي عملية طبيعية تحدث عندما يمر شعاع الأشعة السينية عبر مادة مرشحة، حيث يتم امتصاص الفوتونات منخفضة الطاقة بشكل تفضيلي، مما يترك الفوتونات عالية الطاقة التي تكون أكثر قدرة على النفاذ.
                      </p>
                      
                      <div className="grid md:grid-cols-2 gap-6" data-id="agzxijx1h" data-path="src/components/XRayFilteringEducation.tsx">
                        <div data-id="fnhbfefbd" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-purple-700 mb-3" data-id="jyvmc6w6d" data-path="src/components/XRayFilteringEducation.tsx">التوهين التفضيلي:</h4>
                          <ul className="text-sm text-gray-600 space-y-2" data-id="02fmc0ehb" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="jj50eh6fk" data-path="src/components/XRayFilteringEducation.tsx">• الفوتونات منخفضة الطاقة (&lt;30 keV) تُمتص بسهولة</li>
                            <li data-id="glku3fdjj" data-path="src/components/XRayFilteringEducation.tsx">• الفوتونات عالية الطاقة (&gt;60 keV) تخترق بفعالية</li>
                            <li data-id="rlw9g7z2a" data-path="src/components/XRayFilteringEducation.tsx">• النتيجة: شعاع أكثر تجانساً وقوة نفاذ</li>
                          </ul>
                        </div>
                        <div data-id="zzd7qh3av" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-purple-700 mb-3" data-id="tvx9p1jg6" data-path="src/components/XRayFilteringEducation.tsx">التأثيرات الإيجابية:</h4>
                          <ul className="text-sm text-gray-600 space-y-2" data-id="77yymja3t" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="d3kn23gtb" data-path="src/components/XRayFilteringEducation.tsx">• تحسين التباين في الصورة</li>
                            <li data-id="kybmt8f5v" data-path="src/components/XRayFilteringEducation.tsx">• تقليل الجرعة السطحية للمريض</li>
                            <li data-id="v0adq5lgu" data-path="src/components/XRayFilteringEducation.tsx">• تقليل تشتت الإشعاع</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-3 gap-6" data-id="i9n3g729x" data-path="src/components/XRayFilteringEducation.tsx">
                      <Card className="border-2 border-blue-200" data-id="9k8llvjgr" data-path="src/components/XRayFilteringEducation.tsx">
                        <CardHeader data-id="eib0sra0e" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardTitle className="text-lg text-blue-700" data-id="me88mtxf9" data-path="src/components/XRayFilteringEducation.tsx">التأثير على متوسط الطاقة</CardTitle>
                        </CardHeader>
                        <CardContent data-id="0eqlc87rv" data-path="src/components/XRayFilteringEducation.tsx">
                          <p className="text-sm text-gray-600 mb-3" data-id="f26zxm1ce" data-path="src/components/XRayFilteringEducation.tsx">
                            زيادة متوسط طاقة الشعاع من ~40 keV إلى ~60 keV
                          </p>
                          <div className="bg-blue-50 p-3 rounded text-center" data-id="dxhwntged" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="text-xs text-blue-600" data-id="zoxy12bo6" data-path="src/components/XRayFilteringEducation.tsx">قبل الترشيح</div>
                            <div className="font-bold text-blue-800" data-id="pajhzzph3" data-path="src/components/XRayFilteringEducation.tsx">40 keV</div>
                            <div className="text-2xl text-blue-400 my-1" data-id="zu7dgthkd" data-path="src/components/XRayFilteringEducation.tsx">↓</div>
                            <div className="text-xs text-blue-600" data-id="trahgl4nl" data-path="src/components/XRayFilteringEducation.tsx">بعد الترشيح</div>
                            <div className="font-bold text-blue-800" data-id="pkl9dew53" data-path="src/components/XRayFilteringEducation.tsx">60 keV</div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="border-2 border-green-200" data-id="g2gsdjpno" data-path="src/components/XRayFilteringEducation.tsx">
                        <CardHeader data-id="ik4b29hzf" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardTitle className="text-lg text-green-700" data-id="a84ffutqu" data-path="src/components/XRayFilteringEducation.tsx">التأثير على HVL</CardTitle>
                        </CardHeader>
                        <CardContent data-id="xz1q0cmgj" data-path="src/components/XRayFilteringEducation.tsx">
                          <p className="text-sm text-gray-600 mb-3" data-id="vpar4yibd" data-path="src/components/XRayFilteringEducation.tsx">
                            زيادة قيمة HVL تدل على تحسن جودة الشعاع
                          </p>
                          <div className="bg-green-50 p-3 rounded text-center" data-id="cv7tr96vm" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="text-xs text-green-600" data-id="jilb1bdsm" data-path="src/components/XRayFilteringEducation.tsx">بدون ترشيح</div>
                            <div className="font-bold text-green-800" data-id="10wei7liz" data-path="src/components/XRayFilteringEducation.tsx">2.0 mm Al</div>
                            <div className="text-2xl text-green-400 my-1" data-id="jyqp7eapn" data-path="src/components/XRayFilteringEducation.tsx">↓</div>
                            <div className="text-xs text-green-600" data-id="c92npbfe8" data-path="src/components/XRayFilteringEducation.tsx">مع الترشيح</div>
                            <div className="font-bold text-green-800" data-id="0gsliobtf" data-path="src/components/XRayFilteringEducation.tsx">2.8 mm Al</div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="border-2 border-red-200" data-id="bj2cx0y65" data-path="src/components/XRayFilteringEducation.tsx">
                        <CardHeader data-id="8p8ecke2q" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardTitle className="text-lg text-red-700" data-id="ojh1jt3oc" data-path="src/components/XRayFilteringEducation.tsx">تأثير جرعة المريض</CardTitle>
                        </CardHeader>
                        <CardContent data-id="uvw9chvr8" data-path="src/components/XRayFilteringEducation.tsx">
                          <p className="text-sm text-gray-600 mb-3" data-id="huiylwxbk" data-path="src/components/XRayFilteringEducation.tsx">
                            تقليل الجرعة السطحية مع الحفاظ على جودة الصورة
                          </p>
                          <div className="bg-red-50 p-3 rounded text-center" data-id="9znrwhs97" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="text-xs text-red-600" data-id="k3e1cbz6y" data-path="src/components/XRayFilteringEducation.tsx">جرعة الجلد</div>
                            <div className="font-bold text-red-800" data-id="0cyn3tnby" data-path="src/components/XRayFilteringEducation.tsx">-30%</div>
                            <div className="text-2xl text-red-400 my-1" data-id="2s266oqo6" data-path="src/components/XRayFilteringEducation.tsx">↓</div>
                            <div className="text-xs text-red-600" data-id="1p4h64it0" data-path="src/components/XRayFilteringEducation.tsx">نفس جودة الصورة</div>
                            <div className="font-bold text-red-800" data-id="n4xth6whb" data-path="src/components/XRayFilteringEducation.tsx">100%</div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* أنواع المرشحات */}
              <TabsContent value="filter-types" className="space-y-6" data-id="xl1uk58ug" data-path="src/components/XRayFilteringEducation.tsx">
                <Card data-id="qdyrt2rpt" data-path="src/components/XRayFilteringEducation.tsx">
                  <CardHeader data-id="05w38t6fj" data-path="src/components/XRayFilteringEducation.tsx">
                    <CardTitle className="text-2xl flex items-center gap-3" data-id="vn91y4bbf" data-path="src/components/XRayFilteringEducation.tsx">
                      <div className="p-2 rounded-lg bg-orange-100 text-orange-600" data-id="7x9hqwbk1" data-path="src/components/XRayFilteringEducation.tsx">
                        <Filter className="w-6 h-6" data-id="qw0eg6egg" data-path="src/components/XRayFilteringEducation.tsx" />
                      </div>
                      6.4 أنواع الترشيح
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-8" data-id="vbo4m8mdj" data-path="src/components/XRayFilteringEducation.tsx">
                    
                    {/* الترشيح المتأصل */}
                    <div className="space-y-4" data-id="kbvf6r76z" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="text-xl font-semibold text-gray-800 border-b pb-2" data-id="vzrqwtkx0" data-path="src/components/XRayFilteringEducation.tsx">6.4.1 الترشيح المتأصل (Inherent Filtration)</h3>
                      <div className="grid md:grid-cols-3 gap-4" data-id="rslrcrn89" data-path="src/components/XRayFilteringEducation.tsx">
                        <Card className="bg-blue-50 border-blue-200" data-id="tpexdva2i" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardHeader data-id="k87trxg1p" data-path="src/components/XRayFilteringEducation.tsx">
                            <CardTitle className="text-lg text-blue-700" data-id="va3wyth62" data-path="src/components/XRayFilteringEducation.tsx">نافذة الأنبوب</CardTitle>
                          </CardHeader>
                          <CardContent data-id="9yrj8go1l" data-path="src/components/XRayFilteringEducation.tsx">
                            <ul className="text-sm space-y-1" data-id="swk9oqf27" data-path="src/components/XRayFilteringEducation.tsx">
                              <li data-id="e1j94wmwz" data-path="src/components/XRayFilteringEducation.tsx">• مصنوعة من الزجاج أو البريليوم</li>
                              <li data-id="6j2xq4e35" data-path="src/components/XRayFilteringEducation.tsx">• سماكة: 1-2 mm مكافئ ألمنيوم</li>
                              <li data-id="s17f3dhbd" data-path="src/components/XRayFilteringEducation.tsx">• ترشيح أولي للفوتونات الضعيفة</li>
                            </ul>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-green-50 border-green-200" data-id="jz8i7ifd9" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardHeader data-id="o32a6rf33" data-path="src/components/XRayFilteringEducation.tsx">
                            <CardTitle className="text-lg text-green-700" data-id="rlg2xb9nv" data-path="src/components/XRayFilteringEducation.tsx">زيت التبريد</CardTitle>
                          </CardHeader>
                          <CardContent data-id="ktheqkb92" data-path="src/components/XRayFilteringEducation.tsx">
                            <ul className="text-sm space-y-1" data-id="vk50v5c4u" data-path="src/components/XRayFilteringEducation.tsx">
                              <li data-id="2kc6948nu" data-path="src/components/XRayFilteringEducation.tsx">• يمتص الفوتونات منخفضة الطاقة</li>
                              <li data-id="n0axf2oxq" data-path="src/components/XRayFilteringEducation.tsx">• مكافئ: 0.5-1 mm ألمنيوم</li>
                              <li data-id="3kd3yrdow" data-path="src/components/XRayFilteringEducation.tsx">• يساهم في تقوية الشعاع</li>
                            </ul>
                          </CardContent>
                        </Card>
                        
                        <Card className="bg-purple-50 border-purple-200" data-id="d20o0ouo4" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardHeader data-id="50c5on4ms" data-path="src/components/XRayFilteringEducation.tsx">
                            <CardTitle className="text-lg text-purple-700" data-id="mbvif8y6b" data-path="src/components/XRayFilteringEducation.tsx">منفذ الغلاف</CardTitle>
                          </CardHeader>
                          <CardContent data-id="baa398mqo" data-path="src/components/XRayFilteringEducation.tsx">
                            <ul className="text-sm space-y-1" data-id="4t3z0wr63" data-path="src/components/XRayFilteringEducation.tsx">
                              <li data-id="v5frz4dup" data-path="src/components/XRayFilteringEducation.tsx">• مصنوع من الألمنيوم عادة</li>
                              <li data-id="5mknh565g" data-path="src/components/XRayFilteringEducation.tsx">• سماكة: 0.5-1 mm</li>
                              <li data-id="1081ql6h0" data-path="src/components/XRayFilteringEducation.tsx">• ترشيح إضافي طبيعي</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* الترشيح الإضافي */}
                    <div className="space-y-4" data-id="jozpa3xee" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="text-xl font-semibold text-gray-800 border-b pb-2" data-id="jfvef9gu2" data-path="src/components/XRayFilteringEducation.tsx">6.4.2 الترشيح الإضافي (Added Filtration)</h3>
                      <div className="grid md:grid-cols-2 gap-6" data-id="7081xcddv" data-path="src/components/XRayFilteringEducation.tsx">
                        <Card className="border-l-4 border-l-blue-500" data-id="txrehb6vp" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardHeader data-id="c78bdkm46" data-path="src/components/XRayFilteringEducation.tsx">
                            <CardTitle className="text-lg" data-id="xrgfctmg9" data-path="src/components/XRayFilteringEducation.tsx">مرشحات الألمنيوم</CardTitle>
                          </CardHeader>
                          <CardContent data-id="ulqz5om8k" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="space-y-3" data-id="zdhpv3n93" data-path="src/components/XRayFilteringEducation.tsx">
                              <div className="bg-blue-50 p-3 rounded" data-id="v0b4xubrp" data-path="src/components/XRayFilteringEducation.tsx">
                                <h4 className="font-medium text-blue-700 mb-2" data-id="1d72cj3qv" data-path="src/components/XRayFilteringEducation.tsx">المواصفات:</h4>
                                <ul className="text-sm text-blue-600 space-y-1" data-id="gi2hhqul8" data-path="src/components/XRayFilteringEducation.tsx">
                                  <li data-id="yxr16z826" data-path="src/components/XRayFilteringEducation.tsx">• نقاوة عالية (&gt;99%)</li>
                                  <li data-id="n2sv8llix" data-path="src/components/XRayFilteringEducation.tsx">• سماكات: 1-4 mm</li>
                                  <li data-id="yuj8bls0m" data-path="src/components/XRayFilteringEducation.tsx">• استخدام عام للأشعة التشخيصية</li>
                                </ul>
                              </div>
                              <div className="bg-blue-50 p-3 rounded" data-id="nyyt0nizl" data-path="src/components/XRayFilteringEducation.tsx">
                                <h4 className="font-medium text-blue-700 mb-2" data-id="3dhhu5gc7" data-path="src/components/XRayFilteringEducation.tsx">التطبيقات:</h4>
                                <ul className="text-sm text-blue-600 space-y-1" data-id="w7ltp5bbe" data-path="src/components/XRayFilteringEducation.tsx">
                                  <li data-id="itzdipcey" data-path="src/components/XRayFilteringEducation.tsx">• الأشعة العامة (60-120 kVp)</li>
                                  <li data-id="9cv0fd5gh" data-path="src/components/XRayFilteringEducation.tsx">• تصوير الصدر والأطراف</li>
                                  <li data-id="jsobzx1ko" data-path="src/components/XRayFilteringEducation.tsx">• فحوصات الأطفال</li>
                                </ul>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <Card className="border-l-4 border-l-orange-500" data-id="h8j9smvi4" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardHeader data-id="vx99bgtp9" data-path="src/components/XRayFilteringEducation.tsx">
                            <CardTitle className="text-lg" data-id="h1l42lnx6" data-path="src/components/XRayFilteringEducation.tsx">مرشحات النحاس</CardTitle>
                          </CardHeader>
                          <CardContent data-id="feg9unym0" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="space-y-3" data-id="s2pmju8d7" data-path="src/components/XRayFilteringEducation.tsx">
                              <div className="bg-orange-50 p-3 rounded" data-id="d84lcyjcc" data-path="src/components/XRayFilteringEducation.tsx">
                                <h4 className="font-medium text-orange-700 mb-2" data-id="jnkzib0a7" data-path="src/components/XRayFilteringEducation.tsx">المواصفات:</h4>
                                <ul className="text-sm text-orange-600 space-y-1" data-id="svn5htmqr" data-path="src/components/XRayFilteringEducation.tsx">
                                  <li data-id="adx56kd60" data-path="src/components/XRayFilteringEducation.tsx">• كثافة عالية (8.96 g/cm³)</li>
                                  <li data-id="1yhhc69jh" data-path="src/components/XRayFilteringEducation.tsx">• سماكات: 0.1-0.5 mm</li>
                                  <li data-id="u5y9vqj35" data-path="src/components/XRayFilteringEducation.tsx">• ترشيح أقوى من الألمنيوم</li>
                                </ul>
                              </div>
                              <div className="bg-orange-50 p-3 rounded" data-id="8iaod7wah" data-path="src/components/XRayFilteringEducation.tsx">
                                <h4 className="font-medium text-orange-700 mb-2" data-id="g1v7wwq56" data-path="src/components/XRayFilteringEducation.tsx">التطبيقات:</h4>
                                <ul className="text-sm text-orange-600 space-y-1" data-id="njw6rul17" data-path="src/components/XRayFilteringEducation.tsx">
                                  <li data-id="tksifx4ct" data-path="src/components/XRayFilteringEducation.tsx">• الجهد العالي (&gt;100 kVp)</li>
                                  <li data-id="gb6rpqcz9" data-path="src/components/XRayFilteringEducation.tsx">• المرضى كبار الحجم</li>
                                  <li data-id="frlb11im5" data-path="src/components/XRayFilteringEducation.tsx">• تصوير البطن والحوض</li>
                                </ul>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* الترشيح الكلي */}
                    <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-lg border" data-id="qvq53lywb" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="text-xl font-semibold text-gray-800 mb-4" data-id="9uqt1piep" data-path="src/components/XRayFilteringEducation.tsx">6.4.3 الترشيح الكلي (Total Filtration)</h3>
                      <div className="grid md:grid-cols-2 gap-6" data-id="4p1anj8lh" data-path="src/components/XRayFilteringEducation.tsx">
                        <div data-id="i271sg6gq" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-gray-700 mb-3" data-id="k8bcu3euk" data-path="src/components/XRayFilteringEducation.tsx">المتطلبات التنظيمية:</h4>
                          <div className="space-y-2" data-id="3lwboxs7k" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="flex justify-between items-center bg-white p-2 rounded border" data-id="c175kl8om" data-path="src/components/XRayFilteringEducation.tsx">
                              <span className="text-sm" data-id="ndoeacf3v" data-path="src/components/XRayFilteringEducation.tsx">70 kVp</span>
                              <Badge variant="outline" data-id="scyy877b0" data-path="src/components/XRayFilteringEducation.tsx">1.5 mm Al</Badge>
                            </div>
                            <div className="flex justify-between items-center bg-white p-2 rounded border" data-id="nb8lwba5n" data-path="src/components/XRayFilteringEducation.tsx">
                              <span className="text-sm" data-id="yl5hlero6" data-path="src/components/XRayFilteringEducation.tsx">80-100 kVp</span>
                              <Badge variant="outline" data-id="499jw643y" data-path="src/components/XRayFilteringEducation.tsx">2.5 mm Al</Badge>
                            </div>
                            <div className="flex justify-between items-center bg-white p-2 rounded border" data-id="g28z5vu4p" data-path="src/components/XRayFilteringEducation.tsx">
                              <span className="text-sm" data-id="7achcrb01" data-path="src/components/XRayFilteringEducation.tsx">&gt;100 kVp</span>
                              <Badge variant="outline" data-id="c34guk9q8" data-path="src/components/XRayFilteringEducation.tsx">2.5 mm Al + 0.1 mm Cu</Badge>
                            </div>
                          </div>
                        </div>
                        <div data-id="sm1fz2ojt" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-gray-700 mb-3" data-id="gmd4fezls" data-path="src/components/XRayFilteringEducation.tsx">التوصيات الإضافية:</h4>
                          <ul className="text-sm text-gray-600 space-y-2" data-id="7cf17pix2" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="6db5vja8n" data-path="src/components/XRayFilteringEducation.tsx">• فحص دوري لسماكة المرشحات</li>
                            <li data-id="zjwfnahei" data-path="src/components/XRayFilteringEducation.tsx">• تسجيل قيم HVL المرجعية</li>
                            <li data-id="g9fn9u0te" data-path="src/components/XRayFilteringEducation.tsx">• مراقبة تدهور المرشحات</li>
                            <li data-id="12sfenb9z" data-path="src/components/XRayFilteringEducation.tsx">• تطبيق مبدأ ALARA</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* مرشحات K-Edge */}
                    <div className="space-y-4" data-id="hiqarhfak" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="text-xl font-semibold text-gray-800 border-b pb-2" data-id="iczls4toc" data-path="src/components/XRayFilteringEducation.tsx">6.5 مرشحات K-Edge للتطبيقات المتخصصة</h3>
                      <div className="grid md:grid-cols-3 gap-4" data-id="07ei7xz5g" data-path="src/components/XRayFilteringEducation.tsx">
                        <Card className="bg-gradient-to-br from-pink-50 to-red-50 border-pink-200" data-id="310cuz403" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardHeader data-id="spcog6sj2" data-path="src/components/XRayFilteringEducation.tsx">
                            <CardTitle className="text-lg text-pink-700" data-id="kf7diyitn" data-path="src/components/XRayFilteringEducation.tsx">الموليبدينوم (Mo)</CardTitle>
                          </CardHeader>
                          <CardContent data-id="sja5ixlie" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="space-y-2" data-id="3tffj87xu" data-path="src/components/XRayFilteringEducation.tsx">
                              <div className="text-sm" data-id="057ue1rx9" data-path="src/components/XRayFilteringEducation.tsx">
                                <strong data-id="kbm5j4k4w" data-path="src/components/XRayFilteringEducation.tsx">K-Edge:</strong> 20 keV
                              </div>
                              <div className="text-sm" data-id="foutgq6b9" data-path="src/components/XRayFilteringEducation.tsx">
                                <strong data-id="g8g7m81j4" data-path="src/components/XRayFilteringEducation.tsx">التطبيق:</strong> تصوير الثدي
                              </div>
                              <div className="text-sm" data-id="gp6fn599f" data-path="src/components/XRayFilteringEducation.tsx">
                                <strong data-id="33g8bqv83" data-path="src/components/XRayFilteringEducation.tsx">المزايا:</strong> تحسين التباين للأنسجة الناعمة
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <Card className="bg-gradient-to-br from-purple-50 to-blue-50 border-purple-200" data-id="4d4n6gyhn" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardHeader data-id="5tao5r3wf" data-path="src/components/XRayFilteringEducation.tsx">
                            <CardTitle className="text-lg text-purple-700" data-id="eq4nz9n0o" data-path="src/components/XRayFilteringEducation.tsx">الروديوم (Rh)</CardTitle>
                          </CardHeader>
                          <CardContent data-id="ldd5rqwcz" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="space-y-2" data-id="ofyuje8nm" data-path="src/components/XRayFilteringEducation.tsx">
                              <div className="text-sm" data-id="72adwnire" data-path="src/components/XRayFilteringEducation.tsx">
                                <strong data-id="ww8h4f3n8" data-path="src/components/XRayFilteringEducation.tsx">K-Edge:</strong> 23 keV
                              </div>
                              <div className="text-sm" data-id="wbjhmodog" data-path="src/components/XRayFilteringEducation.tsx">
                                <strong data-id="4doiwh40j" data-path="src/components/XRayFilteringEducation.tsx">التطبيق:</strong> الثدي الكثيف
                              </div>
                              <div className="text-sm" data-id="ympajtfow" data-path="src/components/XRayFilteringEducation.tsx">
                                <strong data-id="g59crkbor" data-path="src/components/XRayFilteringEducation.tsx">المزايا:</strong> نفاذ أفضل للأنسجة الكثيفة
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200" data-id="eli9iyjhs" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardHeader data-id="ol995h2vd" data-path="src/components/XRayFilteringEducation.tsx">
                            <CardTitle className="text-lg text-yellow-700" data-id="7as2b3pah" data-path="src/components/XRayFilteringEducation.tsx">الفضة (Ag)</CardTitle>
                          </CardHeader>
                          <CardContent data-id="8jv6cchco" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="space-y-2" data-id="bebknh7tc" data-path="src/components/XRayFilteringEducation.tsx">
                              <div className="text-sm" data-id="ongbrncw9" data-path="src/components/XRayFilteringEducation.tsx">
                                <strong data-id="qg6ah0lnn" data-path="src/components/XRayFilteringEducation.tsx">K-Edge:</strong> 25.5 keV
                              </div>
                              <div className="text-sm" data-id="ocol7ysle" data-path="src/components/XRayFilteringEducation.tsx">
                                <strong data-id="p7qbl0091" data-path="src/components/XRayFilteringEducation.tsx">التطبيق:</strong> التصوير الرقمي للثدي
                              </div>
                              <div className="text-sm" data-id="7ytd2rm6i" data-path="src/components/XRayFilteringEducation.tsx">
                                <strong data-id="kdm1rh7gm" data-path="src/components/XRayFilteringEducation.tsx">المزايا:</strong> تحسين نسبة الإشارة للضوضاء
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* التطبيقات السريرية */}
              <TabsContent value="clinical" className="space-y-6" data-id="4lect7uko" data-path="src/components/XRayFilteringEducation.tsx">
                <Card data-id="57motul8p" data-path="src/components/XRayFilteringEducation.tsx">
                  <CardHeader data-id="c0mm1q4v0" data-path="src/components/XRayFilteringEducation.tsx">
                    <CardTitle className="text-2xl flex items-center gap-3" data-id="4tgof4tuq" data-path="src/components/XRayFilteringEducation.tsx">
                      <div className="p-2 rounded-lg bg-teal-100 text-teal-600" data-id="nzvhx9150" data-path="src/components/XRayFilteringEducation.tsx">
                        <Users className="w-6 h-6" data-id="9y8f020xg" data-path="src/components/XRayFilteringEducation.tsx" />
                      </div>
                      6.7 التطبيقات السريرية لاستراتيجيات الترشيح المختلفة
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-8" data-id="5ip8elqbw" data-path="src/components/XRayFilteringEducation.tsx">
                    
                    <div className="grid md:grid-cols-2 gap-6" data-id="62czn4z1s" data-path="src/components/XRayFilteringEducation.tsx">
                      <Card className="border-l-4 border-l-green-500" data-id="d4isrgcrp" data-path="src/components/XRayFilteringEducation.tsx">
                        <CardHeader data-id="3xt2kf6w4" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardTitle className="text-lg text-green-700" data-id="xty61sdkc" data-path="src/components/XRayFilteringEducation.tsx">تصوير الأطفال</CardTitle>
                        </CardHeader>
                        <CardContent data-id="78jq8417d" data-path="src/components/XRayFilteringEducation.tsx">
                          <div className="space-y-3" data-id="cj1p124hh" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="bg-green-50 p-3 rounded" data-id="wuxktusra" data-path="src/components/XRayFilteringEducation.tsx">
                              <h4 className="font-medium text-green-700 mb-2" data-id="8njgp4ts5" data-path="src/components/XRayFilteringEducation.tsx">استراتيجية الترشيح:</h4>
                              <ul className="text-sm text-green-600 space-y-1" data-id="3uz03ntyn" data-path="src/components/XRayFilteringEducation.tsx">
                                <li data-id="sql3v8ayb" data-path="src/components/XRayFilteringEducation.tsx">• ترشيح إضافي منخفض (1-2 mm Al)</li>
                                <li data-id="o0wc57lq8" data-path="src/components/XRayFilteringEducation.tsx">• تجنب الترشيح المفرط</li>
                                <li data-id="gwh18vhrz" data-path="src/components/XRayFilteringEducation.tsx">• أولوية للحصول على تباين كافٍ</li>
                              </ul>
                            </div>
                            <div className="bg-green-50 p-3 rounded" data-id="bzpw5ucfo" data-path="src/components/XRayFilteringEducation.tsx">
                              <h4 className="font-medium text-green-700 mb-2" data-id="7836svven" data-path="src/components/XRayFilteringEducation.tsx">الفوائد:</h4>
                              <ul className="text-sm text-green-600 space-y-1" data-id="buuzsla3x" data-path="src/components/XRayFilteringEducation.tsx">
                                <li data-id="p897sf6wx" data-path="src/components/XRayFilteringEducation.tsx">• تقليل الجرعة الإشعاعية</li>
                                <li data-id="tbhzwb0so" data-path="src/components/XRayFilteringEducation.tsx">• الحفاظ على التباين الطبيعي</li>
                                <li data-id="wexrg7x3y" data-path="src/components/XRayFilteringEducation.tsx">• تقليل زمن التعرض</li>
                              </ul>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="border-l-4 border-l-blue-500" data-id="h1yfp7857" data-path="src/components/XRayFilteringEducation.tsx">
                        <CardHeader data-id="mbckogpvj" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardTitle className="text-lg text-blue-700" data-id="xz9ticwsf" data-path="src/components/XRayFilteringEducation.tsx">تصوير الصدر</CardTitle>
                        </CardHeader>
                        <CardContent data-id="uxiulz6hv" data-path="src/components/XRayFilteringEducation.tsx">
                          <div className="space-y-3" data-id="04fn45kj4" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="bg-blue-50 p-3 rounded" data-id="rmjtscqds" data-path="src/components/XRayFilteringEducation.tsx">
                              <h4 className="font-medium text-blue-700 mb-2" data-id="3vl1epqlq" data-path="src/components/XRayFilteringEducation.tsx">استراتيجية الترشيح:</h4>
                              <ul className="text-sm text-blue-600 space-y-1" data-id="1qcli59cn" data-path="src/components/XRayFilteringEducation.tsx">
                                <li data-id="se5pahtpo" data-path="src/components/XRayFilteringEducation.tsx">• ترشيح معتدل (2-3 mm Al)</li>
                                <li data-id="nn0j8kq7c" data-path="src/components/XRayFilteringEducation.tsx">• جهد عالي (100-120 kVp)</li>
                                <li data-id="iu96lpciu" data-path="src/components/XRayFilteringEducation.tsx">• تحسين نفاذ الأضلاع</li>
                              </ul>
                            </div>
                            <div className="bg-blue-50 p-3 rounded" data-id="dxuj0z0um" data-path="src/components/XRayFilteringEducation.tsx">
                              <h4 className="font-medium text-blue-700 mb-2" data-id="5y4xyxojn" data-path="src/components/XRayFilteringEducation.tsx">الفوائد:</h4>
                              <ul className="text-sm text-blue-600 space-y-1" data-id="f61esdd43" data-path="src/components/XRayFilteringEducation.tsx">
                                <li data-id="tm4mpe594" data-path="src/components/XRayFilteringEducation.tsx">• تحسين رؤية الرئتين</li>
                                <li data-id="uqgftew21" data-path="src/components/XRayFilteringEducation.tsx">• تقليل تأثير الأضلاع</li>
                                <li data-id="21p9jc5y1" data-path="src/components/XRayFilteringEducation.tsx">• جودة صورة محسنة</li>
                              </ul>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6" data-id="uvn9qjimh" data-path="src/components/XRayFilteringEducation.tsx">
                      <Card className="border-l-4 border-l-purple-500" data-id="6pkf3niyo" data-path="src/components/XRayFilteringEducation.tsx">
                        <CardHeader data-id="dm3h3r9a3" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardTitle className="text-lg text-purple-700" data-id="d5g99t504" data-path="src/components/XRayFilteringEducation.tsx">تصوير البطن</CardTitle>
                        </CardHeader>
                        <CardContent data-id="okrl043oe" data-path="src/components/XRayFilteringEducation.tsx">
                          <div className="space-y-3" data-id="6z8cl2agx" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="bg-purple-50 p-3 rounded" data-id="3n4ps6rtn" data-path="src/components/XRayFilteringEducation.tsx">
                              <h4 className="font-medium text-purple-700 mb-2" data-id="0b0fbuyhd" data-path="src/components/XRayFilteringEducation.tsx">استراتيجية الترشيح:</h4>
                              <ul className="text-sm text-purple-600 space-y-1" data-id="4vgqbrcca" data-path="src/components/XRayFilteringEducation.tsx">
                                <li data-id="qzlmxw14b" data-path="src/components/XRayFilteringEducation.tsx">• ترشيح عالي (3-4 mm Al أو Cu)</li>
                                <li data-id="sezuerfu2" data-path="src/components/XRayFilteringEducation.tsx">• جهد عالي (&gt;100 kVp)</li>
                                <li data-id="w7p9hcy13" data-path="src/components/XRayFilteringEducation.tsx">• مراعاة سماكة المريض</li>
                              </ul>
                            </div>
                            <div className="bg-purple-50 p-3 rounded" data-id="lminiw992" data-path="src/components/XRayFilteringEducation.tsx">
                              <h4 className="font-medium text-purple-700 mb-2" data-id="e4hr3j5wm" data-path="src/components/XRayFilteringEducation.tsx">الفوائد:</h4>
                              <ul className="text-sm text-purple-600 space-y-1" data-id="b2x6ut22n" data-path="src/components/XRayFilteringEducation.tsx">
                                <li data-id="ac0jnvcgq" data-path="src/components/XRayFilteringEducation.tsx">• نفاذ أفضل للأنسجة السميكة</li>
                                <li data-id="51tk796li" data-path="src/components/XRayFilteringEducation.tsx">• تقليل الجرعة الجلدية</li>
                                <li data-id="epmss50lp" data-path="src/components/XRayFilteringEducation.tsx">• تحسين جودة الصورة</li>
                              </ul>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="border-l-4 border-l-orange-500" data-id="yzwzlg9a1" data-path="src/components/XRayFilteringEducation.tsx">
                        <CardHeader data-id="09yp2tcj5" data-path="src/components/XRayFilteringEducation.tsx">
                          <CardTitle className="text-lg text-orange-700" data-id="fhavxy1lq" data-path="src/components/XRayFilteringEducation.tsx">تصوير الثدي</CardTitle>
                        </CardHeader>
                        <CardContent data-id="f5k0uwa6e" data-path="src/components/XRayFilteringEducation.tsx">
                          <div className="space-y-3" data-id="5skpt5dw1" data-path="src/components/XRayFilteringEducation.tsx">
                            <div className="bg-orange-50 p-3 rounded" data-id="zhnlbg5t7" data-path="src/components/XRayFilteringEducation.tsx">
                              <h4 className="font-medium text-orange-700 mb-2" data-id="4facsjzwc" data-path="src/components/XRayFilteringEducation.tsx">استراتيجية الترشيح:</h4>
                              <ul className="text-sm text-orange-600 space-y-1" data-id="nq6j8nofp" data-path="src/components/XRayFilteringEducation.tsx">
                                <li data-id="ysf6r1gx6" data-path="src/components/XRayFilteringEducation.tsx">• مرشحات K-edge (Mo, Rh, Ag)</li>
                                <li data-id="dlg335web" data-path="src/components/XRayFilteringEducation.tsx">• جهد منخفض (25-35 kVp)</li>
                                <li data-id="hp3084u7n" data-path="src/components/XRayFilteringEducation.tsx">• ترشيح متخصص للأنسجة الناعمة</li>
                              </ul>
                            </div>
                            <div className="bg-orange-50 p-3 rounded" data-id="m13t08i26" data-path="src/components/XRayFilteringEducation.tsx">
                              <h4 className="font-medium text-orange-700 mb-2" data-id="aueqt2fc8" data-path="src/components/XRayFilteringEducation.tsx">الفوائد:</h4>
                              <ul className="text-sm text-orange-600 space-y-1" data-id="h2vqx740k" data-path="src/components/XRayFilteringEducation.tsx">
                                <li data-id="k6y2y1czk" data-path="src/components/XRayFilteringEducation.tsx">• تحسين التباين للأنسجة الناعمة</li>
                                <li data-id="tmqw4f50i" data-path="src/components/XRayFilteringEducation.tsx">• دقة عالية في التشخيص</li>
                                <li data-id="og2k0gvpi" data-path="src/components/XRayFilteringEducation.tsx">• جرعة محسنة للمريضة</li>
                              </ul>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="bg-gradient-to-r from-teal-50 to-blue-50 p-6 rounded-lg border" data-id="z249dn9bt" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="text-xl font-semibold text-teal-800 mb-4" data-id="j90zznuq4" data-path="src/components/XRayFilteringEducation.tsx">6.6 تأثير الترشيح على الطيف والكثافة والجرعة</h3>
                      <div className="grid md:grid-cols-3 gap-6" data-id="g5xlmpxxt" data-path="src/components/XRayFilteringEducation.tsx">
                        <div className="bg-white p-4 rounded-lg border" data-id="cxpl2hhme" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-teal-700 mb-2" data-id="i2wls2ox0" data-path="src/components/XRayFilteringEducation.tsx">تأثير على الطيف:</h4>
                          <ul className="text-sm text-gray-600 space-y-1" data-id="jgrlsl2m6" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="z0wyb11iq" data-path="src/components/XRayFilteringEducation.tsx">• إزالة الفوتونات منخفضة الطاقة</li>
                            <li data-id="xwjkszv1p" data-path="src/components/XRayFilteringEducation.tsx">• زيادة متوسط طاقة الشعاع</li>
                            <li data-id="4x0idosrz" data-path="src/components/XRayFilteringEducation.tsx">• تحسين تجانس الطيف</li>
                            <li data-id="5hi2hh7tz" data-path="src/components/XRayFilteringEducation.tsx">• تقليل المدى الطيفي</li>
                          </ul>
                        </div>
                        <div className="bg-white p-4 rounded-lg border" data-id="mxfah6j7r" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-teal-700 mb-2" data-id="ipfyzqcoj" data-path="src/components/XRayFilteringEducation.tsx">تأثير على الكثافة:</h4>
                          <ul className="text-sm text-gray-600 space-y-1" data-id="gpphsp0mx" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="js5sooh8n" data-path="src/components/XRayFilteringEducation.tsx">• تقليل الكثافة الإجمالية</li>
                            <li data-id="uhqgm8bwm" data-path="src/components/XRayFilteringEducation.tsx">• تحسين نوعية الإشعاع</li>
                            <li data-id="sldie3cgp" data-path="src/components/XRayFilteringEducation.tsx">• زيادة النفاذية النسبية</li>
                            <li data-id="jsy0nmnbr" data-path="src/components/XRayFilteringEducation.tsx">• تحسين كفاءة التصوير</li>
                          </ul>
                        </div>
                        <div className="bg-white p-4 rounded-lg border" data-id="tx7garph8" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-teal-700 mb-2" data-id="0emh1pxzb" data-path="src/components/XRayFilteringEducation.tsx">تأثير على الجرعة:</h4>
                          <ul className="text-sm text-gray-600 space-y-1" data-id="71tke954l" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="6lfuao58k" data-path="src/components/XRayFilteringEducation.tsx">• تقليل الجرعة السطحية</li>
                            <li data-id="rctzbxywo" data-path="src/components/XRayFilteringEducation.tsx">• تحسين توزيع الجرعة</li>
                            <li data-id="lhpn1qmx1" data-path="src/components/XRayFilteringEducation.tsx">• تقليل الجرعة غير المفيدة</li>
                            <li data-id="h9ea5014c" data-path="src/components/XRayFilteringEducation.tsx">• تحسين نسبة الفائدة/المخاطر</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* التقييم */}
              <TabsContent value="assessment" className="space-y-6" data-id="98dbyo802" data-path="src/components/XRayFilteringEducation.tsx">
                <Card data-id="36lscv7v8" data-path="src/components/XRayFilteringEducation.tsx">
                  <CardHeader data-id="ylku823cu" data-path="src/components/XRayFilteringEducation.tsx">
                    <CardTitle className="text-2xl flex items-center gap-3" data-id="65oilgu2y" data-path="src/components/XRayFilteringEducation.tsx">
                      <div className="p-2 rounded-lg bg-indigo-100 text-indigo-600" data-id="qhgssa18n" data-path="src/components/XRayFilteringEducation.tsx">
                        <Award className="w-6 h-6" data-id="inmejllli" data-path="src/components/XRayFilteringEducation.tsx" />
                      </div>
                      أهداف التعلم والتقييم
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-8" data-id="lgnqxzafj" data-path="src/components/XRayFilteringEducation.tsx">
                    
                    <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-lg border" data-id="7dxuyq8s9" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="text-xl font-semibold text-indigo-800 mb-4" data-id="badi7gksk" data-path="src/components/XRayFilteringEducation.tsx">أهداف التعلم</h3>
                      <div className="grid md:grid-cols-2 gap-6" data-id="ivho0ao2n" data-path="src/components/XRayFilteringEducation.tsx">
                        <div data-id="rfc3mtq25" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-indigo-700 mb-3" data-id="64oyha69m" data-path="src/components/XRayFilteringEducation.tsx">المعرفة النظرية:</h4>
                          <ul className="text-sm text-gray-600 space-y-2" data-id="qpl9unika" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="pfeyps72k" data-path="src/components/XRayFilteringEducation.tsx">• فهم مفهوم جودة شعاع الأشعة السينية</li>
                            <li data-id="qyai1n0tz" data-path="src/components/XRayFilteringEducation.tsx">• تعلم حساب وقياس HVL و TVL</li>
                            <li data-id="sde1qwffb" data-path="src/components/XRayFilteringEducation.tsx">• فهم عملية تقوية الشعاع وفوائدها</li>
                            <li data-id="q16jgtg5m" data-path="src/components/XRayFilteringEducation.tsx">• معرفة أنواع المرشحات واستخداماتها</li>
                            <li data-id="cbmevx1j0" data-path="src/components/XRayFilteringEducation.tsx">• فهم التطبيقات السريرية المختلفة</li>
                          </ul>
                        </div>
                        <div data-id="s8wuoh7ri" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium text-indigo-700 mb-3" data-id="zdecn3tgs" data-path="src/components/XRayFilteringEducation.tsx">المهارات العملية:</h4>
                          <ul className="text-sm text-gray-600 space-y-2" data-id="w3ii7rxkt" data-path="src/components/XRayFilteringEducation.tsx">
                            <li data-id="p1c0fv7vh" data-path="src/components/XRayFilteringEducation.tsx">• قياس HVL باستخدام المعدات المناسبة</li>
                            <li data-id="n0ysbcr60" data-path="src/components/XRayFilteringEducation.tsx">• اختيار الترشيح المناسب للفحص</li>
                            <li data-id="uxr15tz97" data-path="src/components/XRayFilteringEducation.tsx">• تحسين معايير التصوير</li>
                            <li data-id="xnesv82u7" data-path="src/components/XRayFilteringEducation.tsx">• تطبيق مبادئ الحماية الإشعاعية</li>
                            <li data-id="r1ysnx9b3" data-path="src/components/XRayFilteringEducation.tsx">• تقييم جودة الصورة</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-6" data-id="uaxp1nsgy" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="text-xl font-semibold text-gray-800 border-b pb-2" data-id="eol1cfwqf" data-path="src/components/XRayFilteringEducation.tsx">المصطلحات الرئيسية</h3>
                      <div className="grid md:grid-cols-2 gap-4" data-id="pnf2sjtj6" data-path="src/components/XRayFilteringEducation.tsx">
                        <div className="space-y-3" data-id="q0oluv7bf" data-path="src/components/XRayFilteringEducation.tsx">
                          <div className="bg-white p-3 border rounded-lg" data-id="lg16f5dwp" data-path="src/components/XRayFilteringEducation.tsx">
                            <strong className="text-blue-600" data-id="qlivyoigr" data-path="src/components/XRayFilteringEducation.tsx">HVL:</strong> طبقة نصف القيمة
                          </div>
                          <div className="bg-white p-3 border rounded-lg" data-id="8he27v1jr" data-path="src/components/XRayFilteringEducation.tsx">
                            <strong className="text-blue-600" data-id="ko78fmkqj" data-path="src/components/XRayFilteringEducation.tsx">TVL:</strong> طبقة القيمة العاشرة
                          </div>
                          <div className="bg-white p-3 border rounded-lg" data-id="h3z3yfn5h" data-path="src/components/XRayFilteringEducation.tsx">
                            <strong className="text-blue-600" data-id="awm40xlfg" data-path="src/components/XRayFilteringEducation.tsx">Beam Hardening:</strong> تقوية الشعاع
                          </div>
                          <div className="bg-white p-3 border rounded-lg" data-id="9pk71uy14" data-path="src/components/XRayFilteringEducation.tsx">
                            <strong className="text-blue-600" data-id="iv96zxw3u" data-path="src/components/XRayFilteringEducation.tsx">K-Edge Filters:</strong> مرشحات الحافة K
                          </div>
                        </div>
                        <div className="space-y-3" data-id="2n5q9e1ho" data-path="src/components/XRayFilteringEducation.tsx">
                          <div className="bg-white p-3 border rounded-lg" data-id="pwca9j18q" data-path="src/components/XRayFilteringEducation.tsx">
                            <strong className="text-green-600" data-id="h2pfhyt29" data-path="src/components/XRayFilteringEducation.tsx">Attenuation:</strong> التوهين
                          </div>
                          <div className="bg-white p-3 border rounded-lg" data-id="923ergn99" data-path="src/components/XRayFilteringEducation.tsx">
                            <strong className="text-green-600" data-id="mqe2l1h5h" data-path="src/components/XRayFilteringEducation.tsx">Spectral Distribution:</strong> التوزيع الطيفي
                          </div>
                          <div className="bg-white p-3 border rounded-lg" data-id="qhdzo264v" data-path="src/components/XRayFilteringEducation.tsx">
                            <strong className="text-green-600" data-id="v5vg0vw5z" data-path="src/components/XRayFilteringEducation.tsx">Penetrability:</strong> قابلية النفاذ
                          </div>
                          <div className="bg-white p-3 border rounded-lg" data-id="bsy1lec2s" data-path="src/components/XRayFilteringEducation.tsx">
                            <strong className="text-green-600" data-id="5n3z0ssof" data-path="src/components/XRayFilteringEducation.tsx">Inherent Filtration:</strong> الترشيح المتأصل
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 p-6 rounded-lg" data-id="g8faodo5a" data-path="src/components/XRayFilteringEducation.tsx">
                      <h3 className="text-xl font-semibold text-yellow-800 mb-4" data-id="3wakisa2v" data-path="src/components/XRayFilteringEducation.tsx">أسئلة التقييم الذاتي</h3>
                      <div className="space-y-4" data-id="cuwlokrnf" data-path="src/components/XRayFilteringEducation.tsx">
                        <div className="bg-white p-4 rounded border" data-id="q05kebel5" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium mb-2" data-id="b7h33rtxu" data-path="src/components/XRayFilteringEducation.tsx">1. ما هو تعريف طبقة نصف القيمة (HVL)؟</h4>
                          <p className="text-sm text-gray-600" data-id="ffdj4o47c" data-path="src/components/XRayFilteringEducation.tsx">سماكة المادة المطلوبة لتقليل شدة الشعاع إلى النصف</p>
                        </div>
                        <div className="bg-white p-4 rounded border" data-id="qzl0wyatq" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium mb-2" data-id="rq0h8ql81" data-path="src/components/XRayFilteringEducation.tsx">2. ما الفائدة الرئيسية من تقوية الشعاع؟</h4>
                          <p className="text-sm text-gray-600" data-id="z4ahr4h24" data-path="src/components/XRayFilteringEducation.tsx">تحسين جودة الشعاع وتقليل الجرعة السطحية للمريض</p>
                        </div>
                        <div className="bg-white p-4 rounded border" data-id="iforoiwz5" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium mb-2" data-id="re3trhpaz" data-path="src/components/XRayFilteringEducation.tsx">3. متى نستخدم مرشحات النحاس بدلاً من الألمنيوم؟</h4>
                          <p className="text-sm text-gray-600" data-id="v107hcrdm" data-path="src/components/XRayFilteringEducation.tsx">عند الجهد العالي (&gt;100 kVp) وللمرضى كبار الحجم</p>
                        </div>
                        <div className="bg-white p-4 rounded border" data-id="8y7zml1vg" data-path="src/components/XRayFilteringEducation.tsx">
                          <h4 className="font-medium mb-2" data-id="ftwv68hoc" data-path="src/components/XRayFilteringEducation.tsx">4. ما هي خصائص مرشحات K-Edge في تصوير الثدي؟</h4>
                          <p className="text-sm text-gray-600" data-id="rtf0b3auc" data-path="src/components/XRayFilteringEducation.tsx">تحسين التباين للأنسجة الناعمة عبر الامتصاص الانتقائي</p>
                        </div>
                      </div>
                    </div>

                    <div className="text-center space-y-4" data-id="m05njtlf8" data-path="src/components/XRayFilteringEducation.tsx">
                      <div className="bg-green-100 border border-green-300 p-4 rounded-lg" data-id="xvbffyk4q" data-path="src/components/XRayFilteringEducation.tsx">
                        <h3 className="text-lg font-semibold text-green-800 mb-2" data-id="auf2b8h3f" data-path="src/components/XRayFilteringEducation.tsx">تهانينا! لقد أكملت الدرس</h3>
                        <p className="text-green-700" data-id="zgo979isx" data-path="src/components/XRayFilteringEducation.tsx">لقد تعلمت المفاهيم الأساسية لترشيح شعاع الأشعة السينية وتطبيقاتها السريرية</p>
                      </div>
                      <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700" data-id="8hx8abs24" data-path="src/components/XRayFilteringEducation.tsx">
                        <Award className="w-5 h-5 ml-2" data-id="ui3xctzx3" data-path="src/components/XRayFilteringEducation.tsx" />
                        الحصول على شهادة الإتمام
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>);

};

export default XRayFilteringEducation;