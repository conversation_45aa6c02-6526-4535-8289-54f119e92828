import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';
import { LanguageProvider } from '@/contexts/LanguageContext';
import HomePage from '@/pages/HomePage';
import CoursePage from '@/pages/CoursePage';
import NotFound from '@/pages/NotFound';
import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000 // 10 minutes
    }
  }
});

function App() {
  return (
    <QueryClientProvider client={queryClient} data-id="b441934ne" data-path="src/App.tsx">
      <LanguageProvider data-id="9dbx7rw0h" data-path="src/App.tsx">
        <TooltipProvider data-id="dtedcljgh" data-path="src/App.tsx">
          <Router data-id="y4pk05rku" data-path="src/App.tsx">
            <Routes data-id="mlsp6runq" data-path="src/App.tsx">
              <Route path="/" element={<HomePage data-id="vi0fwrdt4" data-path="src/App.tsx" />} data-id="7gu18rlzv" data-path="src/App.tsx" />
              <Route path="/course" element={<CoursePage data-id="7boou22a4" data-path="src/App.tsx" />} data-id="5t9dh8rl2" data-path="src/App.tsx" />
              <Route path="*" element={<NotFound data-id="2qqw36sjc" data-path="src/App.tsx" />} data-id="lt7o2mrsp" data-path="src/App.tsx" />
            </Routes>
            <Toaster data-id="2zcf8de0l" data-path="src/App.tsx" />
          </Router>
        </TooltipProvider>
      </LanguageProvider>
    </QueryClientProvider>);

}

export default App;