import React from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ChevronDown,
  ChevronUp,
  Cpu,
  Zap,
  Lightbulb,
  BarChart3,
  Camera,
  Grid3X3,
  Layers,
  Target,
  Book,
  FileText,
  HelpCircle,
  Activity,
  Settings,
  Atom,
  Eye } from
'lucide-react';

const Chapter12DetectorSimulation = () => {
  const [openSections, setOpenSections] = React.useState<Record<string, boolean>>({});

  const toggleSection = (section: string) => {
    setOpenSections((prev) => ({ ...prev, [section]: !prev[section] }));
  };

  const learningObjectives = [
  "فهم طرق نمذجة ترسب الطاقة في مواد الكاشف المختلفة",
  "تعلم محاكاة انتشار ضوء الومض والتداخل البصري",
  "إتقان نمذجة جمع الشحنات والضوضاء الإلكترونية",
  "استيعاب تحليل الأنظمة الخطية المتتالية",
  "تطبيق تقنيات محاكاة تكوين صور الإسقاط الشعاعي",
  "دمج شبكات مكافحة التشتت في عمليات المحاكاة"];


  const keyTerms = [
  { term: "محاكاة مونت كارلو", definition: "تقنية رقمية لمحاكاة العمليات الفيزيائية العشوائية" },
  { term: "تحليل الأنظمة المتتالية", definition: "طريقة لتحليل أداء النظام من خلال سلسلة من المراحل" },
  { term: "انتشار ضوء الومض", definition: "توزيع الضوء المنبعث من نقطة واحدة في الكاشف" },
  { term: "التداخل البصري", definition: "انتشار الضوء بين البكسلات المجاورة" },
  { term: "ضوضاء كم الفوتونات", definition: "التقلبات الإحصائية في عدد الفوتونات المكتشفة" },
  { term: "ضوضاء إلكترونية", definition: "الضوضاء الناتجة عن دوائر القراءة الإلكترونية" },
  { term: "دالة انتشار النقطة", definition: "استجابة النظام لإشارة نقطية" },
  { term: "معامل الشبكة", definition: "النسبة بين ارتفاع الشبكة وعرض الفراغات بينها" }];


  const problems = [
  {
    id: 1,
    question: "احسب كفاءة امتصاص الأشعة السينية لطبقة CsI بسمك 500 ميكرومتر عند طاقة 60 keV.",
    solution: "باستخدام معامل التوهين الشامل μ = 2.1 cm⁻¹ لـ CsI عند 60 keV: كفاءة الامتصاص = 1 - exp(-μt) = 1 - exp(-2.1 × 0.05) = 1 - exp(-0.105) = 0.10 أو 10%"
  },
  {
    id: 2,
    question: "ما تأثير زيادة سمك طبقة الومض على دالة انتشار النقطة؟",
    solution: "زيادة سمك طبقة الومض يؤدي إلى: 1) زيادة انتشار الضوء الجانبي، 2) تدهور الدقة المكانية، 3) زيادة كفاءة امتصاص الأشعة، 4) تحسن نسبة الإشارة إلى الضوضاء."
  },
  {
    id: 3,
    question: "كيف تؤثر شبكة مكافحة التشتت بنسبة 10:1 على جودة الصورة؟",
    solution: "شبكة بنسبة 10:1 تقلل التشتت بحوالي 80-90%، تحسن التباين بنسبة 2-3 أضعاف، لكنها تزيد الجرعة المطلوبة بحوالي 2-3 أضعاف وقد تقلل الدقة المكانية قليلاً."
  }];


  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50" data-id="ya2n8v6xk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
      <Navigation data-id="kkoo7fbv3" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
      <div className="container mx-auto px-4 py-8 max-w-4xl" data-id="e3a8bdsj8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
        <div className="text-center mb-12" data-id="fzn5qnesq" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <div className="inline-flex items-center gap-3 bg-white px-6 py-3 rounded-full shadow-lg mb-6" data-id="hh8zvi4pt" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <Cpu className="h-8 w-8 text-purple-600" data-id="n30k92dda" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
            <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent" data-id="57lrmi058" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              الفصل الثاني عشر
            </span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4" data-id="9mogpecc4" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            محاكاة استجابة الكاشف وتكوين الصورة
          </h1>
          <p className="text-xl text-gray-600 mb-6" data-id="7fvpmw8yy" data-path="src/pages/Chapter12DetectorSimulation.tsx">النمذجة الحاسوبية والمحاكاة</p>
          <div className="flex flex-wrap justify-center gap-3" data-id="gtzilczgi" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <Badge variant="secondary" className="px-4 py-2" data-id="9735rfri6" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <Cpu className="h-4 w-4 mr-2" data-id="242oug42j" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              محاكاة رقمية
            </Badge>
            <Badge variant="secondary" className="px-4 py-2" data-id="exhivyxmt" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <Lightbulb className="h-4 w-4 mr-2" data-id="mhhqnk9mr" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              انتشار الضوء
            </Badge>
            <Badge variant="secondary" className="px-4 py-2" data-id="guwnmv2me" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <Grid3X3 className="h-4 w-4 mr-2" data-id="l6itp7lo3" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              شبكات التشتت
            </Badge>
          </div>
        </div>

        {/* Learning Objectives */}
        <Card className="mb-8 border-l-4 border-l-purple-500" data-id="rvul6pccx" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <CardHeader data-id="mfwbot8li" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CardTitle className="flex items-center gap-3" data-id="tchj05icp" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <Target className="h-6 w-6 text-purple-600" data-id="0zkwnytxa" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              أهداف التعلم
            </CardTitle>
          </CardHeader>
          <CardContent data-id="thelw3pdj" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <ul className="space-y-3" data-id="7zbjyz1wu" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              {learningObjectives.map((objective, index) =>
              <li key={index} className="flex items-start gap-3" data-id="9qha8otsh" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="h-2 w-2 bg-purple-500 rounded-full mt-3 flex-shrink-0" data-id="y2ix5qsyq" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                  <span className="text-gray-700" data-id="grbp8wvdd" data-path="src/pages/Chapter12DetectorSimulation.tsx">{objective}</span>
                </li>
              )}
            </ul>
          </CardContent>
        </Card>

        {/* Section 12.1: Energy Deposition Modeling */}
        <Card className="mb-6" data-id="z9ohtsn43" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['energy-deposition']} onOpenChange={() => toggleSection('energy-deposition')} data-id="1skx3mrno" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="1uojv7v6f" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="xbwix1ux2" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="hn6hs88pl" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="vme4iazjg" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Atom className="h-6 w-6 text-red-600" data-id="n140zgqty" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="ea8cp4603" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.1 نمذجة ترسب الطاقة في مواد الكاشف</span>
                  </div>
                  {openSections['energy-deposition'] ? <ChevronUp data-id="75dw7wcoc" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="pwitvrqm7" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="eryeybfgb" data-path="src/pages/Chapter12DetectorSimulation.tsx">محاكاة التفاعلات والترسب</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="2p5ar5pzx" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="mnva5eb42" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <Alert data-id="gu7o9ucxy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Atom className="h-4 w-4" data-id="ytbg8if6a" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                  <AlertDescription data-id="f43nwv3et" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    نمذجة ترسب الطاقة أساسية لفهم استجابة الكاشف وتحسين تصميمه.
                  </AlertDescription>
                </Alert>

                <div className="grid md:grid-cols-2 gap-6" data-id="rbandvltl" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div data-id="r5q9gfolw" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="hm0p295za" data-path="src/pages/Chapter12DetectorSimulation.tsx">طرق المحاكاة:</h4>
                    <ul className="space-y-2 text-gray-700" data-id="odm566fo9" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="50bgiw22l" data-path="src/pages/Chapter12DetectorSimulation.tsx">• محاكاة مونت كارلو للتفاعلات</li>
                      <li data-id="gy5e2d037" data-path="src/pages/Chapter12DetectorSimulation.tsx">• نمذجة النقل الإلكتروني</li>
                      <li data-id="rxtw69jx8" data-path="src/pages/Chapter12DetectorSimulation.tsx">• حساب ترسب الطاقة المحلي</li>
                      <li data-id="rc79j6i1t" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تتبع المسارات الثانوية</li>
                    </ul>
                  </div>
                  <div data-id="1os5gbvlb" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="amwmt5ijo" data-path="src/pages/Chapter12DetectorSimulation.tsx">العوامل المؤثرة:</h4>
                    <ul className="space-y-2 text-gray-700" data-id="ufgil62p3" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="fyjnt2laa" data-path="src/pages/Chapter12DetectorSimulation.tsx">• طاقة الأشعة السينية الساقطة</li>
                      <li data-id="80plqa0oh" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تركيب المادة وكثافتها</li>
                      <li data-id="0lx3p72p3" data-path="src/pages/Chapter12DetectorSimulation.tsx">• هندسة الكاشف وسمكه</li>
                      <li data-id="xntiik6nr" data-path="src/pages/Chapter12DetectorSimulation.tsx">• وجود طبقات متعددة</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-red-50 p-4 rounded-lg" data-id="f769iwv3u" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-red-800 mb-2" data-id="00f2iitmi" data-path="src/pages/Chapter12DetectorSimulation.tsx">أنواع التفاعلات المنمذجة:</h4>
                  <div className="grid md:grid-cols-3 gap-4 text-red-700" data-id="06q7ipv69" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <div data-id="smbdlmfl0" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold" data-id="uccjc7kz4" data-path="src/pages/Chapter12DetectorSimulation.tsx">التأثير الكهروضوئي:</h5>
                      <p className="text-sm" data-id="uldc24dzh" data-path="src/pages/Chapter12DetectorSimulation.tsx">امتصاص كامل للفوتون</p>
                    </div>
                    <div data-id="y0qz90y1o" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold" data-id="nez340ln9" data-path="src/pages/Chapter12DetectorSimulation.tsx">تشتت كومبتون:</h5>
                      <p className="text-sm" data-id="53d94heo0" data-path="src/pages/Chapter12DetectorSimulation.tsx">امتصاص جزئي مع تشتت</p>
                    </div>
                    <div data-id="y9jt7c6xv" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold" data-id="5rc7vfyu9" data-path="src/pages/Chapter12DetectorSimulation.tsx">التشتت المترابط:</h5>
                      <p className="text-sm" data-id="xja0jyg7w" data-path="src/pages/Chapter12DetectorSimulation.tsx">تغيير اتجاه بدون فقدان طاقة</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.2: Light Propagation Simulation */}
        <Card className="mb-6" data-id="jq5ybs7qy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['light-propagation']} onOpenChange={() => toggleSection('light-propagation')} data-id="iwcuh40pc" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="u6py29fzo" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="147bmuhk6" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="zkj2tm6ky" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="rggspovqs" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Lightbulb className="h-6 w-6 text-yellow-600" data-id="56xp2k37y" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="1u6fx32xz" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.2 محاكاة انتشار ضوء الومض والتداخل البصري</span>
                  </div>
                  {openSections['light-propagation'] ? <ChevronUp data-id="y02jmln6b" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="rpm1hef6t" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="0ukwqnqzn" data-path="src/pages/Chapter12DetectorSimulation.tsx">نمذجة الضوء والتداخل البصري</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="y2d01msxg" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="wd5cjm5f8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="bwkm3v27v" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Card data-id="igc4x3dxs" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="qar1kt15a" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg flex items-center gap-2" data-id="tst1qijmk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <Eye className="h-5 w-5 text-blue-600" data-id="2kdfpxu5h" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                        انتشار الضوء
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4" data-id="0ochqqkdo" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold text-gray-800" data-id="w8vkodwnn" data-path="src/pages/Chapter12DetectorSimulation.tsx">المعاملات الرئيسية:</h5>
                      <ul className="space-y-2 text-gray-700" data-id="4nscerex8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="lm878bcby" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معامل الانكسار للمادة</li>
                        <li data-id="60l83xvv0" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معامل الامتصاص البصري</li>
                        <li data-id="zkerh29qo" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معامل التشتت</li>
                        <li data-id="80hi2p9a7" data-path="src/pages/Chapter12DetectorSimulation.tsx">• عامل الانعكاس في الحدود</li>
                      </ul>
                      
                      <h5 className="font-semibold text-gray-800" data-id="b83le5uus" data-path="src/pages/Chapter12DetectorSimulation.tsx">تقنيات المحاكاة:</h5>
                      <ul className="space-y-1 text-gray-700" data-id="76gouuhaf" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="qf3o7hxx4" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تتبع الأشعة الضوئية</li>
                        <li data-id="gcvnku7q7" data-path="src/pages/Chapter12DetectorSimulation.tsx">• طريقة مونت كارلو البصرية</li>
                        <li data-id="tk8j1qwef" data-path="src/pages/Chapter12DetectorSimulation.tsx">• نمذجة الانتشار</li>
                        <li data-id="k7f9xgmg8" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معادلات النقل الإشعاعي</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card data-id="dc89d8fx4" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="2kfr2k4hl" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg flex items-center gap-2" data-id="rojcruvfu" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <Layers className="h-5 w-5 text-green-600" data-id="pfx1asj14" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                        التداخل البصري
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4" data-id="kku19wy1q" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold text-gray-800" data-id="bjhv17noh" data-path="src/pages/Chapter12DetectorSimulation.tsx">مصادر التداخل:</h5>
                      <ul className="space-y-2 text-gray-700" data-id="7n9fjtijd" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="q46h2711g" data-path="src/pages/Chapter12DetectorSimulation.tsx">• انتشار الضوء الجانبي</li>
                        <li data-id="ey6injiwc" data-path="src/pages/Chapter12DetectorSimulation.tsx">• انعكاسات الحدود</li>
                        <li data-id="ia78qah6e" data-path="src/pages/Chapter12DetectorSimulation.tsx">• عيوب البنية البلورية</li>
                        <li data-id="xjbefu8sr" data-path="src/pages/Chapter12DetectorSimulation.tsx">• طبقات الحماية</li>
                      </ul>
                      
                      <h5 className="font-semibold text-gray-800" data-id="sfnp8nl1o" data-path="src/pages/Chapter12DetectorSimulation.tsx">التأثيرات على الأداء:</h5>
                      <ul className="space-y-1 text-gray-700" data-id="mfwyp78c7" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="pwpr5u93m" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تدهور الدقة المكانية</li>
                        <li data-id="yyvcau9hg" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تقليل التباين</li>
                        <li data-id="qhmkc9rpj" data-path="src/pages/Chapter12DetectorSimulation.tsx">• زيادة الضوضاء المتعلقة</li>
                        <li data-id="wuga5qrvg" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تأثير على MTF</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <div className="bg-yellow-50 p-4 rounded-lg" data-id="ryb32alln" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-yellow-800 mb-2" data-id="5ew7hncfi" data-path="src/pages/Chapter12DetectorSimulation.tsx">دالة انتشار النقطة (PSF):</h4>
                  <p className="text-yellow-700" data-id="ig1pqgnnd" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    تصف كيفية انتشار الضوء من نقطة واحدة في الكاشف، وتحدد الدقة المكانية النهائية للنظام.
                  </p>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.3: Charge Collection and Electronic Noise */}
        <Card className="mb-6" data-id="qgruwzpof" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['charge-collection']} onOpenChange={() => toggleSection('charge-collection')} data-id="rmeac1m48" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="cyhux33wz" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="u3kgxzn5g" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="e37x7cdq8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="foa1e8c4c" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Zap className="h-6 w-6 text-blue-600" data-id="jqbashqmk" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="rbgakehvv" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.3 نمذجة جمع الشحنات والضوضاء الإلكترونية</span>
                  </div>
                  {openSections['charge-collection'] ? <ChevronUp data-id="ttj3qat43" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="lvgys2q1p" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="w1j7hjo36" data-path="src/pages/Chapter12DetectorSimulation.tsx">الشحنات والضوضاء الإلكترونية</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="z5hew7noy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="g3vdp2vqz" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <div className="grid gap-6" data-id="gg9exayww" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="grid md:grid-cols-2 gap-4" data-id="qo7w4jx7l" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <div data-id="228rvp0fe" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="xy3s8jy8v" data-path="src/pages/Chapter12DetectorSimulation.tsx">عملية جمع الشحنات:</h4>
                      <ul className="space-y-2 text-gray-700" data-id="878tq0i7b" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="aprq7301h" data-path="src/pages/Chapter12DetectorSimulation.tsx">• توليد أزواج الإلكترون-الثقب</li>
                        <li data-id="bgv7bw1e7" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الانتشار والانسياق الكهربائي</li>
                        <li data-id="bi5uupluj" data-path="src/pages/Chapter12DetectorSimulation.tsx">• إعادة التركيب والالتقاط</li>
                        <li data-id="5b7iboc85" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التحكم بالمجال الكهربائي</li>
                      </ul>
                    </div>
                    <div data-id="gunnrrdlh" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="sfia0byzd" data-path="src/pages/Chapter12DetectorSimulation.tsx">مصادر الضوضاء:</h4>
                      <ul className="space-y-2 text-gray-700" data-id="3ry3uk09r" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <li data-id="zpy3xxnzm" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء كم الفوتونات</li>
                        <li data-id="in3x1y2p0" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الضوضاء الحرارية</li>
                        <li data-id="ugy8dcuyn" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء التيار المظلم</li>
                        <li data-id="qoygu2ivr" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء دوائر القراءة</li>
                      </ul>
                    </div>
                  </div>

                  <Card data-id="gjadrh29r" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="d4j183qs1" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg" data-id="z94hohegm" data-path="src/pages/Chapter12DetectorSimulation.tsx">نماذج الضوضاء</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4" data-id="vlshzxknb" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="grid md:grid-cols-2 gap-4" data-id="ww6njp3qk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <div data-id="7biw8znzi" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="hwiq1gegn" data-path="src/pages/Chapter12DetectorSimulation.tsx">الضوضاء الأولية:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="gtkk87jb3" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="dta3675dw" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء بواسون للفوتونات</li>
                            <li data-id="w50mflskw" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تقلبات طاقة التفاعل</li>
                            <li data-id="l7pfjlas0" data-path="src/pages/Chapter12DetectorSimulation.tsx">• كفاءة التحويل المتغيرة</li>
                          </ul>
                        </div>
                        <div data-id="imwqohmu4" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="8cjsr5s1p" data-path="src/pages/Chapter12DetectorSimulation.tsx">الضوضاء الثانوية:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="0c2dsaal6" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="bo1f9n96f" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تقلبات التضخيم</li>
                            <li data-id="e4ak87ue9" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء دوائر القراءe</li>
                            <li data-id="x60jhy4y5" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التداخل الكهرومغناطيسي</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg" data-id="rm406h6uy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-blue-800 mb-2" data-id="34lett6qo" data-path="src/pages/Chapter12DetectorSimulation.tsx">عامل فانو (Fano Factor):</h4>
                  <p className="text-blue-700" data-id="r5japgrqj" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    يصف التقلبات الإحصائية في عدد حاملات الشحنة المولدة، وهو أقل من الوحدة للمواد شبه الموصلة.
                  </p>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.4: Cascaded Linear Systems Analysis */}
        <Card className="mb-6" data-id="pvqo1e95d" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['cascaded-systems']} onOpenChange={() => toggleSection('cascaded-systems')} data-id="mgvyjyqs6" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="6tlgta0nk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="76z09pbc8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="l9c1legnz" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="2bl21h075" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <BarChart3 className="h-6 w-6 text-green-600" data-id="2z1761vm5" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="6vsuuyxcn" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.4 تحليل الأنظمة الخطية المتتالية لنمذجة الكاشف</span>
                  </div>
                  {openSections['cascaded-systems'] ? <ChevronUp data-id="tcqy9rk4s" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="1ovnz2dgs" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="ofchnkivq" data-path="src/pages/Chapter12DetectorSimulation.tsx">نمذجة النظام كسلسلة من المراحل</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="y7v0pmn66" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="n25engsyq" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <Alert data-id="7pz4ekkkp" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Activity className="h-4 w-4" data-id="t6p8b5u2f" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                  <AlertDescription data-id="a3e7j76c6" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    تحليل الأنظمة المتتالية يسمح بنمذجة دقيقة لأداء الكاشف من خلال تقسيمه لمراحل منفصلة.
                  </AlertDescription>
                </Alert>

                <div className="grid gap-6" data-id="k79vvttn5" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Card data-id="ed386i9rb" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="fafzpceeu" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg" data-id="fx4u9hj4l" data-path="src/pages/Chapter12DetectorSimulation.tsx">مراحل النظام</CardTitle>
                    </CardHeader>
                    <CardContent data-id="lm2yujoap" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="grid md:grid-cols-2 gap-4" data-id="f1p8q0oe1" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <div data-id="pzpyttsbv" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="ubw89a4bl" data-path="src/pages/Chapter12DetectorSimulation.tsx">المرحلة الأولى - الامتصاص:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="0twwlxvu5" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="3ry0vdjqo" data-path="src/pages/Chapter12DetectorSimulation.tsx">• امتصاص الأشعة السينية</li>
                            <li data-id="isyjuo8we" data-path="src/pages/Chapter12DetectorSimulation.tsx">• كفاءة الكم الأولية</li>
                            <li data-id="qate7t9vy" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء بواسون</li>
                          </ul>
                        </div>
                        <div data-id="lne6sk0up" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="1yf96dn8y" data-path="src/pages/Chapter12DetectorSimulation.tsx">المرحلة الثانية - التحويل:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="yl760p3cf" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="3f0azadhk" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تحويل الطاقة إلى ضوء/شحنة</li>
                            <li data-id="8ezquzpf4" data-path="src/pages/Chapter12DetectorSimulation.tsx">• كسب التحويل</li>
                            <li data-id="1p1zhead4" data-path="src/pages/Chapter12DetectorSimulation.tsx">• انتشار الإشارة</li>
                          </ul>
                        </div>
                        <div data-id="f6srjk9fl" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="qnrf30t8v" data-path="src/pages/Chapter12DetectorSimulation.tsx">المرحلة الثالثة - الكشف:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="rilllopcv" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="1d9mb40ie" data-path="src/pages/Chapter12DetectorSimulation.tsx">• جمع الضوء/الشحنة</li>
                            <li data-id="tte61766u" data-path="src/pages/Chapter12DetectorSimulation.tsx">• كفاءة الجمع</li>
                            <li data-id="6rq38thko" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضوضاء إضافية</li>
                          </ul>
                        </div>
                        <div data-id="2b6ulbt1w" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="m9u97z309" data-path="src/pages/Chapter12DetectorSimulation.tsx">المرحلة النهائية - المعالجة:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="erllu2x3h" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="my7a5dd3r" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التضخيم الإلكتروني</li>
                            <li data-id="xg0vzkrg6" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الرقمنة</li>
                            <li data-id="od6wslcrl" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معالجة الإشارة</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card data-id="w3l2oi9rm" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="urgniwcuq" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg" data-id="knasfq9kg" data-path="src/pages/Chapter12DetectorSimulation.tsx">المعادلات الأساسية</CardTitle>
                    </CardHeader>
                    <CardContent data-id="47a905lw0" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="space-y-4" data-id="4ddxyxauh" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <div className="bg-gray-50 p-4 rounded-lg" data-id="l38lx3k5h" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold mb-2" data-id="w74wfeyod" data-path="src/pages/Chapter12DetectorSimulation.tsx">كفاءة الكم الاستقصائية الإجمالية:</h5>
                          <p className="font-mono" data-id="7e5i3ig3u" data-path="src/pages/Chapter12DetectorSimulation.tsx">DQE = η₀ × T₁² × T₂² × ... × Tₙ²</p>
                          <p className="text-sm text-gray-600 mt-1" data-id="fzjw87l7b" data-path="src/pages/Chapter12DetectorSimulation.tsx">حيث η₀ هي كفاءة الكم الأولية و Tᵢ هي نقل كل مرحلة</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg" data-id="y8c6vxbg8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold mb-2" data-id="h4om0q3w8" data-path="src/pages/Chapter12DetectorSimulation.tsx">دالة نقل التعديل الإجمالية:</h5>
                          <p className="font-mono" data-id="834hoo7xz" data-path="src/pages/Chapter12DetectorSimulation.tsx">MTF_total = MTF₁ × MTF₂ × ... × MTFₙ</p>
                          <p className="text-sm text-gray-600 mt-1" data-id="snmp2m2l1" data-path="src/pages/Chapter12DetectorSimulation.tsx">حاصل ضرب دوال النقل لكل مرحلة</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.5: Radiographic Projection Image Formation */}
        <Card className="mb-6" data-id="vqd4uc3c9" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['image-formation']} onOpenChange={() => toggleSection('image-formation')} data-id="onev26jnt" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="xshoxg1x8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="fh78bbshx" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="c5k8cprmf" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="oru8j8diy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Camera className="h-6 w-6 text-indigo-600" data-id="uesrjpa8j" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="obo2553j4" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.5 محاكاة تكوين صورة الإسقاط الشعاعي</span>
                  </div>
                  {openSections['image-formation'] ? <ChevronUp data-id="7p2knkocj" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="fqlppnyvd" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="l1es8p5wc" data-path="src/pages/Chapter12DetectorSimulation.tsx">من الجسم إلى الصورة النهائية</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="5403ja4q7" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="0nk66hxlw" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="2ha3qqh3h" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div data-id="4man4cye0" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="vnbqy5b07" data-path="src/pages/Chapter12DetectorSimulation.tsx">مراحل تكوين الصورة:</h4>
                    <ol className="space-y-2 text-gray-700 list-decimal list-inside" data-id="ew5ygjtlr" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="cvjwychra" data-path="src/pages/Chapter12DetectorSimulation.tsx">توليد حزمة الأشعة السينية</li>
                      <li data-id="frqabhnjh" data-path="src/pages/Chapter12DetectorSimulation.tsx">التفاعل مع الجسم المفحوص</li>
                      <li data-id="5pkr3kvqx" data-path="src/pages/Chapter12DetectorSimulation.tsx">تشكيل الإسقاط الأولي</li>
                      <li data-id="4xxgzc3t0" data-path="src/pages/Chapter12DetectorSimulation.tsx">الكشف وتكوين الإشارة</li>
                      <li data-id="mydgnz2v0" data-path="src/pages/Chapter12DetectorSimulation.tsx">معالجة وتحسين الصورة</li>
                    </ol>
                  </div>
                  <div data-id="mzecuylhn" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="tkql4ewfm" data-path="src/pages/Chapter12DetectorSimulation.tsx">العوامل المؤثرة:</h4>
                    <ul className="space-y-2 text-gray-700" data-id="8icg1z8ue" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="h7unrr3rz" data-path="src/pages/Chapter12DetectorSimulation.tsx">• هندسة النظام</li>
                      <li data-id="9lhe05vxg" data-path="src/pages/Chapter12DetectorSimulation.tsx">• خصائص حزمة الأشعة</li>
                      <li data-id="2xmw1l8h5" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تركيب الجسم المفحوص</li>
                      <li data-id="0ecb10fdx" data-path="src/pages/Chapter12DetectorSimulation.tsx">• خصائص الكاشف</li>
                      <li data-id="xy8cya75e" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الحركة أثناء التصوير</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-indigo-50 p-4 rounded-lg" data-id="rzxayc88z" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-indigo-800 mb-2" data-id="2odzbmhiz" data-path="src/pages/Chapter12DetectorSimulation.tsx">معادلة تكوين الصورة الأساسية:</h4>
                  <p className="font-mono text-indigo-700" data-id="imeneyues" data-path="src/pages/Chapter12DetectorSimulation.tsx">I(x,y) = I₀ × exp(-∫μ(x,y,z)dz) + S(x,y)</p>
                  <p className="text-indigo-600 text-sm mt-2" data-id="4ne8tv5yx" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    حيث I₀ هي الشدة الأولية، μ معامل التوهين، وS الإشعاع المتشتت
                  </p>
                </div>

                <Card data-id="kcbghqa0b" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <CardHeader data-id="2pm5tc0v5" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardTitle className="text-lg" data-id="wl1f55yn6" data-path="src/pages/Chapter12DetectorSimulation.tsx">محاكاة التشتت</CardTitle>
                  </CardHeader>
                  <CardContent data-id="hc2e4ltqj" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <div className="grid md:grid-cols-2 gap-4" data-id="jgb7hdikn" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div data-id="wg1lkyix7" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <h5 className="font-semibold text-gray-800 mb-2" data-id="0lbz9iydk" data-path="src/pages/Chapter12DetectorSimulation.tsx">أنواع التشتت:</h5>
                        <ul className="space-y-1 text-gray-700" data-id="nqjed0szk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <li data-id="zrito8o6k" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التشتت الأولي من الجسم</li>
                          <li data-id="axy1v2q54" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التشتت المتعدد</li>
                          <li data-id="j4espleqm" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التشتت من الطاولة والدعامات</li>
                        </ul>
                      </div>
                      <div data-id="1jgeeti2n" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <h5 className="font-semibold text-gray-800 mb-2" data-id="0m1lj355k" data-path="src/pages/Chapter12DetectorSimulation.tsx">التأثيرات:</h5>
                        <ul className="space-y-1 text-gray-700" data-id="udxum0a8m" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <li data-id="a8d4yrd6n" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تقليل التباين</li>
                          <li data-id="l8uumagza" data-path="src/pages/Chapter12DetectorSimulation.tsx">• إضافة ضوضاء منظمة</li>
                          <li data-id="gwg8pz3o6" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تدهور جودة الصورة</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.6: Anti-scatter Grids */}
        <Card className="mb-6" data-id="5y4uczjnp" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['anti-scatter']} onOpenChange={() => toggleSection('anti-scatter')} data-id="pgzrkqquy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="y98l57sv8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="lqqwxpmds" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="1xpcd7nl9" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="tv2hb772l" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Grid3X3 className="h-6 w-6 text-orange-600" data-id="5f1ywzjny" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="lnhhha7nk" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.6 دمج شبكات مكافحة التشتت في عمليات المحاكاة</span>
                  </div>
                  {openSections['anti-scatter'] ? <ChevronUp data-id="cmzogbu06" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="bcmleyk8y" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="43qokn0xl" data-path="src/pages/Chapter12DetectorSimulation.tsx">تصميم ونمذجة الشبكات</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="n8vs9zklp" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="zjyreysn0" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <div className="grid gap-6" data-id="mrkqzpwco" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Card data-id="37mk2nvvs" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="vf8qg8f4z" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg flex items-center gap-2" data-id="47don4mra" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <Settings className="h-5 w-5 text-blue-600" data-id="42xykdfn3" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                        12.6.1 معلمات تصميم الشبكة
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="ox1oz7kpe" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="grid md:grid-cols-3 gap-4" data-id="dtpyw7jpg" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <div data-id="7lcgxs4r8" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="s6ask0bip" data-path="src/pages/Chapter12DetectorSimulation.tsx">النسبة (Ratio):</h5>
                          <ul className="space-y-1 text-gray-700" data-id="3jmfgp1xl" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="xrr73ei9t" data-path="src/pages/Chapter12DetectorSimulation.tsx">• نسبة الارتفاع إلى العرض</li>
                            <li data-id="o0y86c525" data-path="src/pages/Chapter12DetectorSimulation.tsx">• القيم الشائعة: 6:1 إلى 16:1</li>
                            <li data-id="2wcel1qrp" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تحدد قدرة رفض التشتت</li>
                          </ul>
                        </div>
                        <div data-id="drt6ijodp" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="ia7mx55q3" data-path="src/pages/Chapter12DetectorSimulation.tsx">التردد (Frequency):</h5>
                          <ul className="space-y-1 text-gray-700" data-id="b5fxqgq0x" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="b8vipxdoz" data-path="src/pages/Chapter12DetectorSimulation.tsx">• عدد الخطوط لكل بوصة</li>
                            <li data-id="389ys66h0" data-path="src/pages/Chapter12DetectorSimulation.tsx">• القيم الشائعة: 40-200 خط/بوصة</li>
                            <li data-id="hkohfp9dn" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تؤثر على ظهور خطوط الشبكة</li>
                          </ul>
                        </div>
                        <div data-id="8shivtdmq" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-gray-800 mb-2" data-id="wggaw0mr0" data-path="src/pages/Chapter12DetectorSimulation.tsx">المادة:</h5>
                          <ul className="space-y-1 text-gray-700" data-id="a68m1z4t4" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <li data-id="tv8s0dnc4" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الرصاص (تقليدي)</li>
                            <li data-id="xe08amwpe" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التنغستن (عالي الأداء)</li>
                            <li data-id="6qbuu5fna" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ألياف الكربون (منخفض الجرعة)</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card data-id="7i8q0iput" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <CardHeader data-id="e9j2xvxhw" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <CardTitle className="text-lg flex items-center gap-2" data-id="rosuda8r9" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <BarChart3 className="h-5 w-5 text-green-600" data-id="sixql4u3a" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                        12.6.2 نمذجة التوهين الشبكي ورفض التشتت
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="3ioolj7fh" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="space-y-4" data-id="ezw0il9am" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <div className="grid md:grid-cols-2 gap-4" data-id="kv63l4fj4" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <div data-id="5whts0u1s" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <h5 className="font-semibold text-gray-800 mb-2" data-id="dory2skur" data-path="src/pages/Chapter12DetectorSimulation.tsx">معاملات الأداء:</h5>
                            <ul className="space-y-1 text-gray-700" data-id="bldzq9x1d" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                              <li data-id="c9ndne0ib" data-path="src/pages/Chapter12DetectorSimulation.tsx">• عامل تحسين التباين (CIF)</li>
                              <li data-id="0c1ymjb33" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معامل البريموس (Bucky factor)</li>
                              <li data-id="2gtjhh11u" data-path="src/pages/Chapter12DetectorSimulation.tsx">• نسبة رفض التشتت (SPR)</li>
                              <li data-id="o93vlggog" data-path="src/pages/Chapter12DetectorSimulation.tsx">• عامل الاختيارية</li>
                            </ul>
                          </div>
                          <div data-id="qx4bzqmfy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <h5 className="font-semibold text-gray-800 mb-2" data-id="wc6yizfbl" data-path="src/pages/Chapter12DetectorSimulation.tsx">العيوب والقيود:</h5>
                            <ul className="space-y-1 text-gray-700" data-id="curxurga4" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                              <li data-id="8hl9e4seb" data-path="src/pages/Chapter12DetectorSimulation.tsx">• زيادة الجرعة المطلوبة</li>
                              <li data-id="xdlubwrfv" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ظهور خطوط الشبكة</li>
                              <li data-id="2sx02cdhi" data-path="src/pages/Chapter12DetectorSimulation.tsx">• قطع الإشعاع الأولي</li>
                              <li data-id="ycujqtrde" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تحديد زاوية الحزمة</li>
                            </ul>
                          </div>
                        </div>

                        <div className="bg-orange-50 p-4 rounded-lg" data-id="m2z04ntga" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          <h5 className="font-semibold text-orange-800 mb-2" data-id="t3smitnyv" data-path="src/pages/Chapter12DetectorSimulation.tsx">معادلات الأداء:</h5>
                          <div className="space-y-2" data-id="bq6db3rup" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                            <p className="font-mono text-orange-700" data-id="sgze45ttr" data-path="src/pages/Chapter12DetectorSimulation.tsx">CIF = (C_with_grid / C_without_grid)</p>
                            <p className="font-mono text-orange-700" data-id="fhf3l73lo" data-path="src/pages/Chapter12DetectorSimulation.tsx">Bucky Factor = (Exposure_with_grid / Exposure_without_grid)</p>
                            <p className="text-orange-600 text-sm" data-id="jge8mgwiu" data-path="src/pages/Chapter12DetectorSimulation.tsx">حيث C هو التباين والـ Exposure هو التعرض المطلوب</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 12.7: Post-acquisition Processing */}
        <Card className="mb-6" data-id="wglm6fb73" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <Collapsible open={openSections['post-processing']} onOpenChange={() => toggleSection('post-processing')} data-id="4w96wcmja" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CollapsibleTrigger asChild data-id="pma7m855x" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors" data-id="p9ku6rlkf" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <CardTitle className="flex items-center justify-between" data-id="dkns61qwz" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div className="flex items-center gap-3" data-id="s3qtb91h3" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <Settings className="h-6 w-6 text-teal-600" data-id="hr12nomcs" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                    <span data-id="0poo3buqo" data-path="src/pages/Chapter12DetectorSimulation.tsx">12.7 خطوات معالجة الصور بعد الاستحواذ</span>
                  </div>
                  {openSections['post-processing'] ? <ChevronUp data-id="wl9jx1l3v" data-path="src/pages/Chapter12DetectorSimulation.tsx" /> : <ChevronDown data-id="a1669i2r1" data-path="src/pages/Chapter12DetectorSimulation.tsx" />}
                </CardTitle>
                <CardDescription data-id="ck6ik6ysz" data-path="src/pages/Chapter12DetectorSimulation.tsx">نظرة عامة موجزة على المعالجة</CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="sh8oe1e75" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <CardContent className="space-y-6" data-id="hyhirtdh9" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                <Alert data-id="t4bx9r5ae" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <Settings className="h-4 w-4" data-id="beme6zsmi" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                  <AlertDescription data-id="wffm5y81d" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    معالجة الصور بعد الاستحواذ تحسن جودة الصورة وتحضرها للعرض والتشخيص.
                  </AlertDescription>
                </Alert>

                <div className="grid md:grid-cols-2 gap-6" data-id="58yrj4gsw" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <div data-id="ztnv6nq6z" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="csa7b5y93" data-path="src/pages/Chapter12DetectorSimulation.tsx">خطوات المعالجة الأساسية:</h4>
                    <ol className="space-y-2 text-gray-700 list-decimal list-inside" data-id="g1crpbey7" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="f171j68z5" data-path="src/pages/Chapter12DetectorSimulation.tsx">تصحيح البكسلات المعيبة</li>
                      <li data-id="c2vh2ol0f" data-path="src/pages/Chapter12DetectorSimulation.tsx">تطبيق تصحيح الكسب والإزاحة</li>
                      <li data-id="1xs66wgic" data-path="src/pages/Chapter12DetectorSimulation.tsx">إزالة الضوضاء</li>
                      <li data-id="0wlmbrnhi" data-path="src/pages/Chapter12DetectorSimulation.tsx">تحسين التباين</li>
                      <li data-id="i5xzsp67z" data-path="src/pages/Chapter12DetectorSimulation.tsx">توضيح الحواف</li>
                      <li data-id="hoa2nx03y" data-path="src/pages/Chapter12DetectorSimulation.tsx">ضبط النافذة والمستوى</li>
                    </ol>
                  </div>
                  <div data-id="2incw0plv" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <h4 className="text-lg font-semibold mb-3 text-gray-800" data-id="fnnzbupsk" data-path="src/pages/Chapter12DetectorSimulation.tsx">تقنيات متقدمة:</h4>
                    <ul className="space-y-2 text-gray-700" data-id="s52tsdr07" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <li data-id="q4tt24bid" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تقليل الضوضاء التكيفي</li>
                      <li data-id="emopoxrwy" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تحسين التباين متعدد المقاييس</li>
                      <li data-id="j0qxs7nmh" data-path="src/pages/Chapter12DetectorSimulation.tsx">• إزالة تشويه الشبكة</li>
                      <li data-id="b53rtxsk2" data-path="src/pages/Chapter12DetectorSimulation.tsx">• التصحيح الهندسي</li>
                      <li data-id="1jscwdemg" data-path="src/pages/Chapter12DetectorSimulation.tsx">• معايرة الطيف الرقمي</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-teal-50 p-4 rounded-lg" data-id="9yt6hhmoa" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-teal-800 mb-2" data-id="gz5a677l5" data-path="src/pages/Chapter12DetectorSimulation.tsx">اعتبارات مهمة:</h4>
                  <ul className="text-teal-700 space-y-1" data-id="cqate0wyk" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <li data-id="bopo21a47" data-path="src/pages/Chapter12DetectorSimulation.tsx">• الحفاظ على المعلومات التشخيصية</li>
                    <li data-id="1ha634o0b" data-path="src/pages/Chapter12DetectorSimulation.tsx">• تجنب الإفراط في المعالجة</li>
                    <li data-id="n2uwor4gu" data-path="src/pages/Chapter12DetectorSimulation.tsx">• مراعاة متطلبات التشخيص المختلفة</li>
                    <li data-id="5fzcz9188" data-path="src/pages/Chapter12DetectorSimulation.tsx">• ضمان الاتساق بين الصور</li>
                  </ul>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Key Terms */}
        <Card className="mb-8 border-l-4 border-l-purple-500" data-id="7io06nryn" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <CardHeader data-id="waaz5zubj" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CardTitle className="flex items-center gap-3" data-id="phxl1q9k0" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <Book className="h-6 w-6 text-purple-600" data-id="xa67y7qaf" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              المصطلحات الرئيسية
            </CardTitle>
          </CardHeader>
          <CardContent data-id="du58st4hs" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <div className="grid md:grid-cols-2 gap-4" data-id="i0cd439ts" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              {keyTerms.map((item, index) =>
              <div key={index} className="bg-gray-50 p-4 rounded-lg" data-id="6b4tyx5ak" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <h4 className="font-semibold text-gray-800 mb-2" data-id="kpde6j6mp" data-path="src/pages/Chapter12DetectorSimulation.tsx">{item.term}</h4>
                  <p className="text-gray-700 text-sm" data-id="trxw66vfw" data-path="src/pages/Chapter12DetectorSimulation.tsx">{item.definition}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Problems Section */}
        <Card className="mb-8 border-l-4 border-l-orange-500" data-id="svq7plivv" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <CardHeader data-id="wjckl80xa" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CardTitle className="flex items-center gap-3" data-id="0pkf7j42m" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <HelpCircle className="h-6 w-6 text-orange-600" data-id="qi4wymq5w" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              مشكلات وتمارين
            </CardTitle>
          </CardHeader>
          <CardContent data-id="ck1uy9ps5" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <div className="space-y-6" data-id="fzxak8dhq" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              {problems.map((problem) =>
              <Collapsible key={problem.id} data-id="18lrmi4of" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                  <CollapsibleTrigger asChild data-id="8dyw1rhv0" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <div className="cursor-pointer bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors" data-id="nucb6m93p" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <div className="flex items-center justify-between" data-id="cuv5qo9ve" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                        <h4 className="font-semibold text-gray-800" data-id="m9gzmah49" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                          المشكلة {problem.id}
                        </h4>
                        <ChevronDown className="h-4 w-4" data-id="buuruxudf" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
                      </div>
                      <p className="text-gray-700 mt-2" data-id="wh1ggdcnx" data-path="src/pages/Chapter12DetectorSimulation.tsx">{problem.question}</p>
                    </div>
                  </CollapsibleTrigger>
                  <CollapsibleContent data-id="pg7lnn53o" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                    <div className="mt-4 p-4 bg-green-50 rounded-lg border-r-4 border-r-green-500" data-id="nlaqfo5nf" data-path="src/pages/Chapter12DetectorSimulation.tsx">
                      <h5 className="font-semibold text-green-800 mb-2" data-id="nhlu6rmzk" data-path="src/pages/Chapter12DetectorSimulation.tsx">الحل:</h5>
                      <p className="text-green-700" data-id="lslfl360p" data-path="src/pages/Chapter12DetectorSimulation.tsx">{problem.solution}</p>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              )}
            </div>
          </CardContent>
        </Card>

        {/* References */}
        <Card className="border-l-4 border-l-green-500" data-id="0kq1jc8mu" data-path="src/pages/Chapter12DetectorSimulation.tsx">
          <CardHeader data-id="4dlggpcwi" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <CardTitle className="flex items-center gap-3" data-id="rorgkkfxy" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <FileText className="h-6 w-6 text-green-600" data-id="l4et6bvj3" data-path="src/pages/Chapter12DetectorSimulation.tsx" />
              المراجع
            </CardTitle>
          </CardHeader>
          <CardContent data-id="ic7iepsog" data-path="src/pages/Chapter12DetectorSimulation.tsx">
            <ol className="space-y-2 text-gray-700 list-decimal list-inside" data-id="uhtron76h" data-path="src/pages/Chapter12DetectorSimulation.tsx">
              <li data-id="eqmdnwojq" data-path="src/pages/Chapter12DetectorSimulation.tsx">Boone, J. M., and Seibert, J. A. "An accurate method for computer-generating tungsten anode x-ray spectra." Medical Physics, 24(11), 1661-1670, 1997.</li>
              <li data-id="3fuukrk4y" data-path="src/pages/Chapter12DetectorSimulation.tsx">Cunningham, I. A. "Applied linear-systems theory." Handbook of Medical Imaging, Vol. 1, SPIE Press, 2000.</li>
              <li data-id="rwubckeec" data-path="src/pages/Chapter12DetectorSimulation.tsx">Zhao, W., and Rowlands, J. A. "X-ray imaging using amorphous selenium." Physics in Medicine & Biology, 40(11), 1846-1864, 1995.</li>
              <li data-id="6wmvf9pas" data-path="src/pages/Chapter12DetectorSimulation.tsx">Swank, R. K. "Absorption and noise in x-ray phosphors." Journal of Applied Physics, 44(9), 4199-4203, 1973.</li>
              <li data-id="mhpr9khjs" data-path="src/pages/Chapter12DetectorSimulation.tsx">Lubberts, G. "Random noise produced by x-ray fluorescent screens." Journal of the Optical Society of America, 58(11), 1475-1483, 1968.</li>
              <li data-id="0tnih89v0" data-path="src/pages/Chapter12DetectorSimulation.tsx">Barrett, H. H., and Swindell, W. "Radiological Imaging: The Theory of Image Formation, Detection, and Processing." Academic Press, 1996.</li>
            </ol>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default Chapter12DetectorSimulation;