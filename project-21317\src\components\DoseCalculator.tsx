import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calculator, AlertTriangle, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const DoseCalculator = () => {
  const { toast } = useToast();

  const [exposureType, setExposureType] = useState('xray');
  const [distance, setDistance] = useState('100');
  const [time, setTime] = useState('1');
  const [sourceActivity, setSourceActivity] = useState('1000');
  const [energy, setEnergy] = useState('100');
  const [results, setResults] = useState(null);

  // Radiation weighting factors
  const radiationWeights = {
    xray: 1,
    gamma: 1,
    beta: 1,
    alpha: 20,
    neutron: 10,
    proton: 2
  };

  // Tissue weighting factors (simplified)
  const tissueWeights = {
    'whole-body': 1.0,
    'gonads': 0.08,
    'bone-marrow': 0.12,
    'colon': 0.12,
    'lung': 0.12,
    'stomach': 0.12,
    'bladder': 0.04,
    'breast': 0.12,
    'liver': 0.04,
    'thyroid': 0.04,
    'skin': 0.01,
    'bone-surface': 0.01
  };

  const calculateDose = () => {
    try {
      const dist = parseFloat(distance);
      const t = parseFloat(time);
      const activity = parseFloat(sourceActivity);
      const e = parseFloat(energy);

      if (!dist || !t || !activity || !e) {
        toast({
          title: "Input Error",
          description: "Please fill in all required fields with valid numbers.",
          variant: "destructive"
        });
        return;
      }

      // Simplified dose calculation
      const exposureRate = activity * 0.5 / Math.pow(dist, 2); // Simplified point source
      const exposure = exposureRate * t;
      const absorbedDose = exposure * 0.00876; // Convert to Gy (simplified)
      const equivalentDose = absorbedDose * radiationWeights[exposureType];
      const effectiveDose = equivalentDose * tissueWeights['whole-body'];

      // Calculate for different organs
      const organDoses = Object.entries(tissueWeights).map(([organ, weight]) => ({
        organ: organ.replace('-', ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
        dose: equivalentDose * weight
      }));

      setResults({
        exposure: exposure,
        absorbedDose: absorbedDose,
        equivalentDose: equivalentDose,
        effectiveDose: effectiveDose,
        organDoses: organDoses,
        exposureRate: exposureRate
      });

      toast({
        title: "Calculation Complete",
        description: "Dose calculations have been updated."
      });

    } catch (error) {
      toast({
        title: "Calculation Error",
        description: "Please check your inputs and try again.",
        variant: "destructive"
      });
    }
  };

  const getDoseLevel = (dose) => {
    if (dose < 0.1) return { level: 'Very Low', color: 'bg-green-500' };
    if (dose < 1) return { level: 'Low', color: 'bg-yellow-500' };
    if (dose < 10) return { level: 'Moderate', color: 'bg-orange-500' };
    return { level: 'High', color: 'bg-red-500' };
  };

  return (
    <div className="space-y-8" data-id="8yg4zw4nf" data-path="src/components/DoseCalculator.tsx">
      {/* Input Parameters */}
      <div className="grid md:grid-cols-2 gap-8" data-id="4hgte3xul" data-path="src/components/DoseCalculator.tsx">
        <Card data-id="9sj26juj1" data-path="src/components/DoseCalculator.tsx">
          <CardHeader data-id="d7wg1i38l" data-path="src/components/DoseCalculator.tsx">
            <CardTitle className="flex items-center gap-2" data-id="mu1wrhe8e" data-path="src/components/DoseCalculator.tsx">
              <Calculator className="w-5 h-5" data-id="txgldxmr7" data-path="src/components/DoseCalculator.tsx" />
              Exposure Parameters
            </CardTitle>
            <CardDescription data-id="cal1yr0dt" data-path="src/components/DoseCalculator.tsx">
              Enter the radiation exposure conditions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4" data-id="ymp7mnrhi" data-path="src/components/DoseCalculator.tsx">
            <div data-id="7qx40swol" data-path="src/components/DoseCalculator.tsx">
              <Label htmlFor="exposure-type" data-id="pexa9hamm" data-path="src/components/DoseCalculator.tsx">Radiation Type</Label>
              <Select value={exposureType} onValueChange={setExposureType} data-id="k2ecre7df" data-path="src/components/DoseCalculator.tsx">
                <SelectTrigger data-id="xsrsjqkwm" data-path="src/components/DoseCalculator.tsx">
                  <SelectValue data-id="hr5k2jixg" data-path="src/components/DoseCalculator.tsx" />
                </SelectTrigger>
                <SelectContent data-id="gi0da08zm" data-path="src/components/DoseCalculator.tsx">
                  <SelectItem value="xray" data-id="ha4wa6n85" data-path="src/components/DoseCalculator.tsx">X-ray</SelectItem>
                  <SelectItem value="gamma" data-id="7jjfd1vly" data-path="src/components/DoseCalculator.tsx">Gamma</SelectItem>
                  <SelectItem value="beta" data-id="w0ygug47i" data-path="src/components/DoseCalculator.tsx">Beta</SelectItem>
                  <SelectItem value="alpha" data-id="yh7ycslna" data-path="src/components/DoseCalculator.tsx">Alpha</SelectItem>
                  <SelectItem value="neutron" data-id="canqp6g5m" data-path="src/components/DoseCalculator.tsx">Neutron</SelectItem>
                  <SelectItem value="proton" data-id="4e0dx04pv" data-path="src/components/DoseCalculator.tsx">Proton</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div data-id="hw3e4twm5" data-path="src/components/DoseCalculator.tsx">
              <Label htmlFor="distance" data-id="5ik53fzln" data-path="src/components/DoseCalculator.tsx">Distance from Source (cm)</Label>
              <Input
                id="distance"
                type="number"
                value={distance}
                onChange={(e) => setDistance(e.target.value)}
                placeholder="100" data-id="z4bhm8d4u" data-path="src/components/DoseCalculator.tsx" />

            </div>

            <div data-id="uuowc0y3q" data-path="src/components/DoseCalculator.tsx">
              <Label htmlFor="time" data-id="e6g8zutwt" data-path="src/components/DoseCalculator.tsx">Exposure Time (minutes)</Label>
              <Input
                id="time"
                type="number"
                value={time}
                onChange={(e) => setTime(e.target.value)}
                placeholder="1" data-id="jtagh6a3l" data-path="src/components/DoseCalculator.tsx" />

            </div>

            <div data-id="teo4xln60" data-path="src/components/DoseCalculator.tsx">
              <Label htmlFor="activity" data-id="p6qslm9u6" data-path="src/components/DoseCalculator.tsx">Source Activity (MBq) or Intensity</Label>
              <Input
                id="activity"
                type="number"
                value={sourceActivity}
                onChange={(e) => setSourceActivity(e.target.value)}
                placeholder="1000" data-id="t5a6auvin" data-path="src/components/DoseCalculator.tsx" />

            </div>

            <div data-id="3nwga84mt" data-path="src/components/DoseCalculator.tsx">
              <Label htmlFor="energy" data-id="nmme9y8gh" data-path="src/components/DoseCalculator.tsx">Average Energy (keV)</Label>
              <Input
                id="energy"
                type="number"
                value={energy}
                onChange={(e) => setEnergy(e.target.value)}
                placeholder="100" data-id="0cc3yxgta" data-path="src/components/DoseCalculator.tsx" />

            </div>

            <Button onClick={calculateDose} className="w-full" data-id="trdt60fdy" data-path="src/components/DoseCalculator.tsx">
              Calculate Dose
            </Button>
          </CardContent>
        </Card>

        {/* Results */}
        <Card data-id="lh8uxld1c" data-path="src/components/DoseCalculator.tsx">
          <CardHeader data-id="ni6rvqv4y" data-path="src/components/DoseCalculator.tsx">
            <CardTitle data-id="7v4mv3obe" data-path="src/components/DoseCalculator.tsx">Dose Calculation Results</CardTitle>
            <CardDescription data-id="kk8ltwnyz" data-path="src/components/DoseCalculator.tsx">
              Calculated radiation doses and exposure levels
            </CardDescription>
          </CardHeader>
          <CardContent data-id="9yyyw9fzu" data-path="src/components/DoseCalculator.tsx">
            {results ?
            <div className="space-y-4" data-id="s6bga2qrj" data-path="src/components/DoseCalculator.tsx">
                <div className="grid grid-cols-2 gap-4" data-id="3qpp8b9xg" data-path="src/components/DoseCalculator.tsx">
                  <div data-id="9ekb01okz" data-path="src/components/DoseCalculator.tsx">
                    <Label className="text-sm font-medium text-gray-600" data-id="hpylk0exr" data-path="src/components/DoseCalculator.tsx">Exposure Rate</Label>
                    <div className="text-lg font-semibold" data-id="n0xde28vw" data-path="src/components/DoseCalculator.tsx">
                      {results.exposureRate.toExponential(2)} R/min
                    </div>
                  </div>
                  <div data-id="dlwxo4zg0" data-path="src/components/DoseCalculator.tsx">
                    <Label className="text-sm font-medium text-gray-600" data-id="4w8dkwkds" data-path="src/components/DoseCalculator.tsx">Total Exposure</Label>
                    <div className="text-lg font-semibold" data-id="7pu5aos2l" data-path="src/components/DoseCalculator.tsx">
                      {results.exposure.toExponential(2)} R
                    </div>
                  </div>
                </div>

                <div className="space-y-3" data-id="04b87jmag" data-path="src/components/DoseCalculator.tsx">
                  <div className="flex justify-between items-center" data-id="d08uqbwcn" data-path="src/components/DoseCalculator.tsx">
                    <span className="text-sm font-medium" data-id="zabdsc5tu" data-path="src/components/DoseCalculator.tsx">Absorbed Dose:</span>
                    <Badge variant="outline" data-id="fuekjl9m7" data-path="src/components/DoseCalculator.tsx">
                      {results.absorbedDose.toExponential(2)} Gy
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center" data-id="kwpqot338" data-path="src/components/DoseCalculator.tsx">
                    <span className="text-sm font-medium" data-id="aju8cxox8" data-path="src/components/DoseCalculator.tsx">Equivalent Dose:</span>
                    <Badge variant="outline" data-id="kg13ql2dv" data-path="src/components/DoseCalculator.tsx">
                      {results.equivalentDose.toExponential(2)} Sv
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center" data-id="1y1jr332e" data-path="src/components/DoseCalculator.tsx">
                    <span className="text-sm font-medium" data-id="02edmo7d5" data-path="src/components/DoseCalculator.tsx">Effective Dose:</span>
                    <div className="flex items-center gap-2" data-id="zlw8dck36" data-path="src/components/DoseCalculator.tsx">
                      <Badge variant="outline" data-id="7ww4zmpdm" data-path="src/components/DoseCalculator.tsx">
                        {results.effectiveDose.toExponential(2)} Sv
                      </Badge>
                      <div className={`w-3 h-3 rounded-full ${getDoseLevel(results.effectiveDose * 1000).color}`} data-id="x9usz09p2" data-path="src/components/DoseCalculator.tsx"></div>
                    </div>
                  </div>
                </div>

                {/* Dose Level Warning */}
                {results.effectiveDose > 0.001 &&
              <Alert data-id="lkar75aqp" data-path="src/components/DoseCalculator.tsx">
                    <AlertTriangle className="h-4 w-4" data-id="1czfaqwg7" data-path="src/components/DoseCalculator.tsx" />
                    <AlertDescription data-id="qkw6jz9fv" data-path="src/components/DoseCalculator.tsx">
                      This dose level ({getDoseLevel(results.effectiveDose * 1000).level}) requires careful consideration. 
                      Ensure ALARA principles are followed.
                    </AlertDescription>
                  </Alert>
              }
              </div> :

            <div className="text-center text-gray-500 py-8" data-id="hlar2gek5" data-path="src/components/DoseCalculator.tsx">
                <Calculator className="w-12 h-12 mx-auto mb-4 opacity-50" data-id="ruojnim2c" data-path="src/components/DoseCalculator.tsx" />
                <p data-id="1gorcqmr8" data-path="src/components/DoseCalculator.tsx">Enter parameters and click "Calculate Dose" to see results</p>
              </div>
            }
          </CardContent>
        </Card>
      </div>

      {/* Organ Doses */}
      {results &&
      <Card data-id="6gym3pvut" data-path="src/components/DoseCalculator.tsx">
          <CardHeader data-id="1s68u3igu" data-path="src/components/DoseCalculator.tsx">
            <CardTitle data-id="u71icmk62" data-path="src/components/DoseCalculator.tsx">Organ-Specific Equivalent Doses</CardTitle>
            <CardDescription data-id="gtc0da3lp" data-path="src/components/DoseCalculator.tsx">
              Doses to different organs and tissues based on tissue weighting factors
            </CardDescription>
          </CardHeader>
          <CardContent data-id="me4wlmm0k" data-path="src/components/DoseCalculator.tsx">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4" data-id="m5g7jp82c" data-path="src/components/DoseCalculator.tsx">
              {results.organDoses.slice(0, 12).map((organ, index) =>
            <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg" data-id="lu6i3p9l4" data-path="src/components/DoseCalculator.tsx">
                  <span className="text-sm font-medium" data-id="wxp2y9ejv" data-path="src/components/DoseCalculator.tsx">{organ.organ}:</span>
                  <Badge variant="secondary" data-id="dg4pay4il" data-path="src/components/DoseCalculator.tsx">
                    {organ.dose.toExponential(2)} Sv
                  </Badge>
                </div>
            )}
            </div>
          </CardContent>
        </Card>
      }

      {/* Information */}
      <div className="grid md:grid-cols-2 gap-8" data-id="4k5g9fjg7" data-path="src/components/DoseCalculator.tsx">
        <Card data-id="p7iiwdxud" data-path="src/components/DoseCalculator.tsx">
          <CardHeader data-id="5v6btscp2" data-path="src/components/DoseCalculator.tsx">
            <CardTitle className="flex items-center gap-2" data-id="1k6b0ysw4" data-path="src/components/DoseCalculator.tsx">
              <Info className="w-5 h-5" data-id="7lc2cyjag" data-path="src/components/DoseCalculator.tsx" />
              Calculation Notes
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm text-gray-600" data-id="chhi3u3hq" data-path="src/components/DoseCalculator.tsx">
            <p data-id="dybkv9z45" data-path="src/components/DoseCalculator.tsx">
              <strong data-id="gyz8c5dnb" data-path="src/components/DoseCalculator.tsx">Simplified Model:</strong> These calculations use simplified models for educational purposes. 
              Real dosimetry requires more complex calculations considering geometry, scatter, and tissue composition.
            </p>
            <p data-id="6811pogl6" data-path="src/components/DoseCalculator.tsx">
              <strong data-id="0r3ggy9t2" data-path="src/components/DoseCalculator.tsx">Radiation Weighting:</strong> Different radiation types have different biological effectiveness, 
              accounted for by radiation weighting factors (wR).
            </p>
            <p data-id="kmmk2c8g5" data-path="src/components/DoseCalculator.tsx">
              <strong data-id="ndjjjnmoc" data-path="src/components/DoseCalculator.tsx">Tissue Weighting:</strong> Different organs have different radiation sensitivities, 
              accounted for by tissue weighting factors (wT).
            </p>
          </CardContent>
        </Card>

        <Card data-id="sc2i29kt6" data-path="src/components/DoseCalculator.tsx">
          <CardHeader data-id="ct8c1asmc" data-path="src/components/DoseCalculator.tsx">
            <CardTitle data-id="hgddht9up" data-path="src/components/DoseCalculator.tsx">Reference Levels</CardTitle>
          </CardHeader>
          <CardContent data-id="axg2zud99" data-path="src/components/DoseCalculator.tsx">
            <div className="space-y-3 text-sm" data-id="n5gyfadmf" data-path="src/components/DoseCalculator.tsx">
              <div className="flex justify-between" data-id="s3e37s0q6" data-path="src/components/DoseCalculator.tsx">
                <span data-id="gc2lf458b" data-path="src/components/DoseCalculator.tsx">Background radiation (annual):</span>
                <Badge variant="outline" data-id="l0gudpctg" data-path="src/components/DoseCalculator.tsx">2-3 mSv</Badge>
              </div>
              <div className="flex justify-between" data-id="h4616t4ti" data-path="src/components/DoseCalculator.tsx">
                <span data-id="9rmz4r8y7" data-path="src/components/DoseCalculator.tsx">Chest X-ray:</span>
                <Badge variant="outline" data-id="0lyjccbkw" data-path="src/components/DoseCalculator.tsx">0.1 mSv</Badge>
              </div>
              <div className="flex justify-between" data-id="t9ajjhrz5" data-path="src/components/DoseCalculator.tsx">
                <span data-id="liosn7xwf" data-path="src/components/DoseCalculator.tsx">CT scan (chest):</span>
                <Badge variant="outline" data-id="2eq21a0ot" data-path="src/components/DoseCalculator.tsx">7 mSv</Badge>
              </div>
              <div className="flex justify-between" data-id="080hf5ol9" data-path="src/components/DoseCalculator.tsx">
                <span data-id="2e5o51pi1" data-path="src/components/DoseCalculator.tsx">Occupational limit (annual):</span>
                <Badge variant="outline" data-id="n0llq7wot" data-path="src/components/DoseCalculator.tsx">20 mSv</Badge>
              </div>
              <div className="flex justify-between" data-id="gm1ujinvj" data-path="src/components/DoseCalculator.tsx">
                <span data-id="klugfl04j" data-path="src/components/DoseCalculator.tsx">Public limit (annual):</span>
                <Badge variant="outline" data-id="j67gdp1vi" data-path="src/components/DoseCalculator.tsx">1 mSv</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default DoseCalculator;