import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import HomePage from "./pages/HomePage";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () =>
<QueryClientProvider client={queryClient} data-id="r8t82c91e" data-path="src/App.tsx">
    <TooltipProvider data-id="sm7i1kb09" data-path="src/App.tsx">
      <Toaster data-id="muoxonc4v" data-path="src/App.tsx" />
      <BrowserRouter data-id="rs26r2ivg" data-path="src/App.tsx">
        <Routes data-id="8smrwfhqo" data-path="src/App.tsx">
          <Route path="/" element={<HomePage data-id="qrqt3ncr6" data-path="src/App.tsx" />} data-id="cab29n34r" data-path="src/App.tsx" />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound data-id="3wo2u81gp" data-path="src/App.tsx" />} data-id="qkbuk6hug" data-path="src/App.tsx" />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>;


export default App;