import React from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  Cpu,
  Target,
  Zap,
  TrendingUp,
  Calculator,
  Image,
  FlaskConical,
  AlertCircle,
  ChevronDown,
  BookOpen,
  Key,
  FileText,
  HelpCircle,
  Dice1,
  BarChart3,
  Waves } from
'lucide-react';

const Chapter10MonteCarloSimulation = () => {
  const [openSections, setOpenSections] = React.useState<{[key: string]: boolean;}>({});

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50" dir="rtl" data-id="rzb8em7mp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
      <Navigation data-id="hb6m1tctf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl" data-id="d10bpd2d5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
        {/* Header */}
        <div className="text-center mb-12" data-id="e8ynm3e6t" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Badge variant="secondary" className="mb-4 text-lg px-6 py-2" data-id="1d19wwv2n" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            الفصل العاشر
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4" data-id="js07xnjqx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            محاكاة مونت كارلو لنقل الفوتونات
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed" data-id="pa96dz76b" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            دراسة شاملة لأساليب محاكاة مونت كارلو في نمذجة نقل الإشعاع الفوتوني وتطبيقاتها الطبية
          </p>
        </div>

        {/* Learning Objectives */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-purple-500" data-id="71rtyh8sw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <CardHeader data-id="n6xsa5qk2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <CardTitle className="flex items-center text-2xl text-purple-700" data-id="w7s0yzcbv" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <Target className="ml-3 h-6 w-6" data-id="tdps08zlv" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
              أهداف التعلم
            </CardTitle>
          </CardHeader>
          <CardContent data-id="y6hwlwhpb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <ul className="space-y-3 text-gray-700" data-id="p98i63fcp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <li className="flex items-start" data-id="kqbqw3z6y" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <span className="text-purple-500 ml-2" data-id="qbnzyazp1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                فهم مبادئ محاكاة مونت كارلو التناظرية لتتبع الفوتونات
              </li>
              <li className="flex items-start" data-id="4h3lizznw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <span className="text-purple-500 ml-2" data-id="o1w7ijt46" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                إتقان تقنيات أخذ العينات للتفاعلات والمسافات والجسيمات الثانوية
              </li>
              <li className="flex items-start" data-id="6clqodivo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <span className="text-purple-500 ml-2" data-id="3gnwjrewz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                تطبيق تقنيات تقليل التباين لتحسين كفاءة المحاكاة
              </li>
              <li className="flex items-start" data-id="r693qbgfz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <span className="text-purple-500 ml-2" data-id="hblu9qp7b" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                حساب وتسجيل الكميات الفيزيائية المختلفة (الجرعة، التدفق، ترسب الطاقة)
              </li>
              <li className="flex items-start" data-id="k6aa9vatx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <span className="text-purple-500 ml-2" data-id="uehgoabgu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                تحليل تأثير الإشعاع المبعثر على جودة الصورة الطبية
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Section 10.1 */}
        <Card className="mb-6 shadow-lg" data-id="4ziswj3ea" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section101']}
            onOpenChange={() => toggleSection('section101')} data-id="32ors5xjd" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="awqnz5m2i" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="98vddybd6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="itfex8s8f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="y8xrnwh3f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <Cpu className="ml-3 h-6 w-6 text-blue-600" data-id="smjkbmhu8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.1 مبادئ مونت كارلو التناظرية لتتبع الفوتونات
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section101'] ? 'rotate-180' : ''}`} data-id="d0wkoubvr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="8lp78adzu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  الأسس النظرية والتطبيقية لمحاكاة مسار الفوتونات باستخدام مونت كارلو
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="xij9xtmkw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="4rrxa1fz7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="puv7yw0b0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="border-l-4 border-l-blue-500" data-id="kaj61m76t" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="e5q7xq0yk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="alkf3i91v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <Dice1 className="ml-2 h-5 w-5 text-blue-600" data-id="i8lux13p1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                        المبادئ الأساسية
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="cx7s4sbsr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <ul className="space-y-2 text-sm text-gray-600" data-id="ju4yqs1fr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <li data-id="i64e7tpwx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• استخدام الأرقام العشوائية لمحاكاة العمليات الفيزيائية</li>
                        <li data-id="vwu32lqle" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تتبع كل فوتون على حدة من المصدر إلى الامتصاص</li>
                        <li data-id="alpaks9q1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• حساب احتمالية كل تفاعل وفقاً للمقاطع العرضية</li>
                        <li data-id="kpn9z8e2f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تراكم الإحصائيات لحساب الكميات المطلوبة</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-green-500" data-id="h1boftmg1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="e6yjlfomz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="cn2zv0aea" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <Target className="ml-2 h-5 w-5 text-green-600" data-id="aqjobbozv" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                        خوارزمية التتبع
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="ypaypzaib" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-3" data-id="ft4590gpz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-green-50 p-3 rounded" data-id="4uxou4mfj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-green-800 mb-2" data-id="kmznlm14p" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">خطوات المحاكاة:</h4>
                          <ol className="text-sm text-gray-700 list-decimal list-inside space-y-1" data-id="tibahneod" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="45r3yzw3t" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تهيئة خصائص الفوتون (الموقع، الاتجاه، الطاقة)</li>
                            <li data-id="wk0hll82x" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">حساب المسافة إلى التفاعل التالي</li>
                            <li data-id="fr0jbv9n7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تحديد نوع التفاعل (امتصاص، تبعثر)</li>
                            <li data-id="fws4138wl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تحديث خصائص الفوتون بعد التفاعل</li>
                            <li data-id="7jvguhxpq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تسجيل المعلومات المطلوبة</li>
                            <li data-id="iekinhfgy" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تكرار العملية حتى امتصاص الفوتون</li>
                          </ol>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Alert data-id="35zqr3x73" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <AlertCircle className="h-4 w-4" data-id="b10zilh15" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                  <AlertDescription data-id="cakyy41hl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    دقة المحاكاة تعتمد على عدد الفوتونات المحاكية وجودة مولد الأرقام العشوائية المستخدم.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 10.2 */}
        <Card className="mb-6 shadow-lg" data-id="7c3ron9on" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section102']}
            onOpenChange={() => toggleSection('section102')} data-id="lufmy3p66" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="xws8q8wj8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="y2f9q5twj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="tpw9dasw5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="qnygbh3lq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <BarChart3 className="ml-3 h-6 w-6 text-purple-600" data-id="7m5j09qif" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.2 تقنيات أخذ العينات
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section102'] ? 'rotate-180' : ''}`} data-id="w70p9b751" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="fs0ier1ig" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  طرق أخذ العينات للتفاعلات والمسافات والجسيمات الثانوية
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="9lxrmox4v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="68a522vxz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="grid md:grid-cols-3 gap-6" data-id="gbr85tvwx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="bg-gradient-to-br from-purple-50 to-purple-100" data-id="ojdquu6qi" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="7mypwolf4" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg text-purple-800" data-id="sdalx394s" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">أخذ العينات للمسافة</CardTitle>
                    </CardHeader>
                    <CardContent data-id="64g5svw8c" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-3" data-id="b5ijub1jq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-white p-3 rounded shadow-sm" data-id="vzdnwrlmt" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="vh17oetqc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">قانون بير-لامبرت:</h4>
                          <p className="text-sm text-gray-600 text-center font-mono bg-gray-100 p-2 rounded" data-id="oneg971j6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            s = -ln(ξ)/Σ
                          </p>
                        </div>
                        <ul className="text-sm text-gray-700" data-id="wab2al4sj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="nhz96ec83" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• s: المسافة إلى التفاعل</li>
                          <li data-id="4hw6purgg" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• ξ: رقم عشوائي [0,1]</li>
                          <li data-id="dmevmbh8q" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• Σ: المقطع العرضي الكلي</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-blue-50 to-blue-100" data-id="aif26q03o" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="e5nm8nelf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg text-blue-800" data-id="lxuvwaeli" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">نوع التفاعل</CardTitle>
                    </CardHeader>
                    <CardContent data-id="d3woqay69" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-3" data-id="s4yuah3f0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-white p-3 rounded shadow-sm" data-id="pol0puvd5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="ff5riao60" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الاختيار المرجح:</h4>
                          <ul className="text-sm text-gray-700 space-y-1" data-id="0w4zr18go" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="nd8gf1eld" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• σₚₕ/Σ: التأثير الكهروضوئي</li>
                            <li data-id="xpdswdl9f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• σᶜ/Σ: تبعثر كومبتون</li>
                            <li data-id="qelwiqozu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• σᵣ/Σ: إنتاج الأزواج</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-green-50 to-green-100" data-id="uoffaa3up" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="dukul8ryz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg text-green-800" data-id="ils4hgtuv" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الجسيمات الثانوية</CardTitle>
                    </CardHeader>
                    <CardContent data-id="rtt77c1lf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-3" data-id="zyx3da8pk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-white p-3 rounded shadow-sm" data-id="s2py73uyo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="2oh824ol6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">توزيع الطاقة والزاوية:</h4>
                          <ul className="text-sm text-gray-700 space-y-1" data-id="hvp51ntzz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="26hbsnx9k" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• معادلة كلاين-نيشينا</li>
                            <li data-id="2w6053swa" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• توزيع الإلكترونات الثانوية</li>
                            <li data-id="5yf69isi1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• أشعة X المميزة</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 10.3 */}
        <Card className="mb-6 shadow-lg" data-id="pwrejrili" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section103']}
            onOpenChange={() => toggleSection('section103')} data-id="7bjvdddfr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="ieif825yv" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="cw0i1q12x" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="kxqlmecf6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="eukoj6tb5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <TrendingUp className="ml-3 h-6 w-6 text-orange-600" data-id="qqplvi34o" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.3 تقنيات تقليل التباين
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section103'] ? 'rotate-180' : ''}`} data-id="x56srjccs" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="ba8s80qyp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  طرق تحسين كفاءة المحاكاة وتقليل الوقت الحاسوبي المطلوب
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="usrplfyru" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="8o5yfz454" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="grid md:grid-cols-3 gap-6" data-id="af5jphzrj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="border-t-4 border-t-red-500" data-id="mxvj67c8l" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="cw33pgw7v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="2erttk6dq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <Target className="ml-2 h-5 w-5 text-red-600" data-id="m2ap7psdq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                        العينة ذات الأهمية
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3" data-id="73776kxlp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div data-id="awtk6b228" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="exk4s6dsk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المبدأ:</h4>
                        <p className="text-sm text-gray-600" data-id="tprwb7zui" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          تركيز العينات في المناطق المهمة للكمية المقاسة
                        </p>
                      </div>
                      <div data-id="8ffl8ldjz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="ixbb935m0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">التطبيقات:</h4>
                        <ul className="text-sm text-gray-600 space-y-1" data-id="4gbxujau5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="2zrj8dj3n" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تحيز الاتجاه نحو الكاشف</li>
                          <li data-id="ooyb4xs6g" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تحيز الطاقة للفوتونات المهمة</li>
                          <li data-id="cgsr75oe7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تحيز المسافة في المناطق الحرجة</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-t-4 border-t-blue-500" data-id="yfabwjmcr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="k7sq9r4pz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="tgwh30pth" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <span className="text-2xl ml-2" data-id="qf8bf5eje" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">🔄</span>
                        التقسيم
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3" data-id="q5oo91ecu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div data-id="qbqm300qs" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="4jlcydtli" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المبدأ:</h4>
                        <p className="text-sm text-gray-600" data-id="q33lqikme" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          تقسيم الجسيم إلى عدة نسخ بأوزان مخفضة
                        </p>
                      </div>
                      <div data-id="jot23meqx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="njlrvs70m" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الاستخدامات:</h4>
                        <ul className="text-sm text-gray-600 space-y-1" data-id="bkallr1pn" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="lgak62xmy" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• زيادة الإحصائيات في المناطق المهمة</li>
                          <li data-id="0b72x8fog" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تحسين دقة القياس</li>
                          <li data-id="s43sanc1x" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تقليل التباين النسبي</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-t-4 border-t-green-500" data-id="fkv1yndgt" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="xt1eumb1m" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="fxxtxj7zx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <span className="text-2xl ml-2" data-id="by42obo57" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">🎰</span>
                        الروليت الروسي
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3" data-id="ubrjnikd9" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div data-id="697j4qfw3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="e1wz13nge" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المبدأ:</h4>
                        <p className="text-sm text-gray-600" data-id="qi1vcsltx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          إنهاء تتبع الجسيمات منخفضة الوزن عشوائياً
                        </p>
                      </div>
                      <div data-id="j08p8taff" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="7aey6pq3a" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الفوائد:</h4>
                        <ul className="text-sm text-gray-600 space-y-1" data-id="lkgnev5vc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="iclmg7z6i" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• توفير الوقت الحاسوبي</li>
                          <li data-id="on9la7bza" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• التركيز على الجسيمات المهمة</li>
                          <li data-id="pmjtftevr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• منع تراكم الأوزان الصغيرة</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Alert data-id="pj56h23ow" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <AlertCircle className="h-4 w-4" data-id="lmuszv0as" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                  <AlertDescription data-id="3dwetqhsf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    تقنيات تقليل التباين يجب أن تُطبق بحذر للحفاظ على دقة النتائج وعدم إدخال تحيز في المحاكاة.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 10.4 */}
        <Card className="mb-6 shadow-lg" data-id="gnkrm14uh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section104']}
            onOpenChange={() => toggleSection('section104')} data-id="kan87bmxt" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="s583amv89" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="s77s307pg" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="rdiyiuway" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="0md33mb7n" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <Calculator className="ml-3 h-6 w-6 text-indigo-600" data-id="dq7w57zdu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.4 العد والتسجيل
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section104'] ? 'rotate-180' : ''}`} data-id="mktlh9e65" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="ysdrvvzb3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  تقدير الكميات الفيزيائية: الجرعة، التدفق، وترسب الطاقة
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="665y1ih9a" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="ua09i51ru" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="182g7fa6m" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100" data-id="tj4cszeeb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="mubs45f8u" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg text-indigo-800" data-id="oppwaswod" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">أنواع العدادات (Tallies)</CardTitle>
                    </CardHeader>
                    <CardContent data-id="82q4sxea8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-4" data-id="t1mtp9rc6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-white p-3 rounded shadow-sm" data-id="waq1eecfj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="d0deq5y6k" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">عداد التدفق:</h4>
                          <ul className="text-sm text-gray-700 space-y-1" data-id="6sfdzs2zo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="ws9pxsq2p" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• F1: التدفق السطحي</li>
                            <li data-id="togeeebzi" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• F2: التدفق الزاوي</li>
                            <li data-id="bzsdueg1w" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• F4: التدفق الحجمي</li>
                          </ul>
                        </div>
                        <div className="bg-white p-3 rounded shadow-sm" data-id="z5jms79ba" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="3jlmuy9sr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">عداد الطاقة:</h4>
                          <ul className="text-sm text-gray-700 space-y-1" data-id="jp4iu11p3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="o7qnaiezt" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• F6: ترسب الطاقة</li>
                            <li data-id="hld7olq1r" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• F8: توزيع الطاقة النبضي</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-cyan-50 to-cyan-100" data-id="r0htykgdm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="z61lm5lvr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg text-cyan-800" data-id="ejqju27k3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">حساب الجرعة</CardTitle>
                    </CardHeader>
                    <CardContent data-id="6x57ile7a" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-4" data-id="055jegmwy" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-white p-3 rounded shadow-sm" data-id="bzs6mmnei" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="q9p1jbamy" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">معادلة الجرعة:</h4>
                          <p className="text-center font-mono bg-gray-100 p-2 rounded text-sm" data-id="xo8ykuvuu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            D = E_dep / (ρ × V)
                          </p>
                          <ul className="text-xs text-gray-600 mt-2 space-y-1" data-id="5d20a9wwj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="78h24y6ba" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• D: الجرعة الممتصة (Gy)</li>
                            <li data-id="sqwc3yd8d" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• E_dep: الطاقة المترسبة (J)</li>
                            <li data-id="klwcxukj1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• ρ: كثافة المادة (kg/m³)</li>
                            <li data-id="69j8yjyz9" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• V: الحجم (m³)</li>
                          </ul>
                        </div>
                        <div className="bg-white p-3 rounded shadow-sm" data-id="97d6ytpy3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="am7rq4p0t" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تحويل الطاقة إلى جرعة:</h4>
                          <p className="text-sm text-gray-700" data-id="u8bgsr3xl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            استخدام عوامل التحويل المعيارية لكل نسيج
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 10.5 */}
        <Card className="mb-6 shadow-lg" data-id="pwsniuuzk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section105']}
            onOpenChange={() => toggleSection('section105')} data-id="jm1j7zsvl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="e0kxf9bls" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="vzf79xe6v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="e1f760wd2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="5euocn76a" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <Waves className="ml-3 h-6 w-6 text-teal-600" data-id="bj2mxukfh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.5 محاكاة الإشعاع المبعثر
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section105'] ? 'rotate-180' : ''}`} data-id="gewrr3qoo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="oxvlrp323" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  تأثير الإشعاع المبعثر على جودة الصورة الطبية
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="08pha585b" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="97ctca4zc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="pga4y1iis" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="border-l-4 border-l-teal-500" data-id="1a83ys7gn" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="4fke63xxp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg" data-id="orovjg1pl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مصادر الإشعاع المبعثر</CardTitle>
                    </CardHeader>
                    <CardContent data-id="46eteedzp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <ul className="space-y-2 text-gray-700" data-id="k6n3kuw74" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <li className="flex items-start" data-id="t23yln0rr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-teal-500 ml-2 mt-1" data-id="dioftgayu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="3f4cifh6b" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تبعثر كومبتون في المريض</span>
                        </li>
                        <li className="flex items-start" data-id="r11g9iz6z" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-teal-500 ml-2 mt-1" data-id="ris9vpmnk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="ki35sqgog" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تبعثر في أجهزة التصوير</span>
                        </li>
                        <li className="flex items-start" data-id="bvbctnr2o" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-teal-500 ml-2 mt-1" data-id="bz6bz3kqr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="16fnu30rz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تبعثر في الهواء المحيط</span>
                        </li>
                        <li className="flex items-start" data-id="3k8k3o4t8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-teal-500 ml-2 mt-1" data-id="vwoo20z13" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="m3lp19j0f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">انتثار خلفي من الكاشف</span>
                        </li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-orange-500" data-id="81h16pqf3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="2zmlgld9v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg" data-id="zam3uyz9b" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تأثيرات على جودة الصورة</CardTitle>
                    </CardHeader>
                    <CardContent data-id="imesfxv20" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <ul className="space-y-2 text-gray-700" data-id="mb8uwhzet" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <li className="flex items-start" data-id="5h05tb0z5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-orange-500 ml-2 mt-1" data-id="3r7ortk81" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="5srns20jk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تقليل التباين في الصورة</span>
                        </li>
                        <li className="flex items-start" data-id="09qdv6o98" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-orange-500 ml-2 mt-1" data-id="reemn2rkd" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="we94kpder" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">زيادة الضوضاء الخلفية</span>
                        </li>
                        <li className="flex items-start" data-id="din5e63o6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-orange-500 ml-2 mt-1" data-id="7b607xg41" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="gf33fra8m" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تشويه التوزيع المكاني للطاقة</span>
                        </li>
                        <li className="flex items-start" data-id="fvwumh4rc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-orange-500 ml-2 mt-1" data-id="ukk4ah8ya" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="1px08glpj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تقليل الدقة التشخيصية</span>
                        </li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <Card className="bg-gradient-to-r from-blue-50 to-purple-50" data-id="psbe0trpe" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <CardHeader data-id="ylonposa3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardTitle className="text-lg" data-id="2snlfg5tb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">استراتيجيات تقليل التبعثر</CardTitle>
                  </CardHeader>
                  <CardContent data-id="o7ar68r5w" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <div className="grid md:grid-cols-2 gap-4" data-id="g7bzmu996" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div data-id="ghw6n7s75" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="zi2fzejmp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الطرق الفيزيائية:</h4>
                        <ul className="text-sm text-gray-700 space-y-1" data-id="y7114hurt" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="uunw1ygd2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• شبكات مضادة للتبعثر</li>
                          <li data-id="gcebclb3b" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تقليل حجم الحزمة</li>
                          <li data-id="8delw0zep" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تحسين هندسة التصوير</li>
                          <li data-id="r12ioc9aw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• استخدام مرشحات طاقية</li>
                        </ul>
                      </div>
                      <div data-id="x5iler72p" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="v2at1l8fw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الطرق الحاسوبية:</h4>
                        <ul className="text-sm text-gray-700 space-y-1" data-id="b19teduzj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="k84ucvq2q" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تصحيح التبعثر بعد المعالجة</li>
                          <li data-id="r0gnh6tko" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• خوارزميات الطرح الطيفي</li>
                          <li data-id="tnctb25o6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• النمذجة الحاسوبية للتبعثر</li>
                          <li data-id="yigzjchf7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تقنيات الذكاء الاصطناعي</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 10.6 */}
        <Card className="mb-6 shadow-lg" data-id="i837ycwhf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section106']}
            onOpenChange={() => toggleSection('section106')} data-id="vfo34kc9g" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="npabgrw1g" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="kbug52afb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="qpchqdre4" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="w9ocm0s7r" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <FlaskConical className="ml-3 h-6 w-6 text-green-600" data-id="ol1dvm8kl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.6 تمرين عملي: محاكاة توهين الشعاع
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section106'] ? 'rotate-180' : ''}`} data-id="r4tvyw8fo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="gkh4b1z7i" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  محاكاة عملية لتوهين شعاع الفوتونات خلال شبح الماء
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="fwfl3etoe" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="rbc4br9nx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg" data-id="cchi3hku2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h3 className="text-xl font-bold text-gray-800 mb-4" data-id="axpsvj2mu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">هدف التمرين</h3>
                  <p className="text-gray-700 mb-4" data-id="1atx9pell" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    محاكاة توهين حزمة فوتونات أحادية الطاقة (100 keV) خلال شبح مائي أسطواني 
                    وحساب معامل التوهين الخطي ومقارنته بالقيم النظرية.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-6" data-id="rvhpqgkgi" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="border-t-4 border-t-blue-500" data-id="afavozkhi" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="s3lnjnexz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg" data-id="66i2rmj8o" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المعطيات التجريبية</CardTitle>
                    </CardHeader>
                    <CardContent data-id="0j9f552ic" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <ul className="space-y-2 text-gray-700" data-id="ihkxmsm56" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <li data-id="o69bikosf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx"><strong data-id="85dsgk4xm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">طاقة الفوتونات:</strong> 100 keV</li>
                        <li data-id="kprrikpdr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx"><strong data-id="tl8dkpil7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">هندسة الشبح:</strong> أسطوانة قطرها 20 cm</li>
                        <li data-id="2kuml5r7t" data-path="src/pages/Chapter10MonteCarloSimulation.tsx"><strong data-id="mj9dpu258" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مادة الشبح:</strong> ماء (ρ = 1.0 g/cm³)</li>
                        <li data-id="jmt5eq8er" data-path="src/pages/Chapter10MonteCarloSimulation.tsx"><strong data-id="f258pxdi3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">عدد الفوتونات:</strong> 10⁶</li>
                        <li data-id="ujs383rue" data-path="src/pages/Chapter10MonteCarloSimulation.tsx"><strong data-id="pgu2db0cs" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">سماكات مختلفة:</strong> 1, 2, 5, 10, 15 cm</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="border-t-4 border-t-purple-500" data-id="cg0qiwkn6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="hyxx62c5d" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg" data-id="nxxhma137" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الكميات المطلوب حسابها</CardTitle>
                    </CardHeader>
                    <CardContent data-id="m0lktliil" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <ul className="space-y-2 text-gray-700" data-id="62s6mhdie" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <li data-id="10a86bc0f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• معامل التوهين الخطي μ</li>
                        <li data-id="qslkany0a" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• النسبة المئوية للفوتونات المنتقلة</li>
                        <li data-id="efom7l7dc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• التوزيع الطيفي للفوتونات المبعثرة</li>
                        <li data-id="jgcks9cs1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• توزيع الجرعة المكانية</li>
                        <li data-id="xtq0loaex" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• مقارنة مع القانون الأسي</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <Card className="bg-yellow-50 border-l-4 border-l-yellow-500" data-id="7df98lq06" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <CardHeader data-id="5gvjk0qad" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardTitle className="text-lg text-yellow-800" data-id="4c7rcr85h" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">خطوات التنفيذ</CardTitle>
                  </CardHeader>
                  <CardContent data-id="deym8mbpq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <ol className="list-decimal list-inside space-y-2 text-gray-700" data-id="j552w7h6t" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <li data-id="u6rcqyzjs" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">إعداد الهندسة والمواد في برنامج المحاكاة</li>
                      <li data-id="80k0o55tc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تعريف مصدر الفوتونات (نقطي، اتجاه واحد)</li>
                      <li data-id="osidv7lg9" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تشغيل المحاكاة لكل سماكة</li>
                      <li data-id="623vs5lef" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تسجيل عدد الفوتونات المنتقلة والمبعثرة</li>
                      <li data-id="d6cyxvxwy" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">حساب معامل التوهين من المعادلة: I = I₀e^(-μx)</li>
                      <li data-id="daxjwed01" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مقارنة النتائج بالقيم النظرية</li>
                      <li data-id="iqizoib80" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تحليل الأخطاء الإحصائية</li>
                    </ol>
                  </CardContent>
                </Card>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Key Terms */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-yellow-500" data-id="gczb2faip" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <CardHeader data-id="zfmemdrnq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <CardTitle className="flex items-center text-2xl text-yellow-700" data-id="y7vid4occ" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <Key className="ml-3 h-6 w-6" data-id="bqw50x4y4" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
              المصطلحات الرئيسية
            </CardTitle>
          </CardHeader>
          <CardContent data-id="ya511idhy" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <div className="grid md:grid-cols-2 gap-6" data-id="8e7n2o0un" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <div className="space-y-3" data-id="msvks64f0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="border-r-4 border-r-blue-400 pr-4" data-id="k8yryuby7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="fgzclxpze" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مونت كارلو التناظرية</h4>
                  <p className="text-sm text-gray-600" data-id="5tgbx5qed" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">محاكاة مباشرة للعمليات الفيزيائية دون تقريبات</p>
                </div>
                <div className="border-r-4 border-r-green-400 pr-4" data-id="jhd9rrk3l" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="qngohy2iq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">أخذ العينات العكسي</h4>
                  <p className="text-sm text-gray-600" data-id="dfxs2g9vy" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تقنية لتحويل التوزيع المنتظم إلى توزيع مطلوب</p>
                </div>
                <div className="border-r-4 border-r-purple-400 pr-4" data-id="7dn1nt135" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="cdnmgvpn8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تقليل التباين</h4>
                  <p className="text-sm text-gray-600" data-id="yelyfi57v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تقنيات لتحسين كفاءة المحاكاة الإحصائية</p>
                </div>
                <div className="border-r-4 border-r-red-400 pr-4" data-id="ud9zkk3bm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="wgb0wffsm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">العداد (Tally)</h4>
                  <p className="text-sm text-gray-600" data-id="sbqbkayk5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">أداة لقياس وتسجيل الكميات الفيزيائية</p>
                </div>
              </div>
              <div className="space-y-3" data-id="234tuo0zm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="border-r-4 border-r-indigo-400 pr-4" data-id="lh6t2eeq3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="oct73tcj2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الوزن الإحصائي</h4>
                  <p className="text-sm text-gray-600" data-id="l23zh6evl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">عامل يمثل أهمية الجسيم في المحاكاة</p>
                </div>
                <div className="border-r-4 border-r-teal-400 pr-4" data-id="o64lt02pl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="7mln75lim" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المقطع العرضي الكلي</h4>
                  <p className="text-sm text-gray-600" data-id="a4rajlih2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مجموع احتماليات جميع التفاعلات الممكنة</p>
                </div>
                <div className="border-r-4 border-r-orange-400 pr-4" data-id="e7ibpia8t" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="1v2jgpj41" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الكيرما (KERMA)</h4>
                  <p className="text-sm text-gray-600" data-id="l9sw70q5c" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الطاقة الحركية المحررة في المادة</p>
                </div>
                <div className="border-r-4 border-r-pink-400 pr-4" data-id="3hzxh657f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="k7k5htcdw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">نسبة التبعثر إلى الأولي</h4>
                  <p className="text-sm text-gray-600" data-id="sbw0vi3jw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مقياس جودة الصورة في التصوير الطبي</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* References */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-green-500" data-id="3eodzty8p" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <CardHeader data-id="j8t766fcp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <CardTitle className="flex items-center text-2xl text-green-700" data-id="kxykosb57" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <FileText className="ml-3 h-6 w-6" data-id="ve47oct4l" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
              المراجع
            </CardTitle>
          </CardHeader>
          <CardContent data-id="yacf0qyat" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <ul className="space-y-2 text-gray-700" data-id="2b69mi9ym" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <li data-id="5jii6t34i" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">1. Kalos, M.H., Whitlock, P.A.: Monte Carlo Methods. Wiley-VCH (2008)</li>
              <li data-id="qmu3v6u0l" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">2. Rogers, D.W.O.: Fifty years of Monte Carlo simulations for medical physics. Phys Med Biol 51, R287-R301 (2006)</li>
              <li data-id="lz3xe3g7f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">3. Salvat, F.: PENELOPE-2018: A Code System for Monte Carlo Simulation (2019)</li>
              <li data-id="z6vflfxvp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">4. Kawrakow, I., et al.: The EGSnrc Code System: Monte Carlo Simulation of Electron and Photon Transport. NRCC Report PIRS-701 (2017)</li>
              <li data-id="1qifgi22m" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">5. Pelowitz, D.B., et al.: MCNP6 User's Manual. Los Alamos National Laboratory (2013)</li>
              <li data-id="4syi6morh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">6. Verhaegen, F., Seuntjens, J.: Monte Carlo modelling of external radiotherapy photon beams. Phys Med Biol 48, R107-R164 (2003)</li>
            </ul>
          </CardContent>
        </Card>

        {/* Problems */}
        <Card className="shadow-lg border-t-4 border-t-orange-500" data-id="4roydps50" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <CardHeader data-id="mxxaeinaf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <CardTitle className="flex items-center text-2xl text-orange-700" data-id="f9ld54pj1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <HelpCircle className="ml-3 h-6 w-6" data-id="y0u40yw18" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
              المشكلات والتمارين
            </CardTitle>
          </CardHeader>
          <CardContent data-id="m6vur45e0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <div className="space-y-6" data-id="7uyohjjjh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <div className="bg-orange-50 p-4 rounded-lg" data-id="oeqlheltv" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <h4 className="font-semibold text-orange-800 mb-2" data-id="9t5recv8g" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المشكلة 1: حساب المسافة إلى التفاعل</h4>
                <p className="text-gray-700" data-id="6xifhkujc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  فوتون بطاقة 500 keV يسافر في الماء. المقطع العرضي الكلي هو 0.096 cm⁻¹. 
                  إذا كان الرقم العشوائي ξ = 0.3، احسب المسافة إلى التفاعل التالي.
                </p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg" data-id="e8pq9hkhw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <h4 className="font-semibold text-blue-800 mb-2" data-id="udnzr3jwt" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المشكلة 2: تحديد نوع التفاعل</h4>
                <p className="text-gray-700" data-id="ztipkwlrs" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  لفوتون بطاقة 200 keV في العظم: σₚₕ = 0.02 cm⁻¹، σᶜ = 0.15 cm⁻¹، σᵣ = 0 cm⁻¹. 
                  إذا كان ξ = 0.7، حدد نوع التفاعل الذي سيحدث.
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg" data-id="e7g0g2d80" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <h4 className="font-semibold text-green-800 mb-2" data-id="1hs1x9ktp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المشكلة 3: حساب كفاءة التقسيم</h4>
                <p className="text-gray-700" data-id="e611j65f7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  تم تقسيم الفوتونات في منطقة معينة بعامل 4. إذا كان التباين الأصلي 0.05، 
                  احسب التباين الجديد والكسب في الكفاءة.
                </p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg" data-id="lr4nlod0w" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <h4 className="font-semibold text-purple-800 mb-2" data-id="kqxpy4wcf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المشكلة 4: تصميم تمرين محاكاة</h4>
                <p className="text-gray-700" data-id="e7mtjyenr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  صمم تجربة محاكاة لحساب نسبة التبعثر إلى الأولي في صورة صدرية. 
                  حدد المعطيات المطلوبة والكميات التي ستقيسها.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default Chapter10MonteCarloSimulation;