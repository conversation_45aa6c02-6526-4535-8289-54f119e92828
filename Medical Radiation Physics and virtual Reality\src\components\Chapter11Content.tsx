import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Monitor, Camera, Zap, BarChart3, PlayCircle, BookOpen } from 'lucide-react';
import { motion } from 'motion/react';

const Chapter11Content = () => {
  const [activeSection, setActiveSection] = useState('introduction');
  const [vrMode, setVrMode] = useState(false);

  const sections = [
  {
    id: 'introduction',
    title: 'مقدمة - تطور أجهزة الكشف',
    icon: <BookOpen className="w-5 h-5" data-id="gcl8pivob" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: `
          تطورت أجهزة الكشف بالأشعة السينية عبر عقود من البحث والتطوير، من الأنظمة التناظرية البسيطة 
          إلى الأنظمة الرقمية المعقدة. هذا التطور شمل ثلاث مراحل رئيسية:
          
          1. **المرحلة التناظرية (1895-1980)**: بدأت مع اكتشاف رونتغن للأشعة السينية
          2. **المرحلة المحوسبة (1980-1995)**: ظهور التصوير الشعاعي المحوسب (CR)
          3. **المرحلة الرقمية (1995-الآن)**: التصوير الرقمي المباشر (DR)
        `,
      vrContent: 'نموذج ثلاثي الأبعاد تفاعلي يوضح تطور أجهزة الكشف عبر الزمن',
      equations: [
      'كفاءة الكشف = (عدد الفوتونات المكتشفة) / (عدد الفوتونات الساقطة)',
      'دقة التباين = (S_max - S_min) / (S_max + S_min)']

    }
  },
  {
    id: 'film-screen',
    title: '11.1 أنظمة الشاشة والفيلم',
    icon: <Camera className="w-5 h-5" data-id="sf8l5yj6n" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: `
          أنظمة الشاشة والفيلم كانت الأساس في التصوير الشعاعي لعقود طويلة. تتكون من:
          
          **مكونات النظام:**
          - **الفيلم الفوتوغرافي**: يحتوي على بلورات هاليد الفضة الحساسة للضوء
          - **الشاشة المكثفة**: تحتوي على فوسفور يحول الأشعة السينية إلى ضوء مرئي
          - **الكاسيت**: يحمي الفيلم ويضمن التلامس الجيد مع الشاشة
          
          **مبدأ العمل:**
          1. الأشعة السينية تخترق المريض وتصل إلى الشاشة المكثفة
          2. الفوسفور في الشاشة يمتص الأشعة السينية ويصدر ضوء مرئي
          3. الضوء المرئي يعرض الفيلم الفوتوغرافي
          4. الفيلم المعرض يُطور كيميائياً لإظهار الصورة
        `,
      vrContent: 'محاكاة ثلاثية الأبعاد لعملية تكوين الصورة في نظام الشاشة والفيلم',
      advantages: ['تكلفة منخفضة', 'دقة مكانية عالية', 'نطاق ديناميكي واسع'],
      disadvantages: ['معالجة كيميائية', 'تخزين فيزيائي', 'عدم إمكانية التعديل الرقمي']
    }
  },
  {
    id: 'computed-radiography',
    title: '11.2 التصوير الشعاعي المحوسب (CR)',
    icon: <Monitor className="w-5 h-5" data-id="39hhgq1b4" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: `
          التصوير الشعاعي المحوسب يستخدم الفوسفور القابل للتحفيز الضوئي (PSP) لتخزين طاقة الأشعة السينية.
          
          **مبدأ الفوسفور القابل للتحفيز الضوئي:**
          - **المادة الأساسية**: بروميد الباريوm المخلوط بالأوروبيوم (BaBrF:Eu)
          - **آلية التخزين**: الإلكترونات المثارة تُحبس في مستويات طاقة وسطية
          - **القراءة**: ليزر أحمر يحرر الإلكترونات المحبوسة فتصدر ضوء أزرق
          
          **مراحل العمل:**
          1. **التعرض**: الأشعة السينية تخزن كصورة كامنة في اللوح الفوسفوري
          2. **القراءة**: الليزر يمسح اللوح ويحول الصورة الكامنة إلى إشارة ضوئية
          3. **التحويل**: أنبوب مضاعف الضوء (PMT) يحول الضوء إلى إشارة كهربائية
          4. **الرقمنة**: محول تناظري-رقمي (ADC) يحول الإشارة إلى بيانات رقمية
        `,
      vrContent: 'نموذج تفاعلي يوضح عملية تحفيز الفوسفور وقراءة الصورة',
      technicalSpecs: {
        'دقة مكانية': '2.5-5 lp/mm',
        'عمق البت': '10-12 bit',
        'كفاءة الكم': '15-25%',
        'نطاق التعرض': '1:10000'
      }
    }
  },
  {
    id: 'digital-radiography',
    title: '11.3 التصوير الشعاعي الرقمي (DR)',
    icon: <Zap className="w-5 h-5" data-id="0ntgsax36" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: `
          التصوير الرقمي المباشر يحول الأشعة السينية إلى إشارة كهربائية مباشرة دون خطوات وسطية.
          
          **11.3.1 التحويل غير المباشر:**
          يتم على مرحلتين: أشعة سينية → ضوء → إشارة كهربائية
          
          **أ) الومضات + الثنائيات الضوئية:**
          - **المادة الومضة**: يوديد السيزيوم (CsI) أو أكسيد الغادولينيوم (Gd2O2S)
          - **ثنائيات السيليكون**: تحول الضوء إلى شحنات كهربائية
          - **مصفوفة TFT**: ترانزستورات الأغشية الرقيقة لقراءة الإشارة
          
          **ب) أجهزة CCD (Charge-Coupled Device):**
          - تستخدم مع شاشة فوسفورية ونظام عدسات
          - دقة عالية لكن مجال رؤية محدود
          - مناسبة للتطبيقات المتخصصة
          
          **ج) أجهزة CMOS:**
          - استهلاك طاقة أقل من CCD
          - قراءة أسرع ومعالجة محلية
          - مقاومة أفضل للإشعاع
        `,
      vrContent: 'تصور ثلاثي الأبعاد لبنية الكاشف وعملية التحويل',
      comparison: {
        'CsI': { resolution: 'عالية', efficiency: '20-25%', structure: 'عمودية' },
        'Gd2O2S': { resolution: 'متوسطة', efficiency: '15-20%', structure: 'بودرة' }
      }
    }
  },
  {
    id: 'direct-conversion',
    title: '11.3.2 التحويل المباشر',
    icon: <Zap className="w-5 h-5" data-id="eq8d5gzfl" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: `
          **التحويل المباشر - السيلينيوم غير المتبلور:**
          
          يحول الأشعة السينية مباشرة إلى شحنات كهربائية دون مرحلة الضوء الوسطية.
          
          **مبدأ العمل:**
          1. **امتصاص الفوتون**: فوتون الأشعة السينية يُمتص في طبقة السيلينيوم
          2. **توليد الشحنات**: كل فوتون يولد آلاف أزواج الإلكترون-الثقب
          3. **الانجراف**: مجال كهربائي قوي (10 V/μm) يفصل الشحنات
          4. **الجمع**: الشحنات تُجمع في مصفوفة البكسل
          
          **مميزات السيلينيوم:**
          - **دقة مكانية عالية**: عدم انتشار الضوء الجانبي
          - **استجابة خطية**: علاقة مباشرة بين التعرض والإشارة
          - **كفاءة كم جيدة**: 40-80% في النطاق التشخيصي
          
          **التحديات:**
          - حساسية للحرارة والرطوبة
          - يتطلب جهد كهربائي عالي
          - محدود بسماكة الطبقة الحساسة
        `,
      vrContent: 'نموذج جزيئي تفاعلي يوضح عملية توليد وانجراف الشحنات',
      performance: {
        'السماكة النموذجية': '100-1000 μm',
        'الجهد المطبق': '1000-10000 V',
        'دقة البكسل': '50-200 μm',
        'معدل الإطارات': '1-30 fps'
      }
    }
  },
  {
    id: 'performance-metrics',
    title: '11.4 مقاييس أداء الكاشف',
    icon: <BarChart3 className="w-5 h-5" data-id="wmjck32bm" data-path="src/components/Chapter11Content.tsx" />,
    content: {
      theory: `
          **11.4.1 كفاءة الكم (QE) وكفاءة الكم الاستقصائية (DQE):**
          
          **كفاءة الكم (Quantum Efficiency):**
          QE(E) = η_abs(E) × η_conv(E)
          
          حيث:
          - η_abs: كفاءة الامتصاص
          - η_conv: كفاءة التحويل
          
          **كفاءة الكم الاستقصائية (Detective Quantum Efficiency):**
          DQE(f) = [MTF(f)]² × QE / [1 + (σ_add/σ_quantum)²]
          
          حيث:
          - MTF: دالة نقل التعديل
          - σ_add: الضوضاء المضافة
          - σ_quantum: الضوضاء الكمية
          
          **11.4.2 دالة نقل التعديل (MTF):**
          
          تقيس قدرة النظام على نقل التفاصيل المكانية:
          MTF(f) = |FFT[LSF(x)]|
          
          حيث LSF هي دالة الانتشار الخطي.
          
          **طيف قدرة الضوضاء (NPS):**
          NPS(f) = pixel_size² × |FFT[noise_image - mean]|²
          
          **11.4.3 المعايير الأساسية:**
          
          **الدقة المكانية:**
          - تُقاس بخطوط الأزواج في المليمتر (lp/mm)
          - تحدد أصغر تفصيل يمكن تمييزه
          
          **دقة التباين:**
          - القدرة على تمييز الاختلافات الطفيفة في الكثافة
          - تعتمد على نسبة الإشارة إلى الضوضاء (SNR)
          
          **النطاق الديناميكي:**
          - نسبة أقصى إشارة إلى أدنى إشارة قابلة للكشف
          - يُعبر عنه بـ dB أو نسبة عددية
        `,
      vrContent: 'تصور تفاعلي لمنحنيات MTF وNPS وDQE',
      benchmarks: {
        'الأشعة العامة': { MTF_50: '2-4 lp/mm', DQE_0: '60-80%' },
        'تصوير الثدي': { MTF_50: '4-6 lp/mm', DQE_0: '40-60%' },
        'التصوير الصدري': { MTF_50: '2-3 lp/mm', DQE_0: '50-70%' }
      }
    }
  }];


  const InteractiveVisualization = ({ section }: {section: any;}) =>
  <motion.div
    initial={{ opacity: 0, scale: 0.9 }}
    animate={{ opacity: 1, scale: 1 }}
    className="bg-gradient-to-br from-blue-50 to-purple-50 p-6 rounded-lg border" data-id="ihedfp0se" data-path="src/components/Chapter11Content.tsx">

      <div className="flex items-center justify-between mb-4" data-id="atvepu3wa" data-path="src/components/Chapter11Content.tsx">
        <h3 className="text-lg font-semibold text-right" data-id="l35iy4cjb" data-path="src/components/Chapter11Content.tsx">{vrMode ? 'وضع الواقع الافتراضي' : 'التصور التفاعلي'}</h3>
        <Button
        variant={vrMode ? "default" : "outline"}
        size="sm"
        onClick={() => setVrMode(!vrMode)} data-id="fp6yi2zd1" data-path="src/components/Chapter11Content.tsx">

          <PlayCircle className="w-4 h-4 ml-2" data-id="zq5of00wa" data-path="src/components/Chapter11Content.tsx" />
          {vrMode ? 'إيقاف VR' : 'تشغيل VR'}
        </Button>
      </div>
      
      <div className="aspect-video bg-gradient-to-br from-slate-800 to-slate-900 rounded-lg flex items-center justify-center text-white" data-id="lqe8ghyrf" data-path="src/components/Chapter11Content.tsx">
        {vrMode ?
      <motion.div
        initial={{ rotateY: 0 }}
        animate={{ rotateY: 360 }}
        transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
        className="text-center" data-id="ab4x4xaws" data-path="src/components/Chapter11Content.tsx">

            <Monitor className="w-16 h-16 mx-auto mb-4" data-id="u6tb7xqbg" data-path="src/components/Chapter11Content.tsx" />
            <p className="text-lg" data-id="kfffrz5bv" data-path="src/components/Chapter11Content.tsx">{section.content.vrContent}</p>
            <div className="mt-4 text-sm opacity-75" data-id="06lu41f0o" data-path="src/components/Chapter11Content.tsx">
              استخدم الماوس أو اللمس للتفاعل مع النموذج ثلاثي الأبعاد
            </div>
          </motion.div> :

      <div className="text-center" data-id="vuq5w4r9k" data-path="src/components/Chapter11Content.tsx">
            <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4" data-id="lk87j3bgm" data-path="src/components/Chapter11Content.tsx">
              {section.icon}
            </div>
            <p data-id="v2dwph8bf" data-path="src/components/Chapter11Content.tsx">انقر على "تشغيل VR" لبدء التجربة التفاعلية</p>
          </div>
      }
      </div>
      
      {section.content.equations &&
    <div className="mt-4 p-4 bg-white rounded-lg" data-id="cf2hilyrt" data-path="src/components/Chapter11Content.tsx">
          <h4 className="font-semibold text-right mb-2" data-id="9tx737erw" data-path="src/components/Chapter11Content.tsx">المعادلات الأساسية:</h4>
          {section.content.equations.map((eq: string, index: number) =>
      <div key={index} className="text-sm font-mono bg-slate-100 p-2 rounded mb-2 text-center" data-id="oybq8re7a" data-path="src/components/Chapter11Content.tsx">
              {eq}
            </div>
      )}
        </div>
    }
    </motion.div>;


  const currentSection = sections.find((s) => s.id === activeSection);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6" dir="rtl" data-id="j6n20y785" data-path="src/components/Chapter11Content.tsx">
      <div className="max-w-7xl mx-auto" data-id="xj1aq6g0v" data-path="src/components/Chapter11Content.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8" data-id="uzj2y40lr" data-path="src/components/Chapter11Content.tsx">

          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4" data-id="zswrzw2nm" data-path="src/components/Chapter11Content.tsx">
            الفصل الحادي عشر: أجهزة الكشف بالأشعة السينية
          </h1>
          <p className="text-xl text-muted-foreground" data-id="rwtfy6jfc" data-path="src/components/Chapter11Content.tsx">
            المبادئ والخصائص والتطبيقات العملية
          </p>
          <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mt-4" data-id="db5kh270x" data-path="src/components/Chapter11Content.tsx">
            <Badge variant="outline" data-id="bk83rlj6k" data-path="src/components/Chapter11Content.tsx">التصوير الطبي</Badge>
            <Badge variant="outline" data-id="2a3itrzrc" data-path="src/components/Chapter11Content.tsx">الفيزياء الطبية</Badge>
            <Badge variant="outline" data-id="ofa99gudz" data-path="src/components/Chapter11Content.tsx">التكنولوجيا الحديثة</Badge>
          </div>
        </motion.div>

        {/* Navigation */}
        <Card className="mb-8" data-id="b2pbl3omt" data-path="src/components/Chapter11Content.tsx">
          <CardHeader data-id="7r3jqcvew" data-path="src/components/Chapter11Content.tsx">
            <CardTitle className="text-right" data-id="vzgz9cei9" data-path="src/components/Chapter11Content.tsx">محتويات الفصل</CardTitle>
          </CardHeader>
          <CardContent data-id="uzkpoa4r4" data-path="src/components/Chapter11Content.tsx">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2" data-id="nrqgdajao" data-path="src/components/Chapter11Content.tsx">
              {sections.map((section) =>
              <Button
                key={section.id}
                variant={activeSection === section.id ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveSection(section.id)}
                className="justify-end" data-id="svtqvy8o5" data-path="src/components/Chapter11Content.tsx">

                  <span className="mr-2" data-id="l8ewcs0i9" data-path="src/components/Chapter11Content.tsx">{section.title}</span>
                  {section.icon}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-8" data-id="6m7vi2uc6" data-path="src/components/Chapter11Content.tsx">
          {/* Theory Content */}
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }} data-id="6pohdyxla" data-path="src/components/Chapter11Content.tsx">

            <Card className="h-full" data-id="iwcqlives" data-path="src/components/Chapter11Content.tsx">
              <CardHeader data-id="fjby5awdq" data-path="src/components/Chapter11Content.tsx">
                <div className="flex items-center justify-between" data-id="67oxqbpkv" data-path="src/components/Chapter11Content.tsx">
                  <CardTitle className="text-right flex items-center space-x-3 rtl:space-x-reverse" data-id="wva3da15o" data-path="src/components/Chapter11Content.tsx">
                    {currentSection?.icon}
                    <span data-id="yljxaucko" data-path="src/components/Chapter11Content.tsx">{currentSection?.title}</span>
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-6" data-id="y7bfmx7ec" data-path="src/components/Chapter11Content.tsx">
                <div className="prose prose-sm max-w-none text-right" style={{ direction: 'rtl' }} data-id="5sxgms9bj" data-path="src/components/Chapter11Content.tsx">
                  <div className="whitespace-pre-line text-sm leading-relaxed" data-id="2hczzb9h6" data-path="src/components/Chapter11Content.tsx">
                    {currentSection?.content.theory}
                  </div>
                </div>

                {/* Additional Content Sections */}
                {currentSection?.content.advantages &&
                <div className="bg-green-50 p-4 rounded-lg" data-id="bz2xlybbz" data-path="src/components/Chapter11Content.tsx">
                    <h4 className="font-semibold text-green-800 text-right mb-2" data-id="wxqtdg0ow" data-path="src/components/Chapter11Content.tsx">المميزات:</h4>
                    <ul className="text-sm text-green-700 space-y-1" data-id="b2o6c6g8l" data-path="src/components/Chapter11Content.tsx">
                      {currentSection.content.advantages.map((advantage: string, index: number) =>
                    <li key={index} className="text-right" data-id="e4si2cnpp" data-path="src/components/Chapter11Content.tsx">• {advantage}</li>
                    )}
                    </ul>
                  </div>
                }

                {currentSection?.content.disadvantages &&
                <div className="bg-red-50 p-4 rounded-lg" data-id="0oaly5ufd" data-path="src/components/Chapter11Content.tsx">
                    <h4 className="font-semibold text-red-800 text-right mb-2" data-id="f3onbt4q5" data-path="src/components/Chapter11Content.tsx">القيود:</h4>
                    <ul className="text-sm text-red-700 space-y-1" data-id="k7w7ykbst" data-path="src/components/Chapter11Content.tsx">
                      {currentSection.content.disadvantages.map((disadvantage: string, index: number) =>
                    <li key={index} className="text-right" data-id="h01hrdmf5" data-path="src/components/Chapter11Content.tsx">• {disadvantage}</li>
                    )}
                    </ul>
                  </div>
                }

                {currentSection?.content.technicalSpecs &&
                <div className="bg-blue-50 p-4 rounded-lg" data-id="9dt9xl832" data-path="src/components/Chapter11Content.tsx">
                    <h4 className="font-semibold text-blue-800 text-right mb-3" data-id="alhqbrhnu" data-path="src/components/Chapter11Content.tsx">المواصفات التقنية:</h4>
                    <div className="grid grid-cols-2 gap-3" data-id="haler5kqg" data-path="src/components/Chapter11Content.tsx">
                      {Object.entries(currentSection.content.technicalSpecs).map(([key, value]) =>
                    <div key={key} className="text-sm" data-id="wxug5m8rl" data-path="src/components/Chapter11Content.tsx">
                          <div className="font-medium text-right" data-id="sqqbkghao" data-path="src/components/Chapter11Content.tsx">{key}</div>
                          <div className="text-blue-600 text-right" data-id="lb2fl040n" data-path="src/components/Chapter11Content.tsx">{value}</div>
                        </div>
                    )}
                    </div>
                  </div>
                }
              </CardContent>
            </Card>
          </motion.div>

          {/* Interactive Visualization */}
          <motion.div
            key={`${activeSection}-viz`}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }} data-id="5l0a469r0" data-path="src/components/Chapter11Content.tsx">

            {currentSection && <InteractiveVisualization section={currentSection} data-id="9ptt582qt" data-path="src/components/Chapter11Content.tsx" />}
          </motion.div>
        </div>

        {/* Learning Objectives */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8" data-id="i0o6k4b97" data-path="src/components/Chapter11Content.tsx">

          <Card data-id="cgendeia8" data-path="src/components/Chapter11Content.tsx">
            <CardHeader data-id="bwq2sdbf3" data-path="src/components/Chapter11Content.tsx">
              <CardTitle className="text-right" data-id="2zl8oqrws" data-path="src/components/Chapter11Content.tsx">أهداف التعلم</CardTitle>
            </CardHeader>
            <CardContent data-id="u2du9n4bt" data-path="src/components/Chapter11Content.tsx">
              <div className="grid md:grid-cols-2 gap-4" data-id="qb60zxiqg" data-path="src/components/Chapter11Content.tsx">
                <div data-id="pt8o2ff42" data-path="src/components/Chapter11Content.tsx">
                  <h4 className="font-semibold text-right mb-2" data-id="jf9t92cll" data-path="src/components/Chapter11Content.tsx">المعرفة النظرية:</h4>
                  <ul className="text-sm space-y-1 text-right" data-id="3ckjoamcf" data-path="src/components/Chapter11Content.tsx">
                    <li data-id="vpa9jnu6x" data-path="src/components/Chapter11Content.tsx">• فهم مبادئ عمل أجهزة الكشف المختلفة</li>
                    <li data-id="dgtp7qeyt" data-path="src/components/Chapter11Content.tsx">• مقارنة مميزات وقيود كل نوع</li>
                    <li data-id="osua3pc0f" data-path="src/components/Chapter11Content.tsx">• تحليل مقاييس الأداء والجودة</li>
                  </ul>
                </div>
                <div data-id="tj4cucfvj" data-path="src/components/Chapter11Content.tsx">
                  <h4 className="font-semibold text-right mb-2" data-id="ww9lk9h86" data-path="src/components/Chapter11Content.tsx">التطبيق العملي:</h4>
                  <ul className="text-sm space-y-1 text-right" data-id="16klgej0t" data-path="src/components/Chapter11Content.tsx">
                    <li data-id="a2gb4yfc7" data-path="src/components/Chapter11Content.tsx">• اختيار الكاشف المناسب للتطبيق</li>
                    <li data-id="cp42lbhee" data-path="src/components/Chapter11Content.tsx">• تقييم جودة الصور السريرية</li>
                    <li data-id="7cysca83q" data-path="src/components/Chapter11Content.tsx">• حل مشاكل الأداء والصيانة</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>);

};

export default Chapter11Content;