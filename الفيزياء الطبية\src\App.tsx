import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';

// Pages
import HomePage from '@/pages/HomePage';
import NotFound from '@/pages/NotFound';
import CoherentScattering from '@/pages/CoherentScattering';
import PhotoelectricEffect from '@/pages/PhotoelectricEffect';
import ComptonScattering from '@/pages/ComptonScattering';
import PairProduction from '@/pages/PairProduction';
import AttenuationCoefficients from '@/pages/AttenuationCoefficients';
import RelativeImportance from '@/pages/RelativeImportance';
import LearningObjectives from '@/pages/LearningObjectives';
import KeyTerms from '@/pages/KeyTerms';
import References from '@/pages/References';
import Problems from '@/pages/Problems';
import Chapter9PatientModeling from '@/pages/Chapter9PatientModeling';
import Chapter10MonteCarloSimulation from '@/pages/Chapter10MonteCarloSimulation';
import Chapter11XrayDetection from '@/pages/Chapter11XrayDetection';
import Chapter12DetectorSimulation from '@/pages/Chapter12DetectorSimulation';
import Chapter13SimulationApplications from '@/pages/Chapter13SimulationApplications';
import Chapter14PatientDose from '@/pages/Chapter14PatientDose';
import Chapter15ImageQuality from '@/pages/Chapter15ImageQuality';
import Chapter16Verification from '@/pages/Chapter16Verification';
import Chapter17FutureTrends from '@/pages/Chapter17FutureTrends';

import './App.css';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient} data-id="bj6gugpfo" data-path="src/App.tsx">
      <TooltipProvider data-id="4lce0fklj" data-path="src/App.tsx">
        <Router data-id="mpkzne8e5" data-path="src/App.tsx">
          <div className="min-h-screen bg-gray-50" data-id="mmxl8irwx" data-path="src/App.tsx">
            <Routes data-id="4p6cbco6s" data-path="src/App.tsx">
              <Route path="/" element={<HomePage data-id="dhcbi7oq8" data-path="src/App.tsx" />} data-id="5r2a7axma" data-path="src/App.tsx" />
              <Route path="/learning-objectives" element={<LearningObjectives data-id="emiq8a33p" data-path="src/App.tsx" />} data-id="8yrvtthcn" data-path="src/App.tsx" />
              <Route path="/coherent-scattering" element={<CoherentScattering data-id="whvm6xi9h" data-path="src/App.tsx" />} data-id="75f1cqh7x" data-path="src/App.tsx" />
              <Route path="/photoelectric-effect" element={<PhotoelectricEffect data-id="jdl1l7hkp" data-path="src/App.tsx" />} data-id="vu14fh2tm" data-path="src/App.tsx" />
              <Route path="/compton-scattering" element={<ComptonScattering data-id="nrg7sgbr2" data-path="src/App.tsx" />} data-id="w45gai469" data-path="src/App.tsx" />
              <Route path="/pair-production" element={<PairProduction data-id="12jmc4mxk" data-path="src/App.tsx" />} data-id="c5h0abveq" data-path="src/App.tsx" />
              <Route path="/attenuation-coefficients" element={<AttenuationCoefficients data-id="0gg8zdozy" data-path="src/App.tsx" />} data-id="nunsitxhg" data-path="src/App.tsx" />
              <Route path="/relative-importance" element={<RelativeImportance data-id="v56jpn1r5" data-path="src/App.tsx" />} data-id="vm26e5y8m" data-path="src/App.tsx" />
              <Route path="/key-terms" element={<KeyTerms data-id="gcnmxna3m" data-path="src/App.tsx" />} data-id="sx3rllbob" data-path="src/App.tsx" />
              <Route path="/references" element={<References data-id="cvuyylrss" data-path="src/App.tsx" />} data-id="p0j3qjsev" data-path="src/App.tsx" />
              <Route path="/problems" element={<Problems data-id="cx1jj1qpz" data-path="src/App.tsx" />} data-id="t9ol1kt8m" data-path="src/App.tsx" />
              <Route path="/chapter9-patient-modeling" element={<Chapter9PatientModeling data-id="d644vz3ae" data-path="src/App.tsx" />} data-id="a6obxll5c" data-path="src/App.tsx" />
              <Route path="/chapter10-monte-carlo" element={<Chapter10MonteCarloSimulation data-id="h00ro1mux" data-path="src/App.tsx" />} data-id="fhu5i5151" data-path="src/App.tsx" />
              <Route path="/chapter11-xray-detection" element={<Chapter11XrayDetection data-id="xyzylca55" data-path="src/App.tsx" />} data-id="1dk5gtjjh" data-path="src/App.tsx" />
              <Route path="/chapter12-detector-simulation" element={<Chapter12DetectorSimulation data-id="zhf8w9i1x" data-path="src/App.tsx" />} data-id="7274xdamp" data-path="src/App.tsx" />
              <Route path="/chapter13-simulation-applications" element={<Chapter13SimulationApplications data-id="74y009fkb" data-path="src/App.tsx" />} data-id="tgpyiealf" data-path="src/App.tsx" />
              <Route path="/chapter14-patient-dose" element={<Chapter14PatientDose data-id="vkdfpw3wo" data-path="src/App.tsx" />} data-id="klqmwsuo1" data-path="src/App.tsx" />
              <Route path="/chapter15-image-quality" element={<Chapter15ImageQuality data-id="w7l7llr3a" data-path="src/App.tsx" />} data-id="zv3cc625v" data-path="src/App.tsx" />
              <Route path="/chapter16-verification" element={<Chapter16Verification data-id="hgtjenbxl" data-path="src/App.tsx" />} data-id="ob8twwavh" data-path="src/App.tsx" />
              <Route path="/chapter17-future-trends" element={<Chapter17FutureTrends data-id="xfevuhcld" data-path="src/App.tsx" />} data-id="hf12hb1k7" data-path="src/App.tsx" />
              <Route path="*" element={<NotFound data-id="y3uf8axsd" data-path="src/App.tsx" />} data-id="crqxzjk1g" data-path="src/App.tsx" />
            </Routes>
          </div>
        </Router>
        <Toaster data-id="1yy9ubz5p" data-path="src/App.tsx" />
      </TooltipProvider>
    </QueryClientProvider>);

}

export default App;