import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useNavigate } from 'react-router-dom';
import {
  Zap,
  Target,
  ArrowRight,
  BookOpen,
  FileText,
  HelpCircle,
  Activity,
  Cpu,
  Monitor,
  Atom,
  Grid3X3,
  User,
  BarChart3 } from
'lucide-react';

const HomePage = () => {
  const navigate = useNavigate();

  const chapters = [
  {
    title: "التشتت المترابط",
    description: "فهم آليات التشتت المترابط للأشعة السينية",
    path: "/coherent-scattering",
    icon: <Zap className="h-6 w-6" data-id="xoeadfwn2" data-path="src/pages/HomePage.tsx" />,
    color: "blue",
    difficulty: "متوسط"
  },
  {
    title: "التأثير الكهروضوئي",
    description: "دراسة التأثير الكهروضوئي وتطبيقاته",
    path: "/photoelectric-effect",
    icon: <Target className="h-6 w-6" data-id="wa1k3yf13" data-path="src/pages/HomePage.tsx" />,
    color: "green",
    difficulty: "أساسي"
  },
  {
    title: "تشتت كومبتون",
    description: "آليات وخصائص تشتت كومبتون",
    path: "/compton-scattering",
    icon: <Activity className="h-6 w-6" data-id="loayi83lb" data-path="src/pages/HomePage.tsx" />,
    color: "purple",
    difficulty: "متقدم"
  },
  {
    title: "إنتاج الأزواج",
    description: "فهم عملية إنتاج الأزواج عند الطاقات العالية",
    path: "/pair-production",
    icon: <Atom className="h-6 w-6" data-id="c84fkmbb8" data-path="src/pages/HomePage.tsx" />,
    color: "red",
    difficulty: "متقدم"
  },
  {
    title: "معاملات التوهين",
    description: "حساب وتطبيق معاملات التوهين المختلفة",
    path: "/attenuation-coefficients",
    icon: <BarChart3 className="h-6 w-6" data-id="jwn4l5ljd" data-path="src/pages/HomePage.tsx" />,
    color: "indigo",
    difficulty: "متوسط"
  },
  {
    title: "الأهمية النسبية",
    description: "مقارنة أهمية التفاعلات المختلفة",
    path: "/relative-importance",
    icon: <Target className="h-6 w-6" data-id="dhcpk33x1" data-path="src/pages/HomePage.tsx" />,
    color: "orange",
    difficulty: "متوسط"
  },
  {
    title: "الفصل التاسع: نمذجة المريض",
    description: "نمذجة المريض والفانتوم الحاسوبي",
    path: "/chapter-9-patient-modeling",
    icon: <User className="h-6 w-6" data-id="hsrq60d8j" data-path="src/pages/HomePage.tsx" />,
    color: "teal",
    difficulty: "متقدم"
  },
  {
    title: "الفصل العاشر: محاكاة مونت كارلو",
    description: "تقنيات محاكاة مونت كارلو في الفيزياء الطبية",
    path: "/chapter-10-monte-carlo",
    icon: <Cpu className="h-6 w-6" data-id="snqni9ksk" data-path="src/pages/HomePage.tsx" />,
    color: "cyan",
    difficulty: "متقدم"
  },
  {
    title: "الفصل الحادي عشر: أجهزة الكشف بالأشعة السينية",
    description: "أجهزة الكشف في التصوير الشعاعي: المبادئ والخصائص",
    path: "/chapter-11-xray-detection",
    icon: <Monitor className="h-6 w-6" data-id="wyvbct28w" data-path="src/pages/HomePage.tsx" />,
    color: "blue",
    difficulty: "متقدم"
  },
  {
    title: "الفصل الثاني عشر: محاكاة استجابة الكاشف",
    description: "محاكاة استجابة الكاشف وتكوين الصورة",
    path: "/chapter-12-detector-simulation",
    icon: <Grid3X3 className="h-6 w-6" data-id="jg4x56ml4" data-path="src/pages/HomePage.tsx" />,
    color: "purple",
    difficulty: "متقدم"
  }];


  const resources = [
  {
    title: "أهداف التعلم",
    description: "الأهداف التعليمية لكل فصل",
    path: "/learning-objectives",
    icon: <Target className="h-6 w-6" data-id="47a8mv3bt" data-path="src/pages/HomePage.tsx" />,
    color: "green"
  },
  {
    title: "المصطلحات الرئيسية",
    description: "قاموس المصطلحات الطبية والفيزيائية",
    path: "/key-terms",
    icon: <BookOpen className="h-6 w-6" data-id="yxaqps8ad" data-path="src/pages/HomePage.tsx" />,
    color: "blue"
  },
  {
    title: "المراجع",
    description: "قائمة المراجع والمصادر العلمية",
    path: "/references",
    icon: <FileText className="h-6 w-6" data-id="k6rqf9dc8" data-path="src/pages/HomePage.tsx" />,
    color: "purple"
  },
  {
    title: "المشكلات والتمارين",
    description: "مجموعة من المشكلات والحلول",
    path: "/problems",
    icon: <HelpCircle className="h-6 w-6" data-id="l8x9oqay3" data-path="src/pages/HomePage.tsx" />,
    color: "orange"
  }];


  const getColorClasses = (color: string) => {
    const colors = {
      blue: "border-blue-200 hover:border-blue-300 bg-blue-50 hover:bg-blue-100",
      green: "border-green-200 hover:border-green-300 bg-green-50 hover:bg-green-100",
      purple: "border-purple-200 hover:border-purple-300 bg-purple-50 hover:bg-purple-100",
      red: "border-red-200 hover:border-red-300 bg-red-50 hover:bg-red-100",
      indigo: "border-indigo-200 hover:border-indigo-300 bg-indigo-50 hover:bg-indigo-100",
      orange: "border-orange-200 hover:border-orange-300 bg-orange-50 hover:bg-orange-100",
      teal: "border-teal-200 hover:border-teal-300 bg-teal-50 hover:bg-teal-100",
      cyan: "border-cyan-200 hover:border-cyan-300 bg-cyan-50 hover:bg-cyan-100"
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      "أساسي": "bg-green-100 text-green-800",
      "متوسط": "bg-yellow-100 text-yellow-800",
      "متقدم": "bg-red-100 text-red-800"
    };
    return colors[difficulty as keyof typeof colors] || colors["متوسط"];
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50" data-id="f0qcaipxy" data-path="src/pages/HomePage.tsx">
      <div className="container mx-auto px-4 py-12 max-w-6xl" data-id="xt8jhb9vj" data-path="src/pages/HomePage.tsx">
        {/* Hero Section */}
        <div className="text-center mb-16" data-id="su6nt9pjn" data-path="src/pages/HomePage.tsx">
          <div className="mb-8" data-id="wdjc2ir73" data-path="src/pages/HomePage.tsx">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-800 mb-6" data-id="md0tgr3fw" data-path="src/pages/HomePage.tsx">
              الفيزياء الطبية
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto" data-id="d2e50kqur" data-path="src/pages/HomePage.tsx">
              دليل شامل لفهم تفاعلات الأشعة السينية مع المادة ونمذجة أنظمة التصوير الطبي
            </p>
          </div>
          
          <div className="flex flex-wrap justify-center gap-4 mb-8" data-id="acze3vzi2" data-path="src/pages/HomePage.tsx">
            <Badge variant="secondary" className="px-4 py-2 text-lg" data-id="9u2smmewu" data-path="src/pages/HomePage.tsx">
              <BookOpen className="h-5 w-5 mr-2" data-id="qlp7u49pk" data-path="src/pages/HomePage.tsx" />
              تعليم تفاعلي
            </Badge>
            <Badge variant="secondary" className="px-4 py-2 text-lg" data-id="raqk53ihy" data-path="src/pages/HomePage.tsx">
              <Target className="h-5 w-5 mr-2" data-id="pox1xpsby" data-path="src/pages/HomePage.tsx" />
              محتوى علمي دقيق
            </Badge>
            <Badge variant="secondary" className="px-4 py-2 text-lg" data-id="u1b8pl7xx" data-path="src/pages/HomePage.tsx">
              <Activity className="h-5 w-5 mr-2" data-id="zp01ui1xx" data-path="src/pages/HomePage.tsx" />
              تطبيقات عملية
            </Badge>
          </div>
        </div>

        {/* Main Chapters */}
        <section className="mb-16" data-id="86wwp55ii" data-path="src/pages/HomePage.tsx">
          <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center" data-id="f1jd47dpl" data-path="src/pages/HomePage.tsx">
            الفصول الرئيسية
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-id="rgdwgtpq0" data-path="src/pages/HomePage.tsx">
            {chapters.map((chapter, index) =>
            <Card
              key={index}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${getColorClasses(chapter.color)}`}
              onClick={() => navigate(chapter.path)} data-id="1defqhqm9" data-path="src/pages/HomePage.tsx">

                <CardHeader data-id="al2wc5gei" data-path="src/pages/HomePage.tsx">
                  <div className="flex items-center justify-between mb-2" data-id="bg0oh3xby" data-path="src/pages/HomePage.tsx">
                    <div className={`p-2 rounded-lg bg-${chapter.color}-100`} data-id="5xkmmfrb2" data-path="src/pages/HomePage.tsx">
                      {chapter.icon}
                    </div>
                    <Badge className={getDifficultyColor(chapter.difficulty)} data-id="ao6mes6ic" data-path="src/pages/HomePage.tsx">
                      {chapter.difficulty}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg font-bold text-gray-800" data-id="bzl7mtzb3" data-path="src/pages/HomePage.tsx">
                    {chapter.title}
                  </CardTitle>
                  <CardDescription className="text-gray-600" data-id="3m48fngqp" data-path="src/pages/HomePage.tsx">
                    {chapter.description}
                  </CardDescription>
                </CardHeader>
                <CardContent data-id="beqkt76rs" data-path="src/pages/HomePage.tsx">
                  <Button
                  variant="ghost"
                  className="w-full justify-between group"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(chapter.path);
                  }} data-id="chg2s4yo4" data-path="src/pages/HomePage.tsx">

                    <span data-id="hti902rqx" data-path="src/pages/HomePage.tsx">استكشف الفصل</span>
                    <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" data-id="g0blp0wlz" data-path="src/pages/HomePage.tsx" />
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </section>

        {/* Resources Section */}
        <section data-id="ei8213xdl" data-path="src/pages/HomePage.tsx">
          <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center" data-id="9b095lpci" data-path="src/pages/HomePage.tsx">
            مصادر إضافية
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" data-id="8z2fuwf2f" data-path="src/pages/HomePage.tsx">
            {resources.map((resource, index) =>
            <Card
              key={index}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${getColorClasses(resource.color)}`}
              onClick={() => navigate(resource.path)} data-id="j24zjkty0" data-path="src/pages/HomePage.tsx">

                <CardHeader className="text-center" data-id="7ugueiywi" data-path="src/pages/HomePage.tsx">
                  <div className={`mx-auto p-3 rounded-full bg-${resource.color}-100 w-fit mb-4`} data-id="ftuib9g1t" data-path="src/pages/HomePage.tsx">
                    {resource.icon}
                  </div>
                  <CardTitle className="text-lg font-bold text-gray-800" data-id="uaior6yqh" data-path="src/pages/HomePage.tsx">
                    {resource.title}
                  </CardTitle>
                  <CardDescription className="text-gray-600" data-id="eqqnykzc0" data-path="src/pages/HomePage.tsx">
                    {resource.description}
                  </CardDescription>
                </CardHeader>
                <CardContent data-id="2b9au6n4u" data-path="src/pages/HomePage.tsx">
                  <Button
                  variant="outline"
                  className="w-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(resource.path);
                  }} data-id="kp4tyudt2" data-path="src/pages/HomePage.tsx">

                    عرض المحتوى
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </section>

        {/* Call to Action */}
        <div className="text-center mt-16" data-id="4d000hjki" data-path="src/pages/HomePage.tsx">
          <Card className="max-w-2xl mx-auto bg-white/80 backdrop-blur-sm" data-id="3vjnwasa0" data-path="src/pages/HomePage.tsx">
            <CardContent className="p-8" data-id="6zvptn0ws" data-path="src/pages/HomePage.tsx">
              <h3 className="text-2xl font-bold text-gray-800 mb-4" data-id="91jc4o6ta" data-path="src/pages/HomePage.tsx">
                ابدأ رحلتك التعليمية
              </h3>
              <p className="text-gray-600 mb-6" data-id="p7kjrvdc9" data-path="src/pages/HomePage.tsx">
                اختر أي فصل للبدء في تعلم المفاهيم الأساسية للفيزياء الطبية والتصوير الشعاعي
              </p>
              <Button
                size="lg"
                onClick={() => navigate('/learning-objectives')}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700" data-id="7tet3mboe" data-path="src/pages/HomePage.tsx">

                <Target className="h-5 w-5 mr-2" data-id="y5tecly04" data-path="src/pages/HomePage.tsx" />
                عرض أهداف التعلم
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>);

};

export default HomePage;