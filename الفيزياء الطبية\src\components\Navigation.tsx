import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Menu,
  X,
  Home,
  Target,
  Zap,
  Users,
  Settings,
  BarChart3,
  Lightbulb,
  BookOpen,
  FileText,
  HelpCircle,
  User,
  Stethoscope,
  Activity,
  Image,
  CheckCircle,
  Brain } from
'lucide-react';
import { Link, useLocation } from 'react-router-dom';

const Navigation = () => {
  const [isOpen, setIsOpen] = React.useState(false);
  const location = useLocation();

  const navigationItems = [
  { path: '/', name: 'الصفحة الرئيسية', icon: Home },
  { path: '/learning-objectives', name: 'أهداف التعلم', icon: Lightbulb },
  {
    path: '#',
    name: 'الجزء الأول: الأساسيات',
    icon: BookOpen,
    subItems: [
    { path: '/coherent-scattering', name: 'التشتت المترابط', icon: Target },
    { path: '/photoelectric-effect', name: 'التأثير الكهروضوئي', icon: Zap },
    { path: '/compton-scattering', name: 'تشتت كومبتون', icon: Users },
    { path: '/pair-production', name: 'إنتاج الأزواج', icon: Settings },
    { path: '/attenuation-coefficients', name: 'معاملات الإضعاف', icon: BarChart3 },
    { path: '/relative-importance', name: 'الأهمية النسبية', icon: Target }]

  },
  {
    path: '#',
    name: 'الجزء الثاني: النمذجة',
    icon: User,
    subItems: [
    { path: '/chapter9-patient-modeling', name: 'الفصل 9: نمذجة المريض', icon: User },
    { path: '/chapter10-monte-carlo', name: 'الفصل 10: محاكاة مونت كارلو', icon: BarChart3 }]

  },
  {
    path: '#',
    name: 'الجزء الثالث: الكشف',
    icon: Stethoscope,
    subItems: [
    { path: '/chapter11-xray-detection', name: 'الفصل 11: كشف الأشعة السينية', icon: Stethoscope },
    { path: '/chapter12-detector-simulation', name: 'الفصل 12: محاكاة الكاشف', icon: Settings }]

  },
  {
    path: '#',
    name: 'الجزء الرابع: التطبيقات المتقدمة',
    icon: Activity,
    subItems: [
    { path: '/chapter13-simulation-applications', name: 'الفصل 13: تطبيقات المحاكاة', icon: Settings },
    { path: '/chapter14-patient-dose', name: 'الفصل 14: جرعة المريض', icon: Activity },
    { path: '/chapter15-image-quality', name: 'الفصل 15: جودة الصورة', icon: Image },
    { path: '/chapter16-verification', name: 'الفصل 16: التحقق', icon: CheckCircle },
    { path: '/chapter17-future-trends', name: 'الفصل 17: الاتجاهات المستقبلية', icon: Brain }]

  },
  { path: '/key-terms', name: 'المصطلحات الرئيسية', icon: BookOpen },
  { path: '/references', name: 'المراجع', icon: FileText },
  { path: '/problems', name: 'المشكلات والتمارين', icon: HelpCircle }];


  const renderNavigationItem = (item: any, index: number) => {
    const isActive = location.pathname === item.path;
    const Icon = item.icon;

    if (item.subItems) {
      return (
        <div key={index} className="space-y-2" data-id="rbinvw3j1" data-path="src/components/Navigation.tsx">
          <div className="flex items-center gap-3 px-4 py-2 text-gray-700 font-medium" data-id="jo02k92cu" data-path="src/components/Navigation.tsx">
            <Icon className="h-5 w-5" data-id="f4cox7863" data-path="src/components/Navigation.tsx" />
            <span data-id="tufsktqlu" data-path="src/components/Navigation.tsx">{item.name}</span>
          </div>
          <div className="space-y-1 pl-6" data-id="f16zg16ds" data-path="src/components/Navigation.tsx">
            {item.subItems.map((subItem: any, subIndex: number) => {
              const SubIcon = subItem.icon;
              const isSubActive = location.pathname === subItem.path;
              return (
                <Link
                  key={subIndex}
                  to={subItem.path}
                  className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors ${
                  isSubActive ?
                  'bg-blue-500 text-white' :
                  'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`
                  }
                  onClick={() => setIsOpen(false)} data-id="k1f6yl9q5" data-path="src/components/Navigation.tsx">

                  <SubIcon className="h-4 w-4" data-id="uzswuexmk" data-path="src/components/Navigation.tsx" />
                  <span className="text-sm" data-id="wfhsi0kls" data-path="src/components/Navigation.tsx">{subItem.name}</span>
                </Link>);

            })}
          </div>
        </div>);

    }

    return (
      <Link
        key={index}
        to={item.path}
        className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
        isActive ?
        'bg-blue-500 text-white' :
        'text-gray-700 hover:bg-gray-100 hover:text-gray-900'}`
        }
        onClick={() => setIsOpen(false)} data-id="w2femu24g" data-path="src/components/Navigation.tsx">

        <Icon className="h-5 w-5" data-id="2mhbbgd2c" data-path="src/components/Navigation.tsx" />
        <span data-id="u3m2z2bkd" data-path="src/components/Navigation.tsx">{item.name}</span>
      </Link>);

  };

  return (
    <>
      {/* Mobile Navigation Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50" data-id="fccjbd61x" data-path="src/components/Navigation.tsx">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="bg-white/90 backdrop-blur-sm" data-id="84nrowvc2" data-path="src/components/Navigation.tsx">

          {isOpen ? <X className="h-4 w-4" data-id="0hu857s8n" data-path="src/components/Navigation.tsx" /> : <Menu className="h-4 w-4" data-id="uaedgawjv" data-path="src/components/Navigation.tsx" />}
        </Button>
      </div>

      {/* Backdrop */}
      {isOpen &&
      <div
        className="lg:hidden fixed inset-0 bg-black/50 z-40"
        onClick={() => setIsOpen(false)} data-id="w934tzagv" data-path="src/components/Navigation.tsx" />

      }

      {/* Navigation Sidebar */}
      <nav
        className={`fixed top-0 left-0 h-full w-80 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 ${
        isOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`
        } data-id="49zhbid0n" data-path="src/components/Navigation.tsx">

        <div className="flex flex-col h-full" data-id="z1gib7nzh" data-path="src/components/Navigation.tsx">
          {/* Header */}
          <div className="p-6 border-b border-gray-200" data-id="l7n3xbtzl" data-path="src/components/Navigation.tsx">
            <h1 className="text-xl font-bold text-gray-900 text-center" data-id="04a1qpajw" data-path="src/components/Navigation.tsx">
              محاكاة الأشعة السينية
            </h1>
            <p className="text-sm text-gray-600 text-center mt-2" data-id="2s9d9a0c8" data-path="src/components/Navigation.tsx">
              دليل شامل للتصوير الطبي
            </p>
          </div>

          {/* Navigation Items */}
          <div className="flex-1 overflow-y-auto p-4" data-id="lmds3c4ab" data-path="src/components/Navigation.tsx">
            <div className="space-y-2" data-id="lmrykpu5q" data-path="src/components/Navigation.tsx">
              {navigationItems.map((item, index) => renderNavigationItem(item, index))}
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200" data-id="4y8r8n90g" data-path="src/components/Navigation.tsx">
            <p className="text-xs text-gray-500 text-center" data-id="ldbj9ge1z" data-path="src/components/Navigation.tsx">
              تم التطوير لأغراض تعليمية
            </p>
          </div>
        </div>
      </nav>

      {/* Main Content Spacer for Desktop */}
      <div className="hidden lg:block w-80 flex-shrink-0" data-id="we6qpot4x" data-path="src/components/Navigation.tsx" />
    </>);

};

export default Navigation;