import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';
import Navigation from '@/components/Navigation';
import HomePage from '@/pages/HomePage';
import RadiationPhysics from '@/pages/RadiationPhysics';
import ImagingModalities from '@/pages/ImagingModalities';
import DosimetryProtection from '@/pages/DosimetryProtection';
import ImageQuality from '@/pages/ImageQuality';
import Glossary from '@/pages/Glossary';
import About from '@/pages/About';
import NotFound from '@/pages/NotFound';
import './App.css';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient} data-id="0leu5ydw7" data-path="src/App.tsx">
      <TooltipProvider data-id="bzi9bn1ou" data-path="src/App.tsx">
        <Router data-id="0hnqvlwva" data-path="src/App.tsx">
          <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50" data-id="wr13dcz3f" data-path="src/App.tsx">
            <Navigation data-id="awfgyeayc" data-path="src/App.tsx" />
            <main className="pb-8" data-id="0nnj3nnlv" data-path="src/App.tsx">
              <Routes data-id="v8thhxc91" data-path="src/App.tsx">
                <Route path="/" element={<HomePage data-id="9ossijii1" data-path="src/App.tsx" />} data-id="s6cnircpa" data-path="src/App.tsx" />
                <Route path="/radiation-physics" element={<RadiationPhysics data-id="sbus5q9tl" data-path="src/App.tsx" />} data-id="j3caprkgk" data-path="src/App.tsx" />
                <Route path="/imaging-modalities" element={<ImagingModalities data-id="am5lf94x1" data-path="src/App.tsx" />} data-id="l7m5d7tex" data-path="src/App.tsx" />
                <Route path="/dosimetry-protection" element={<DosimetryProtection data-id="a1ila3ys5" data-path="src/App.tsx" />} data-id="84xcyh6x6" data-path="src/App.tsx" />
                <Route path="/image-quality" element={<ImageQuality data-id="c27gbwazr" data-path="src/App.tsx" />} data-id="4ws4a0eex" data-path="src/App.tsx" />
                <Route path="/glossary" element={<Glossary data-id="s6ssd7ma9" data-path="src/App.tsx" />} data-id="lrc9f06i8" data-path="src/App.tsx" />
                <Route path="/about" element={<About data-id="39qycigc2" data-path="src/App.tsx" />} data-id="yyri3r0cp" data-path="src/App.tsx" />
                <Route path="*" element={<NotFound data-id="pjs3aospy" data-path="src/App.tsx" />} data-id="i6tbuip1p" data-path="src/App.tsx" />
              </Routes>
            </main>
            <Toaster data-id="vkw8ec1eu" data-path="src/App.tsx" />
          </div>
        </Router>
      </TooltipProvider>
    </QueryClientProvider>);

}

export default App;