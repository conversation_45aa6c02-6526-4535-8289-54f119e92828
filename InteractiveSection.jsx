import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Button } from '@/components/ui/button.jsx'
import { Input } from '@/components/ui/input.jsx'
import { useState } from 'react'
import { MessageCircle, Brain, HelpCircle, BookOpen, Calculator, Lightbulb } from 'lucide-react'
import AIChat from './AIChat.jsx'

const InteractiveSection = () => {
  const [chatInput, setChatInput] = useState('')
  const [chatMessages, setChatMessages] = useState([
    {
      type: 'ai',
      message: 'مرحباً! أنا مساعدك الذكي لتعلم التصوير الطبي. اسألني أي سؤال حول أجهزة الأشعة السينية!'
    }
  ])
  const [selectedQuiz, setSelectedQuiz] = useState(null)

  const handleSendMessage = () => {
    if (chatInput.trim()) {
      setChatMessages([...chatMessages, 
        { type: 'user', message: chatInput },
        { type: 'ai', message: 'شكراً لسؤالك! هذه ميزة تجريبية. في النسخة الكاملة، سأتمكن من الإجابة على جميع أسئلتك حول التصوير الطبي بالإشعاع المؤين.' }
      ])
      setChatInput('')
    }
  }

  const quizQuestions = [
    {
      id: 1,
      question: 'ما هو المكون المسؤول عن توليد الأشعة السينية في الجهاز؟',
      options: ['الكاشف الرقمي', 'أنبوب الأشعة السينية', 'لوحة التحكم', 'مولد الجهد العالي'],
      correct: 1
    },
    {
      id: 2,
      question: 'ما هي المادة المستخدمة عادة في صنع الأنود؟',
      options: ['الألومنيوم', 'النحاس', 'التنغستن', 'الحديد'],
      correct: 2
    },
    {
      id: 3,
      question: 'ما هو الغرض من الكوليماتور في جهاز الأشعة السينية؟',
      options: ['تبريد الأنبوب', 'تحديد حجم الحزمة', 'رفع الجهد', 'تحويل الإشارة'],
      correct: 1
    }
  ]

  const interactiveFeatures = [
    {
      icon: MessageCircle,
      title: 'مساعد ذكي',
      description: 'اسأل أي سؤال واحصل على إجابات فورية',
      color: 'blue'
    },
    {
      icon: Brain,
      title: 'تعلم تكيفي',
      description: 'محتوى مخصص حسب مستوى فهمك',
      color: 'purple'
    },
    {
      icon: Calculator,
      title: 'حاسبات تفاعلية',
      description: 'احسب معاملات التصوير والجرعات',
      color: 'green'
    },
    {
      icon: Lightbulb,
      title: 'محاكاة تفاعلية',
      description: 'جرب تشغيل الأجهزة افتراضياً',
      color: 'yellow'
    }
  ]

  return (
    <section id="interactive" className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">التعلم التفاعلي بالذكاء الاصطناعي</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            استخدم تقنيات الذكاء الاصطناعي المتقدمة للحصول على تجربة تعليمية مخصصة وتفاعلية
          </p>
        </div>

        {/* Interactive Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {interactiveFeatures.map((feature, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <div className={`bg-${feature.color}-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <feature.icon className={`text-${feature.color}-600`} size={32} />
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        <AIChat />

        {/* Additional Interactive Tools */}
        <div className="mt-12 grid md:grid-cols-3 gap-6">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calculator className="mr-2 text-green-600" size={20} />
                حاسبة الجرعة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">احسب الجرعة الإشعاعية المطلوبة</p>
              <Button className="w-full">افتح الحاسبة</Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="mr-2 text-blue-600" size={20} />
                دليل المراجع
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">مراجع علمية ومصادر إضافية</p>
              <Button className="w-full" variant="outline">تصفح المراجع</Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="mr-2 text-yellow-600" size={20} />
                محاكي الجهاز
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">جرب تشغيل جهاز الأشعة افتراضياً</p>
              <Button className="w-full" variant="outline">بدء المحاكاة</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}

export default InteractiveSection

