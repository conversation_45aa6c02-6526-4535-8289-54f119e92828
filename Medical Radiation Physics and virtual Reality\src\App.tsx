import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';
import HomePage from '@/pages/HomePage';
import CoursePage from '@/pages/CoursePage';
import NotFound from '@/pages/NotFound';
import './App.css';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient} data-id="c9usy460i" data-path="src/App.tsx">
      <TooltipProvider data-id="7yyns6u6j" data-path="src/App.tsx">
        <Router data-id="tcyp55tqk" data-path="src/App.tsx">
          <div className="min-h-screen" data-id="zbp7w92fg" data-path="src/App.tsx">
            <Routes data-id="10kdhfvg5" data-path="src/App.tsx">
              <Route path="/" element={<HomePage data-id="eisjvzfh4" data-path="src/App.tsx" />} data-id="wpy8gktoi" data-path="src/App.tsx" />
              <Route path="/course" element={<CoursePage data-id="wp4yxc1r4" data-path="src/App.tsx" />} data-id="u6l25ttoh" data-path="src/App.tsx" />
              <Route path="*" element={<NotFound data-id="d6c92i44o" data-path="src/App.tsx" />} data-id="5909p5yur" data-path="src/App.tsx" />
            </Routes>
          </div>
          <Toaster data-id="wv64sc9l3" data-path="src/App.tsx" />
        </Router>
      </TooltipProvider>
    </QueryClientProvider>);

}

export default App;