import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, RotateCcw, Info } from 'lucide-react';

const ArtifactSimulator = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const cleanCanvasRef = useRef<HTMLCanvasElement>(null);

  const [artifactType, setArtifactType] = useState('motion');
  const [severity, setSeverity] = useState([30]);
  const [imageData, setImageData] = useState(null);

  const artifacts = {
    motion: {
      name: 'Motion Artifact',
      description: 'Blurring and ghosting caused by patient movement',
      causes: ['Patient movement', 'Organ motion', 'Cardiac motion', 'Respiration'],
      solutions: ['Immobilization', 'Faster sequences', 'Motion compensation', 'Breath-holding']
    },
    metal: {
      name: 'Metal Artifact',
      description: 'Streaking and beam hardening from metallic objects',
      causes: ['Implants', 'Dental fillings', 'Surgical clips', 'Jewelry'],
      solutions: ['Metal artifact reduction', 'Iterative reconstruction', 'Dual-energy CT', 'Alternative positioning']
    },
    beamHardening: {
      name: 'Beam Hardening',
      description: 'Dark bands and cupping artifacts from polychromatic beam',
      causes: ['Polychromatic X-ray beam', 'Dense objects', 'Thick body parts'],
      solutions: ['Beam filtration', 'Calibration correction', 'Iterative reconstruction', 'Dual-energy techniques']
    },
    truncation: {
      name: 'Truncation Artifact',
      description: 'Ringing artifacts from insufficient sampling or FOV',
      causes: ['Small field of view', 'Insufficient data', 'Sharp edges', 'Inadequate sampling'],
      solutions: ['Larger FOV', 'Higher resolution', 'Apodization filters', 'Padding techniques']
    },
    partial: {
      name: 'Partial Volume',
      description: 'Averaging effects at tissue boundaries',
      causes: ['Thick slices', 'Oblique structures', 'Small lesions', 'Mixed tissues'],
      solutions: ['Thinner slices', 'Higher resolution', 'Multiplanar reconstruction', 'Isotropic voxels']
    }
  };

  useEffect(() => {
    generateCleanImage();
  }, []);

  useEffect(() => {
    if (imageData) {
      simulateArtifact();
    }
  }, [artifactType, severity, imageData]);

  const generateCleanImage = () => {
    const canvas = cleanCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;
    const imgData = ctx.createImageData(width, height);
    const data = imgData.data;

    // Create a synthetic medical image with various structures
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4;

        const centerX = width / 2;
        const centerY = height / 2;

        let intensity = 40; // Background

        // Create body outline
        const bodyDist = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
        if (bodyDist < 100) {
          intensity = 120; // Soft tissue
        }

        // Add spine
        if (Math.abs(x - centerX) < 8 && Math.abs(y - centerY) < 80) {
          intensity = 200; // Bone
        }

        // Add ribs
        for (let i = -60; i <= 60; i += 20) {
          const ribX = centerX + Math.cos(i * Math.PI / 180) * 70;
          const ribY = centerY + Math.sin(i * Math.PI / 180) * 70;
          if (Math.sqrt((x - ribX) ** 2 + (y - ribY) ** 2) < 4) {
            intensity = 180; // Rib
          }
        }

        // Add organs
        const heart = Math.sqrt((x - centerX + 20) ** 2 + (y - centerY) ** 2);
        if (heart < 25) {
          intensity = 140; // Heart
        }

        // Add metal implant for metal artifact simulation
        if (artifactType === 'metal') {
          const implant = Math.sqrt((x - centerX + 40) ** 2 + (y - centerY + 30) ** 2);
          if (implant < 8) {
            intensity = 255; // Metal
          }
        }

        // Add texture
        intensity += Math.random() * 15 - 7.5;
        intensity = Math.max(0, Math.min(255, intensity));

        data[idx] = intensity;
        data[idx + 1] = intensity;
        data[idx + 2] = intensity;
        data[idx + 3] = 255;
      }
    }

    ctx.putImageData(imgData, 0, 0);
    setImageData(imgData);
  };

  const simulateArtifact = () => {
    const canvas = canvasRef.current;
    const cleanCanvas = cleanCanvasRef.current;
    if (!canvas || !cleanCanvas || !imageData) return;

    const ctx = canvas.getContext('2d');
    const cleanCtx = cleanCanvas.getContext('2d');
    if (!ctx || !cleanCtx) return;

    const width = canvas.width;
    const height = canvas.height;
    const cleanData = cleanCtx.getImageData(0, 0, width, height);
    const artifactData = ctx.createImageData(width, height);

    // Copy clean image
    for (let i = 0; i < cleanData.data.length; i++) {
      artifactData.data[i] = cleanData.data[i];
    }

    const intensityFactor = severity[0] / 100;

    switch (artifactType) {
      case 'motion':
        applyMotionArtifact(artifactData, intensityFactor);
        break;
      case 'metal':
        applyMetalArtifact(artifactData, intensityFactor);
        break;
      case 'beamHardening':
        applyBeamHardening(artifactData, intensityFactor);
        break;
      case 'truncation':
        applyTruncationArtifact(artifactData, intensityFactor);
        break;
      case 'partial':
        applyPartialVolumeArtifact(artifactData, intensityFactor);
        break;
    }

    ctx.putImageData(artifactData, 0, 0);
  };

  const applyMotionArtifact = (imageData, intensity) => {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const motionBlur = Math.floor(intensity * 10);

    // Create motion blur by averaging with shifted versions
    const originalData = new Uint8ClampedArray(data);

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let sum = 0;
        let count = 0;

        for (let dx = -motionBlur; dx <= motionBlur; dx += 2) {
          const newX = x + dx;
          if (newX >= 0 && newX < width) {
            const idx = (y * width + newX) * 4;
            sum += originalData[idx];
            count++;
          }
        }

        const avgIntensity = sum / count;
        const idx = (y * width + x) * 4;
        data[idx] = avgIntensity;
        data[idx + 1] = avgIntensity;
        data[idx + 2] = avgIntensity;
      }
    }
  };

  const applyMetalArtifact = (imageData, intensity) => {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const centerX = width / 2;
    const centerY = height / 2;
    const metalX = centerX - 40;
    const metalY = centerY - 30;

    // Create streaking artifacts
    for (let angle = 0; angle < 360; angle += 10) {
      const rad = angle * Math.PI / 180;
      const dx = Math.cos(rad);
      const dy = Math.sin(rad);

      for (let dist = 0; dist < 120; dist += 2) {
        const x = Math.round(metalX + dx * dist);
        const y = Math.round(metalY + dy * dist);

        if (x >= 0 && x < width && y >= 0 && y < height) {
          const idx = (y * width + x) * 4;
          const streakIntensity = Math.max(0, 100 - dist) * intensity * 0.5;

          // Create dark streaks
          data[idx] = Math.max(0, data[idx] - streakIntensity);
          data[idx + 1] = Math.max(0, data[idx + 1] - streakIntensity);
          data[idx + 2] = Math.max(0, data[idx + 2] - streakIntensity);
        }
      }
    }
  };

  const applyBeamHardening = (imageData, intensity) => {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const centerX = width / 2;
    const centerY = height / 2;

    // Create cupping artifact (center brighter, edges darker)
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4;
        const distFromCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
        const maxDist = Math.sqrt(centerX ** 2 + centerY ** 2);
        const normalizedDist = distFromCenter / maxDist;

        // Cupping effect: reduce intensity towards edges
        const cuppingFactor = 1 - normalizedDist * intensity * 0.3;

        data[idx] *= cuppingFactor;
        data[idx + 1] *= cuppingFactor;
        data[idx + 2] *= cuppingFactor;

        // Add horizontal streaks
        if (y % 20 < 2) {
          const streakReduction = intensity * 0.2;
          data[idx] = Math.max(0, data[idx] - streakReduction * 50);
          data[idx + 1] = Math.max(0, data[idx + 1] - streakReduction * 50);
          data[idx + 2] = Math.max(0, data[idx + 2] - streakReduction * 50);
        }
      }
    }
  };

  const applyTruncationArtifact = (imageData, intensity) => {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;

    // Create ringing artifacts near edges
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4;

        // Find edges by checking neighboring pixels
        let isEdge = false;
        if (x > 0 && x < width - 1) {
          const leftIdx = (y * width + (x - 1)) * 4;
          const rightIdx = (y * width + (x + 1)) * 4;
          const diff = Math.abs(data[leftIdx] - data[rightIdx]);
          if (diff > 50) isEdge = true;
        }

        if (isEdge) {
          // Add oscillating pattern near edges
          const oscillation = Math.sin(x * 0.5) * intensity * 0.3;
          data[idx] = Math.max(0, Math.min(255, data[idx] + oscillation * 20));
          data[idx + 1] = Math.max(0, Math.min(255, data[idx + 1] + oscillation * 20));
          data[idx + 2] = Math.max(0, Math.min(255, data[idx + 2] + oscillation * 20));
        }
      }
    }
  };

  const applyPartialVolumeArtifact = (imageData, intensity) => {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;

    // Blur boundaries between different tissues
    const blurRadius = Math.floor(intensity * 3);
    const originalData = new Uint8ClampedArray(data);

    for (let y = blurRadius; y < height - blurRadius; y++) {
      for (let x = blurRadius; x < width - blurRadius; x++) {
        let sum = 0;
        let count = 0;

        // Check if we're at a boundary
        const centerIdx = (y * width + x) * 4;
        let isBoundary = false;

        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const checkIdx = ((y + dy) * width + (x + dx)) * 4;
            if (Math.abs(originalData[centerIdx] - originalData[checkIdx]) > 30) {
              isBoundary = true;
              break;
            }
          }
          if (isBoundary) break;
        }

        if (isBoundary) {
          // Apply averaging at boundaries
          for (let dy = -blurRadius; dy <= blurRadius; dy++) {
            for (let dx = -blurRadius; dx <= blurRadius; dx++) {
              const checkIdx = ((y + dy) * width + (x + dx)) * 4;
              sum += originalData[checkIdx];
              count++;
            }
          }

          const avgIntensity = sum / count;
          data[centerIdx] = avgIntensity;
          data[centerIdx + 1] = avgIntensity;
          data[centerIdx + 2] = avgIntensity;
        }
      }
    }
  };

  const resetArtifact = () => {
    setSeverity([0]);
    setArtifactType('motion');
  };

  const currentArtifact = artifacts[artifactType];

  return (
    <div className="space-y-8" data-id="fyw6ngh8c" data-path="src/components/ArtifactSimulator.tsx">
      {/* Image Display */}
      <div className="grid md:grid-cols-2 gap-6" data-id="yufunxh0b" data-path="src/components/ArtifactSimulator.tsx">
        <Card data-id="bluo5hfit" data-path="src/components/ArtifactSimulator.tsx">
          <CardHeader data-id="6emomiliz" data-path="src/components/ArtifactSimulator.tsx">
            <CardTitle data-id="jwun14dr1" data-path="src/components/ArtifactSimulator.tsx">Clean Image</CardTitle>
            <CardDescription data-id="axbxbukzt" data-path="src/components/ArtifactSimulator.tsx">Original image without artifacts</CardDescription>
          </CardHeader>
          <CardContent data-id="wtqqf2zgc" data-path="src/components/ArtifactSimulator.tsx">
            <canvas
              ref={cleanCanvasRef}
              width={300}
              height={300}
              className="w-full border rounded-lg bg-black" data-id="knt2abgq6" data-path="src/components/ArtifactSimulator.tsx" />

          </CardContent>
        </Card>

        <Card data-id="hcv7sblwc" data-path="src/components/ArtifactSimulator.tsx">
          <CardHeader data-id="996kr5apw" data-path="src/components/ArtifactSimulator.tsx">
            <CardTitle className="flex items-center justify-between" data-id="h782l6i6k" data-path="src/components/ArtifactSimulator.tsx">
              With Artifacts
              <Button size="sm" variant="outline" onClick={resetArtifact} data-id="mboiail9i" data-path="src/components/ArtifactSimulator.tsx">
                <RotateCcw className="w-4 h-4" data-id="903t9vim7" data-path="src/components/ArtifactSimulator.tsx" />
              </Button>
            </CardTitle>
            <CardDescription data-id="u91cil185" data-path="src/components/ArtifactSimulator.tsx">Image showing {currentArtifact.name.toLowerCase()}</CardDescription>
          </CardHeader>
          <CardContent data-id="c2xjay4wf" data-path="src/components/ArtifactSimulator.tsx">
            <canvas
              ref={canvasRef}
              width={300}
              height={300}
              className="w-full border rounded-lg bg-black" data-id="mcyluw8sv" data-path="src/components/ArtifactSimulator.tsx" />

          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="grid md:grid-cols-2 gap-8" data-id="n6hvajxwr" data-path="src/components/ArtifactSimulator.tsx">
        <Card data-id="x6a3vkcan" data-path="src/components/ArtifactSimulator.tsx">
          <CardHeader data-id="2f3aksjnl" data-path="src/components/ArtifactSimulator.tsx">
            <CardTitle className="flex items-center gap-2" data-id="zo8j0ep9u" data-path="src/components/ArtifactSimulator.tsx">
              <AlertTriangle className="w-5 h-5" data-id="0n73w0zn6" data-path="src/components/ArtifactSimulator.tsx" />
              Artifact Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4" data-id="6mdclmkab" data-path="src/components/ArtifactSimulator.tsx">
            <div data-id="eycgyu4vo" data-path="src/components/ArtifactSimulator.tsx">
              <Label className="text-sm font-medium" data-id="60w4q3sma" data-path="src/components/ArtifactSimulator.tsx">Artifact Type</Label>
              <Select value={artifactType} onValueChange={setArtifactType} data-id="g0qk9tgau" data-path="src/components/ArtifactSimulator.tsx">
                <SelectTrigger data-id="2zfoavueq" data-path="src/components/ArtifactSimulator.tsx">
                  <SelectValue data-id="gfabpofwb" data-path="src/components/ArtifactSimulator.tsx" />
                </SelectTrigger>
                <SelectContent data-id="jn1xpbzkn" data-path="src/components/ArtifactSimulator.tsx">
                  {Object.entries(artifacts).map(([key, artifact]) =>
                  <SelectItem key={key} value={key} data-id="e7tvvl21x" data-path="src/components/ArtifactSimulator.tsx">{artifact.name}</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div data-id="n2si1t0ah" data-path="src/components/ArtifactSimulator.tsx">
              <Label className="text-sm font-medium" data-id="81mwd2ppi" data-path="src/components/ArtifactSimulator.tsx">Severity</Label>
              <Slider
                value={severity}
                onValueChange={setSeverity}
                max={100}
                min={0}
                step={5}
                className="mt-2" data-id="mtitw5icf" data-path="src/components/ArtifactSimulator.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="r2gp2lznc" data-path="src/components/ArtifactSimulator.tsx">
                <span data-id="tztkqyxgw" data-path="src/components/ArtifactSimulator.tsx">None</span>
                <span className="font-medium" data-id="xxvx1m2ln" data-path="src/components/ArtifactSimulator.tsx">{severity[0]}%</span>
                <span data-id="vmgovmizu" data-path="src/components/ArtifactSimulator.tsx">Severe</span>
              </div>
            </div>

            <div className="pt-4" data-id="2iz9dy68h" data-path="src/components/ArtifactSimulator.tsx">
              <Badge variant="outline" className="mb-2" data-id="uxjqqlkga" data-path="src/components/ArtifactSimulator.tsx">
                Current: {currentArtifact.name}
              </Badge>
              <p className="text-sm text-gray-600" data-id="r72mqx8w4" data-path="src/components/ArtifactSimulator.tsx">
                {currentArtifact.description}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card data-id="825lyg7g9" data-path="src/components/ArtifactSimulator.tsx">
          <CardHeader data-id="ax4867vk2" data-path="src/components/ArtifactSimulator.tsx">
            <CardTitle className="flex items-center gap-2" data-id="jmqb8xn5u" data-path="src/components/ArtifactSimulator.tsx">
              <Info className="w-5 h-5" data-id="jwis7wb99" data-path="src/components/ArtifactSimulator.tsx" />
              Artifact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4" data-id="pyedl4nup" data-path="src/components/ArtifactSimulator.tsx">
            <div data-id="gupzbbkm0" data-path="src/components/ArtifactSimulator.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="u1g3ei3h1" data-path="src/components/ArtifactSimulator.tsx">Common Causes</h4>
              <ul className="text-sm text-gray-600 space-y-1" data-id="93c9uznd0" data-path="src/components/ArtifactSimulator.tsx">
                {currentArtifact.causes.map((cause, index) =>
                <li key={index} data-id="cbn6fsqew" data-path="src/components/ArtifactSimulator.tsx">• {cause}</li>
                )}
              </ul>
            </div>

            <div data-id="j2skf2ijh" data-path="src/components/ArtifactSimulator.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="sjgnaijcb" data-path="src/components/ArtifactSimulator.tsx">Mitigation Strategies</h4>
              <ul className="text-sm text-gray-600 space-y-1" data-id="cfi1can93" data-path="src/components/ArtifactSimulator.tsx">
                {currentArtifact.solutions.map((solution, index) =>
                <li key={index} data-id="lc0tg08wv" data-path="src/components/ArtifactSimulator.tsx">• {solution}</li>
                )}
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Artifact Comparison */}
      <Card data-id="7mrf7zr86" data-path="src/components/ArtifactSimulator.tsx">
        <CardHeader data-id="uyydgjex6" data-path="src/components/ArtifactSimulator.tsx">
          <CardTitle data-id="k5hrr4tch" data-path="src/components/ArtifactSimulator.tsx">Artifact Comparison</CardTitle>
          <CardDescription data-id="gp3k9ts0e" data-path="src/components/ArtifactSimulator.tsx">
            Overview of different artifact types and their characteristics
          </CardDescription>
        </CardHeader>
        <CardContent data-id="z2ooi4yjc" data-path="src/components/ArtifactSimulator.tsx">
          <div className="overflow-x-auto" data-id="nn7whdvw9" data-path="src/components/ArtifactSimulator.tsx">
            <table className="w-full text-sm" data-id="n5kfn3hp2" data-path="src/components/ArtifactSimulator.tsx">
              <thead data-id="712xzgdtd" data-path="src/components/ArtifactSimulator.tsx">
                <tr className="border-b" data-id="afz4jr4so" data-path="src/components/ArtifactSimulator.tsx">
                  <th className="text-left p-3" data-id="es28y16j9" data-path="src/components/ArtifactSimulator.tsx">Artifact Type</th>
                  <th className="text-left p-3" data-id="29rskh2vy" data-path="src/components/ArtifactSimulator.tsx">Appearance</th>
                  <th className="text-left p-3" data-id="ka5dnu7lx" data-path="src/components/ArtifactSimulator.tsx">Primary Cause</th>
                  <th className="text-left p-3" data-id="uhxor4f0m" data-path="src/components/ArtifactSimulator.tsx">Impact</th>
                  <th className="text-left p-3" data-id="76xtso0uj" data-path="src/components/ArtifactSimulator.tsx">Prevention</th>
                </tr>
              </thead>
              <tbody data-id="z7c94czem" data-path="src/components/ArtifactSimulator.tsx">
                <tr className="border-b" data-id="e29syur8s" data-path="src/components/ArtifactSimulator.tsx">
                  <td className="p-3 font-medium" data-id="jfc724w5v" data-path="src/components/ArtifactSimulator.tsx">Motion</td>
                  <td className="p-3" data-id="qn2c5o6re" data-path="src/components/ArtifactSimulator.tsx">Blurring, ghosting</td>
                  <td className="p-3" data-id="wjngzzl8i" data-path="src/components/ArtifactSimulator.tsx">Patient movement</td>
                  <td className="p-3" data-id="uz9sbv0g1" data-path="src/components/ArtifactSimulator.tsx">Reduced detail</td>
                  <td className="p-3" data-id="f7wpd0dcw" data-path="src/components/ArtifactSimulator.tsx">Immobilization</td>
                </tr>
                <tr className="border-b" data-id="5ia7jzbjn" data-path="src/components/ArtifactSimulator.tsx">
                  <td className="p-3 font-medium" data-id="g5t3w38su" data-path="src/components/ArtifactSimulator.tsx">Metal</td>
                  <td className="p-3" data-id="te0w8yto0" data-path="src/components/ArtifactSimulator.tsx">Dark streaks</td>
                  <td className="p-3" data-id="akifvmuqu" data-path="src/components/ArtifactSimulator.tsx">High-Z materials</td>
                  <td className="p-3" data-id="n07ioab8a" data-path="src/components/ArtifactSimulator.tsx">Obscured anatomy</td>
                  <td className="p-3" data-id="4t6q3yy6h" data-path="src/components/ArtifactSimulator.tsx">MAR algorithms</td>
                </tr>
                <tr className="border-b" data-id="v7jslhfq4" data-path="src/components/ArtifactSimulator.tsx">
                  <td className="p-3 font-medium" data-id="rib8m4gfo" data-path="src/components/ArtifactSimulator.tsx">Beam Hardening</td>
                  <td className="p-3" data-id="696r7a5vo" data-path="src/components/ArtifactSimulator.tsx">Cupping, streaks</td>
                  <td className="p-3" data-id="ewjxlmb8q" data-path="src/components/ArtifactSimulator.tsx">Polychromatic beam</td>
                  <td className="p-3" data-id="btvqjbw9e" data-path="src/components/ArtifactSimulator.tsx">Non-uniform appearance</td>
                  <td className="p-3" data-id="4hvtgg2n1" data-path="src/components/ArtifactSimulator.tsx">Filtration, correction</td>
                </tr>
                <tr className="border-b" data-id="s9dituq5r" data-path="src/components/ArtifactSimulator.tsx">
                  <td className="p-3 font-medium" data-id="7zkmxvxhj" data-path="src/components/ArtifactSimulator.tsx">Truncation</td>
                  <td className="p-3" data-id="ktit2r62l" data-path="src/components/ArtifactSimulator.tsx">Ringing patterns</td>
                  <td className="p-3" data-id="nmb6xkbuj" data-path="src/components/ArtifactSimulator.tsx">Limited sampling</td>
                  <td className="p-3" data-id="bh4wrpzxc" data-path="src/components/ArtifactSimulator.tsx">False structures</td>
                  <td className="p-3" data-id="adzumohw4" data-path="src/components/ArtifactSimulator.tsx">Larger FOV</td>
                </tr>
                <tr data-id="0kozvdqzr" data-path="src/components/ArtifactSimulator.tsx">
                  <td className="p-3 font-medium" data-id="8ujzxjadg" data-path="src/components/ArtifactSimulator.tsx">Partial Volume</td>
                  <td className="p-3" data-id="zvvqxnrk9" data-path="src/components/ArtifactSimulator.tsx">Blurred boundaries</td>
                  <td className="p-3" data-id="ro8hmonhd" data-path="src/components/ArtifactSimulator.tsx">Thick slices</td>
                  <td className="p-3" data-id="961gxc50r" data-path="src/components/ArtifactSimulator.tsx">Loss of detail</td>
                  <td className="p-3" data-id="xx2ck1ujd" data-path="src/components/ArtifactSimulator.tsx">Thin sections</td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Clinical Impact */}
      <div className="grid md:grid-cols-2 gap-8" data-id="zyqj0kqh9" data-path="src/components/ArtifactSimulator.tsx">
        <Card data-id="nmjdl3x41" data-path="src/components/ArtifactSimulator.tsx">
          <CardHeader data-id="v0mgwrgop" data-path="src/components/ArtifactSimulator.tsx">
            <CardTitle data-id="5wxnxdpo2" data-path="src/components/ArtifactSimulator.tsx">Clinical Impact Assessment</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4" data-id="exwne9qmi" data-path="src/components/ArtifactSimulator.tsx">
            <div data-id="8zsxt9rng" data-path="src/components/ArtifactSimulator.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="kptzcz469" data-path="src/components/ArtifactSimulator.tsx">Diagnostic Confidence</h4>
              <div className="space-y-2" data-id="dim92a81r" data-path="src/components/ArtifactSimulator.tsx">
                {['Excellent', 'Good', 'Fair', 'Poor'].map((level, index) =>
                <div key={level} className="flex justify-between items-center" data-id="esdq486wc" data-path="src/components/ArtifactSimulator.tsx">
                    <span className="text-sm" data-id="xxwvwe2dh" data-path="src/components/ArtifactSimulator.tsx">{level}:</span>
                    <div className={`h-2 w-16 rounded ${
                  index === 0 ? 'bg-green-500' :
                  index === 1 ? 'bg-yellow-500' :
                  index === 2 ? 'bg-orange-500' : 'bg-red-500'} ${
                  severity[0] > (3 - index) * 25 ? 'opacity-100' : 'opacity-30'}`} data-id="iq3nkqtjs" data-path="src/components/ArtifactSimulator.tsx"></div>
                  </div>
                )}
              </div>
            </div>

            <div data-id="ofr0q5iuh" data-path="src/components/ArtifactSimulator.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="xbnipxter" data-path="src/components/ArtifactSimulator.tsx">Recommended Actions</h4>
              <div className="text-sm text-gray-600" data-id="5o8rtx6pu" data-path="src/components/ArtifactSimulator.tsx">
                {severity[0] < 25 && <p className="text-green-600" data-id="e6q39kuyz" data-path="src/components/ArtifactSimulator.tsx">✓ Acceptable for diagnosis</p>}
                {severity[0] >= 25 && severity[0] < 50 && <p className="text-yellow-600" data-id="4ga4jw6vh" data-path="src/components/ArtifactSimulator.tsx">⚠ Consider repeat if critical</p>}
                {severity[0] >= 50 && severity[0] < 75 && <p className="text-orange-600" data-id="7e5438c3s" data-path="src/components/ArtifactSimulator.tsx">⚠ Repeat recommended</p>}
                {severity[0] >= 75 && <p className="text-red-600" data-id="wcnw6kbsl" data-path="src/components/ArtifactSimulator.tsx">✗ Not suitable for diagnosis</p>}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="97mrvds6m" data-path="src/components/ArtifactSimulator.tsx">
          <CardHeader data-id="necpaj6hr" data-path="src/components/ArtifactSimulator.tsx">
            <CardTitle data-id="29dixy1lw" data-path="src/components/ArtifactSimulator.tsx">Quality Improvement</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4" data-id="frpnzw8ga" data-path="src/components/ArtifactSimulator.tsx">
            <div data-id="5vsz9bre9" data-path="src/components/ArtifactSimulator.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="h7qf3z3qr" data-path="src/components/ArtifactSimulator.tsx">Workflow Optimization</h4>
              <ul className="text-sm text-gray-600 space-y-1" data-id="qt50r92uv" data-path="src/components/ArtifactSimulator.tsx">
                <li data-id="1bmcywvfw" data-path="src/components/ArtifactSimulator.tsx">• Pre-scan patient preparation</li>
                <li data-id="1w6y84eiy" data-path="src/components/ArtifactSimulator.tsx">• Real-time motion monitoring</li>
                <li data-id="ivot0tb0j" data-path="src/components/ArtifactSimulator.tsx">• Automated artifact detection</li>
                <li data-id="f6a5ahl58" data-path="src/components/ArtifactSimulator.tsx">• Post-processing protocols</li>
              </ul>
            </div>

            <div data-id="gxeeal9n3" data-path="src/components/ArtifactSimulator.tsx">
              <h4 className="font-semibold text-gray-900 mb-2" data-id="6jdkpeoz4" data-path="src/components/ArtifactSimulator.tsx">Technology Solutions</h4>
              <ul className="text-sm text-gray-600 space-y-1" data-id="q3oc4z6nr" data-path="src/components/ArtifactSimulator.tsx">
                <li data-id="qpqwn9q5k" data-path="src/components/ArtifactSimulator.tsx">• Advanced reconstruction algorithms</li>
                <li data-id="48d6c6kiq" data-path="src/components/ArtifactSimulator.tsx">• AI-powered artifact reduction</li>
                <li data-id="2un4z9505" data-path="src/components/ArtifactSimulator.tsx">• Real-time image quality assessment</li>
                <li data-id="gn1kqi4j6" data-path="src/components/ArtifactSimulator.tsx">• Adaptive scanning protocols</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default ArtifactSimulator;