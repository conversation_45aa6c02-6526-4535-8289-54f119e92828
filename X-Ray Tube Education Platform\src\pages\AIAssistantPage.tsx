import { useState, useRef, useEffect } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Brain,
  Send,
  Bot,
  User,
  Lightbulb,
  HelpCircle,
  MessageSquare } from
'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const AIAssistantPage = () => {
  const { t, language } = useLanguage();
  const [messages, setMessages] = useState<Message[]>([
  {
    id: '1',
    type: 'assistant',
    content: t('ai.welcome'),
    timestamp: new Date()
  }]
  );
  const [input, setInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const quickQuestions = language === 'en' ? [
  'How does an X-ray tube work?',
  'What is ionizing radiation?',
  'How to protect from radiation?',
  'What are circuit components?',
  'How are X-rays produced?',
  'What is the radiation energy equation?'] :
  [
  'كيف يعمل أنبوب الأشعة السينية؟',
  'ما هو الإشعاع المؤين؟',
  'كيف تتم الحماية من الإشعاع؟',
  'ما هي مكونات الدائرة الكهربائية؟',
  'كيف يتم إنتاج الأشعة السينية؟',
  'ما هي معادلة الطاقة الإشعاعية؟'];


  const knowledgeBase = language === 'en' ? {
    'X-ray tube': {
      keywords: ['tube', 'xray', 'x-ray', 'cathode', 'anode', 'filament'],
      response: `X-ray tube consists of main components:

**Cathode:**
- Heated tungsten filament
- Produces electrons through thermionic emission
- Operating temperature: 2000-2500°C

**Anode:**
- Tungsten or molybdenum target
- Receives accelerated electrons
- Target angle: 12-17 degrees

**Operation:**
1. Heat filament to produce electrons
2. Accelerate electrons with high voltage (40-150kV)
3. Electrons strike the anode
4. X-ray production (1%) and heat (99%)`
    },
    'Ionizing radiation': {
      keywords: ['radiation', 'ionizing', 'ion', 'energy', 'photon'],
      response: `Ionizing radiation is energy capable of removing electrons from atoms:

**Characteristics:**
- High energy (>12.4 eV)
- Penetration capability
- Interaction with biological tissues
- Measurable and detectable

**Types:**
- X-rays
- Gamma rays
- Alpha and beta particles

**Medical Applications:**
- Diagnostic imaging
- Radiation therapy
- Nuclear medicine`
    },
    'Radiation protection': {
      keywords: ['protection', 'safety', 'dose', 'alara', 'shield'],
      response: `Three principles of radiation protection:

**1. Justification:**
- Benefits outweigh risks
- Medical necessity for examination
- Clinical indication assessment

**2. Optimization (ALARA):**
- As Low As Reasonably Achievable
- Minimize dose to lowest level
- Use appropriate techniques

**3. Dose Limits:**
- Workers: 20 mSv/year
- Public: 1 mSv/year
- Monitor cumulative doses

**Protection Methods:**
- Time: Reduce exposure time
- Distance: Increase distance from source
- Shielding: Use lead and protective materials`
    }
  } : {
    'أنبوب الأشعة السينية': {
      keywords: ['أنبوب', 'اشعة', 'سينية', 'كاثود', 'انود', 'فتيل'],
      response: `أنبوب الأشعة السينية يتكون من مكونات رئيسية:

**الكاثود (المهبط):**
- فتيل التنجستن المُسخن
- ينتج الإلكترونات بالانبعاث الحراري
- درجة حرارة التشغيل: 2000-2500°C

**الأنود (المصعد):**
- هدف من التنجستن أو الموليبدينوم
- يستقبل الإلكترونات المتسارعة
- زاوية الهدف: 12-17 درجة

**آلية العمل:**
1. تسخين الفتيل لإنتاج الإلكترونات
2. تسريع الإلكترونات بجهد عالي (40-150kV)
3. اصطدام الإلكترونات بالأنود
4. إنتاج الأشعة السينية (1%) والحرارة (99%)`
    },
    'الإشعاع المؤين': {
      keywords: ['اشعاع', 'مؤين', 'تأين', 'طاقة', 'فوتون'],
      response: `الإشعاع المؤين هو نوع من الطاقة له القدرة على إزالة الإلكترونات من الذرات:

**الخصائص:**
- طاقة عالية (>12.4 eV)
- قدرة على اختراق المواد
- تفاعل مع الأنسجة البيولوجية
- إمكانية القياس والكشف

**الأنواع:**
- الأشعة السينية (X-rays)
- أشعة جاما (Gamma rays)
- جسيمات ألفا وبيتا

**التطبيقات الطبية:**
- التصوير التشخيصي
- العلاج الإشعاعي
- الطب النووي`
    },
    'الحماية الإشعاعية': {
      keywords: ['حماية', 'وقاية', 'سلامة', 'جرعة', 'alara'],
      response: `مبادئ الحماية الإشعاعية الثلاثة:

**1. التبرير (Justification):**
- الفوائد أكبر من المخاطر
- ضرورة طبية للفحص
- تقييم المؤشرات الطبية

**2. التحسين (Optimization) - مبدأ ALARA:**
- As Low As Reasonably Achievable
- تقليل الجرعة للحد الأدنى
- استخدام التقنيات المناسبة

**3. حدود الجرعة (Dose Limits):**
- حدود للعاملين: 20 mSv/سنة
- حدود للجمهور: 1 mSv/سنة
- مراقبة الجرعات المتراكمة

**وسائل الحماية:**
- الزمن: تقليل وقت التعرض
- المسافة: زيادة المسافة من المصدر
- الحاجز: استخدام الرصاص والمواد الواقية`
    }
  };

  const generateResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    // Search knowledge base
    for (const [topic, data] of Object.entries(knowledgeBase)) {
      if (data.keywords.some((keyword) => input.includes(keyword))) {
        return data.response;
      }
    }

    // General responses
    if (input.includes('thank') || input.includes('شكر')) {
      return language === 'en' ?
      'You\'re welcome! Happy to help. Do you have any other questions about medical imaging?' :
      'عفواً! سعيد لمساعدتك. هل لديك أسئلة أخرى حول التصوير الطبي؟';
    }

    if (input.includes('help') || input.includes('مساعدة')) {
      return language === 'en' ?
      `I can help you with the following topics:

📚 **Basic Concepts:**
- Ionizing radiation and properties
- X-ray physics

🔧 **X-ray Tube:**
- Components and structure
- Operation and functioning

⚡ **Electrical Circuits:**
- Power supply and control
- Electrical diagrams

🛡️ **Radiation Protection:**
- Safety principles
- Protection methods

Ask your question or choose from quick questions!` :
      `يمكنني مساعدتك في المواضيع التالية:

📚 **المفاهيم الأساسية:**
- الإشعاع المؤين وخصائصه
- فيزياء الأشعة السينية

🔧 **أنبوب الأشعة السينية:**
- المكونات والتركيب
- آلية العمل والتشغيل

⚡ **الدوائر الكهربائية:**
- مزود الطاقة والتحكم
- المخططات الكهربائية

🛡️ **الحماية الإشعاعية:**
- مبادئ السلامة
- طرق الوقاية

اطرح سؤالك أو اختر من الأسئلة السريعة!`;
    }

    // Default response
    return language === 'en' ?
    `Interesting question! Let me help you generally:

For accurate answers, you can:
- Use more specific keywords
- Visit educational content pages
- Ask about a specific topic

Available topics:
• X-ray tube
• Ionizing radiation
• Electrical circuits
• Radiation protection

How can I help you more?` :
    `سؤال مثير للاهتمام! دعني أساعدك بشكل عام:

للحصول على إجابة دقيقة، يمكنك:
- استخدام كلمات مفتاحية أكثر تحديداً
- زيارة صفحات المحتوى التعليمي
- طرح سؤال حول موضوع محدد

المواضيع المتاحة:
• أنبوب الأشعة السينية
• الإشعاع المؤين  
• الدوائر الكهربائية
• الحماية الإشعاعية

كيف يمكنني مساعدتك أكثر؟`;
  };

  const handleSend = async () => {
    if (!input.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: input,
      timestamp: new Date()
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setIsTyping(true);

    // Simulate thinking time
    setTimeout(() => {
      const response = generateResponse(input);
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response,
        timestamp: new Date()
      };

      setMessages((prev) => [...prev, assistantMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 2000);
  };

  const handleQuickQuestion = (question: string) => {
    setInput(question);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className="min-h-screen py-8" data-id="m0brdymga" data-path="src/pages/AIAssistantPage.tsx">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8" data-id="lo5agjpeg" data-path="src/pages/AIAssistantPage.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8" data-id="b9dyfmulk" data-path="src/pages/AIAssistantPage.tsx">

          <Badge variant="secondary" className="mb-4" data-id="ljvizg6dq" data-path="src/pages/AIAssistantPage.tsx">{t('ai.title')}</Badge>
          <h1 className="text-4xl font-bold text-gray-900 mb-4" data-id="ucunvjpfu" data-path="src/pages/AIAssistantPage.tsx">
            {t('ai.title')}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto" data-id="k6zw0tmo2" data-path="src/pages/AIAssistantPage.tsx">
            {t('ai.subtitle')}
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8" data-id="go0v3wt3a" data-path="src/pages/AIAssistantPage.tsx">
          {/* Chat Interface */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-2" data-id="fadh7qdpw" data-path="src/pages/AIAssistantPage.tsx">

            <Card className="h-[600px] flex flex-col" data-id="0kxhf18gb" data-path="src/pages/AIAssistantPage.tsx">
              <CardHeader data-id="1qcytyt4u" data-path="src/pages/AIAssistantPage.tsx">
                <CardTitle className="flex items-center gap-2" data-id="o15o5hfn4" data-path="src/pages/AIAssistantPage.tsx">
                  <Brain className="w-5 h-5 text-purple-600" data-id="diuazhfdh" data-path="src/pages/AIAssistantPage.tsx" />
                  Chat with Assistant
                </CardTitle>
                <CardDescription data-id="jm7lc8gif" data-path="src/pages/AIAssistantPage.tsx">
                  Ask your questions and get detailed answers
                </CardDescription>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col p-0" data-id="w7ftlhhz9" data-path="src/pages/AIAssistantPage.tsx">
                {/* Messages */}
                <ScrollArea className="flex-1 p-4" data-id="z2y506yqq" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="space-y-4" data-id="p6bo4xdzx" data-path="src/pages/AIAssistantPage.tsx">
                    {messages.map((message) =>
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex gap-3 ${
                      message.type === 'user' ? 'justify-end' : 'justify-start'}`
                      } data-id="ym26huy0f" data-path="src/pages/AIAssistantPage.tsx">

                        {message.type === 'assistant' &&
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0" data-id="i78crodae" data-path="src/pages/AIAssistantPage.tsx">
                            <Bot className="w-4 h-4 text-purple-600" data-id="y00nvybrl" data-path="src/pages/AIAssistantPage.tsx" />
                          </div>
                      }
                        
                        <div
                        className={`max-w-[80%] rounded-lg px-4 py-3 ${
                        message.type === 'user' ?
                        'bg-blue-600 text-white' :
                        'bg-gray-100 text-gray-900'}`
                        } data-id="hq2pwkdny" data-path="src/pages/AIAssistantPage.tsx">

                          <div className="whitespace-pre-line text-sm leading-relaxed" data-id="wlcym4zba" data-path="src/pages/AIAssistantPage.tsx">
                            {message.content}
                          </div>
                          <div
                          className={`text-xs mt-2 ${
                          message.type === 'user' ? 'text-blue-100' : 'text-gray-500'}`
                          } data-id="7ooiki366" data-path="src/pages/AIAssistantPage.tsx">

                            {message.timestamp.toLocaleTimeString(language === 'en' ? 'en-US' : 'ar-SA', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                          </div>
                        </div>

                        {message.type === 'user' &&
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0" data-id="ctolkaia7" data-path="src/pages/AIAssistantPage.tsx">
                            <User className="w-4 h-4 text-blue-600" data-id="vr0lots4t" data-path="src/pages/AIAssistantPage.tsx" />
                          </div>
                      }
                      </motion.div>
                    )}

                    {isTyping &&
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex gap-3 justify-start" data-id="adc03ocnj" data-path="src/pages/AIAssistantPage.tsx">

                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0" data-id="pmwv9s7el" data-path="src/pages/AIAssistantPage.tsx">
                          <Bot className="w-4 h-4 text-purple-600" data-id="ec9dybk2b" data-path="src/pages/AIAssistantPage.tsx" />
                        </div>
                        <div className="bg-gray-100 rounded-lg px-4 py-3" data-id="lasavxcm5" data-path="src/pages/AIAssistantPage.tsx">
                          <div className="flex space-x-1" data-id="nncuq1rjd" data-path="src/pages/AIAssistantPage.tsx">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" data-id="lyob2vtme" data-path="src/pages/AIAssistantPage.tsx"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} data-id="swhcwav1w" data-path="src/pages/AIAssistantPage.tsx"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} data-id="qejim8dts" data-path="src/pages/AIAssistantPage.tsx"></div>
                          </div>
                        </div>
                      </motion.div>
                    }
                    <div ref={messagesEndRef} data-id="wubprhifv" data-path="src/pages/AIAssistantPage.tsx" />
                  </div>
                </ScrollArea>

                {/* Input */}
                <div className="p-4 border-t" data-id="mjqvqq2hs" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="flex gap-2" data-id="6xqwk1stz" data-path="src/pages/AIAssistantPage.tsx">
                    <Input
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder={t('ai.placeholder')}
                      onKeyPress={(e) => e.key === 'Enter' && handleSend()}
                      disabled={isTyping} data-id="xxqkq384v" data-path="src/pages/AIAssistantPage.tsx" />

                    <Button
                      onClick={handleSend}
                      disabled={!input.trim() || isTyping}
                      size="icon" data-id="g6m4yq9ik" data-path="src/pages/AIAssistantPage.tsx">

                      <Send className="w-4 h-4" data-id="dblxfihj5" data-path="src/pages/AIAssistantPage.tsx" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Quick Questions & Help */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6" data-id="ne4vhde97" data-path="src/pages/AIAssistantPage.tsx">

            {/* Quick Questions */}
            <Card data-id="f7w6l6nvz" data-path="src/pages/AIAssistantPage.tsx">
              <CardHeader data-id="3hcel1951" data-path="src/pages/AIAssistantPage.tsx">
                <CardTitle className="flex items-center gap-2 text-lg" data-id="rc7568rwx" data-path="src/pages/AIAssistantPage.tsx">
                  <HelpCircle className="w-5 h-5 text-green-600" data-id="qnbsk2irs" data-path="src/pages/AIAssistantPage.tsx" />
                  Quick Questions
                </CardTitle>
                <CardDescription data-id="5xja747wf" data-path="src/pages/AIAssistantPage.tsx">
                  Click on a question to ask it directly
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2" data-id="nbliawl9a" data-path="src/pages/AIAssistantPage.tsx">
                {quickQuestions.map((question, index) =>
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }} data-id="st1mqbh49" data-path="src/pages/AIAssistantPage.tsx">

                    <Button
                    variant="ghost"
                    className="w-full text-left justify-start text-sm h-auto py-3 px-3 hover:bg-blue-50"
                    onClick={() => handleQuickQuestion(question)} data-id="02gb2g55u" data-path="src/pages/AIAssistantPage.tsx">

                      <MessageSquare className="w-4 h-4 mr-2 flex-shrink-0" data-id="6argua9bv" data-path="src/pages/AIAssistantPage.tsx" />
                      <span className="text-left leading-relaxed" data-id="qpynfxjxh" data-path="src/pages/AIAssistantPage.tsx">{question}</span>
                    </Button>
                  </motion.div>
                )}
              </CardContent>
            </Card>

            {/* Tips */}
            <Card data-id="xcjsdl7z4" data-path="src/pages/AIAssistantPage.tsx">
              <CardHeader data-id="p0oeq22mu" data-path="src/pages/AIAssistantPage.tsx">
                <CardTitle className="flex items-center gap-2 text-lg" data-id="st666bfra" data-path="src/pages/AIAssistantPage.tsx">
                  <Lightbulb className="w-5 h-5 text-yellow-600" data-id="a1943l5da" data-path="src/pages/AIAssistantPage.tsx" />
                  Usage Tips
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm text-gray-600" data-id="4bl2c8efm" data-path="src/pages/AIAssistantPage.tsx">
                <div className="flex items-start gap-2" data-id="p3tnxg66d" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" data-id="ndqtt75d5" data-path="src/pages/AIAssistantPage.tsx"></div>
                  <span data-id="ilzp23zvo" data-path="src/pages/AIAssistantPage.tsx">Use clear keywords in your questions</span>
                </div>
                <div className="flex items-start gap-2" data-id="aknwb4tpn" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" data-id="mfil8drm5" data-path="src/pages/AIAssistantPage.tsx"></div>
                  <span data-id="zqinkn61d" data-path="src/pages/AIAssistantPage.tsx">Ask specific questions for accurate answers</span>
                </div>
                <div className="flex items-start gap-2" data-id="43v18c1f2" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" data-id="7rygl57da" data-path="src/pages/AIAssistantPage.tsx"></div>
                  <span data-id="mpm18i0og" data-path="src/pages/AIAssistantPage.tsx">You can ask about equations and calculations</span>
                </div>
                <div className="flex items-start gap-2" data-id="e1ut7r5uh" data-path="src/pages/AIAssistantPage.tsx">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" data-id="w0i59vixa" data-path="src/pages/AIAssistantPage.tsx"></div>
                  <span data-id="du412x2yv" data-path="src/pages/AIAssistantPage.tsx">Explore interactive content in other pages</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>);

};

export default AIAssistantPage;