import { useState } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  CircuitBoard,
  Zap,
  Battery,
  Settings,
  Info } from
'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import CircuitDiagram from '@/components/CircuitDiagram';
import PowerSupplyDiagram from '@/components/PowerSupplyDiagram';

const CircuitDiagramPage = () => {
  const [selectedCircuit, setSelectedCircuit] = useState('main');
  const { t } = useLanguage();

  const circuitTypes = [
  {
    id: 'main',
    title: t('circuit.mainCircuit'),
    description: 'Main control circuit for X-ray equipment',
    icon: CircuitBoard,
    color: 'bg-blue-500'
  },
  {
    id: 'power',
    title: t('circuit.powerSupplySystem'),
    description: 'Power conversion and regulation circuit',
    icon: Battery,
    color: 'bg-green-500'
  },
  {
    id: 'control',
    title: 'Control Circuit',
    description: 'Operating parameter control circuit',
    icon: Settings,
    color: 'bg-purple-500'
  }];


  const circuitComponents = [
  {
    name: t('circuit.transformer'),
    function: 'Steps up voltage from 220V to 40-150kV',
    type: 'Step-up transformer',
    specifications: 'Transformation ratio: 1:500-700'
  },
  {
    name: t('circuit.rectifier'),
    function: 'Converts AC to DC current',
    type: 'Diode array',
    specifications: 'Full-wave or half-wave rectification'
  },
  {
    name: t('circuit.smoothing'),
    function: 'Smooths DC current and reduces ripple',
    type: 'Capacitors and inductors',
    specifications: 'Reduces ripple to less than 5%'
  },
  {
    name: 'Filament Transformer',
    function: 'Provides low voltage for filament heating',
    type: 'Step-down transformer',
    specifications: '5-12V, 3-5A'
  },
  {
    name: 'Current Control Circuit',
    function: 'Controls tube current (mA)',
    type: 'Variable resistor or electronic switch',
    specifications: '50-1000 mA'
  },
  {
    name: 'Exposure Timer',
    function: 'Controls exposure time',
    type: 'Digital electronic circuit',
    specifications: '0.001-10 seconds'
  }];


  return (
    <div className="min-h-screen py-8" data-id="kihiad6du" data-path="src/pages/CircuitDiagramPage.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="w25j6gmv5" data-path="src/pages/CircuitDiagramPage.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12" data-id="3f2bkipgz" data-path="src/pages/CircuitDiagramPage.tsx">

          <Badge variant="secondary" className="mb-4" data-id="7l5v91mgw" data-path="src/pages/CircuitDiagramPage.tsx">{t('circuit.title')}</Badge>
          <h1 className="text-4xl font-bold text-gray-900 mb-4" data-id="7r3x7a51s" data-path="src/pages/CircuitDiagramPage.tsx">
            {t('circuit.title')}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="wiphu4h9p" data-path="src/pages/CircuitDiagramPage.tsx">
            {t('circuit.subtitle')}
          </p>
        </motion.div>

        <Tabs defaultValue="diagrams" className="w-full" data-id="j15fdeos8" data-path="src/pages/CircuitDiagramPage.tsx">
          <TabsList className="grid w-full grid-cols-3 mb-8" data-id="8hb4r1ord" data-path="src/pages/CircuitDiagramPage.tsx">
            <TabsTrigger value="diagrams" data-id="36s2pnntz" data-path="src/pages/CircuitDiagramPage.tsx">{t('circuit.xrayCircuit')}</TabsTrigger>
            <TabsTrigger value="components" data-id="s5tvpyocq" data-path="src/pages/CircuitDiagramPage.tsx">Components</TabsTrigger>
            <TabsTrigger value="analysis" data-id="l4ylrx1hz" data-path="src/pages/CircuitDiagramPage.tsx">Analysis</TabsTrigger>
          </TabsList>

          {/* Diagrams Tab */}
          <TabsContent value="diagrams" data-id="1409cljnd" data-path="src/pages/CircuitDiagramPage.tsx">
            <div className="grid gap-6" data-id="hly1uak53" data-path="src/pages/CircuitDiagramPage.tsx">
              {/* Circuit Type Selector */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="grid md:grid-cols-3 gap-4 mb-8" data-id="6ht1ttx6i" data-path="src/pages/CircuitDiagramPage.tsx">

                {circuitTypes.map((type) => {
                  const Icon = type.icon;
                  return (
                    <Card
                      key={type.id}
                      className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                      selectedCircuit === type.id ? 'ring-2 ring-blue-500 shadow-lg' : ''}`
                      }
                      onClick={() => setSelectedCircuit(type.id)} data-id="vtao9129a" data-path="src/pages/CircuitDiagramPage.tsx">

                      <CardHeader className="text-center pb-3" data-id="cami4aebe" data-path="src/pages/CircuitDiagramPage.tsx">
                        <div className={`inline-flex items-center justify-center w-12 h-12 ${type.color} rounded-lg mb-3 mx-auto`} data-id="x79q8adai" data-path="src/pages/CircuitDiagramPage.tsx">
                          <Icon className="w-6 h-6 text-white" data-id="qkp7ubgyn" data-path="src/pages/CircuitDiagramPage.tsx" />
                        </div>
                        <CardTitle className="text-lg" data-id="8hf3xbj94" data-path="src/pages/CircuitDiagramPage.tsx">{type.title}</CardTitle>
                        <CardDescription className="text-sm" data-id="z2hw4f9lg" data-path="src/pages/CircuitDiagramPage.tsx">
                          {type.description}
                        </CardDescription>
                      </CardHeader>
                    </Card>);

                })}
              </motion.div>

              {/* Selected Circuit Display */}
              <motion.div
                key={selectedCircuit}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }} data-id="9vccimf6b" data-path="src/pages/CircuitDiagramPage.tsx">

                <Card className="overflow-hidden" data-id="s1fg06fvj" data-path="src/pages/CircuitDiagramPage.tsx">
                  <CardHeader data-id="c1ljyx2cd" data-path="src/pages/CircuitDiagramPage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="toowdzcvm" data-path="src/pages/CircuitDiagramPage.tsx">
                      <CircuitBoard className="w-5 h-5 text-blue-600" data-id="cghmccyzg" data-path="src/pages/CircuitDiagramPage.tsx" />
                      {circuitTypes.find((t) => t.id === selectedCircuit)?.title}
                    </CardTitle>
                    <CardDescription data-id="7ud5365vi" data-path="src/pages/CircuitDiagramPage.tsx">
                      Click on components to explore their functions
                    </CardDescription>
                  </CardHeader>
                  <CardContent data-id="e12t3hbdp" data-path="src/pages/CircuitDiagramPage.tsx">
                    {selectedCircuit === 'main' && <CircuitDiagram data-id="caj6gr4y2" data-path="src/pages/CircuitDiagramPage.tsx" />}
                    {selectedCircuit === 'power' && <PowerSupplyDiagram data-id="s6yslpcfp" data-path="src/pages/CircuitDiagramPage.tsx" />}
                    {selectedCircuit === 'control' &&
                    <div className="text-center py-12 text-gray-500" data-id="pap4mm82o" data-path="src/pages/CircuitDiagramPage.tsx">
                        Control circuit diagram - under development
                      </div>
                    }
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </TabsContent>

          {/* Components Tab */}
          <TabsContent value="components" data-id="zp2ucadsv" data-path="src/pages/CircuitDiagramPage.tsx">
            <div className="grid gap-4" data-id="cddzggff2" data-path="src/pages/CircuitDiagramPage.tsx">
              {circuitComponents.map((component, index) =>
              <motion.div
                key={component.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }} data-id="gbpeu8nkr" data-path="src/pages/CircuitDiagramPage.tsx">

                  <Card className="hover:shadow-lg transition-shadow" data-id="muc3qegar" data-path="src/pages/CircuitDiagramPage.tsx">
                    <CardHeader data-id="33rlithh7" data-path="src/pages/CircuitDiagramPage.tsx">
                      <div className="flex items-start justify-between" data-id="lhp9krkia" data-path="src/pages/CircuitDiagramPage.tsx">
                        <div data-id="jagwj5isu" data-path="src/pages/CircuitDiagramPage.tsx">
                          <CardTitle className="text-lg" data-id="k6ar6ba9d" data-path="src/pages/CircuitDiagramPage.tsx">{component.name}</CardTitle>
                          <CardDescription className="mt-1" data-id="fzxryihtn" data-path="src/pages/CircuitDiagramPage.tsx">
                            {component.function}
                          </CardDescription>
                        </div>
                        <Badge variant="outline" data-id="at38a6n1x" data-path="src/pages/CircuitDiagramPage.tsx">{component.type}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent data-id="idf5kfm0j" data-path="src/pages/CircuitDiagramPage.tsx">
                      <div className="bg-gray-50 p-3 rounded-lg" data-id="jq0kwhmf9" data-path="src/pages/CircuitDiagramPage.tsx">
                        <p className="text-sm text-gray-700" data-id="oizbsydxc" data-path="src/pages/CircuitDiagramPage.tsx">
                          <strong data-id="5ojmeazsy" data-path="src/pages/CircuitDiagramPage.tsx">Specifications:</strong> {component.specifications}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </div>
          </TabsContent>

          {/* Analysis Tab */}
          <TabsContent value="analysis" data-id="mlidrfc2j" data-path="src/pages/CircuitDiagramPage.tsx">
            <div className="grid lg:grid-cols-2 gap-8" data-id="t7mdn2qvh" data-path="src/pages/CircuitDiagramPage.tsx">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }} data-id="x2p5dq3gw" data-path="src/pages/CircuitDiagramPage.tsx">

                <Card data-id="nlftcoca6" data-path="src/pages/CircuitDiagramPage.tsx">
                  <CardHeader data-id="irwuk2z29" data-path="src/pages/CircuitDiagramPage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="uj7dxovoe" data-path="src/pages/CircuitDiagramPage.tsx">
                      <Zap className="w-5 h-5 text-yellow-600" data-id="1gpnbhinl" data-path="src/pages/CircuitDiagramPage.tsx" />
                      Power Analysis
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4" data-id="mz6jlpax7" data-path="src/pages/CircuitDiagramPage.tsx">
                    <div className="space-y-3" data-id="jz49z4b01" data-path="src/pages/CircuitDiagramPage.tsx">
                      <h4 className="font-semibold" data-id="9wz8afyoi" data-path="src/pages/CircuitDiagramPage.tsx">System Efficiency</h4>
                      <div className="space-y-2" data-id="xvo02cxcc" data-path="src/pages/CircuitDiagramPage.tsx">
                        <div className="flex justify-between text-sm" data-id="ez4y8szi2" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="dtt3gdcys" data-path="src/pages/CircuitDiagramPage.tsx">Transformer efficiency:</span>
                          <span className="font-medium" data-id="spxdh1y4a" data-path="src/pages/CircuitDiagramPage.tsx">95-98%</span>
                        </div>
                        <div className="flex justify-between text-sm" data-id="n217p01qs" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="ibk6omphz" data-path="src/pages/CircuitDiagramPage.tsx">Rectifier losses:</span>
                          <span className="font-medium" data-id="mdze9zkdt" data-path="src/pages/CircuitDiagramPage.tsx">2-3%</span>
                        </div>
                        <div className="flex justify-between text-sm" data-id="1fiab49xi" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="lbm7g6j7x" data-path="src/pages/CircuitDiagramPage.tsx">Cable losses:</span>
                          <span className="font-medium" data-id="gyqnwz6sh" data-path="src/pages/CircuitDiagramPage.tsx">1-2%</span>
                        </div>
                        <hr className="my-2" data-id="947mhs2q1" data-path="src/pages/CircuitDiagramPage.tsx" />
                        <div className="flex justify-between text-sm font-semibold" data-id="4dmsg3w38" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="pbchq52r6" data-path="src/pages/CircuitDiagramPage.tsx">Overall efficiency:</span>
                          <span data-id="tbrujtvh2" data-path="src/pages/CircuitDiagramPage.tsx">92-95%</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3" data-id="6et4my23y" data-path="src/pages/CircuitDiagramPage.tsx">
                      <h4 className="font-semibold" data-id="ym77m5801" data-path="src/pages/CircuitDiagramPage.tsx">Energy Distribution</h4>
                      <div className="space-y-2" data-id="xepmvpgc7" data-path="src/pages/CircuitDiagramPage.tsx">
                        <div className="flex justify-between text-sm" data-id="posvy0yuf" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="fa0y8djc8" data-path="src/pages/CircuitDiagramPage.tsx">X-rays:</span>
                          <span className="font-medium text-blue-600" data-id="fs44rg626" data-path="src/pages/CircuitDiagramPage.tsx">1%</span>
                        </div>
                        <div className="flex justify-between text-sm" data-id="mxr36bc9x" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="ix0uxsq3a" data-path="src/pages/CircuitDiagramPage.tsx">Heat:</span>
                          <span className="font-medium text-red-600" data-id="6kkm0hi0d" data-path="src/pages/CircuitDiagramPage.tsx">99%</span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-4" data-id="gtqfskuz2" data-path="src/pages/CircuitDiagramPage.tsx">
                        <div className="bg-blue-500 h-4 rounded-l-full" style={{ width: '1%' }} data-id="yoaksxl6o" data-path="src/pages/CircuitDiagramPage.tsx"></div>
                        <div className="bg-red-500 h-4 rounded-r-full ml-0" style={{ width: '99%', marginTop: '-16px', marginLeft: '1%' }} data-id="etkf40y8j" data-path="src/pages/CircuitDiagramPage.tsx"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }} data-id="z0cjyuk1i" data-path="src/pages/CircuitDiagramPage.tsx">

                <Card data-id="e0ahixps8" data-path="src/pages/CircuitDiagramPage.tsx">
                  <CardHeader data-id="yvd9w59t5" data-path="src/pages/CircuitDiagramPage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="opgnssj90" data-path="src/pages/CircuitDiagramPage.tsx">
                      <Info className="w-5 h-5 text-blue-600" data-id="v31d8evsn" data-path="src/pages/CircuitDiagramPage.tsx" />
                      Important Equations
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4" data-id="mv3gwm5z2" data-path="src/pages/CircuitDiagramPage.tsx">
                    <div className="space-y-3" data-id="bq6bqotzq" data-path="src/pages/CircuitDiagramPage.tsx">
                      <div className="bg-blue-50 p-3 rounded-lg" data-id="1eniu6jsf" data-path="src/pages/CircuitDiagramPage.tsx">
                        <h5 className="font-semibold text-blue-900 mb-2" data-id="3zaap6a80" data-path="src/pages/CircuitDiagramPage.tsx">Ohm's Law</h5>
                        <div className="text-center font-mono text-lg text-blue-800" data-id="tqlnokypd" data-path="src/pages/CircuitDiagramPage.tsx">
                          V = I × R
                        </div>
                        <p className="text-xs text-blue-600 mt-1" data-id="dl9v4f9cb" data-path="src/pages/CircuitDiagramPage.tsx">
                          Voltage = Current × Resistance
                        </p>
                      </div>

                      <div className="bg-green-50 p-3 rounded-lg" data-id="kvitr4gpc" data-path="src/pages/CircuitDiagramPage.tsx">
                        <h5 className="font-semibold text-green-900 mb-2" data-id="sxbx6rvjf" data-path="src/pages/CircuitDiagramPage.tsx">Electrical Power</h5>
                        <div className="text-center font-mono text-lg text-green-800" data-id="0u3tvnoqu" data-path="src/pages/CircuitDiagramPage.tsx">
                          P = V × I
                        </div>
                        <p className="text-xs text-green-600 mt-1" data-id="xmyiiukac" data-path="src/pages/CircuitDiagramPage.tsx">
                          Power = Voltage × Current
                        </p>
                      </div>

                      <div className="bg-purple-50 p-3 rounded-lg" data-id="p88euzvev" data-path="src/pages/CircuitDiagramPage.tsx">
                        <h5 className="font-semibold text-purple-900 mb-2" data-id="3585yq9b0" data-path="src/pages/CircuitDiagramPage.tsx">Transformer Ratio</h5>
                        <div className="text-center font-mono text-lg text-purple-800" data-id="z9enlpj1m" data-path="src/pages/CircuitDiagramPage.tsx">
                          V₂/V₁ = N₂/N₁
                        </div>
                        <p className="text-xs text-purple-600 mt-1" data-id="hmlnxgdzp" data-path="src/pages/CircuitDiagramPage.tsx">
                          Voltage ratio = Turns ratio
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>);

};

export default CircuitDiagramPage;