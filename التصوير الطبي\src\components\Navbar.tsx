import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { Menu, Microscope, Zap, CircuitBoard, Brain, BookOpen } from 'lucide-react';

const navigationItems = [
{ path: '/', label: 'الرئيسية', icon: Microscope },
{ path: '/concepts', label: 'المفاهيم الأساسية', icon: BookOpen },
{ path: '/xray-tube', label: 'أنبوب الأشعة السينية', icon: Zap },
{ path: '/circuits', label: 'الدوائر الكهربائية', icon: CircuitBoard },
{ path: '/ai-assistant', label: 'المساعد الذكي', icon: Brain }];


const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();

  const NavItems = ({ mobile = false }) =>
  <div className={`flex ${mobile ? 'flex-col space-y-2' : 'space-x-4'}`} data-id="tid0xlhd4" data-path="src/components/Navbar.tsx">
      {navigationItems.map((item) => {
      const Icon = item.icon;
      const isActive = location.pathname === item.path;

      return (
        <Link
          key={item.path}
          to={item.path}
          onClick={() => mobile && setIsOpen(false)}
          className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
          isActive ?
          'bg-blue-100 text-blue-700 font-medium' :
          'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`
          } data-id="7zrkt83fx" data-path="src/components/Navbar.tsx">

            <Icon size={18} data-id="30gaxbv6l" data-path="src/components/Navbar.tsx" />
            <span className="text-sm" data-id="68b3h942h" data-path="src/components/Navbar.tsx">{item.label}</span>
            {isActive && <Badge variant="secondary" className="text-xs" data-id="gvatrqd3j" data-path="src/components/Navbar.tsx">نشط</Badge>}
          </Link>);

    })}
    </div>;


  return (
    <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-sm" data-id="gdqi2kkef" data-path="src/components/Navbar.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="3b06kh37s" data-path="src/components/Navbar.tsx">
        <div className="flex justify-between items-center h-16" data-id="bub7zu7uf" data-path="src/components/Navbar.tsx">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3" data-id="zjdt9dv2t" data-path="src/components/Navbar.tsx">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-lg" data-id="kbwqp2wj0" data-path="src/components/Navbar.tsx">
              <Microscope className="h-6 w-6 text-white" data-id="1adz33agf" data-path="src/components/Navbar.tsx" />
            </div>
            <div className="text-right" data-id="gl8s6y7gf" data-path="src/components/Navbar.tsx">
              <h1 className="text-xl font-bold text-gray-900" data-id="vxg13ntvv" data-path="src/components/Navbar.tsx">التصوير الطبي</h1>
              <p className="text-xs text-gray-500" data-id="lst8lcq56" data-path="src/components/Navbar.tsx">منصة تعليمية تفاعلية</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:block" data-id="1viusjufe" data-path="src/components/Navbar.tsx">
            <NavItems data-id="l1cwusxiv" data-path="src/components/Navbar.tsx" />
          </div>

          {/* Mobile Menu */}
          <div className="md:hidden" data-id="p34g5khze" data-path="src/components/Navbar.tsx">
            <Sheet open={isOpen} onOpenChange={setIsOpen} data-id="agws0y72w" data-path="src/components/Navbar.tsx">
              <SheetTrigger asChild data-id="qddozlvqu" data-path="src/components/Navbar.tsx">
                <Button variant="ghost" size="sm" data-id="8xoad1i67" data-path="src/components/Navbar.tsx">
                  <Menu className="h-5 w-5" data-id="ow7q9i14m" data-path="src/components/Navbar.tsx" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-64" data-id="d0p52nozo" data-path="src/components/Navbar.tsx">
                <div className="py-4" data-id="73x9nyz1w" data-path="src/components/Navbar.tsx">
                  <h2 className="text-lg font-semibold mb-4 text-right" data-id="sry0lhzhj" data-path="src/components/Navbar.tsx">القائمة الرئيسية</h2>
                  <NavItems mobile data-id="c6e8y3syo" data-path="src/components/Navbar.tsx" />
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>);

};

export default Navbar;