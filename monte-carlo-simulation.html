<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاكاة مونت كارلو للأشعة السينية | Monte Carlo X-Ray Simulation</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal for Arabic, Inter for English -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Base Styles */
        :root {
            --primary-color: #4f46e5;
            --primary-light: #e0e7ff;
            --primary-dark: #312e81;
            --secondary-color: #0ea5e9;
            --secondary-light: #e0f2fe;
            --secondary-dark: #0369a1;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8fafc;
        }
        
        .en {
            font-family: 'Inter', sans-serif;
            direction: ltr;
            text-align: left;
        }
        
        /* Particle animation */
        .particle-container {
            position: relative;
            overflow: hidden;
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background-color: rgba(59, 130, 246, 0.7);
            border-radius: 50%;
            transform-origin: center;
        }
        
        /* Code block styling */
        .code-block {
            background-color: #1e293b;
            color: #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        
        .code-comment {
            color: #94a3b8;
        }
        
        .code-keyword {
            color: #c084fc;
        }
        
        .code-string {
            color: #86efac;
        }
        
        .code-number {
            color: #fca5a5;
        }
        
        .code-function {
            color: #93c5fd;
        }
        
        /* Equation styling */
        .equation {
            background-color: #f8fafc;
            border-left: 4px solid #4f46e5;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
            overflow-x: auto;
            font-family: 'Times New Roman', serif;
            direction: ltr;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-4 rtl:space-x-reverse">
                <a href="landing.html" class="text-gray-600 hover:text-blue-600">
                    <i class="fas fa-arrow-right rtl:hidden"></i>
                    <i class="fas fa-arrow-left hidden rtl:inline"></i>
                    <span class="ml-2 rtl:mr-2" id="back-link">العودة للرئيسية</span>
                </a>
            </div>
            <div>
                <button id="language-toggle" class="text-gray-600 hover:text-blue-600 px-3 py-1 rounded-full border border-gray-200 text-sm flex items-center">
                    <i class="fas fa-globe mr-2 rtl:ml-2 rtl:mr-0"></i>
                    <span id="language-text">English</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Title Section -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div class="md:flex">
                <div class="md:w-2/3 p-8">
                    <h1 class="text-3xl font-bold text-gray-800 mb-4" id="page-title">محاكاة مونت كارلو للأشعة السينية</h1>
                    <p class="text-gray-600 mb-4 leading-relaxed" id="page-subtitle">تقنيات متقدمة لمحاكاة تفاعلات الأشعة السينية مع المادة</p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-sm">الفصل السابع</span>
                        <span class="bg-purple-50 text-purple-600 px-3 py-1 rounded-full text-sm" id="simulation-tag">المحاكاة الحاسوبية</span>
                        <span class="bg-green-50 text-green-600 px-3 py-1 rounded-full text-sm" id="physics-tag">الفيزياء الإشعاعية</span>
                    </div>
                </div>
                <div class="md:w-1/3 bg-gradient-to-br from-purple-500 to-indigo-600 p-8 flex items-center justify-center">
                    <div class="text-center">
                        <div class="w-20 h-20 mx-auto bg-white/20 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-dice text-4xl text-white"></i>
                        </div>
                        <h3 class="text-white text-xl font-bold mb-2" id="interactive-title">محاكاة تفاعلية</h3>
                        <p class="text-white/80 text-sm" id="interactive-desc">تجربة محاكاة مونت كارلو لتفاعلات الأشعة السينية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Introduction Section -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center" id="intro-title">
                <i class="fas fa-info-circle text-purple-500 ml-2 rtl:ml-2 rtl:mr-0"></i>
                مقدمة في محاكاة مونت كارلو
            </h2>
            <p class="text-gray-700 mb-4 leading-relaxed" id="intro-p1">
                تُعد طرق مونت كارلو من أكثر الأساليب دقة وشمولية لمحاكاة تفاعل الأشعة السينية مع المادة. تعتمد هذه الطرق على المحاكاة الإحصائية للعمليات الفيزيائية الأساسية، حيث يتم تتبع مسارات الفوتونات والإلكترونات الفردية خلال تفاعلاتها المتعددة مع الأنسجة أو المواد.
            </p>
            <p class="text-gray-700 leading-relaxed" id="intro-p2">
                تستخدم محاكاة مونت كارلو الأعداد العشوائية لتحديد مسار وتفاعلات كل جسيم، مع الالتزام بالتوزيعات الاحتمالية المستمدة من النظريات الفيزيائية والبيانات التجريبية. تسمح هذه المنهجية بنمذجة ظواهر معقدة مثل التشتت المتعدد، والتفاعلات الثانوية، وتوليد الإلكترونات الثانوية، وهي عوامل يصعب تضمينها في النماذج التحليلية البسيطة.
            </p>
        </div>

        <!-- Monte Carlo Principles -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center" id="principles-title">
                <i class="fas fa-cogs text-blue-500 ml-2 rtl:ml-2 rtl:mr-0"></i>
                المبادئ الأساسية لمحاكاة مونت كارلو
            </h2>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-3" id="random-sampling-title">أخذ العينات العشوائية</h3>
                    <p class="text-gray-700 mb-4" id="random-sampling-desc">
                        تعتمد محاكاة مونت كارلو على توليد أرقام عشوائية وتحويلها إلى متغيرات فيزيائية باستخدام دوال التوزيع الاحتمالية المناسبة. يتم استخدام هذه المتغيرات لتحديد:
                    </p>
                    <ul class="list-disc list-inside text-gray-700 space-y-1 mb-4">
                        <li id="sampling-point-1">مسار الفوتون أو الإلكترون</li>
                        <li id="sampling-point-2">المسافة الحرة بين التفاعلات</li>
                        <li id="sampling-point-3">نوع التفاعل الذي سيحدث</li>
                        <li id="sampling-point-4">زاوية التشتت والطاقة المنقولة</li>
                    </ul>
                    
                    <div class="equation">
                        <span id="equation-label">معادلة أخذ العينات العشوائية:</span><br>
                        <span class="text-lg">x = F<sup>-1</sup>(ξ)</span>
                    </div>
                    <p class="text-sm text-gray-500 text-center" id="equation-desc">
                        حيث ξ هو رقم عشوائي موحد بين 0 و 1، و F<sup>-1</sup> هي الدالة العكسية للتوزيع التراكمي
                    </p>
                </div>
                
                <div class="particle-container bg-gray-900 rounded-lg h-64 flex items-center justify-center">
                    <!-- Particle animation will be added with JavaScript -->
                    <div class="text-center">
                        <p class="text-white text-sm mb-2" id="animation-title">محاكاة تفاعلات الفوتونات</p>
                        <button id="start-animation" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                            <span id="animation-button">بدء المحاكاة</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="mt-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3" id="interaction-types-title">أنواع التفاعلات المحاكاة</h3>
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                        <h4 class="font-semibold text-blue-800 mb-2" id="photoelectric-title">التأثير الكهروضوئي</h4>
                        <p class="text-sm text-gray-700" id="photoelectric-desc">
                            امتصاص كامل للفوتون وإطلاق إلكترون من الطبقات الذرية الداخلية. يهيمن هذا التأثير عند الطاقات المنخفضة وفي المواد ذات العدد الذري العالي.
                        </p>
                    </div>
                    
                    <div class="bg-purple-50 rounded-lg p-4 border border-purple-100">
                        <h4 class="font-semibold text-purple-800 mb-2" id="compton-title">تشتت كومبتون</h4>
                        <p class="text-sm text-gray-700" id="compton-desc">
                            تفاعل الفوتون مع إلكترون حر أو مرتبط ضعيفاً، مما يؤدي إلى تشتت الفوتون بطاقة أقل وإطلاق إلكترون. يهيمن هذا التأثير في نطاق الطاقات المتوسطة.
                        </p>
                    </div>
                    
                    <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                        <h4 class="font-semibold text-green-800 mb-2" id="rayleigh-title">تشتت رايلي</h4>
                        <p class="text-sm text-gray-700" id="rayleigh-desc">
                            تشتت مرن للفوتون دون فقدان للطاقة. يحدث هذا التفاعل مع الذرة ككل ويؤدي فقط إلى تغيير اتجاه الفوتون. يكون أكثر أهمية عند الطاقات المنخفضة.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monte Carlo Code Example -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center" id="code-title">
                <i class="fas fa-code text-green-500 ml-2 rtl:ml-2 rtl:mr-0"></i>
                مثال على كود محاكاة مونت كارلو
            </h2>
            
            <div class="code-block mb-4" dir="ltr">
<pre><span class="code-comment"># Python code for a simple Monte Carlo simulation of X-ray photon transport</span>
<span class="code-keyword">import</span> numpy <span class="code-keyword">as</span> np
<span class="code-keyword">import</span> matplotlib.pyplot <span class="code-keyword">as</span> plt

<span class="code-function">def</span> <span class="code-function">simulate_photon_path</span>(energy, material_properties, max_interactions=<span class="code-number">100</span>):
    <span class="code-comment"># Initial position and direction</span>
    position = np.array([<span class="code-number">0.0</span>, <span class="code-number">0.0</span>, <span class="code-number">0.0</span>])
    direction = np.array([<span class="code-number">0.0</span>, <span class="code-number">0.0</span>, <span class="code-number">1.0</span>])  <span class="code-comment"># Initial direction along z-axis</span>
    
    path = [position.copy()]
    current_energy = energy
    
    <span class="code-keyword">for</span> _ <span class="code-keyword">in</span> range(max_interactions):
        <span class="code-comment"># Calculate mean free path based on energy and material</span>
        mfp = calculate_mean_free_path(current_energy, material_properties)
        
        <span class="code-comment"># Sample distance to next interaction</span>
        distance = -mfp * np.log(np.random.random())
        
        <span class="code-comment"># Move photon to interaction site</span>
        position = position + direction * distance
        path.append(position.copy())
        
        <span class="code-comment"># Determine interaction type</span>
        interaction_type = sample_interaction_type(current_energy, material_properties)
        
        <span class="code-keyword">if</span> interaction_type == <span class="code-string">"photoelectric"</span>:
            <span class="code-comment"># Photon is absorbed</span>
            <span class="code-keyword">break</span>
        <span class="code-keyword">elif</span> interaction_type == <span class="code-string">"compton"</span>:
            <span class="code-comment"># Calculate new energy and direction after Compton scattering</span>
            current_energy, direction = compton_scatter(current_energy, direction)
        <span class="code-keyword">elif</span> interaction_type == <span class="code-string">"rayleigh"</span>:
            <span class="code-comment"># Calculate new direction after Rayleigh scattering</span>
            direction = rayleigh_scatter(current_energy, direction)
    
    <span class="code-keyword">return</span> np.array(path)

<span class="code-comment"># Simulate multiple photons</span>
photon_paths = [simulate_photon_path(<span class="code-number">80</span>, material_properties) <span class="code-keyword">for</span> _ <span class="code-keyword">in</span> range(<span class="code-number">1000</span>)]</pre>
            </div>
            
            <p class="text-gray-700 text-sm" id="code-explanation">
                هذا مثال مبسط لكود محاكاة مونت كارلو بلغة Python. يقوم الكود بمحاكاة مسار فوتونات الأشعة السينية خلال المادة، مع تحديد المسافة بين التفاعلات ونوع التفاعل (كهروضوئي، كومبتون، رايلي) بشكل عشوائي بناءً على التوزيعات الاحتمالية الفيزيائية. في التطبيقات الحقيقية، تستخدم برامج أكثر تعقيداً مثل MCNP أو Geant4 أو EGSnrc.
            </p>
        </div>

        <!-- Monte Carlo Applications -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center" id="applications-title">
                <i class="fas fa-laptop-medical text-indigo-500 ml-2 rtl:ml-2 rtl:mr-0"></i>
                تطبيقات محاكاة مونت كارلو في التصوير بالأشعة السينية
            </h2>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-indigo-50 rounded-lg p-5 border border-indigo-100">
                    <div class="flex items-center mb-3">
                        <div class="bg-indigo-100 text-indigo-600 p-2 rounded-full ml-3 rtl:ml-3 rtl:mr-0">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800" id="app1-title">تصميم وتحسين أنظمة التصوير</h3>
                    </div>
                    <p class="text-gray-700 text-sm" id="app1-desc">
                        تستخدم محاكاة مونت كارلو لتقييم تصاميم أنظمة التصوير الجديدة قبل بنائها، مما يوفر الوقت والتكلفة. يمكن تحسين معلمات النظام مثل هندسة الكاشف، ومواد الترشيح، وإعدادات الأشعة السينية لتحقيق التوازن الأمثل بين جودة الصورة والجرعة الإشعاعية.
                    </p>
                </div>
                
                <div class="bg-blue-50 rounded-lg p-5 border border-blue-100">
                    <div class="flex items-center mb-3">
                        <div class="bg-blue-100 text-blue-600 p-2 rounded-full ml-3 rtl:ml-3 rtl:mr-0">
                            <i class="fas fa-radiation"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800" id="app2-title">حساب الجرعة الإشعاعية</h3>
                    </div>
                    <p class="text-gray-700 text-sm" id="app2-desc">
                        توفر محاكاة مونت كارلو تقديرات دقيقة لتوزيع الجرعة الإشعاعية في الأنسجة المختلفة، مما يساعد في تقييم المخاطر الإشعاعية وتحسين بروتوكولات التصوير. يمكن استخدامها لحساب الجرعة للأعضاء الحساسة والجنين في حالات التصوير للنساء الحوامل.
                    </p>
                </div>
                
                <div class="bg-green-50 rounded-lg p-5 border border-green-100">
                    <div class="flex items-center mb-3">
                        <div class="bg-green-100 text-green-600 p-2 rounded-full ml-3 rtl:ml-3 rtl:mr-0">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800" id="app3-title">تحليل جودة الصورة</h3>
                    </div>
                    <p class="text-gray-700 text-sm" id="app3-desc">
                        تسمح محاكاة مونت كارلو بتقييم مقاييس جودة الصورة مثل نسبة التباين إلى الضوضاء (CNR) والكشف عن التفاصيل الدقيقة. يمكن استخدامها لدراسة تأثير عوامل مختلفة مثل تشتت الأشعة والضوضاء الإلكترونية والتشوهات الهندسية على جودة الصورة النهائية.
                    </p>
                </div>
                
                <div class="bg-amber-50 rounded-lg p-5 border border-amber-100">
                    <div class="flex items-center mb-3">
                        <div class="bg-amber-100 text-amber-600 p-2 rounded-full ml-3 rtl:ml-3 rtl:mr-0">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800" id="app4-title">تدريب خوارزميات الذكاء الاصطناعي</h3>
                    </div>
                    <p class="text-gray-700 text-sm" id="app4-desc">
                        تستخدم بيانات المحاكاة لتدريب خوارزميات التعلم العميق لتحسين جودة الصورة وتقليل الجرعة. يمكن توليد آلاف الصور الاصطناعية بمعلمات مختلفة، مما يوفر مجموعات بيانات واسعة لتدريب نماذج الذكاء الاصطناعي دون الحاجة للتعرض الإشعاعي الإضافي للمرضى.
                    </p>
                </div>
            </div>
        </div>

        <!-- Popular Monte Carlo Codes -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center" id="codes-title">
                <i class="fas fa-terminal text-gray-500 ml-2 rtl:ml-2 rtl:mr-0"></i>
                برامج محاكاة مونت كارلو الشائعة
            </h2>
            
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white rounded-lg overflow-hidden">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="py-3 px-4 text-right font-semibold text-gray-700" id="table-header-1">البرنامج</th>
                            <th class="py-3 px-4 text-right font-semibold text-gray-700" id="table-header-2">المطور</th>
                            <th class="py-3 px-4 text-right font-semibold text-gray-700" id="table-header-3">الاستخدامات الرئيسية</th>
                            <th class="py-3 px-4 text-right font-semibold text-gray-700" id="table-header-4">المميزات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <tr>
                            <td class="py-3 px-4 font-medium">MCNP</td>
                            <td class="py-3 px-4" id="mcnp-dev">مختبر لوس ألاموس الوطني</td>
                            <td class="py-3 px-4" id="mcnp-uses">الوقاية الإشعاعية، حسابات الدرع، تصميم المفاعلات</td>
                            <td class="py-3 px-4" id="mcnp-features">برنامج شامل، دعم هندسي متقدم، تاريخ طويل من التحقق</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-medium">Geant4</td>
                            <td class="py-3 px-4" id="geant-dev">المنظمة الأوروبية للأبحاث النووية (CERN)</td>
                            <td class="py-3 px-4" id="geant-uses">فيزياء الجسيمات، التصوير الطبي، العلاج الإشعاعي</td>
                            <td class="py-3 px-4" id="geant-features">مكتبة C++ مفتوحة المصدر، مرونة عالية، نماذج فيزيائية متقدمة</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-medium">EGSnrc</td>
                            <td class="py-3 px-4" id="egs-dev">المجلس الوطني للبحوث الكندية</td>
                            <td class="py-3 px-4" id="egs-uses">التصوير بالأشعة السينية، العلاج الإشعاعي، قياس الجرعات</td>
                            <td class="py-3 px-4" id="egs-features">دقة عالية في نقل الإلكترونات والفوتونات، سرعة حسابية جيدة</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-medium">PENELOPE</td>
                            <td class="py-3 px-4" id="penelope-dev">جامعة برشلونة</td>
                            <td class="py-3 px-4" id="penelope-uses">التصوير الطبي، تحليل العينات، المجهر الإلكتروني</td>
                            <td class="py-3 px-4" id="penelope-features">دقة عالية في الطاقات المنخفضة، نمذجة تفصيلية للتفاعلات</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="md:flex md:justify-between">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center gap-3 mb-4">
                        <div class="p-2 bg-white/10 rounded-lg">
                            <i class="fas fa-book-open text-xl"></i>
                        </div>
                        <div>
                            <h2 class="text-lg font-bold" id="footer-title">منصة التعلم الطبي</h2>
                        </div>
                    </div>
                    <p class="text-gray-400 text-sm max-w-md" id="footer-desc">
                        منصة تعليمية متخصصة في مجال الفيزياء الطبية وتقنيات التصوير بالأشعة السينية، تجمع بين المحتوى العلمي العميق والتطبيقات التفاعلية.
                    </p>
                </div>
                
                <div>
                    <h3 class="text-sm font-semibold uppercase tracking-wider mb-4" id="quick-links">روابط سريعة</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors" id="home-link">الصفحة الرئيسية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors" id="content-link">المحتوى العلمي</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors" id="simulations-link">المحاكاة التفاعلية</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors" id="about-link">عن المؤلف</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-8 pt-8 border-t border-gray-700 flex flex-col md:flex-row md:justify-between md:items-center">
                <p class="text-gray-400 text-sm" id="copyright">
                    © 2024 جميع الحقوق محفوظة
                </p>
                <div class="flex space-x-6 rtl:space-x-reverse mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white">
                        <i class="fab fa-github"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript for interactivity -->
    <script>
        // DOM Elements
        const languageToggle = document.getElementById('language-toggle');
        const languageText = document.getElementById('language-text');
        const startAnimationBtn = document.getElementById('start-animation');
        const particleContainer = document.querySelector('.particle-container');
        
        // Bilingual content
        const translations = {
            ar: {
                pageTitle: "محاكاة مونت كارلو للأشعة السينية",
                pageSubtitle: "تقنيات متقدمة لمحاكاة تفاعلات الأشعة السينية مع المادة",
                simulationTag: "المحاكاة الحاسوبية",
                physicsTag: "الفيزياء الإشعاعية",
                interactiveTitle: "محاكاة تفاعلية",
                interactiveDesc: "تجربة محاكاة مونت كارلو لتفاعلات الأشعة السينية",
                introTitle: "مقدمة في محاكاة مونت كارلو",
                introP1: "تُعد طرق مونت كارلو من أكثر الأساليب دقة وشمولية لمحاكاة تفاعل الأشعة السينية مع المادة. تعتمد هذه الطرق على المحاكاة الإحصائية للعمليات الفيزيائية الأساسية، حيث يتم تتبع مسارات الفوتونات والإلكترونات الفردية خلال تفاعلاتها المتعددة مع الأنسجة أو المواد.",
                introP2: "تستخدم محاكاة مونت كارلو الأعداد العشوائية لتحديد مسار وتفاعلات كل جسيم، مع الالتزام بالتوزيعات الاحتمالية المستمدة من النظريات الفيزيائية والبيانات التجريبية. تسمح هذه المنهجية بنمذجة ظواهر معقدة مثل التشتت المتعدد، والتفاعلات الثانوية، وتوليد الإلكترونات الثانوية، وهي عوامل يصعب تضمينها في النماذج التحليلية البسيطة.",
                principlesTitle: "المبادئ الأساسية لمحاكاة مونت كارلو",
                randomSamplingTitle: "أخذ العينات العشوائية",
                randomSamplingDesc: "تعتمد محاكاة مونت كارلو على توليد أرقام عشوائية وتحويلها إلى متغيرات فيزيائية باستخدام دوال التوزيع الاحتمالية المناسبة. يتم استخدام هذه المتغيرات لتحديد:",
                samplingPoint1: "مسار الفوتون أو الإلكترون",
                samplingPoint2: "المسافة الحرة بين التفاعلات",
                samplingPoint3: "نوع التفاعل الذي سيحدث",
                samplingPoint4: "زاوية التشتت والطاقة المنقولة",
                equationLabel: "معادلة أخذ العينات العشوائية:",
                equationDesc: "حيث ξ هو رقم عشوائي موحد بين 0 و 1، و F⁻¹ هي الدالة العكسية للتوزيع التراكمي",
                animationTitle: "محاكاة تفاعلات الفوتونات",
                animationButton: "بدء المحاكاة",
                interactionTypesTitle: "أنواع التفاعلات المحاكاة",
                photoelectricTitle: "التأثير الكهروضوئي",
                photoelectricDesc: "امتصاص كامل للفوتون وإطلاق إلكترون من الطبقات الذرية الداخلية. يهيمن هذا التأثير عند الطاقات المنخفضة وفي المواد ذات العدد الذري العالي.",
                comptonTitle: "تشتت كومبتون",
                comptonDesc: "تفاعل الفوتون مع إلكترون حر أو مرتبط ضعيفاً، مما يؤدي إلى تشتت الفوتون بطاقة أقل وإطلاق إلكترون. يهيمن هذا التأثير في نطاق الطاقات المتوسطة.",
                rayleighTitle: "تشتت رايلي",
                rayleighDesc: "تشتت مرن للفوتون دون فقدان للطاقة. يحدث هذا التفاعل مع الذرة ككل ويؤدي فقط إلى تغيير اتجاه الفوتون. يكون أكثر أهمية عند الطاقات المنخفضة.",
                codeTitle: "مثال على كود محاكاة مونت كارلو",
                codeExplanation: "هذا مثال مبسط لكود محاكاة مونت كارلو بلغة Python. يقوم الكود بمحاكاة مسار فوتونات الأشعة السينية خلال المادة، مع تحديد المسافة بين التفاعلات ونوع التفاعل (كهروضوئي، كومبتون، رايلي) بشكل عشوائي بناءً على التوزيعات الاحتمالية الفيزيائية. في التطبيقات الحقيقية، تستخدم برامج أكثر تعقيداً مثل MCNP أو Geant4 أو EGSnrc.",
                applicationsTitle: "تطبيقات محاكاة مونت كارلو في التصوير بالأشعة السينية",
                app1Title: "تصميم وتحسين أنظمة التصوير",
                app1Desc: "تستخدم محاكاة مونت كارلو لتقييم تصاميم أنظمة التصوير الجديدة قبل بنائها، مما يوفر الوقت والتكلفة. يمكن تحسين معلمات النظام مثل هندسة الكاشف، ومواد الترشيح، وإعدادات الأشعة السينية لتحقيق التوازن الأمثل بين جودة الصورة والجرعة الإشعاعية.",
                app2Title: "حساب الجرعة الإشعاعية",
                app2Desc: "توفر محاكاة مونت كارلو تقديرات دقيقة لتوزيع الجرعة الإشعاعية في الأنسجة المختلفة، مما يساعد في تقييم المخاطر الإشعاعية وتحسين بروتوكولات التصوير. يمكن استخدامها لحساب الجرعة للأعضاء الحساسة والجنين في حالات التصوير للنساء الحوامل.",
                app3Title: "تحليل جودة الصورة",
                app3Desc: "تسمح محاكاة مونت كارلو بتقييم مقاييس جودة الصورة مثل نسبة التباين إلى الضوضاء (CNR) والكشف عن التفاصيل الدقيقة. يمكن استخدامها لدراسة تأثير عوامل مختلفة مثل تشتت الأشعة والضوضاء الإلكترونية والتشوهات الهندسية على جودة الصورة النهائية.",
                app4Title: "تدريب خوارزميات الذكاء الاصطناعي",
                app4Desc: "تستخدم بيانات المحاكاة لتدريب خوارزميات التعلم العميق لتحسين جودة الصورة وتقليل الجرعة. يمكن توليد آلاف الصور الاصطناعية بمعلمات مختلفة، مما يوفر مجموعات بيانات واسعة لتدريب نماذج الذكاء الاصطناعي دون الحاجة للتعرض الإشعاعي الإضافي للمرضى.",
                codesTitle: "برامج محاكاة مونت كارلو الشائعة",
                tableHeader1: "البرنامج",
                tableHeader2: "المطور",
                tableHeader3: "الاستخدامات الرئيسية",
                tableHeader4: "المميزات",
                mcnpDev: "مختبر لوس ألاموس الوطني",
                mcnpUses: "الوقاية الإشعاعية، حسابات الدرع، تصميم المفاعلات",
                mcnpFeatures: "برنامج شامل، دعم هندسي متقدم، تاريخ طويل من التحقق",
                geantDev: "المنظمة الأوروبية للأبحاث النووية (CERN)",
                geantUses: "فيزياء الجسيمات، التصوير الطبي، العلاج الإشعاعي",
                geantFeatures: "مكتبة C++ مفتوحة المصدر، مرونة عالية، نماذج فيزيائية متقدمة",
                egsDev: "المجلس الوطني للبحوث الكندية",
                egsUses: "التصوير بالأشعة السينية، العلاج الإشعاعي، قياس الجرعات",
                egsFeatures: "دقة عالية في نقل الإلكترونات والفوتونات، سرعة حسابية جيدة",
                penelopeDev: "جامعة برشلونة",
                penelopeUses: "التصوير الطبي، تحليل العينات، المجهر الإلكتروني",
                penelopeFeatures: "دقة عالية في الطاقات المنخفضة، نمذجة تفصيلية للتفاعلات",
                footerTitle: "منصة التعلم الطبي",
                footerDesc: "منصة تعليمية متخصصة في مجال الفيزياء الطبية وتقنيات التصوير بالأشعة السينية، تجمع بين المحتوى العلمي العميق والتطبيقات التفاعلية.",
                quickLinks: "روابط سريعة",
                homeLink: "الصفحة الرئيسية",
                contentLink: "المحتوى العلمي",
                simulationsLink: "المحاكاة التفاعلية",
                aboutLink: "عن المؤلف",
                copyright: "© 2024 جميع الحقوق محفوظة",
                backLink: "العودة للرئيسية"
            },
            en: {
                pageTitle: "Monte Carlo Simulation for X-Ray Imaging",
                pageSubtitle: "Advanced Techniques for Simulating X-Ray Interactions with Matter",
                simulationTag: "Computational Simulation",
                physicsTag: "Radiation Physics",
                interactiveTitle: "Interactive Simulation",
                interactiveDesc: "Experience Monte Carlo simulation of X-ray interactions",
                introTitle: "Introduction to Monte Carlo Simulation",
                introP1: "Monte Carlo methods are among the most accurate and comprehensive approaches for simulating X-ray interaction with matter. These methods rely on statistical simulation of fundamental physical processes, tracking individual photon and electron paths through their multiple interactions with tissues or materials.",
                introP2: "Monte Carlo simulation uses random numbers to determine the path and interactions of each particle, adhering to probability distributions derived from physical theories and experimental data. This methodology allows modeling of complex phenomena such as multiple scattering, secondary interactions, and secondary electron generation, which are difficult to include in simple analytical models.",
                principlesTitle: "Basic Principles of Monte Carlo Simulation",
                randomSamplingTitle: "Random Sampling",
                randomSamplingDesc: "Monte Carlo simulation relies on generating random numbers and transforming them into physical variables using appropriate probability distribution functions. These variables are used to determine:",
                samplingPoint1: "Photon or electron path",
                samplingPoint2: "Free path length between interactions",
                samplingPoint3: "Type of interaction that will occur",
                samplingPoint4: "Scattering angle and transferred energy",
                equationLabel: "Random sampling equation:",
                equationDesc: "where ξ is a uniform random number between 0 and 1, and F⁻¹ is the inverse of the cumulative distribution function",
                animationTitle: "Photon Interaction Simulation",
                animationButton: "Start Simulation",
                interactionTypesTitle: "Simulated Interaction Types",
                photoelectricTitle: "Photoelectric Effect",
                photoelectricDesc: "Complete absorption of the photon and release of an electron from inner atomic shells. This effect dominates at low energies and in materials with high atomic number.",
                comptonTitle: "Compton Scattering",
                comptonDesc: "Interaction of a photon with a free or loosely bound electron, resulting in a scattered photon with lower energy and an ejected electron. This effect dominates in the medium energy range.",
                rayleighTitle: "Rayleigh Scattering",
                rayleighDesc: "Elastic scattering of a photon without energy loss. This interaction occurs with the atom as a whole and only changes the direction of the photon. It is more significant at low energies.",
                codeTitle: "Monte Carlo Simulation Code Example",
                codeExplanation: "This is a simplified example of Monte Carlo simulation code in Python. The code simulates the path of X-ray photons through matter, determining the distance between interactions and the type of interaction (photoelectric, Compton, Rayleigh) randomly based on physical probability distributions. In real applications, more sophisticated programs like MCNP, Geant4, or EGSnrc are used.",
                applicationsTitle: "Applications of Monte Carlo Simulation in X-Ray Imaging",
                app1Title: "Imaging System Design and Optimization",
                app1Desc: "Monte Carlo simulation is used to evaluate new imaging system designs before they are built, saving time and cost. System parameters such as detector geometry, filtration materials, and X-ray settings can be optimized to achieve the best balance between image quality and radiation dose.",
                app2Title: "Radiation Dose Calculation",
                app2Desc: "Monte Carlo simulation provides accurate estimates of radiation dose distribution in different tissues, helping to assess radiation risks and improve imaging protocols. It can be used to calculate dose to sensitive organs and fetuses in cases of imaging pregnant women.",
                app3Title: "Image Quality Analysis",
                app3Desc: "Monte Carlo simulation allows evaluation of image quality metrics such as contrast-to-noise ratio (CNR) and detection of fine details. It can be used to study the effect of various factors such as scatter radiation, electronic noise, and geometric distortions on the final image quality.",
                app4Title: "Training AI Algorithms",
                app4Desc: "Simulation data is used to train deep learning algorithms for image quality improvement and dose reduction. Thousands of synthetic images with different parameters can be generated, providing extensive datasets for training AI models without additional radiation exposure to patients.",
                codesTitle: "Popular Monte Carlo Simulation Codes",
                tableHeader1: "Software",
                tableHeader2: "Developer",
                tableHeader3: "Main Uses",
                tableHeader4: "Features",
                mcnpDev: "Los Alamos National Laboratory",
                mcnpUses: "Radiation protection, shielding calculations, reactor design",
                mcnpFeatures: "Comprehensive package, advanced geometry support, long history of validation",
                geantDev: "European Organization for Nuclear Research (CERN)",
                geantUses: "Particle physics, medical imaging, radiation therapy",
                geantFeatures: "Open-source C++ library, high flexibility, advanced physics models",
                egsDev: "National Research Council Canada",
                egsUses: "X-ray imaging, radiation therapy, dosimetry",
                egsFeatures: "High accuracy in electron and photon transport, good computational speed",
                penelopeDev: "University of Barcelona",
                penelopeUses: "Medical imaging, sample analysis, electron microscopy",
                penelopeFeatures: "High accuracy at low energies, detailed interaction modeling",
                footerTitle: "Medical Learning Platform",
                footerDesc: "A specialized educational platform in the field of medical physics and X-ray imaging techniques, combining in-depth scientific content with interactive applications.",
                quickLinks: "Quick Links",
                homeLink: "Home",
                contentLink: "Scientific Content",
                simulationsLink: "Interactive Simulations",
                aboutLink: "About the Author",
                copyright: "© 2024 All Rights Reserved",
                backLink: "Back to Home"
            }
        };
        
        // Current language
        let currentLanguage = 'ar';
        
        // Function to update UI language
        function updateLanguage(lang) {
            currentLanguage = lang;
            document.documentElement.lang = lang;
            document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
            
            // Update text content for all elements
            Object.keys(translations[lang]).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = translations[lang][key];
                }
            });
            
            // Update language toggle button
            languageText.textContent = lang === 'ar' ? 'English' : 'العربية';
        }
        
        // Toggle language
        languageToggle.addEventListener('click', () => {
            const newLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            updateLanguage(newLanguage);
        });
        
        // Particle animation
        let animationRunning = false;
        let particles = [];
        
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Random starting position at the top
            const startX = particleContainer.offsetWidth / 2;
            const startY = 20;
            
            particle.style.left = `${startX}px`;
            particle.style.top = `${startY}px`;
            
            particleContainer.appendChild(particle);
            
            // Random properties for simulation
            const speed = 0.5 + Math.random() * 1.5;
            const maxBounces = 3 + Math.floor(Math.random() * 5);
            const particleType = Math.random() > 0.7 ? 'photon' : 'electron';
            
            if (particleType === 'electron') {
                particle.style.backgroundColor = 'rgba(239, 68, 68, 0.7)'; // Red for electrons
            }
            
            return {
                element: particle,
                x: startX,
                y: startY,
                directionX: (Math.random() - 0.5) * 2,
                directionY: 0.5 + Math.random() * 0.5,
                speed,
                bounces: 0,
                maxBounces,
                type: particleType,
                alive: true
            };
        }
        
        function updateParticles() {
            if (!animationRunning) return;
            
            // Add new particles occasionally
            if (Math.random() > 0.9 && particles.length < 50) {
                particles.push(createParticle());
            }
            
            // Update existing particles
            particles.forEach(particle => {
                if (!particle.alive) return;
                
                // Move particle
                particle.x += particle.directionX * particle.speed;
                particle.y += particle.directionY * particle.speed;
                
                // Check for interactions
                if (Math.random() > 0.98) {
                    // Simulate an interaction
                    if (particle.type === 'photon') {
                        if (Math.random() > 0.7) {
                            // Photoelectric effect - photon disappears
                            particle.alive = false;
                            particle.element.style.opacity = '0';
                            setTimeout(() => {
                                particleContainer.removeChild(particle.element);
                            }, 500);
                        } else {
                            // Compton or Rayleigh scattering - change direction
                            particle.directionX = (Math.random() - 0.5) * 2;
                            particle.directionY = 0.2 + Math.random() * 0.8;
                            particle.bounces++;
                            
                            // Flash the particle to indicate interaction
                            particle.element.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
                            setTimeout(() => {
                                particle.element.style.backgroundColor = 'rgba(59, 130, 246, 0.7)';
                            }, 100);
                        }
                    } else {
                        // Electron interactions - more random movement
                        particle.directionX = (Math.random() - 0.5) * 2;
                        particle.directionY = (Math.random() - 0.5) * 2;
                        particle.speed *= 0.9; // Slow down
                        particle.bounces++;
                    }
                }
                
                // Check boundaries
                const containerWidth = particleContainer.offsetWidth;
                const containerHeight = particleContainer.offsetHeight;
                
                if (particle.x < 0 || particle.x > containerWidth || 
                    particle.y < 0 || particle.y > containerHeight || 
                    particle.bounces >= particle.maxBounces) {
                    // Remove particle if it's out of bounds or has bounced too many times
                    particle.alive = false;
                    particle.element.style.opacity = '0';
                    setTimeout(() => {
                        if (particle.element.parentNode === particleContainer) {
                            particleContainer.removeChild(particle.element);
                        }
                    }, 500);
                } else {
                    // Update position
                    particle.element.style.left = `${particle.x}px`;
                    particle.element.style.top = `${particle.y}px`;
                }
            });
            
            // Clean up dead particles
            particles = particles.filter(p => p.alive);
            
            requestAnimationFrame(updateParticles);
        }
        
        // Start/stop animation
        startAnimationBtn.addEventListener('click', () => {
            if (animationRunning) {
                animationRunning = false;
                startAnimationBtn.textContent = currentLanguage === 'ar' ? 'بدء المحاكاة' : 'Start Simulation';
                
                // Clear all particles
                particles.forEach(p => {
                    if (p.element.parentNode === particleContainer) {
                        particleContainer.removeChild(p.element);
                    }
                });
                particles = [];
            } else {
                animationRunning = true;
                startAnimationBtn.textContent = currentLanguage === 'ar' ? 'إيقاف المحاكاة' : 'Stop Simulation';
                updateParticles();
            }
        });
        
        // Initialize
        updateLanguage('ar');
    </script>
    <!-- Language Switcher Script -->
    <script src="./js/language-switcher.js"></script>
    
    <!-- Monte Carlo Simulation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get DOM elements
            const particleContainer = document.querySelector('.particle-container');
            const startAnimationButton = document.getElementById('start-animation');
            
            // Animation variables
            let particles = [];
            let animationRunning = false;
            let animationId;
            
            // Event listener for start animation button
            startAnimationButton.addEventListener('click', function() {
                if (animationRunning) {
                    stopAnimation();
                    startAnimationButton.querySelector('#animation-button').textContent = 
                        document.documentElement.lang === 'ar' ? 'بدء المحاكاة' : 'Start Simulation';
                } else {
                    startAnimation();
                    startAnimationButton.querySelector('#animation-button').textContent = 
                        document.documentElement.lang === 'ar' ? 'إيقاف المحاكاة' : 'Stop Simulation';
                }
            });
            
            // Function to start the particle animation
            function startAnimation() {
                if (animationRunning) return;
                
                animationRunning = true;
                particles = [];
                
                // Create initial particles
                createParticle(particleContainer.clientWidth / 2, 20, 0, 1);
                
                // Start animation loop
                animationLoop();
            }
            
            // Function to stop the animation
            function stopAnimation() {
                if (!animationRunning) return;
                
                animationRunning = false;
                cancelAnimationFrame(animationId);
                
                // Remove all particles
                const existingParticles = particleContainer.querySelectorAll('.particle');
                existingParticles.forEach(p => p.remove());
            }
            
            // Function to create a new particle
            function createParticle(x, y, vx, vy) {
                // Create DOM element
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                
                // Add to container
                particleContainer.appendChild(particle);
                
                // Add to particles array
                particles.push({
                    element: particle,
                    x: x,
                    y: y,
                    vx: vx,
                    vy: vy,
                    energy: 100,
                    interactions: 0,
                    maxInteractions: 5 + Math.floor(Math.random() * 5)
                });
            }
            
            // Function to update particle positions and handle interactions
            function updateParticles() {
                const containerWidth = particleContainer.clientWidth;
                const containerHeight = particleContainer.clientHeight;
                
                // Create new photons occasionally
                if (Math.random() < 0.05 && particles.length < 50) {
                    createParticle(containerWidth / 2, 20, 0, 1);
                }
                
                // Update each particle
                for (let i = particles.length - 1; i >= 0; i--) {
                    const p = particles[i];
                    
                    // Move particle
                    p.x += p.vx;
                    p.y += p.vy;
                    
                    // Update DOM element
                    p.element.style.left = p.x + 'px';
                    p.element.style.top = p.y + 'px';
                    
                    // Check for interaction (random chance based on position)
                    if (p.y > 50 && Math.random() < 0.03 && p.interactions < p.maxInteractions) {
                        // Increment interaction counter
                        p.interactions++;
                        
                        // Determine interaction type
                        const interactionType = Math.random();
                        
                        if (interactionType < 0.2) {
                            // Photoelectric effect - particle is absorbed
                            p.element.remove();
                            particles.splice(i, 1);
                        } else if (interactionType < 0.8) {
                            // Compton scattering - change direction and create secondary electron
                            const angle = Math.random() * Math.PI * 2;
                            p.vx = Math.cos(angle);
                            p.vy = Math.sin(angle);
                            p.energy *= 0.8;
                            
                            // Create secondary electron
                            if (Math.random() < 0.5) {
                                const electronAngle = angle + Math.PI / 2 + (Math.random() - 0.5);
                                createParticle(
                                    p.x, 
                                    p.y, 
                                    Math.cos(electronAngle) * 0.5, 
                                    Math.sin(electronAngle) * 0.5
                                );
                            }
                            
                            // Visual feedback for interaction
                            p.element.style.backgroundColor = 'rgba(59, 130, 246, 0.7)';
                            setTimeout(() => {
                                if (p.element.parentNode) {
                                    p.element.style.backgroundColor = 'rgba(59, 130, 246, 0.7)';
                                }
                            }, 100);
                        } else {
                            // Rayleigh scattering - just change direction
                            const angle = Math.random() * Math.PI * 2;
                            p.vx = Math.cos(angle);
                            p.vy = Math.sin(angle);
                            
                            // Visual feedback for interaction
                            p.element.style.backgroundColor = 'rgba(124, 58, 237, 0.7)';
                            setTimeout(() => {
                                if (p.element.parentNode) {
                                    p.element.style.backgroundColor = 'rgba(59, 130, 246, 0.7)';
                                }
                            }, 100);
                        }
                    }
                    
                    // Remove particles that leave the container
                    if (p.x < 0 || p.x > containerWidth || p.y < 0 || p.y > containerHeight) {
                        p.element.remove();
                        particles.splice(i, 1);
                    }
                }
            }
            
            // Animation loop
            function animationLoop() {
                if (!animationRunning) return;
                
                updateParticles();
                animationId = requestAnimationFrame(animationLoop);
            }
        });
    </script>
</body>
</html>