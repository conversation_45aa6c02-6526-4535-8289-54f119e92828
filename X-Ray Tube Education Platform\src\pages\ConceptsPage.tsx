import { useState } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Atom,
  Zap,
  Target,
  Shield,
  Activity,
  BookOpen,
  ChevronRight,
  CheckCircle } from
'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const ConceptsPage = () => {
  const [completedConcepts, setCompletedConcepts] = useState<string[]>([]);
  const { t } = useLanguage();

  const conceptCategories = [
  {
    id: 'basics',
    title: t('concepts.basics'),
    icon: BookOpen,
    concepts: [
    {
      id: 'electron-flow',
      title: t('concepts.electronFlow'),
      description: t('concepts.electronFlowDesc'),
      content: `Understanding electron flow is fundamental to X-ray tube operation.

Key Points:
• Electrons are produced at the cathode through thermionic emission
• High voltage accelerates electrons toward the anode
• Electron velocity determines X-ray energy
• Current controls the number of electrons

The Process:
1. Cathode heating produces electron cloud
2. High voltage (kVp) accelerates electrons
3. Electrons strike the anode target
4. X-rays are produced upon impact`,
      difficulty: 'Beginner'
    },
    {
      id: 'xray-production',
      title: t('concepts.xrayProduction'),
      description: t('concepts.xrayProductionDesc'),
      content: `X-rays are produced when high-speed electrons interact with the anode target.

Two Production Mechanisms:
1. Bremsstrahlung (Braking Radiation):
   • Continuous spectrum
   • Electrons decelerate in target material
   • Energy converted to X-ray photons

2. Characteristic Radiation:
   • Discrete energy levels
   • Inner shell electron ejection
   • Specific to target material

Energy Conversion:
• Only ~1% becomes X-rays
• Remaining 99% becomes heat`,
      difficulty: 'Intermediate'
    },
    {
      id: 'heat-management',
      title: t('concepts.heatManagement'),
      description: t('concepts.heatManagementDesc'),
      content: `Heat management is critical for X-ray tube operation and longevity.

Heat Generation:
• 99% of electron energy becomes heat
• Concentrated at anode focal spot
• Can damage tube if not managed

Cooling Methods:
1. Radiation cooling
2. Conduction through anode stem
3. Convection in oil bath
4. Active cooling systems

Rotating Anode Benefits:
• Distributes heat over larger area
• Increases heat capacity
• Extends tube life`,
      difficulty: 'Intermediate'
    }]

  },
  {
    id: 'advanced',
    title: t('concepts.advanced'),
    icon: Atom,
    concepts: [
    {
      id: 'kvp-effects',
      title: t('concepts.kvpEffects'),
      description: t('concepts.kvpEffectsDesc'),
      content: `Tube voltage (kVp) is a critical parameter affecting X-ray beam characteristics.

Effects of Increasing kVp:
• Higher maximum photon energy
• Increased beam penetration
• More X-ray quantity
• Shorter exposure times possible

Quality vs Quantity:
• kVp primarily affects beam quality (penetration)
• Higher kVp = more penetrating beam
• Affects contrast in imaging

Optimal kVp Selection:
• Depends on patient thickness
• Body part being imaged
• Desired image contrast`,
      difficulty: 'Advanced'
    },
    {
      id: 'mas-effects',
      title: t('concepts.masEffects'),
      description: t('concepts.masEffectsDesc'),
      content: `mAs (milliampere-seconds) controls X-ray quantity.

mAs Formula:
mAs = mA × time (seconds)

Effects of mAs:
• Directly proportional to X-ray quantity
• Does not affect beam quality
• Controls image brightness/density

Clinical Applications:
• Thicker patients need more mAs
• Motion considerations affect time selection
• Reciprocity law: mA and time are interchangeable`,
      difficulty: 'Advanced'
    },
    {
      id: 'beam-filtration',
      title: t('concepts.beamFiltration'),
      description: t('concepts.beamFiltrationDesc'),
      content: `Beam filtration improves X-ray beam quality by removing low-energy photons.

Types of Filtration:
1. Inherent Filtration:
   • Glass envelope
   • Oil in housing
   • Collimator materials

2. Added Filtration:
   • Aluminum filters most common
   • Copper for high-energy beams
   • Rare earth filters for specialized use

Benefits:
• Removes soft radiation
• Reduces patient dose
• Improves image quality
• Hardens the beam`,
      difficulty: 'Advanced'
    }]

  },
  {
    id: 'physics',
    title: t('concepts.physics'),
    icon: Shield,
    concepts: [
    {
      id: 'interaction',
      title: 'Radiation Interaction',
      description: 'How radiation interacts with different materials and tissues',
      content: `Radiation interaction with matter determines imaging characteristics.

Primary Interactions:
1. Photoelectric Effect:
   • Complete photon absorption
   • Predominant at low energies
   • High contrast imaging

2. Compton Scattering:
   • Partial energy transfer
   • Predominant at medium energies
   • Scatter radiation source

3. Pair Production:
   • High energy interactions
   • Not relevant for diagnostic imaging

Factors Affecting Interaction:
• Photon energy
• Atomic number of material
• Material density`,
      difficulty: 'Advanced'
    }]

  }];


  const markAsCompleted = (conceptId: string) => {
    if (!completedConcepts.includes(conceptId)) {
      setCompletedConcepts([...completedConcepts, conceptId]);
    }
  };

  const totalConcepts = conceptCategories.reduce((total, category) => total + category.concepts.length, 0);
  const progressPercentage = completedConcepts.length / totalConcepts * 100;

  return (
    <div className="min-h-screen py-8" data-id="o58obbumo" data-path="src/pages/ConceptsPage.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="yruex3xrf" data-path="src/pages/ConceptsPage.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12" data-id="8o8soxhf4" data-path="src/pages/ConceptsPage.tsx">

          <Badge variant="secondary" className="mb-4" data-id="cqu2t01z3" data-path="src/pages/ConceptsPage.tsx">{t('concepts.title')}</Badge>
          <h1 className="text-4xl font-bold text-gray-900 mb-4" data-id="7svwn4gno" data-path="src/pages/ConceptsPage.tsx">
            {t('concepts.title')}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8" data-id="sysm0a4ky" data-path="src/pages/ConceptsPage.tsx">
            {t('concepts.subtitle')}
          </p>
          
          {/* Progress */}
          <div className="max-w-md mx-auto" data-id="mrl3a77ah" data-path="src/pages/ConceptsPage.tsx">
            <div className="flex justify-between items-center mb-2" data-id="e6p5icpxb" data-path="src/pages/ConceptsPage.tsx">
              <span className="text-sm text-gray-600" data-id="smdm576r5" data-path="src/pages/ConceptsPage.tsx">{t('concepts.progress')}</span>
              <span className="text-sm font-medium" data-id="tdx0hj7al" data-path="src/pages/ConceptsPage.tsx">{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" data-id="ie3hq50ot" data-path="src/pages/ConceptsPage.tsx" />
            <p className="text-xs text-gray-500 mt-2" data-id="l5tsql7cr" data-path="src/pages/ConceptsPage.tsx">
              {completedConcepts.length} of {totalConcepts} concepts completed
            </p>
          </div>
        </motion.div>

        {/* Concepts */}
        <Tabs defaultValue="basics" className="w-full" data-id="433g7zffk" data-path="src/pages/ConceptsPage.tsx">
          <TabsList className="grid w-full grid-cols-3 mb-8" data-id="q41dx59tl" data-path="src/pages/ConceptsPage.tsx">
            {conceptCategories.map((category) => {
              const Icon = category.icon;
              return (
                <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2" data-id="itagfva4z" data-path="src/pages/ConceptsPage.tsx">
                  <Icon size={18} data-id="1ndsg1fcn" data-path="src/pages/ConceptsPage.tsx" />
                  {category.title}
                </TabsTrigger>);

            })}
          </TabsList>

          {conceptCategories.map((category) =>
          <TabsContent key={category.id} value={category.id} data-id="ri5ueqfl2" data-path="src/pages/ConceptsPage.tsx">
              <div className="grid gap-6" data-id="zvrx9njiw" data-path="src/pages/ConceptsPage.tsx">
                {category.concepts.map((concept, index) =>
              <motion.div
                key={concept.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }} data-id="a60owu0lt" data-path="src/pages/ConceptsPage.tsx">

                    <Card className="overflow-hidden hover:shadow-lg transition-shadow" data-id="qd6rgpq4v" data-path="src/pages/ConceptsPage.tsx">
                      <CardHeader data-id="8i8ytc5om" data-path="src/pages/ConceptsPage.tsx">
                        <div className="flex items-start justify-between" data-id="q1rnt7f4n" data-path="src/pages/ConceptsPage.tsx">
                          <div className="flex-1" data-id="h32svgg0a" data-path="src/pages/ConceptsPage.tsx">
                            <div className="flex items-center gap-2 mb-2" data-id="0gemqfvms" data-path="src/pages/ConceptsPage.tsx">
                              <CardTitle className="text-xl" data-id="coccvial4" data-path="src/pages/ConceptsPage.tsx">{concept.title}</CardTitle>
                              {completedConcepts.includes(concept.id) &&
                          <CheckCircle className="w-5 h-5 text-green-500" data-id="s7t1chzbp" data-path="src/pages/ConceptsPage.tsx" />
                          }
                            </div>
                            <CardDescription className="text-base" data-id="h0xo4n492" data-path="src/pages/ConceptsPage.tsx">
                              {concept.description}
                            </CardDescription>
                          </div>
                          <Badge variant={
                      concept.difficulty === 'Beginner' ? 'secondary' :
                      concept.difficulty === 'Intermediate' ? 'default' : 'destructive'
                      } data-id="xum3bzl2m" data-path="src/pages/ConceptsPage.tsx">
                            {concept.difficulty}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent data-id="9tzjsjy1u" data-path="src/pages/ConceptsPage.tsx">
                        <div className="prose prose-sm max-w-none mb-4" data-id="ictsldyk5" data-path="src/pages/ConceptsPage.tsx">
                          <div className="whitespace-pre-line text-gray-700 leading-relaxed" data-id="480ckegy2" data-path="src/pages/ConceptsPage.tsx">
                            {concept.content}
                          </div>
                        </div>
                        <div className="flex justify-between items-center" data-id="3hcwqroms" data-path="src/pages/ConceptsPage.tsx">
                          <Button
                        onClick={() => markAsCompleted(concept.id)}
                        disabled={completedConcepts.includes(concept.id)}
                        variant={completedConcepts.includes(concept.id) ? "secondary" : "default"} data-id="eqhuxdmee" data-path="src/pages/ConceptsPage.tsx">

                            {completedConcepts.includes(concept.id) ?
                        <>
                                <CheckCircle className="w-4 h-4 mr-2" data-id="7oazxsnfn" data-path="src/pages/ConceptsPage.tsx" />
                                Completed
                              </> :

                        <>
                                Mark Complete
                                <ChevronRight className="w-4 h-4 ml-2" data-id="qmdd97rpm" data-path="src/pages/ConceptsPage.tsx" />
                              </>
                        }
                          </Button>
                          <div className="text-sm text-gray-500" data-id="5rocw6otj" data-path="src/pages/ConceptsPage.tsx">
                            {Math.ceil(concept.content.length / 1000)} min read
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
              )}
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>);

};

export default ConceptsPage;