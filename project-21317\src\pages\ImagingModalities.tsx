import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import XRaySimulation from '@/components/simulations/XRaySimulation';
import { Camera, Zap, Radio, Activity, Waves } from 'lucide-react';

const ImagingModalities = () => {
  const [activeModality, setActiveModality] = useState('xray');

  const modalities = [
  {
    id: 'xray',
    title: 'X-Ray Imaging',
    description: 'Projection radiography using X-ray photons',
    icon: Camera,
    energyRange: '20-150 kVp',
    principle: 'Differential attenuation'
  },
  {
    id: 'ct',
    title: 'Computed Tomography',
    description: 'Cross-sectional imaging using X-rays',
    icon: Zap,
    energyRange: '80-140 kVp',
    principle: 'Tomographic reconstruction'
  },
  {
    id: 'mri',
    title: 'Magnetic Resonance',
    description: 'Nuclear magnetic resonance imaging',
    icon: Radio,
    energyRange: 'RF waves',
    principle: 'Spin relaxation'
  },
  {
    id: 'nuclear',
    title: 'Nuclear Medicine',
    description: 'Functional imaging with radioisotopes',
    icon: Activity,
    energyRange: '140-511 keV',
    principle: 'Gamma detection'
  },
  {
    id: 'ultrasound',
    title: 'Ultrasound',
    description: 'Sound wave imaging',
    icon: Waves,
    energyRange: '2-15 MHz',
    principle: 'Echo reflection'
  }];


  const renderSimulation = () => {
    switch (activeModality) {
      case 'xray':
        return <XRaySimulation data-id="jj5wa8e7r" data-path="src/pages/ImagingModalities.tsx" />;
      case 'ct':
        return <div className="flex items-center justify-center h-96 text-gray-500" data-id="f3ird7e8l" data-path="src/pages/ImagingModalities.tsx">CT Simulation - Coming Soon</div>;
      case 'mri':
        return <div className="flex items-center justify-center h-96 text-gray-500" data-id="khvxa07gy" data-path="src/pages/ImagingModalities.tsx">MRI Simulation - Coming Soon</div>;
      case 'nuclear':
        return <div className="flex items-center justify-center h-96 text-gray-500" data-id="fuixntx38" data-path="src/pages/ImagingModalities.tsx">Nuclear Medicine Simulation - Coming Soon</div>;
      case 'ultrasound':
        return <div className="flex items-center justify-center h-96 text-gray-500" data-id="vbpt2cxgi" data-path="src/pages/ImagingModalities.tsx">Ultrasound Simulation - Coming Soon</div>;
      default:
        return <XRaySimulation data-id="uk5qfdk34" data-path="src/pages/ImagingModalities.tsx" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50" data-id="avvbnzgjc" data-path="src/pages/ImagingModalities.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" data-id="ayagikjgs" data-path="src/pages/ImagingModalities.tsx">
        {/* Header */}
        <div className="text-center mb-12" data-id="qkwuw7eev" data-path="src/pages/ImagingModalities.tsx">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4" data-id="s2nljoyx2" data-path="src/pages/ImagingModalities.tsx">
            Medical Imaging Modalities
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="ev1f3hyko" data-path="src/pages/ImagingModalities.tsx">
            Explore different medical imaging techniques and understand their underlying physics principles
          </p>
        </div>

        {/* Modality Overview */}
        <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-4 mb-12" data-id="zq470k5bk" data-path="src/pages/ImagingModalities.tsx">
          {modalities.map((modality) => {
            const Icon = modality.icon;
            const isActive = activeModality === modality.id;

            return (
              <Card
                key={modality.id}
                className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${
                isActive ? 'ring-2 ring-green-500 shadow-lg' : ''}`
                }
                onClick={() => setActiveModality(modality.id)} data-id="krdpvd606" data-path="src/pages/ImagingModalities.tsx">

                <CardHeader className="text-center pb-2" data-id="mi77gbh3a" data-path="src/pages/ImagingModalities.tsx">
                  <div className={`w-12 h-12 mx-auto mb-2 rounded-lg flex items-center justify-center transition-colors duration-300 ${
                  isActive ? 'bg-green-600' : 'bg-gray-100'}`
                  } data-id="g8flj79wj" data-path="src/pages/ImagingModalities.tsx">
                    <Icon className={`w-6 h-6 ${isActive ? 'text-white' : 'text-gray-600'}`} data-id="f2c7hvhga" data-path="src/pages/ImagingModalities.tsx" />
                  </div>
                  <CardTitle className="text-sm" data-id="78fwbtusg" data-path="src/pages/ImagingModalities.tsx">{modality.title}</CardTitle>
                </CardHeader>
                <CardContent className="text-center pt-0 space-y-2" data-id="e2r86qls6" data-path="src/pages/ImagingModalities.tsx">
                  <Badge variant="outline" className="text-xs" data-id="7y9tc8z8a" data-path="src/pages/ImagingModalities.tsx">
                    {modality.energyRange}
                  </Badge>
                  <p className="text-xs text-gray-500" data-id="0oq2eq2m7" data-path="src/pages/ImagingModalities.tsx">{modality.principle}</p>
                </CardContent>
              </Card>);

          })}
        </div>

        {/* Main Simulation Area */}
        <div className="grid lg:grid-cols-4 gap-8" data-id="dcsrvhznm" data-path="src/pages/ImagingModalities.tsx">
          {/* Simulation Controls */}
          <div className="lg:col-span-1" data-id="1crdgkl6p" data-path="src/pages/ImagingModalities.tsx">
            <Card className="sticky top-24" data-id="rxcvg85z4" data-path="src/pages/ImagingModalities.tsx">
              <CardHeader data-id="glecs248y" data-path="src/pages/ImagingModalities.tsx">
                <CardTitle className="flex items-center gap-2" data-id="2qke7ee8r" data-path="src/pages/ImagingModalities.tsx">
                  <Camera className="w-5 h-5" data-id="ztjao8bl8" data-path="src/pages/ImagingModalities.tsx" />
                  Imaging Parameters
                </CardTitle>
              </CardHeader>
              <CardContent data-id="v8o0ec2ox" data-path="src/pages/ImagingModalities.tsx">
                <Tabs value={activeModality} onValueChange={setActiveModality} data-id="4ms4zmp69" data-path="src/pages/ImagingModalities.tsx">
                  <TabsList className="grid w-full grid-cols-1 gap-1 h-auto bg-transparent" data-id="74we2s8wd" data-path="src/pages/ImagingModalities.tsx">
                    {modalities.map((modality) =>
                    <TabsTrigger
                      key={modality.id}
                      value={modality.id}
                      className="w-full text-left justify-start text-xs data-[state=active]:bg-green-600 data-[state=active]:text-white" data-id="xihv607go" data-path="src/pages/ImagingModalities.tsx">

                        {modality.title}
                      </TabsTrigger>
                    )}
                  </TabsList>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Simulation Display */}
          <div className="lg:col-span-3" data-id="0y052paml" data-path="src/pages/ImagingModalities.tsx">
            <Card data-id="99e223vfq" data-path="src/pages/ImagingModalities.tsx">
              <CardHeader data-id="5mzo7d7lt" data-path="src/pages/ImagingModalities.tsx">
                <CardTitle data-id="hzyeconv4" data-path="src/pages/ImagingModalities.tsx">
                  {modalities.find((m) => m.id === activeModality)?.title} Simulation
                </CardTitle>
                <CardDescription data-id="b2m9w47l1" data-path="src/pages/ImagingModalities.tsx">
                  {modalities.find((m) => m.id === activeModality)?.description}
                </CardDescription>
              </CardHeader>
              <CardContent data-id="y6xpg2jz3" data-path="src/pages/ImagingModalities.tsx">
                {renderSimulation()}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Educational Content */}
        <div className="mt-16 grid md:grid-cols-2 lg:grid-cols-3 gap-8" data-id="4gkr8utpy" data-path="src/pages/ImagingModalities.tsx">
          <Card data-id="dhq2qjghf" data-path="src/pages/ImagingModalities.tsx">
            <CardHeader data-id="9ulxjp8mc" data-path="src/pages/ImagingModalities.tsx">
              <CardTitle data-id="9prbiwjjp" data-path="src/pages/ImagingModalities.tsx">X-Ray & CT Imaging</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3" data-id="hd3ptpiwi" data-path="src/pages/ImagingModalities.tsx">
              <div data-id="925h1l79l" data-path="src/pages/ImagingModalities.tsx">
                <h4 className="font-semibold text-gray-900 mb-1" data-id="15b1map0l" data-path="src/pages/ImagingModalities.tsx">Principle</h4>
                <p className="text-gray-600 text-sm" data-id="no2toka1o" data-path="src/pages/ImagingModalities.tsx">
                  Based on differential attenuation of X-rays by tissues with different densities and atomic numbers.
                </p>
              </div>
              <div data-id="v5naub8mi" data-path="src/pages/ImagingModalities.tsx">
                <h4 className="font-semibold text-gray-900 mb-1" data-id="jrb9xw59t" data-path="src/pages/ImagingModalities.tsx">Image Formation</h4>
                <p className="text-gray-600 text-sm" data-id="byeo54392" data-path="src/pages/ImagingModalities.tsx">
                  Projection (X-ray) or tomographic reconstruction (CT) creates images showing internal structures.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card data-id="usp0m27c5" data-path="src/pages/ImagingModalities.tsx">
            <CardHeader data-id="dp3zzc1jk" data-path="src/pages/ImagingModalities.tsx">
              <CardTitle data-id="b9rs3zvdg" data-path="src/pages/ImagingModalities.tsx">MRI Imaging</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3" data-id="2rwn9i52l" data-path="src/pages/ImagingModalities.tsx">
              <div data-id="rsv3humuh" data-path="src/pages/ImagingModalities.tsx">
                <h4 className="font-semibold text-gray-900 mb-1" data-id="j80j7merj" data-path="src/pages/ImagingModalities.tsx">Principle</h4>
                <p className="text-gray-600 text-sm" data-id="ikvagn0cx" data-path="src/pages/ImagingModalities.tsx">
                  Uses nuclear magnetic resonance of hydrogen atoms in magnetic fields to create detailed images.
                </p>
              </div>
              <div data-id="de8zs7uve" data-path="src/pages/ImagingModalities.tsx">
                <h4 className="font-semibold text-gray-900 mb-1" data-id="h3ahps6mf" data-path="src/pages/ImagingModalities.tsx">Contrast Mechanisms</h4>
                <p className="text-gray-600 text-sm" data-id="qk3g2m5f3" data-path="src/pages/ImagingModalities.tsx">
                  T1, T2 relaxation times and proton density determine image contrast and tissue differentiation.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card data-id="fephd3sp9" data-path="src/pages/ImagingModalities.tsx">
            <CardHeader data-id="enhle1mrn" data-path="src/pages/ImagingModalities.tsx">
              <CardTitle data-id="lasd5ewvh" data-path="src/pages/ImagingModalities.tsx">Nuclear Medicine</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3" data-id="p2aike7rq" data-path="src/pages/ImagingModalities.tsx">
              <div data-id="2kuqyxvt5" data-path="src/pages/ImagingModalities.tsx">
                <h4 className="font-semibold text-gray-900 mb-1" data-id="0n0driaak" data-path="src/pages/ImagingModalities.tsx">Principle</h4>
                <p className="text-gray-600 text-sm" data-id="uknd7n4mv" data-path="src/pages/ImagingModalities.tsx">
                  Functional imaging using radioactive tracers that accumulate in specific organs or tissues.
                </p>
              </div>
              <div data-id="yxp3n2wdz" data-path="src/pages/ImagingModalities.tsx">
                <h4 className="font-semibold text-gray-900 mb-1" data-id="p1tlc0m5c" data-path="src/pages/ImagingModalities.tsx">Detection</h4>
                <p className="text-gray-600 text-sm" data-id="h77jjxnda" data-path="src/pages/ImagingModalities.tsx">
                  Gamma cameras detect radiation emitted by radioisotopes to create functional images.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Comparison Table */}
        <div className="mt-16" data-id="g04oovg3u" data-path="src/pages/ImagingModalities.tsx">
          <Card data-id="aafqximkf" data-path="src/pages/ImagingModalities.tsx">
            <CardHeader data-id="msv2edwdz" data-path="src/pages/ImagingModalities.tsx">
              <CardTitle data-id="3jkagal0j" data-path="src/pages/ImagingModalities.tsx">Modality Comparison</CardTitle>
              <CardDescription data-id="s8uhn7axe" data-path="src/pages/ImagingModalities.tsx">
                Key characteristics of different imaging modalities
              </CardDescription>
            </CardHeader>
            <CardContent data-id="pknt8gw99" data-path="src/pages/ImagingModalities.tsx">
              <div className="overflow-x-auto" data-id="lqh9cpwox" data-path="src/pages/ImagingModalities.tsx">
                <table className="w-full text-sm" data-id="pslyw7q4c" data-path="src/pages/ImagingModalities.tsx">
                  <thead data-id="8bh8z3m72" data-path="src/pages/ImagingModalities.tsx">
                    <tr className="border-b" data-id="fre8kpjct" data-path="src/pages/ImagingModalities.tsx">
                      <th className="text-left p-2" data-id="eli86komg" data-path="src/pages/ImagingModalities.tsx">Modality</th>
                      <th className="text-left p-2" data-id="ojk2grprd" data-path="src/pages/ImagingModalities.tsx">Radiation Type</th>
                      <th className="text-left p-2" data-id="ej6b2qm79" data-path="src/pages/ImagingModalities.tsx">Image Type</th>
                      <th className="text-left p-2" data-id="cbefusxsk" data-path="src/pages/ImagingModalities.tsx">Best For</th>
                      <th className="text-left p-2" data-id="xmkvbahz7" data-path="src/pages/ImagingModalities.tsx">Limitations</th>
                    </tr>
                  </thead>
                  <tbody data-id="81kv6rksj" data-path="src/pages/ImagingModalities.tsx">
                    <tr className="border-b" data-id="q46drrb0c" data-path="src/pages/ImagingModalities.tsx">
                      <td className="p-2 font-medium" data-id="oagso0nki" data-path="src/pages/ImagingModalities.tsx">X-Ray</td>
                      <td className="p-2" data-id="apmibxfqu" data-path="src/pages/ImagingModalities.tsx">Ionizing</td>
                      <td className="p-2" data-id="70i93j6cr" data-path="src/pages/ImagingModalities.tsx">Anatomical</td>
                      <td className="p-2" data-id="98lpf7hd9" data-path="src/pages/ImagingModalities.tsx">Bones, chest</td>
                      <td className="p-2" data-id="nrvtu8hj3" data-path="src/pages/ImagingModalities.tsx">Limited soft tissue contrast</td>
                    </tr>
                    <tr className="border-b" data-id="sv0r71u9u" data-path="src/pages/ImagingModalities.tsx">
                      <td className="p-2 font-medium" data-id="rhzs16vkg" data-path="src/pages/ImagingModalities.tsx">CT</td>
                      <td className="p-2" data-id="ikqxu4fmv" data-path="src/pages/ImagingModalities.tsx">Ionizing</td>
                      <td className="p-2" data-id="gma1k61qj" data-path="src/pages/ImagingModalities.tsx">Anatomical</td>
                      <td className="p-2" data-id="ritmr3u17" data-path="src/pages/ImagingModalities.tsx">Cross-sectional anatomy</td>
                      <td className="p-2" data-id="44sp16px1" data-path="src/pages/ImagingModalities.tsx">Radiation dose</td>
                    </tr>
                    <tr className="border-b" data-id="2949c1jzj" data-path="src/pages/ImagingModalities.tsx">
                      <td className="p-2 font-medium" data-id="oj2vjt9ba" data-path="src/pages/ImagingModalities.tsx">MRI</td>
                      <td className="p-2" data-id="hzoa9x3cp" data-path="src/pages/ImagingModalities.tsx">Non-ionizing</td>
                      <td className="p-2" data-id="jt0owvq3r" data-path="src/pages/ImagingModalities.tsx">Anatomical</td>
                      <td className="p-2" data-id="8ej4z4zie" data-path="src/pages/ImagingModalities.tsx">Soft tissues, brain</td>
                      <td className="p-2" data-id="x14f1q0tl" data-path="src/pages/ImagingModalities.tsx">Long scan times</td>
                    </tr>
                    <tr className="border-b" data-id="li33ymcsr" data-path="src/pages/ImagingModalities.tsx">
                      <td className="p-2 font-medium" data-id="8ytqy0yay" data-path="src/pages/ImagingModalities.tsx">Nuclear</td>
                      <td className="p-2" data-id="jd6vjg75c" data-path="src/pages/ImagingModalities.tsx">Ionizing</td>
                      <td className="p-2" data-id="2t1vn1nz4" data-path="src/pages/ImagingModalities.tsx">Functional</td>
                      <td className="p-2" data-id="oig7cclbe" data-path="src/pages/ImagingModalities.tsx">Organ function</td>
                      <td className="p-2" data-id="wm3ga2pll" data-path="src/pages/ImagingModalities.tsx">Limited resolution</td>
                    </tr>
                    <tr data-id="6db89qqcs" data-path="src/pages/ImagingModalities.tsx">
                      <td className="p-2 font-medium" data-id="hm4f9bry8" data-path="src/pages/ImagingModalities.tsx">Ultrasound</td>
                      <td className="p-2" data-id="bg4zfohea" data-path="src/pages/ImagingModalities.tsx">Non-ionizing</td>
                      <td className="p-2" data-id="90wlt3kgc" data-path="src/pages/ImagingModalities.tsx">Real-time</td>
                      <td className="p-2" data-id="wdo4hhztq" data-path="src/pages/ImagingModalities.tsx">Soft tissues, cardiac</td>
                      <td className="p-2" data-id="98ndtogsi" data-path="src/pages/ImagingModalities.tsx">Operator dependent</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>);

};

export default ImagingModalities;