import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Shield, Layers, Zap } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const ShieldingCalculator = () => {
  const { toast } = useToast();

  const [shieldMaterial, setShieldMaterial] = useState('lead');
  const [thickness, setThickness] = useState('2');
  const [energy, setEnergy] = useState('100');
  const [initialIntensity, setInitialIntensity] = useState('1000');
  const [results, setResults] = useState(null);

  // Material properties (linear attenuation coefficients at 100 keV, cm⁻¹)
  const materials = {
    lead: {
      name: 'Lead (Pb)',
      density: 11.34, // g/cm³
      mu: 5.88, // cm⁻¹ at 100 keV
      hvl: 0.12, // mm
      color: '#64748b'
    },
    concrete: {
      name: 'Concrete',
      density: 2.3,
      mu: 0.16,
      hvl: 43,
      color: '#9ca3af'
    },
    steel: {
      name: 'Steel',
      density: 7.9,
      mu: 0.67,
      hvl: 10.3,
      color: '#374151'
    },
    aluminum: {
      name: 'Aluminum',
      density: 2.7,
      mu: 0.23,
      hvl: 30,
      color: '#d1d5db'
    },
    copper: {
      name: 'Copper',
      density: 8.96,
      mu: 1.41,
      hvl: 4.9,
      color: '#92400e'
    },
    tungsten: {
      name: 'Tungsten',
      density: 19.3,
      mu: 8.1,
      hvl: 0.85,
      color: '#1f2937'
    }
  };

  const calculateShielding = () => {
    try {
      const t = parseFloat(thickness);
      const e = parseFloat(energy);
      const i0 = parseFloat(initialIntensity);

      if (!t || !e || !i0) {
        toast({
          title: "Input Error",
          description: "Please fill in all required fields with valid numbers.",
          variant: "destructive"
        });
        return;
      }

      const material = materials[shieldMaterial];

      // Energy-dependent attenuation coefficient (simplified)
      const energyFactor = Math.pow(100 / e, 2.5); // Rough approximation
      const muEffective = material.mu * energyFactor;

      // Calculate transmission
      const transmission = Math.exp(-muEffective * t);
      const transmittedIntensity = i0 * transmission;
      const attenuationFactor = i0 / transmittedIntensity;
      const percentageReduction = (1 - transmission) * 100;

      // Calculate number of HVLs
      const hvlCount = t / (material.hvl / 10); // Convert mm to cm

      // Calculate required thickness for different reduction factors
      const reductionFactors = [2, 10, 100, 1000];
      const requiredThicknesses = reductionFactors.map((factor) => ({
        factor: factor,
        thickness: Math.log(factor) / muEffective,
        hvls: Math.log(factor) / Math.log(2)
      }));

      setResults({
        transmission: transmission,
        transmittedIntensity: transmittedIntensity,
        attenuationFactor: attenuationFactor,
        percentageReduction: percentageReduction,
        hvlCount: hvlCount,
        requiredThicknesses: requiredThicknesses,
        material: material
      });

      toast({
        title: "Calculation Complete",
        description: "Shielding calculations have been updated."
      });

    } catch (error) {
      toast({
        title: "Calculation Error",
        description: "Please check your inputs and try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-8" data-id="frqqrp80g" data-path="src/components/ShieldingCalculator.tsx">
      {/* Input Parameters */}
      <div className="grid md:grid-cols-2 gap-8" data-id="x0u7komf" data-path="src/components/ShieldingCalculator.tsx">
        <Card data-id="axvjb31kb" data-path="src/components/ShieldingCalculator.tsx">
          <CardHeader data-id="ktttooamy" data-path="src/components/ShieldingCalculator.tsx">
            <CardTitle className="flex items-center gap-2" data-id="s89ik385m" data-path="src/components/ShieldingCalculator.tsx">
              <Shield className="w-5 h-5" data-id="kme03ctum" data-path="src/components/ShieldingCalculator.tsx" />
              Shielding Parameters
            </CardTitle>
            <CardDescription data-id="l1adifbg6" data-path="src/components/ShieldingCalculator.tsx">
              Configure the shielding material and radiation conditions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4" data-id="rdnzbwbp8" data-path="src/components/ShieldingCalculator.tsx">
            <div data-id="m8x6347nk" data-path="src/components/ShieldingCalculator.tsx">
              <Label htmlFor="shield-material" data-id="1crajctf4" data-path="src/components/ShieldingCalculator.tsx">Shielding Material</Label>
              <Select value={shieldMaterial} onValueChange={setShieldMaterial} data-id="27l59586j" data-path="src/components/ShieldingCalculator.tsx">
                <SelectTrigger data-id="heubkkjod" data-path="src/components/ShieldingCalculator.tsx">
                  <SelectValue data-id="tdvsv7col" data-path="src/components/ShieldingCalculator.tsx" />
                </SelectTrigger>
                <SelectContent data-id="b7b3wmda5" data-path="src/components/ShieldingCalculator.tsx">
                  {Object.entries(materials).map(([key, material]) =>
                  <SelectItem key={key} value={key} data-id="g2czzq11e" data-path="src/components/ShieldingCalculator.tsx">
                      {material.name} (ρ = {material.density} g/cm³)
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div data-id="he56pgtjb" data-path="src/components/ShieldingCalculator.tsx">
              <Label htmlFor="thickness" data-id="850icf2ad" data-path="src/components/ShieldingCalculator.tsx">Thickness (cm)</Label>
              <Input
                id="thickness"
                type="number"
                value={thickness}
                onChange={(e) => setThickness(e.target.value)}
                placeholder="2"
                step="0.1" data-id="2bop6vklq" data-path="src/components/ShieldingCalculator.tsx" />

            </div>

            <div data-id="d5iznrbmb" data-path="src/components/ShieldingCalculator.tsx">
              <Label htmlFor="energy" data-id="zqvi79rqu" data-path="src/components/ShieldingCalculator.tsx">Photon Energy (keV)</Label>
              <Input
                id="energy"
                type="number"
                value={energy}
                onChange={(e) => setEnergy(e.target.value)}
                placeholder="100" data-id="jzkrglyb1" data-path="src/components/ShieldingCalculator.tsx" />

            </div>

            <div data-id="8ndd00try" data-path="src/components/ShieldingCalculator.tsx">
              <Label htmlFor="initial-intensity" data-id="0dsopeghk" data-path="src/components/ShieldingCalculator.tsx">Initial Intensity (relative units)</Label>
              <Input
                id="initial-intensity"
                type="number"
                value={initialIntensity}
                onChange={(e) => setInitialIntensity(e.target.value)}
                placeholder="1000" data-id="lkbcox1x8" data-path="src/components/ShieldingCalculator.tsx" />

            </div>

            <Button onClick={calculateShielding} className="w-full" data-id="z3bp44sxb" data-path="src/components/ShieldingCalculator.tsx">
              <Layers className="w-4 h-4 mr-2" data-id="ghvxx2r0k" data-path="src/components/ShieldingCalculator.tsx" />
              Calculate Shielding
            </Button>
          </CardContent>
        </Card>

        {/* Results */}
        <Card data-id="kbv77nbdu" data-path="src/components/ShieldingCalculator.tsx">
          <CardHeader data-id="947xwc7iq" data-path="src/components/ShieldingCalculator.tsx">
            <CardTitle data-id="7vh7y6fav" data-path="src/components/ShieldingCalculator.tsx">Shielding Effectiveness</CardTitle>
            <CardDescription data-id="myvl8ia5h" data-path="src/components/ShieldingCalculator.tsx">
              Calculated attenuation and transmission values
            </CardDescription>
          </CardHeader>
          <CardContent data-id="j6ywfk5wd" data-path="src/components/ShieldingCalculator.tsx">
            {results ?
            <div className="space-y-6" data-id="i8sx01knw" data-path="src/components/ShieldingCalculator.tsx">
                {/* Visual Progress */}
                <div className="space-y-2" data-id="e0c6adyhn" data-path="src/components/ShieldingCalculator.tsx">
                  <div className="flex justify-between text-sm" data-id="i2njk166t" data-path="src/components/ShieldingCalculator.tsx">
                    <span data-id="93xegwelb" data-path="src/components/ShieldingCalculator.tsx">Radiation Reduction</span>
                    <span data-id="vwpbbhl5n" data-path="src/components/ShieldingCalculator.tsx">{results.percentageReduction.toFixed(1)}%</span>
                  </div>
                  <Progress value={results.percentageReduction} className="h-3" data-id="a5z6xy2tu" data-path="src/components/ShieldingCalculator.tsx" />
                </div>

                {/* Key Results */}
                <div className="grid grid-cols-2 gap-4" data-id="4cdlitb6u" data-path="src/components/ShieldingCalculator.tsx">
                  <div className="text-center p-3 bg-blue-50 rounded-lg" data-id="h9tzl33xd" data-path="src/components/ShieldingCalculator.tsx">
                    <div className="text-lg font-bold text-blue-800" data-id="xvkr07e4u" data-path="src/components/ShieldingCalculator.tsx">
                      {results.transmission.toFixed(4)}
                    </div>
                    <div className="text-sm text-blue-600" data-id="bzscxilb1" data-path="src/components/ShieldingCalculator.tsx">Transmission</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg" data-id="ginldyn6f" data-path="src/components/ShieldingCalculator.tsx">
                    <div className="text-lg font-bold text-green-800" data-id="7t93j7k4w" data-path="src/components/ShieldingCalculator.tsx">
                      {results.attenuationFactor.toFixed(1)}x
                    </div>
                    <div className="text-sm text-green-600" data-id="z9jtvrndp" data-path="src/components/ShieldingCalculator.tsx">Attenuation</div>
                  </div>
                </div>

                {/* Detailed Results */}
                <div className="space-y-3" data-id="3f4vk1zd2" data-path="src/components/ShieldingCalculator.tsx">
                  <div className="flex justify-between items-center" data-id="69bcplaaz" data-path="src/components/ShieldingCalculator.tsx">
                    <span className="text-sm font-medium" data-id="4zieextew" data-path="src/components/ShieldingCalculator.tsx">Initial Intensity:</span>
                    <Badge variant="outline" data-id="4q1izpqxs" data-path="src/components/ShieldingCalculator.tsx">
                      {parseFloat(initialIntensity).toLocaleString()}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center" data-id="zzwfgarha" data-path="src/components/ShieldingCalculator.tsx">
                    <span className="text-sm font-medium" data-id="n0axnixl1" data-path="src/components/ShieldingCalculator.tsx">Transmitted Intensity:</span>
                    <Badge variant="outline" data-id="m8snapsl1" data-path="src/components/ShieldingCalculator.tsx">
                      {results.transmittedIntensity.toFixed(2)}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center" data-id="b4658aqgd" data-path="src/components/ShieldingCalculator.tsx">
                    <span className="text-sm font-medium" data-id="e6zg9gt8o" data-path="src/components/ShieldingCalculator.tsx">Number of HVLs:</span>
                    <Badge variant="secondary" data-id="j9cstp3u2" data-path="src/components/ShieldingCalculator.tsx">
                      {results.hvlCount.toFixed(2)}
                    </Badge>
                  </div>
                </div>

                {/* Material Info */}
                <div className="p-3 bg-gray-50 rounded-lg" data-id="yghjob4eq" data-path="src/components/ShieldingCalculator.tsx">
                  <h4 className="font-medium text-gray-900 mb-2" data-id="bnopi5ie9" data-path="src/components/ShieldingCalculator.tsx">{results.material.name}</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm" data-id="zqt7fslfv" data-path="src/components/ShieldingCalculator.tsx">
                    <div data-id="snmofykcf" data-path="src/components/ShieldingCalculator.tsx">Density: {results.material.density} g/cm³</div>
                    <div data-id="f1b4bjrlk" data-path="src/components/ShieldingCalculator.tsx">HVL: {results.material.hvl} mm</div>
                  </div>
                </div>
              </div> :

            <div className="text-center text-gray-500 py-8" data-id="mxw8fphzr" data-path="src/components/ShieldingCalculator.tsx">
                <Shield className="w-12 h-12 mx-auto mb-4 opacity-50" data-id="vnwpgvciw" data-path="src/components/ShieldingCalculator.tsx" />
                <p data-id="8fw6ibggi" data-path="src/components/ShieldingCalculator.tsx">Configure parameters and calculate to see shielding effectiveness</p>
              </div>
            }
          </CardContent>
        </Card>
      </div>

      {/* Required Thickness Table */}
      {results &&
      <Card data-id="yfyox7veq" data-path="src/components/ShieldingCalculator.tsx">
          <CardHeader data-id="9mcve20ti" data-path="src/components/ShieldingCalculator.tsx">
            <CardTitle className="flex items-center gap-2" data-id="zdkdv74u2" data-path="src/components/ShieldingCalculator.tsx">
              <Zap className="w-5 h-5" data-id="0kfnf099b" data-path="src/components/ShieldingCalculator.tsx" />
              Required Thickness for Different Attenuation Factors
            </CardTitle>
            <CardDescription data-id="c71mhvy7j" data-path="src/components/ShieldingCalculator.tsx">
              Thickness needed to achieve specific reduction factors
            </CardDescription>
          </CardHeader>
          <CardContent data-id="qsr0dy1rf" data-path="src/components/ShieldingCalculator.tsx">
            <div className="overflow-x-auto" data-id="2u3f5r01b" data-path="src/components/ShieldingCalculator.tsx">
              <table className="w-full text-sm" data-id="un46pve3y" data-path="src/components/ShieldingCalculator.tsx">
                <thead data-id="9ywlxj9qy" data-path="src/components/ShieldingCalculator.tsx">
                  <tr className="border-b" data-id="4z16uey7r" data-path="src/components/ShieldingCalculator.tsx">
                    <th className="text-left p-3" data-id="st4ujhui6" data-path="src/components/ShieldingCalculator.tsx">Reduction Factor</th>
                    <th className="text-left p-3" data-id="y80v3uovr" data-path="src/components/ShieldingCalculator.tsx">Percentage Reduction</th>
                    <th className="text-left p-3" data-id="fu9n8lary" data-path="src/components/ShieldingCalculator.tsx">Required Thickness (cm)</th>
                    <th className="text-left p-3" data-id="8ea801bgb" data-path="src/components/ShieldingCalculator.tsx">Number of HVLs</th>
                  </tr>
                </thead>
                <tbody data-id="qcwrfpsri" data-path="src/components/ShieldingCalculator.tsx">
                  {results.requiredThicknesses.map((req, index) =>
                <tr key={index} className="border-b" data-id="i0uv95amx" data-path="src/components/ShieldingCalculator.tsx">
                      <td className="p-3 font-medium" data-id="ysff2i5ua" data-path="src/components/ShieldingCalculator.tsx">{req.factor}x</td>
                      <td className="p-3" data-id="5mmy2pkp2" data-path="src/components/ShieldingCalculator.tsx">{((1 - 1 / req.factor) * 100).toFixed(1)}%</td>
                      <td className="p-3" data-id="mke4e9fi3" data-path="src/components/ShieldingCalculator.tsx">
                        <Badge variant={req.thickness <= parseFloat(thickness) ? "default" : "outline"} data-id="ovmwtkju9" data-path="src/components/ShieldingCalculator.tsx">
                          {req.thickness.toFixed(2)} cm
                        </Badge>
                      </td>
                      <td className="p-3" data-id="as35cm9ne" data-path="src/components/ShieldingCalculator.tsx">{req.hvls.toFixed(1)} HVLs</td>
                    </tr>
                )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      }

      {/* Material Comparison */}
      <Card data-id="kjy9w87uy" data-path="src/components/ShieldingCalculator.tsx">
        <CardHeader data-id="cuodt9w9i" data-path="src/components/ShieldingCalculator.tsx">
          <CardTitle data-id="glvq37y3d" data-path="src/components/ShieldingCalculator.tsx">Material Properties Comparison</CardTitle>
          <CardDescription data-id="30c808s24" data-path="src/components/ShieldingCalculator.tsx">
            Half-value layers for different shielding materials at 100 keV
          </CardDescription>
        </CardHeader>
        <CardContent data-id="x6i8y8xuz" data-path="src/components/ShieldingCalculator.tsx">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4" data-id="15xl35b2i" data-path="src/components/ShieldingCalculator.tsx">
            {Object.entries(materials).map(([key, material]) =>
            <div
              key={key}
              className={`p-4 rounded-lg border-2 transition-colors ${
              key === shieldMaterial ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`
              } data-id="fcq8g83ja" data-path="src/components/ShieldingCalculator.tsx">

                <div className="flex items-center gap-3 mb-2" data-id="w25hyo2b8" data-path="src/components/ShieldingCalculator.tsx">
                  <div
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: material.color }} data-id="j1o99y95n" data-path="src/components/ShieldingCalculator.tsx">
                </div>
                  <h4 className="font-medium" data-id="x8ishw6wz" data-path="src/components/ShieldingCalculator.tsx">{material.name}</h4>
                </div>
                <div className="space-y-1 text-sm text-gray-600" data-id="n8xpc1tqc" data-path="src/components/ShieldingCalculator.tsx">
                  <div data-id="gtkvfwywl" data-path="src/components/ShieldingCalculator.tsx">Density: {material.density} g/cm³</div>
                  <div data-id="03m58ygxc" data-path="src/components/ShieldingCalculator.tsx">HVL: {material.hvl} mm</div>
                  <div data-id="nc3w6adxa" data-path="src/components/ShieldingCalculator.tsx">μ: {material.mu} cm⁻¹</div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Design Guidelines */}
      <div className="grid md:grid-cols-2 gap-8" data-id="u8oxnu92l" data-path="src/components/ShieldingCalculator.tsx">
        <Card data-id="myawslr71" data-path="src/components/ShieldingCalculator.tsx">
          <CardHeader data-id="0xh10nct2" data-path="src/components/ShieldingCalculator.tsx">
            <CardTitle data-id="cuwv92t23" data-path="src/components/ShieldingCalculator.tsx">Shielding Design Guidelines</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm text-gray-600" data-id="rwu8q3bv4" data-path="src/components/ShieldingCalculator.tsx">
            <div data-id="5m5yuvad6" data-path="src/components/ShieldingCalculator.tsx">
              <h4 className="font-semibold text-gray-900 mb-1" data-id="jkgy880y7" data-path="src/components/ShieldingCalculator.tsx">Primary Barriers</h4>
              <p data-id="zkcnsekfv" data-path="src/components/ShieldingCalculator.tsx">Designed to attenuate the primary beam. Typically require 1.5-2.5 mm lead equivalent for diagnostic X-ray rooms.</p>
            </div>
            <div data-id="ovy8e4pz1" data-path="src/components/ShieldingCalculator.tsx">
              <h4 className="font-semibold text-gray-900 mb-1" data-id="ogrceogxo" data-path="src/components/ShieldingCalculator.tsx">Secondary Barriers</h4>
              <p data-id="cgmf0oj0k" data-path="src/components/ShieldingCalculator.tsx">Protect against scattered and leakage radiation. Usually require 0.5-1.0 mm lead equivalent.</p>
            </div>
            <div data-id="hf49dyx8q" data-path="src/components/ShieldingCalculator.tsx">
              <h4 className="font-semibold text-gray-900 mb-1" data-id="98l816gdc" data-path="src/components/ShieldingCalculator.tsx">Safety Factors</h4>
              <p data-id="uyiiaovrw" data-path="src/components/ShieldingCalculator.tsx">Include safety margins and consider occupancy factors, use factors, and workload distribution.</p>
            </div>
          </CardContent>
        </Card>

        <Card data-id="6u5fxw0ti" data-path="src/components/ShieldingCalculator.tsx">
          <CardHeader data-id="3ffn7vz8z" data-path="src/components/ShieldingCalculator.tsx">
            <CardTitle data-id="xg7862aqq" data-path="src/components/ShieldingCalculator.tsx">Practical Considerations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm text-gray-600" data-id="6bzt1a1cq" data-path="src/components/ShieldingCalculator.tsx">
            <div data-id="smdkjg0m6" data-path="src/components/ShieldingCalculator.tsx">
              <h4 className="font-semibold text-gray-900 mb-1" data-id="ieij3f4mi" data-path="src/components/ShieldingCalculator.tsx">Material Selection</h4>
              <p data-id="f7kpfodmr" data-path="src/components/ShieldingCalculator.tsx">Lead provides excellent attenuation but is heavy. Consider lead-free alternatives for some applications.</p>
            </div>
            <div data-id="ta0xjr05n" data-path="src/components/ShieldingCalculator.tsx">
              <h4 className="font-semibold text-gray-900 mb-1" data-id="pazetmr33" data-path="src/components/ShieldingCalculator.tsx">Installation</h4>
              <p data-id="77y38gahu" data-path="src/components/ShieldingCalculator.tsx">Ensure proper overlap of shielding materials and seal all penetrations (doors, windows, conduits).</p>
            </div>
            <div data-id="2srjltsvt" data-path="src/components/ShieldingCalculator.tsx">
              <h4 className="font-semibold text-gray-900 mb-1" data-id="j908bh0ws" data-path="src/components/ShieldingCalculator.tsx">Regulations</h4>
              <p data-id="1xxfqg90m" data-path="src/components/ShieldingCalculator.tsx">Follow local radiation protection regulations and building codes for shielding requirements.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default ShieldingCalculator;