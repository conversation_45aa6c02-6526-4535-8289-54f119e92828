
import React from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Target, CheckCircle, BookOpen, Lightbulb } from 'lucide-react';

const LearningObjectives = () => {
  const objectives = [
  {
    category: 'المفاهيم الأساسية',
    categoryEn: 'Basic Concepts',
    icon: <BookOpen className="w-5 h-5" data-id="pyct8mphz" data-path="src/pages/LearningObjectives.tsx" />,
    color: 'bg-blue-50 border-blue-200',
    items: [
    'فهم الآليات الفيزيائية الأساسية لتفاعل الأشعة السينية مع المادة',
    'التمييز بين أنواع التفاعلات المختلفة (متماسك، غير متماسك، امتصاص)',
    'شرح العلاقة بين طاقة الفوتون وآلية التفاعل المهيمنة',
    'وصف دور العدد الذري في تحديد نوع التفاعل']

  },
  {
    category: 'المعادلات والحسابات',
    categoryEn: 'Equations & Calculations',
    icon: <Target className="w-5 h-5" data-id="g67lga35y" data-path="src/pages/LearningObjectives.tsx" />,
    color: 'bg-green-50 border-green-200',
    items: [
    'تطبيق معادلات كل نوع من أنواع التفاعل',
    'حساب المقاطع العرضية والمعاملات المختلفة',
    'استخدام قوانين الحفظ في تحليل التفاعلات',
    'حساب الطاقة المنقولة والمتشتتة في كل تفاعل']

  },
  {
    category: 'التطبيقات السريرية',
    categoryEn: 'Clinical Applications',
    icon: <CheckCircle className="w-5 h-5" data-id="dtjvf2ioc" data-path="src/pages/LearningObjectives.tsx" />,
    color: 'bg-purple-50 border-purple-200',
    items: [
    'ربط المفاهيم الفيزيائية بالتطبيقات في التصوير الطبي',
    'فهم كيفية تأثير كل تفاعل على جودة الصورة',
    'تحديد الطاقة المناسبة لكل نوع من أنواع التصوير',
    'تقييم تأثير التفاعلات على الجرعة الإشعاعية']

  },
  {
    category: 'التحليل والتقييم',
    categoryEn: 'Analysis & Evaluation',
    icon: <Lightbulb className="w-5 h-5" data-id="ctqr78yey" data-path="src/pages/LearningObjectives.tsx" />,
    color: 'bg-orange-50 border-orange-200',
    items: [
    'مقارنة الأهمية النسبية للتفاعلات في ظروف مختلفة',
    'تحليل البيانات من قواعد بيانات NIST وغيرها',
    'تقييم تأثير معاملات التوهين على الاختراق والامتصاص',
    'حل مسائل متقدمة في تفاعل الإشعاع مع المادة']

  }];


  const skillsMatrix = [
  {
    skill: 'فهم المفاهيم الأساسية',
    level: 'أساسي',
    description: 'التعرف على أنواع التفاعلات ومبادئها'
  },
  {
    skill: 'تطبيق المعادلات',
    level: 'متوسط',
    description: 'استخدام الصيغ الرياضية في حل المسائل'
  },
  {
    skill: 'التحليل السريري',
    level: 'متقدم',
    description: 'ربط النظرية بالتطبيقات العملية'
  },
  {
    skill: 'التقييم النقدي',
    level: 'متقدم',
    description: 'تحليل وتقييم البيانات والنتائج'
  }];


  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-slate-50 to-purple-50" data-id="xm5yatle5" data-path="src/pages/LearningObjectives.tsx">
      <Navigation
        title="أهداف التعلم"
        titleEn="Learning Objectives" data-id="8bu5sjxuo" data-path="src/pages/LearningObjectives.tsx" />

      
      <div className="container mx-auto px-4 py-8" data-id="szi1qhugj" data-path="src/pages/LearningObjectives.tsx">
        {/* Header */}
        <Card className="mb-8" data-id="veyll9wp6" data-path="src/pages/LearningObjectives.tsx">
          <CardHeader data-id="z16i329wd" data-path="src/pages/LearningObjectives.tsx">
            <div className="flex items-center gap-3" data-id="uw0rvdqcy" data-path="src/pages/LearningObjectives.tsx">
              <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center" data-id="2i384k9wp" data-path="src/pages/LearningObjectives.tsx">
                <Target className="w-5 h-5 text-indigo-600" data-id="wgsrwh3rp" data-path="src/pages/LearningObjectives.tsx" />
              </div>
              <div data-id="zjc7ge508" data-path="src/pages/LearningObjectives.tsx">
                <CardTitle className="text-2xl text-right" data-id="dtaoluevg" data-path="src/pages/LearningObjectives.tsx">أهداف التعلم</CardTitle>
                <p className="text-gray-600 text-right" data-id="bbk4auke6" data-path="src/pages/LearningObjectives.tsx">
                  ما يجب على الطالب تحقيقه بعد دراسة هذا الفصل
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent data-id="082nfw2zw" data-path="src/pages/LearningObjectives.tsx">
            <p className="text-gray-700 text-right leading-relaxed" data-id="a93h50dxm" data-path="src/pages/LearningObjectives.tsx">
              يهدف هذا الفصل إلى تزويد الطلاب بفهم شامل لآليات تفاعل الأشعة السينية مع المادة، 
              وتطبيق هذه المعرفة في السياق السريري والعملي.
            </p>
          </CardContent>
        </Card>

        {/* Main Objectives */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8" data-id="9lna1v6l4" data-path="src/pages/LearningObjectives.tsx">
          {objectives.map((category, index) =>
          <Card key={index} className={`${category.color}`} data-id="2tb03mddx" data-path="src/pages/LearningObjectives.tsx">
              <CardHeader data-id="jnqo3gvm5" data-path="src/pages/LearningObjectives.tsx">
                <div className="flex items-center gap-3" data-id="9wc78o1qa" data-path="src/pages/LearningObjectives.tsx">
                  <div className="p-2 bg-white rounded-lg" data-id="7lc4c614g" data-path="src/pages/LearningObjectives.tsx">
                    {category.icon}
                  </div>
                  <div data-id="61ksb4cem" data-path="src/pages/LearningObjectives.tsx">
                    <CardTitle className="text-lg text-right" data-id="sy9hvd97a" data-path="src/pages/LearningObjectives.tsx">{category.category}</CardTitle>
                    <p className="text-sm text-gray-600 italic" data-id="p05rjgq0q" data-path="src/pages/LearningObjectives.tsx">{category.categoryEn}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent data-id="p3jca8agz" data-path="src/pages/LearningObjectives.tsx">
                <ul className="space-y-3" data-id="8vg7sp724" data-path="src/pages/LearningObjectives.tsx">
                  {category.items.map((item, itemIndex) =>
                <li key={itemIndex} className="flex items-start gap-3 text-right" data-id="w52jt0ni7" data-path="src/pages/LearningObjectives.tsx">
                      <CheckCircle className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" data-id="6z0mpa49w" data-path="src/pages/LearningObjectives.tsx" />
                      <span className="text-sm leading-relaxed" data-id="i0kp3ufku" data-path="src/pages/LearningObjectives.tsx">{item}</span>
                    </li>
                )}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Skills Matrix */}
        <Card className="mb-8" data-id="rsirzc4ab" data-path="src/pages/LearningObjectives.tsx">
          <CardHeader data-id="rfypfuman" data-path="src/pages/LearningObjectives.tsx">
            <CardTitle className="text-xl text-right" data-id="kh76awyrs" data-path="src/pages/LearningObjectives.tsx">مصفوفة المهارات المطلوبة</CardTitle>
            <p className="text-gray-600 text-right" data-id="g5srggp28" data-path="src/pages/LearningObjectives.tsx">توزيع المهارات حسب مستوى الصعوبة</p>
          </CardHeader>
          <CardContent data-id="3zzsh9lyc" data-path="src/pages/LearningObjectives.tsx">
            <div className="space-y-4" data-id="crau0i39g" data-path="src/pages/LearningObjectives.tsx">
              {skillsMatrix.map((skill, index) =>
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg" data-id="bcnw682c8" data-path="src/pages/LearningObjectives.tsx">
                  <div className="flex items-center gap-3" data-id="tgwgx6y7s" data-path="src/pages/LearningObjectives.tsx">
                    <Badge
                    variant={skill.level === 'أساسي' ? 'secondary' : skill.level === 'متوسط' ? 'default' : 'destructive'} data-id="suzjdjwmd" data-path="src/pages/LearningObjectives.tsx">

                      {skill.level}
                    </Badge>
                    <span className="text-sm text-gray-600" data-id="pgpbaxga4" data-path="src/pages/LearningObjectives.tsx">{skill.description}</span>
                  </div>
                  <h3 className="font-semibold text-right" data-id="0hm0n133i" data-path="src/pages/LearningObjectives.tsx">{skill.skill}</h3>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Assessment Criteria */}
        <Card className="mb-8" data-id="261f7te2o" data-path="src/pages/LearningObjectives.tsx">
          <CardHeader data-id="xefdsqpck" data-path="src/pages/LearningObjectives.tsx">
            <CardTitle className="text-xl text-right" data-id="zraub1xh5" data-path="src/pages/LearningObjectives.tsx">معايير التقييم</CardTitle>
          </CardHeader>
          <CardContent data-id="clxis5pul" data-path="src/pages/LearningObjectives.tsx">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4" data-id="fiknn2fvc" data-path="src/pages/LearningObjectives.tsx">
              <Card className="border-green-200" data-id="gfw81isdm" data-path="src/pages/LearningObjectives.tsx">
                <CardHeader className="pb-3" data-id="ue4r2qbta" data-path="src/pages/LearningObjectives.tsx">
                  <CardTitle className="text-base text-right text-green-700" data-id="fwjqolxgp" data-path="src/pages/LearningObjectives.tsx">امتياز (90-100%)</CardTitle>
                </CardHeader>
                <CardContent data-id="cml8h3nwm" data-path="src/pages/LearningObjectives.tsx">
                  <ul className="text-sm space-y-1 text-right" data-id="5y8s62amv" data-path="src/pages/LearningObjectives.tsx">
                    <li data-id="caqr764os" data-path="src/pages/LearningObjectives.tsx">• فهم عميق لجميع المفاهيم</li>
                    <li data-id="t11mifur6" data-path="src/pages/LearningObjectives.tsx">• حل مسائل معقدة بدقة</li>
                    <li data-id="eqv9koh7g" data-path="src/pages/LearningObjectives.tsx">• ربط النظرية بالتطبيق</li>
                    <li data-id="4ngz3qlz9" data-path="src/pages/LearningObjectives.tsx">• تحليل نقدي للبيانات</li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card className="border-blue-200" data-id="y3yj37h7c" data-path="src/pages/LearningObjectives.tsx">
                <CardHeader className="pb-3" data-id="sgt6expb6" data-path="src/pages/LearningObjectives.tsx">
                  <CardTitle className="text-base text-right text-blue-700" data-id="hnciykmqm" data-path="src/pages/LearningObjectives.tsx">جيد جداً (80-89%)</CardTitle>
                </CardHeader>
                <CardContent data-id="akvj4ufy3" data-path="src/pages/LearningObjectives.tsx">
                  <ul className="text-sm space-y-1 text-right" data-id="75jl29gb8" data-path="src/pages/LearningObjectives.tsx">
                    <li data-id="dhdrbrblq" data-path="src/pages/LearningObjectives.tsx">• فهم جيد للمفاهيم الأساسية</li>
                    <li data-id="1k7ln87xc" data-path="src/pages/LearningObjectives.tsx">• حل المسائل العادية</li>
                    <li data-id="vhb9wkm8h" data-path="src/pages/LearningObjectives.tsx">• تطبيق المعادلات بشكل صحيح</li>
                    <li data-id="hlmxjd63s" data-path="src/pages/LearningObjectives.tsx">• فهم التطبيقات السريرية</li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card className="border-yellow-200" data-id="ob30qux4p" data-path="src/pages/LearningObjectives.tsx">
                <CardHeader className="pb-3" data-id="ucxlryicm" data-path="src/pages/LearningObjectives.tsx">
                  <CardTitle className="text-base text-right text-yellow-700" data-id="ebpf1o19v" data-path="src/pages/LearningObjectives.tsx">مقبول (70-79%)</CardTitle>
                </CardHeader>
                <CardContent data-id="4raqb1n4s" data-path="src/pages/LearningObjectives.tsx">
                  <ul className="text-sm space-y-1 text-right" data-id="7xkxqmwc1" data-path="src/pages/LearningObjectives.tsx">
                    <li data-id="lj4appzva" data-path="src/pages/LearningObjectives.tsx">• فهم أساسي للمفاهيم</li>
                    <li data-id="uh01m2g2f" data-path="src/pages/LearningObjectives.tsx">• حل مسائل بسيطة</li>
                    <li data-id="j0guv0y49" data-path="src/pages/LearningObjectives.tsx">• معرفة المعادلات الأساسية</li>
                    <li data-id="tabhn27g4" data-path="src/pages/LearningObjectives.tsx">• إدراك التطبيقات العامة</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>

        {/* Study Timeline */}
        <Card data-id="qbzxast8d" data-path="src/pages/LearningObjectives.tsx">
          <CardHeader data-id="f2j2txq8f" data-path="src/pages/LearningObjectives.tsx">
            <CardTitle className="text-xl text-right" data-id="j7fwrpqd9" data-path="src/pages/LearningObjectives.tsx">الجدول الزمني المقترح للدراسة</CardTitle>
          </CardHeader>
          <CardContent data-id="jf7cj1mmd" data-path="src/pages/LearningObjectives.tsx">
            <div className="space-y-4" data-id="zsbed75lm" data-path="src/pages/LearningObjectives.tsx">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" data-id="xixw52p39" data-path="src/pages/LearningObjectives.tsx">
                <div className="bg-blue-50 p-4 rounded-lg text-center" data-id="3a3btow7f" data-path="src/pages/LearningObjectives.tsx">
                  <h3 className="font-semibold text-blue-800 mb-2" data-id="1kllx8mfc" data-path="src/pages/LearningObjectives.tsx">الأسبوع الأول</h3>
                  <p className="text-sm text-blue-600" data-id="102kcip0o" data-path="src/pages/LearningObjectives.tsx">المفاهيم الأساسية والتشتت المتماسك</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center" data-id="hyx3b6lk9" data-path="src/pages/LearningObjectives.tsx">
                  <h3 className="font-semibold text-green-800 mb-2" data-id="jpwbvaqw5" data-path="src/pages/LearningObjectives.tsx">الأسبوع الثاني</h3>
                  <p className="text-sm text-green-600" data-id="vnqu0gpal" data-path="src/pages/LearningObjectives.tsx">التأثير الكهروضوئي وتشتت كومبتون</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg text-center" data-id="i3n5wt5dp" data-path="src/pages/LearningObjectives.tsx">
                  <h3 className="font-semibold text-purple-800 mb-2" data-id="fc54ai2wt" data-path="src/pages/LearningObjectives.tsx">الأسبوع الثالث</h3>
                  <p className="text-sm text-purple-600" data-id="tk4vbr30r" data-path="src/pages/LearningObjectives.tsx">معاملات التوهين والتطبيقات</p>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg text-center" data-id="5fidos23e" data-path="src/pages/LearningObjectives.tsx">
                  <h3 className="font-semibold text-orange-800 mb-2" data-id="zo9p6wql3" data-path="src/pages/LearningObjectives.tsx">الأسبوع الرابع</h3>
                  <p className="text-sm text-orange-600" data-id="7sszug92u" data-path="src/pages/LearningObjectives.tsx">المراجعة وحل المسائل</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default LearningObjectives;