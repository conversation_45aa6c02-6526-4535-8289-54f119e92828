import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, BookOpen, History, Microscope, Zap, Target, Brain } from 'lucide-react';
import { motion } from 'motion/react';

const Chapter1Content = () => {
  const [openSections, setOpenSections] = useState<{[key: string]: boolean;}>({});
  const [completedSections, setCompletedSections] = useState<string[]>([]);

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const markAsCompleted = (sectionId: string) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections((prev) => [...prev, sectionId]);
    }
  };

  const sections = [
  {
    id: '1.1',
    title: 'المنظور التاريخي: من رونتجن إلى التصوير الشعاعي الحديث',
    icon: <History className="w-5 h-5" data-id="ypwvzoatk" data-path="src/components/Chapter1Content.tsx" />,
    subsections: [
    {
      id: '1.1.1',
      title: 'الاكتشاف والتطبيقات المبكرة',
      content: 'في عام 1895، اكتشف فيلهلم رونتجن الأشعة السينية بالصدفة أثناء تجاربه مع أنابيب الكاثود. هذا الاكتشاف الثوري فتح آفاقاً جديدة في الطب والعلوم.'
    },
    {
      id: '1.1.2',
      title: 'تطور تكنولوجيا الأشعة السينية',
      content: 'شهدت تكنولوجيا الأشعة السينية تطوراً مستمراً من الأنابيب البسيطة إلى أنظمة التصوير الرقمية المتقدمة اليوم.'
    }]

  },
  {
    id: '1.2',
    title: 'دور التصوير بالأشعة السينية في الممارسة السريرية',
    icon: <Microscope className="w-5 h-5" data-id="r25hxv0xy" data-path="src/components/Chapter1Content.tsx" />,
    subsections: [
    {
      id: '1.2.1',
      title: 'التصوير الشعاعي التشخيصي والتنظير الفلوري والتصوير المقطعي',
      content: 'يشمل التصوير التشخيصي عدة تقنيات: الأشعة السينية العادية، التنظير الفلوري، تصوير الثدي، والتصوير المقطعي المحوسب.'
    },
    {
      id: '1.2.2',
      title: 'الأشعة التداخلية',
      content: 'الأشعة التداخلية تستخدم التصوير لتوجيه العمليات الطبية البسيطة والعلاجات غير الجراحية.'
    }]

  },
  {
    id: '1.3',
    title: 'المبادئ الأساسية لتكوين صور الأشعة السينية',
    icon: <Zap className="w-5 h-5" data-id="b6685v5r6" data-path="src/components/Chapter1Content.tsx" />,
    content: 'تعتمد صور الأشعة السينية على اختلاف امتصاص الأنسجة للإشعاع، مما ينتج عنه تباين في الصورة يسمح بتمييز الهياكل المختلفة.'
  },
  {
    id: '1.4',
    title: 'سلسلة التصوير بالأشعة السينية: من المصدر إلى الصورة',
    icon: <Target className="w-5 h-5" data-id="xlk5ivoqh" data-path="src/components/Chapter1Content.tsx" />,
    content: 'تشمل سلسلة التصوير: توليد الأشعة السينية، تفاعلها مع المريض، كشف الإشعاع، ومعالجة الصورة.'
  },
  {
    id: '1.5',
    title: 'التحديات في التصوير بالأشعة السينية',
    icon: <BookOpen className="w-5 h-5" data-id="uzww9ekzz" data-path="src/components/Chapter1Content.tsx" />,
    content: 'التحديات الرئيسية تشمل تحسين جودة الصورة مع تقليل الجرعة الإشعاعية، والحصول على أفضل توازن بين وضوح الصورة والسلامة.'
  },
  {
    id: '1.6',
    title: 'مقدمة في المحاكاة في الفيزياء الطبية',
    icon: <Brain className="w-5 h-5" data-id="xygfga5vn" data-path="src/components/Chapter1Content.tsx" />,
    subsections: [
    {
      id: '1.6.1',
      title: 'لماذا المحاكاة؟ فوائدها في التصميم والتحسين',
      content: 'المحاكاة تسمح بدراسة الأنظمة المعقدة، تحسين التصميم، تقليل التكاليف، وتعزيز التعليم والبحث.'
    },
    {
      id: '1.6.2',
      title: 'نظرة عامة على منهجيات المحاكاة',
      content: 'تشمل منهجيات المحاكاة: مونت كارلو للنمذجة الإحصائية، الطرق التحليلية، والطرق الهجينة.'
    }]

  },
  {
    id: '1.7',
    title: 'نطاق الكتاب وبنيته',
    icon: <BookOpen className="w-5 h-5" data-id="5bdo5h5k4" data-path="src/components/Chapter1Content.tsx" />,
    content: 'يغطي هذا الكتاب الأسس النظرية والتطبيقية للتصوير بالأشعة السينية والمحاكاة في الفيزياء الطبية.'
  }];


  const progress = completedSections.length / sections.length * 100;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6" dir="rtl" data-id="tvle0z4zv" data-path="src/components/Chapter1Content.tsx">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }} data-id="75e1l4wdo" data-path="src/components/Chapter1Content.tsx">

        <Card className="mb-6" data-id="eeqocxfmw" data-path="src/components/Chapter1Content.tsx">
          <CardHeader className="text-center" data-id="xjovntq05" data-path="src/components/Chapter1Content.tsx">
            <CardTitle className="text-2xl font-bold text-right" data-id="iox5fqbe8" data-path="src/components/Chapter1Content.tsx">
              الفصل الأول: مقدمة في التصوير بالأشعة السينية في الطب
            </CardTitle>
            <CardDescription className="text-right" data-id="72mg9cbjj" data-path="src/components/Chapter1Content.tsx">
              استكشف الأسس التاريخية والمبادئ الأساسية للتصوير بالأشعة السينية
            </CardDescription>
            <div className="mt-4" data-id="bk4yuxoj5" data-path="src/components/Chapter1Content.tsx">
              <div className="flex justify-between items-center mb-2" data-id="4hztw4zsn" data-path="src/components/Chapter1Content.tsx">
                <span className="text-sm text-muted-foreground" data-id="psh3z2zpq" data-path="src/components/Chapter1Content.tsx">التقدم</span>
                <span className="text-sm font-medium" data-id="46vvam8f7" data-path="src/components/Chapter1Content.tsx">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" data-id="ta0wslo2h" data-path="src/components/Chapter1Content.tsx" />
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      <div className="space-y-4" data-id="vumcvvtif" data-path="src/components/Chapter1Content.tsx">
        {sections.map((section, index) =>
        <motion.div
          key={section.id}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }} data-id="zq9fq4b5m" data-path="src/components/Chapter1Content.tsx">

            <Card className="overflow-hidden" data-id="jr6wojsni" data-path="src/components/Chapter1Content.tsx">
              <Collapsible
              open={openSections[section.id]}
              onOpenChange={() => toggleSection(section.id)} data-id="jgjal4twz" data-path="src/components/Chapter1Content.tsx">

                <CollapsibleTrigger asChild data-id="c8wdql96t" data-path="src/components/Chapter1Content.tsx">
                  <CardHeader className="hover:bg-muted/50 cursor-pointer transition-colors" data-id="wqa462rsd" data-path="src/components/Chapter1Content.tsx">
                    <div className="flex items-center justify-between" data-id="lf1vasdec" data-path="src/components/Chapter1Content.tsx">
                      <div className="flex items-center gap-3" data-id="s5zyj9p3x" data-path="src/components/Chapter1Content.tsx">
                        <div className="flex items-center gap-2" data-id="m3og86ecl" data-path="src/components/Chapter1Content.tsx">
                          {section.icon}
                          <Badge variant="outline" data-id="2l2732p27" data-path="src/components/Chapter1Content.tsx">{section.id}</Badge>
                        </div>
                        <CardTitle className="text-lg text-right" data-id="6zp8gu3zo" data-path="src/components/Chapter1Content.tsx">{section.title}</CardTitle>
                      </div>
                      <div className="flex items-center gap-2" data-id="xyfg0utiq" data-path="src/components/Chapter1Content.tsx">
                        {completedSections.includes(section.id) &&
                      <Badge variant="default" data-id="lx3d40hps" data-path="src/components/Chapter1Content.tsx">مكتمل</Badge>
                      }
                        {openSections[section.id] ?
                      <ChevronDown className="w-4 h-4" data-id="gffy5i4jq" data-path="src/components/Chapter1Content.tsx" /> :

                      <ChevronRight className="w-4 h-4" data-id="j1fw7lcxx" data-path="src/components/Chapter1Content.tsx" />
                      }
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>
                
                <CollapsibleContent data-id="gm6b2nzmc" data-path="src/components/Chapter1Content.tsx">
                  <CardContent className="pt-0" data-id="rza1etiep" data-path="src/components/Chapter1Content.tsx">
                    {section.subsections ?
                  <div className="space-y-4" data-id="98w2l1f5n" data-path="src/components/Chapter1Content.tsx">
                        {section.subsections.map((subsection) =>
                    <Card key={subsection.id} className="border-l-4 border-l-primary" data-id="ttvez3s24" data-path="src/components/Chapter1Content.tsx">
                            <CardHeader className="pb-2" data-id="f51839ao2" data-path="src/components/Chapter1Content.tsx">
                              <div className="flex items-center gap-2" data-id="ellh8y6c7" data-path="src/components/Chapter1Content.tsx">
                                <Badge variant="secondary" data-id="jlq7ad8a8" data-path="src/components/Chapter1Content.tsx">{subsection.id}</Badge>
                                <CardTitle className="text-base text-right" data-id="o8z5i6ltr" data-path="src/components/Chapter1Content.tsx">
                                  {subsection.title}
                                </CardTitle>
                              </div>
                            </CardHeader>
                            <CardContent data-id="5jy42hx4n" data-path="src/components/Chapter1Content.tsx">
                              <p className="text-muted-foreground text-right leading-relaxed" data-id="64ptyd4z8" data-path="src/components/Chapter1Content.tsx">
                                {subsection.content}
                              </p>
                            </CardContent>
                          </Card>
                    )}
                      </div> :

                  <p className="text-muted-foreground text-right leading-relaxed" data-id="5y2mqw278" data-path="src/components/Chapter1Content.tsx">
                        {section.content}
                      </p>
                  }
                    
                    <div className="mt-4 flex justify-start" data-id="2t1nps29f" data-path="src/components/Chapter1Content.tsx">
                      <Button
                      onClick={() => markAsCompleted(section.id)}
                      disabled={completedSections.includes(section.id)}
                      size="sm" data-id="ad92vpmur" data-path="src/components/Chapter1Content.tsx">

                        {completedSections.includes(section.id) ? 'مكتمل' : 'وضع علامة كمكتمل'}
                      </Button>
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          </motion.div>
        )}
      </div>

      <Card className="mt-8" data-id="3221ja19f" data-path="src/components/Chapter1Content.tsx">
        <CardHeader data-id="q3a5jyrmf" data-path="src/components/Chapter1Content.tsx">
          <CardTitle className="text-right" data-id="7kzvd7e8y" data-path="src/components/Chapter1Content.tsx">أهداف التعلم</CardTitle>
        </CardHeader>
        <CardContent data-id="9zvlswcon" data-path="src/components/Chapter1Content.tsx">
          <ul className="list-disc list-inside space-y-2 text-right" data-id="glp5h652d" data-path="src/components/Chapter1Content.tsx">
            <li data-id="6ninc0rwr" data-path="src/components/Chapter1Content.tsx">فهم التطور التاريخي للتصوير بالأشعة السينية</li>
            <li data-id="j46u4cfjt" data-path="src/components/Chapter1Content.tsx">تعلم المبادئ الأساسية لتكوين الصور الشعاعية</li>
            <li data-id="qijjp52eq" data-path="src/components/Chapter1Content.tsx">التعرف على التطبيقات السريرية المختلفة</li>
            <li data-id="2ku44tlg7" data-path="src/components/Chapter1Content.tsx">فهم أهمية المحاكاة في الفيزياء الطبية</li>
            <li data-id="a6pzmlzkx" data-path="src/components/Chapter1Content.tsx">إدراك التحديات الحالية في التصوير الشعاعي</li>
          </ul>
        </CardContent>
      </Card>
    </div>);

};

export default Chapter1Content;