import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Button } from '@/components/ui/button.jsx'
import { Input } from '@/components/ui/input.jsx'
import { useState } from 'react'
import { MessageCircle, Bo<PERSON>, User, Lightbulb, BookOpen, Calculator } from 'lucide-react'

const AIChat = () => {
  const [chatInput, setChatInput] = useState('')
  const [chatMessages, setChatMessages] = useState([
    {
      type: 'ai',
      message: 'مرحباً! أنا مساعدك الذكي لتعلم التصوير الطبي بالإشعاع المؤين. يمكنني مساعدتك في فهم مكونات أجهزة الأشعة السينية، الدوائر الكهربائية، ومبادئ الأمان. اسألني أي سؤال!'
    }
  ])
  const [isTyping, setIsTyping] = useState(false)

  const aiResponses = {
    'أنبوب الأشعة السينية': 'أنبوب الأشعة السينية هو القلب النابض لجهاز التصوير الطبي. يتكون من الكاثود الذي ينبعث منه الإلكترونات، والأنود المصنوع من التنغستن الذي تصطدم به الإلكترونات لإنتاج الأشعة السينية. الأنبوب محاط بحاوية زجاجية مفرغة من الهواء لضمان حركة الإلكترونات بحرية.',
    'الكاثود': 'الكاثود هو القطب السالب في أنبوب الأشعة السينية، ويتكون من فتيل مصنوع من التنغستن. عند تسخين الفتيل بالتيار الكهربائي، ينبعث منه الإلكترونات بعملية تُسمى الانبعاث الحراري. هذه الإلكترونات تُسرع نحو الأنود بفعل الجهد العالي.',
    'الأنود': 'الأنود هو القطب الموجب المصنوع من التنغستن نظراً لدرجة انصهاره العالية. عندما تصطدم الإلكترونات عالية السرعة بالأنود، تتحول طاقتها الحركية إلى أشعة سينية (1%) وحرارة (99%). لذلك يحتاج الأنود إلى نظام تبريد فعال.',
    'الدوائر الكهربائية': 'الدوائر الكهربائية في جهاز الأشعة السينية معقدة وتشمل: محول الجهد العالي الذي يرفع الجهد إلى 50-150 كيلو فولت، دائرة الفتيل التي تسخن الكاثود، ودوائر التحكم والأمان. كل دائرة لها وظيفة محددة لضمان التشغيل الآمن والفعال.',
    'الأمان': 'الأمان في أجهزة الأشعة السينية أولوية قصوى. يشمل ذلك: الحماية من الإشعاع المتسرب بواسطة الرصاص، أنظمة التبريد لمنع ارتفاع الحرارة، قواطع كهربائية للحماية من التيار الزائد، وأجهزة مراقبة الجرعة الإشعاعية.',
    'كيف يعمل': 'يعمل جهاز الأشعة السينية بالخطوات التالية: 1) تسخين فتيل الكاثود لإنتاج الإلكترونات، 2) تسريع الإلكترونات نحو الأنود بالجهد العالي، 3) اصطدام الإلكترونات بالأنود وإنتاج الأشعة السينية، 4) مرور الأشعة عبر المريض والكشف بواسطة الكاشف الرقمي.',
    'التطبيقات': 'تُستخدم أجهزة الأشعة السينية في تطبيقات طبية متنوعة: تصوير العظام والمفاصل، فحص الصدر والرئتين، تصوير الأسنان، التصوير المقطعي المحوسب، والتصوير التداخلي. كل تطبيق يتطلب معايرة خاصة للحصول على أفضل جودة صورة.'
  }

  const getAIResponse = (userMessage) => {
    const lowerMessage = userMessage.toLowerCase()
    
    for (const [keyword, response] of Object.entries(aiResponses)) {
      if (lowerMessage.includes(keyword.toLowerCase())) {
        return response
      }
    }
    
    // Default response
    return 'سؤال ممتاز! في النسخة الكاملة من هذا النظام، سأتمكن من تقديم إجابات مفصلة ومخصصة لجميع أسئلتك حول التصوير الطبي بالإشعاع المؤين. حالياً، يمكنني مساعدتك في المواضيع الأساسية مثل أنبوب الأشعة السينية، الكاثود، الأنود، الدوائر الكهربائية، والأمان.'
  }

  const handleSendMessage = () => {
    if (chatInput.trim()) {
      const userMessage = chatInput
      setChatMessages(prev => [...prev, { type: 'user', message: userMessage }])
      setChatInput('')
      setIsTyping(true)
      
      // Simulate AI thinking time
      setTimeout(() => {
        const aiResponse = getAIResponse(userMessage)
        setChatMessages(prev => [...prev, { type: 'ai', message: aiResponse }])
        setIsTyping(false)
      }, 1500)
    }
  }

  const quickQuestions = [
    'ما هو أنبوب الأشعة السينية؟',
    'كيف يعمل الكاثود؟',
    'ما هي وظيفة الأنود؟',
    'اشرح لي الدوائر الكهربائية',
    'ما هي إجراءات الأمان؟'
  ]

  return (
    <div className="grid lg:grid-cols-2 gap-8">
      {/* Enhanced AI Chat Interface */}
      <Card className="h-96">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bot className="mr-2 text-blue-600" size={24} />
            المساعد الذكي المتقدم
          </CardTitle>
          <CardDescription>
            مساعد ذكي متخصص في التصوير الطبي بالإشعاع المؤين
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col h-full">
          <div className="flex-1 overflow-y-auto mb-4 space-y-3 max-h-48">
            {chatMessages.map((msg, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg ${
                  msg.type === 'user' 
                    ? 'bg-blue-500 text-white ml-8 flex items-start' 
                    : 'bg-gray-100 text-gray-800 mr-8 flex items-start'
                }`}
              >
                {msg.type === 'ai' && (
                  <Bot className="mr-2 mt-1 text-blue-600 flex-shrink-0" size={16} />
                )}
                <span className="text-sm leading-relaxed">{msg.message}</span>
                {msg.type === 'user' && (
                  <User className="ml-2 mt-1 text-white flex-shrink-0" size={16} />
                )}
              </div>
            ))}
            {isTyping && (
              <div className="bg-gray-100 text-gray-800 mr-8 p-3 rounded-lg flex items-center">
                <Bot className="mr-2 text-blue-600" size={16} />
                <span className="text-sm">المساعد يكتب...</span>
                <div className="ml-2 flex space-x-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
              </div>
            )}
          </div>
          
          {/* Quick Questions */}
          <div className="mb-3">
            <p className="text-xs text-gray-500 mb-2">أسئلة سريعة:</p>
            <div className="flex flex-wrap gap-1">
              {quickQuestions.slice(0, 3).map((question, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="text-xs h-6 px-2"
                  onClick={() => {
                    setChatInput(question)
                    handleSendMessage()
                  }}
                >
                  {question.split('؟')[0]}؟
                </Button>
              ))}
            </div>
          </div>
          
          <div className="flex gap-2">
            <Input
              placeholder="اكتب سؤالك هنا..."
              value={chatInput}
              onChange={(e) => setChatInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              className="text-sm"
            />
            <Button onClick={handleSendMessage} disabled={isTyping}>
              إرسال
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* AI-Powered Learning Tools */}
      <Card className="h-96">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lightbulb className="mr-2 text-yellow-600" size={24} />
            أدوات التعلم الذكية
          </CardTitle>
          <CardDescription>
            أدوات مدعومة بالذكاء الاصطناعي لتعزيز التعلم
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-3">
            <Button
              variant="outline"
              className="h-auto p-4 text-right justify-start"
              onClick={() => setChatInput('اشرح لي كيف يعمل أنبوب الأشعة السينية بالتفصيل')}
            >
              <div className="flex items-center w-full">
                <BookOpen className="mr-3 text-blue-600" size={20} />
                <div>
                  <div className="font-semibold text-sm">شرح تفاعلي</div>
                  <div className="text-xs text-gray-500">احصل على شرح مفصل لأي مكون</div>
                </div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              className="h-auto p-4 text-right justify-start"
              onClick={() => setChatInput('احسب لي الجرعة الإشعاعية المناسبة')}
            >
              <div className="flex items-center w-full">
                <Calculator className="mr-3 text-green-600" size={20} />
                <div>
                  <div className="font-semibold text-sm">حاسبة ذكية</div>
                  <div className="text-xs text-gray-500">احسب المعاملات والجرعات</div>
                </div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              className="h-auto p-4 text-right justify-start"
              onClick={() => setChatInput('ما هي أهم إجراءات الأمان في التصوير الطبي؟')}
            >
              <div className="flex items-center w-full">
                <MessageCircle className="mr-3 text-purple-600" size={20} />
                <div>
                  <div className="font-semibold text-sm">استشارة فورية</div>
                  <div className="text-xs text-gray-500">اسأل عن أي موضوع متخصص</div>
                </div>
              </div>
            </Button>
          </div>
          
          <div className="bg-blue-50 p-3 rounded-lg">
            <h4 className="font-semibold text-sm text-blue-800 mb-2">ميزات الذكاء الاصطناعي:</h4>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• فهم السياق والأسئلة المعقدة</li>
              <li>• إجابات مخصصة حسب مستوى المعرفة</li>
              <li>• شرح المفاهيم بطرق متعددة</li>
              <li>• ربط المعلومات ببعضها البعض</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default AIChat

