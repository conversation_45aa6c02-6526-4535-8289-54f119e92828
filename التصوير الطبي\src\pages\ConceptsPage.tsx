import { useState } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Atom,
  Zap,
  Target,
  Shield,
  Activity,
  BookOpen,
  ChevronRight,
  CheckCircle } from
'lucide-react';

const ConceptsPage = () => {
  const [completedConcepts, setCompletedConcepts] = useState<string[]>([]);

  const conceptCategories = [
  {
    id: 'basics',
    title: 'الأساسيات',
    icon: BookOpen,
    concepts: [
    {
      id: 'radiation-basics',
      title: 'ما هو الإشعاع المؤين؟',
      description: 'فهم طبيعة الإشعاع المؤين وخصائصه الأساسية',
      content: `
            الإشعاع المؤين هو نوع من الطاقة له القدرة على إزالة الإلكترونات من الذرات، مما يؤدي إلى تكوين أيونات.
            
            الخصائص الرئيسية:
            • طاقة عالية كافية للتأين
            • قدرة على اختراق المواد
            • تفاعل مع الأنسجة البيولوجية
            • إمكانية الكشف والقياس
            
            أنواع الإشعاع المؤين:
            1. الأشعة السينية (X-rays)
            2. أشعة جاما (Gamma rays)
            3. جسيمات ألفا (Alpha particles)
            4. جسيمات بيتا (Beta particles)
          `,
      difficulty: 'مبتدئ'
    },
    {
      id: 'xray-production',
      title: 'إنتاج الأشعة السينية',
      description: 'آلية إنتاج الأشعة السينية في الأنبوب',
      content: `
            تنتج الأشعة السينية عندما تصطدم الإلكترونات عالية السرعة بهدف معدني (عادة التنجستن).
            
            العملية:
            1. تسخين الكاثود لإنتاج الإلكترونات
            2. تسريع الإلكترونات بواسطة فولتية عالية
            3. اصطدام الإلكترونات بالأنود
            4. تحويل الطاقة الحركية إلى أشعة سينية (1%) وحرارة (99%)
            
            نوعان من الأشعة السينية:
            • أشعة المكابح (Bremsstrahlung)
            • الأشعة المميزة (Characteristic)
          `,
      difficulty: 'متوسط'
    }]

  },
  {
    id: 'physics',
    title: 'الفيزياء',
    icon: Atom,
    concepts: [
    {
      id: 'interaction',
      title: 'تفاعل الإشعاع مع المادة',
      description: 'كيف يتفاعل الإشعاع مع الأنسجة والمواد المختلفة',
      content: `
            يتفاعل الإشعاع مع المادة بطرق مختلفة حسب نوع الإشعاع وطاقته:
            
            التفاعلات الرئيسية للأشعة السينية:
            1. التشتت الكوهيري (Coherent Scattering)
            2. التأثير الكهروضوئي (Photoelectric Effect)
            3. تشتت كومبتون (Compton Scattering)
            4. إنتاج الأزواج (Pair Production)
            
            العوامل المؤثرة:
            • طاقة الفوتون
            • العدد الذري للمادة
            • كثافة المادة
          `,
      difficulty: 'متقدم'
    }]

  },
  {
    id: 'safety',
    title: 'الحماية الإشعاعية',
    icon: Shield,
    concepts: [
    {
      id: 'protection-principles',
      title: 'مبادئ الحماية الإشعاعية',
      description: 'الأسس العلمية للحماية من الإشعاع',
      content: `
            المبادئ الثلاثة للحماية الإشعاعية:
            
            1. التبرير (Justification):
            • يجب أن تكون الفوائد أكبر من المخاطر
            • تقييم الحاجة الطبية للفحص
            
            2. التحسين (Optimization) - مبدأ ALARA:
            • As Low As Reasonably Achievable
            • تقليل الجرعة إلى أدنى مستوى ممكن
            
            3. تطبيق حدود الجرعة (Dose Limits):
            • حدود للعاملين والجمهور
            • مراقبة الجرعات المتراكمة
          `,
      difficulty: 'مبتدئ'
    }]

  }];


  const markAsCompleted = (conceptId: string) => {
    if (!completedConcepts.includes(conceptId)) {
      setCompletedConcepts([...completedConcepts, conceptId]);
    }
  };

  const totalConcepts = conceptCategories.reduce((total, category) => total + category.concepts.length, 0);
  const progressPercentage = completedConcepts.length / totalConcepts * 100;

  return (
    <div className="min-h-screen py-8" data-id="qu48ybvdm" data-path="src/pages/ConceptsPage.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="pdqvjj8vp" data-path="src/pages/ConceptsPage.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12" data-id="j87wc6j5j" data-path="src/pages/ConceptsPage.tsx">

          <Badge variant="secondary" className="mb-4" data-id="k6055snkv" data-path="src/pages/ConceptsPage.tsx">المفاهيم التعليمية</Badge>
          <h1 className="text-4xl font-bold text-gray-900 mb-4" data-id="lmhhuwq89" data-path="src/pages/ConceptsPage.tsx">
            أساسيات التصوير الطبي الإشعاعي
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8" data-id="m5bpkylpc" data-path="src/pages/ConceptsPage.tsx">
            تعلم المفاهيم الأساسية والمتقدمة في فيزياء الإشعاع والحماية الإشعاعية
          </p>
          
          {/* Progress */}
          <div className="max-w-md mx-auto" data-id="e2azyfx93" data-path="src/pages/ConceptsPage.tsx">
            <div className="flex justify-between items-center mb-2" data-id="utt22a5ql" data-path="src/pages/ConceptsPage.tsx">
              <span className="text-sm text-gray-600" data-id="uh0y19rzc" data-path="src/pages/ConceptsPage.tsx">التقدم العام</span>
              <span className="text-sm font-medium" data-id="rhmtxdl7o" data-path="src/pages/ConceptsPage.tsx">{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" data-id="3kpxcte21" data-path="src/pages/ConceptsPage.tsx" />
            <p className="text-xs text-gray-500 mt-2" data-id="dephhpp7o" data-path="src/pages/ConceptsPage.tsx">
              {completedConcepts.length} من {totalConcepts} مفهوم مكتمل
            </p>
          </div>
        </motion.div>

        {/* Concepts */}
        <Tabs defaultValue="basics" className="w-full" data-id="y2ypfg6sb" data-path="src/pages/ConceptsPage.tsx">
          <TabsList className="grid w-full grid-cols-3 mb-8" data-id="eecipixdm" data-path="src/pages/ConceptsPage.tsx">
            {conceptCategories.map((category) => {
              const Icon = category.icon;
              return (
                <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2" data-id="zvgpnlgd8" data-path="src/pages/ConceptsPage.tsx">
                  <Icon size={18} data-id="vf5kamvzz" data-path="src/pages/ConceptsPage.tsx" />
                  {category.title}
                </TabsTrigger>);

            })}
          </TabsList>

          {conceptCategories.map((category) =>
          <TabsContent key={category.id} value={category.id} data-id="768yayrzi" data-path="src/pages/ConceptsPage.tsx">
              <div className="grid gap-6" data-id="nbkmhgo7t" data-path="src/pages/ConceptsPage.tsx">
                {category.concepts.map((concept, index) =>
              <motion.div
                key={concept.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }} data-id="ilmhmgng8" data-path="src/pages/ConceptsPage.tsx">

                    <Card className="overflow-hidden hover:shadow-lg transition-shadow" data-id="dtqzfe5vh" data-path="src/pages/ConceptsPage.tsx">
                      <CardHeader data-id="wow7wu388" data-path="src/pages/ConceptsPage.tsx">
                        <div className="flex items-start justify-between" data-id="pdey7oz2i" data-path="src/pages/ConceptsPage.tsx">
                          <div className="flex-1" data-id="a105wtxve" data-path="src/pages/ConceptsPage.tsx">
                            <div className="flex items-center gap-2 mb-2" data-id="gsc02ox9f" data-path="src/pages/ConceptsPage.tsx">
                              <CardTitle className="text-xl" data-id="fskbjc35u" data-path="src/pages/ConceptsPage.tsx">{concept.title}</CardTitle>
                              {completedConcepts.includes(concept.id) &&
                          <CheckCircle className="w-5 h-5 text-green-500" data-id="y7trscdg7" data-path="src/pages/ConceptsPage.tsx" />
                          }
                            </div>
                            <CardDescription className="text-base" data-id="z1l56yrlm" data-path="src/pages/ConceptsPage.tsx">
                              {concept.description}
                            </CardDescription>
                          </div>
                          <Badge variant={
                      concept.difficulty === 'مبتدئ' ? 'secondary' :
                      concept.difficulty === 'متوسط' ? 'default' : 'destructive'
                      } data-id="i3souwir8" data-path="src/pages/ConceptsPage.tsx">
                            {concept.difficulty}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent data-id="hd752ljwc" data-path="src/pages/ConceptsPage.tsx">
                        <div className="prose prose-sm max-w-none mb-4" data-id="7qa2xhjot" data-path="src/pages/ConceptsPage.tsx">
                          <div className="whitespace-pre-line text-gray-700 leading-relaxed" data-id="foyx24tug" data-path="src/pages/ConceptsPage.tsx">
                            {concept.content}
                          </div>
                        </div>
                        <div className="flex justify-between items-center" data-id="yj5ywm1da" data-path="src/pages/ConceptsPage.tsx">
                          <Button
                        onClick={() => markAsCompleted(concept.id)}
                        disabled={completedConcepts.includes(concept.id)}
                        variant={completedConcepts.includes(concept.id) ? "secondary" : "default"} data-id="0j5liq8wv" data-path="src/pages/ConceptsPage.tsx">

                            {completedConcepts.includes(concept.id) ?
                        <>
                                <CheckCircle className="w-4 h-4 ml-2" data-id="dmfwt6p2s" data-path="src/pages/ConceptsPage.tsx" />
                                مكتمل
                              </> :

                        <>
                                تم الفهم
                                <ChevronRight className="w-4 h-4 mr-2" data-id="qwznkmi0m" data-path="src/pages/ConceptsPage.tsx" />
                              </>
                        }
                          </Button>
                          <div className="text-sm text-gray-500" data-id="jud8ocer8" data-path="src/pages/ConceptsPage.tsx">
                            {Math.ceil(concept.content.length / 1000)} دقائق قراءة
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
              )}
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>);

};

export default ConceptsPage;