/* Main CSS for Bilingual Medical Radiation Physics and Imaging Simulation */

/* Base Styles */
:root {
  --primary-color: #4f46e5;
  --primary-light: #e0e7ff;
  --primary-dark: #312e81;
  --secondary-color: #0ea5e9;
  --secondary-light: #e0f2fe;
  --secondary-dark: #0369a1;
  --accent-color: #8b5cf6;
  --accent-light: #ede9fe;
  --accent-dark: #5b21b6;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
}

/* Typography */
body {
  font-family: 'Tajawal', sans-serif;
  background-color: var(--gray-50);
  color: var(--gray-800);
}

/* For English text */
.en {
  font-family: 'Inter', sans-serif;
  direction: ltr;
  text-align: left;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Interactive Elements */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Language Switcher */
.language-switcher {
  position: relative;
  cursor: pointer;
}

.language-switcher::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.language-switcher:hover::after {
  width: 100%;
}

.language-switcher.active::after {
  width: 100%;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background-color: var(--gray-300);
  border-radius: 20px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--gray-400);
}

/* Cards and Containers */
.glass-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

.content-card {
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Module Cards */
.module-card {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, transparent 50%, rgba(0, 0, 0, 0.7) 100%);
  z-index: 1;
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.module-card-content {
  position: relative;
  z-index: 2;
}

/* Interactive Simulation Elements */
.simulation-control {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.simulation-control:hover {
  border-color: var(--primary-light);
  background-color: var(--gray-50);
}

.simulation-control.active {
  border-color: var(--primary-color);
  background-color: var(--primary-light);
  color: var(--primary-dark);
}

/* X-Ray Simulation Styles */
.xray-beam {
  height: 20px;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.8) 0%, rgba(37, 99, 235, 0.6) 100%);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.xray-beam::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    90deg,
    transparent,
    transparent 10px,
    rgba(255, 255, 255, 0.1) 10px,
    rgba(255, 255, 255, 0.1) 20px
  );
  animation: moveBeam 2s linear infinite;
}

@keyframes moveBeam {
  from { background-position: 0 0; }
  to { background-position: 40px 0; }
}

/* Particle animation */
.particle-container {
  position: relative;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: rgba(59, 130, 246, 0.7);
  border-radius: 50%;
  transform-origin: center;
}

/* Code block styling */
.code-block {
  background-color: #1e293b;
  color: #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
}

.code-comment {
  color: #94a3b8;
}

.code-keyword {
  color: #c084fc;
}

.code-string {
  color: #86efac;
}

.code-number {
  color: #fca5a5;
}

.code-function {
  color: #93c5fd;
}

/* Responsive Design Helpers */
@media (max-width: 640px) {
  .mobile-menu-hidden {
    display: none;
  }
}

/* Equations and Scientific Content */
.equation {
  background-color: var(--gray-100);
  border-left: 4px solid var(--primary-color);
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0.375rem;
  overflow-x: auto;
}

.figure {
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1.5rem 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.figure img {
  border-radius: 0.375rem;
}

/* Bilingual Toggle */
.bilingual-toggle {
  display: flex;
  align-items: center;
  background-color: var(--gray-100);
  border-radius: 9999px;
  padding: 0.25rem;
  width: fit-content;
}

.bilingual-toggle-option {
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bilingual-toggle-option.active {
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Progress Indicators */
.progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--gray-200);
  z-index: 0;
}

.progress-step {
  position: relative;
  z-index: 1;
  background-color: white;
  border: 2px solid var(--gray-200);
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.progress-step.active {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.progress-step.completed {
  border-color: var(--success-color);
  background-color: var(--success-color);
  color: white;
}