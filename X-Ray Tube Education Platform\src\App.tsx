import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TooltipProvider } from '@/components/ui/tooltip';
import { Toaster } from '@/components/ui/toaster';
import { LanguageProvider } from '@/contexts/LanguageContext';
import HomePage from '@/pages/HomePage';
import XRayTubePage from '@/pages/XRayTubePage';
import CircuitDiagramPage from '@/pages/CircuitDiagramPage';
import ConceptsPage from '@/pages/ConceptsPage';
import AIAssistantPage from '@/pages/AIAssistantPage';
import NotFound from '@/pages/NotFound';
import Navbar from '@/components/Navbar';
import './App.css';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient} data-id="z5tte4slr" data-path="src/App.tsx">
      <LanguageProvider data-id="m2fw0ozer" data-path="src/App.tsx">
        <TooltipProvider data-id="itqt20ikx" data-path="src/App.tsx">
          <Router data-id="qgpc4fs3m" data-path="src/App.tsx">
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50" data-id="vv44qp1ds" data-path="src/App.tsx">
              <Navbar data-id="p9c4dznyr" data-path="src/App.tsx" />
              <main data-id="l4hcz2hcv" data-path="src/App.tsx">
                <Routes data-id="kemqkmoz4" data-path="src/App.tsx">
                  <Route path="/" element={<HomePage data-id="58kcf6grq" data-path="src/App.tsx" />} data-id="90n2e451n" data-path="src/App.tsx" />
                  <Route path="/xray-tube" element={<XRayTubePage data-id="2i5insk5x" data-path="src/App.tsx" />} data-id="d29081frj" data-path="src/App.tsx" />
                  <Route path="/circuits" element={<CircuitDiagramPage data-id="h840zhg47" data-path="src/App.tsx" />} data-id="o1tkz4ybi" data-path="src/App.tsx" />
                  <Route path="/concepts" element={<ConceptsPage data-id="u9nb4fxms" data-path="src/App.tsx" />} data-id="f3ecuhlgx" data-path="src/App.tsx" />
                  <Route path="/ai-assistant" element={<AIAssistantPage data-id="qml7hc893" data-path="src/App.tsx" />} data-id="0uy2kwx9x" data-path="src/App.tsx" />
                  <Route path="*" element={<NotFound data-id="u2eyt5vz6" data-path="src/App.tsx" />} data-id="ei5pfc7lq" data-path="src/App.tsx" />
                </Routes>
              </main>
            </div>
            <Toaster data-id="6ppi197xh" data-path="src/App.tsx" />
          </Router>
        </TooltipProvider>
      </LanguageProvider>
    </QueryClientProvider>);

}

export default App;