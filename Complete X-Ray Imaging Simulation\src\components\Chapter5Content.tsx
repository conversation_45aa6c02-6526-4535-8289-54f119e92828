import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Zap, Settings, BarChart3, Clock, Power, Cpu } from 'lucide-react';
import { motion } from 'motion/react';

const Chapter5Content = () => {
  const [openSections, setOpenSections] = useState<{[key: string]: boolean;}>({});
  const [completedSections, setCompletedSections] = useState<string[]>([]);

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const markAsCompleted = (sectionId: string) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections((prev) => [...prev, sectionId]);
    }
  };

  const sections = [
  {
    id: '5.1',
    title: 'دور مولد الجهد العالي في إنتاج الأشعة السينية',
    icon: <Power className="w-5 h-5" data-id="7vxhdo3hm" data-path="src/components/Chapter5Content.tsx" />,
    content: 'مولد الجهد العالي هو القلب النابض لنظام الأشعة السينية، حيث يوفر الطاقة اللازمة لتسريع الإلكترونات من الكاثود إلى الأنود. جودة الجهد المولد تؤثر مباشرة على جودة وكمية الأشعة السينية المنتجة.'
  },
  {
    id: '5.2',
    title: 'مبادئ توليد الجهد العالي',
    icon: <Zap className="w-5 h-5" data-id="os482jbb3" data-path="src/components/Chapter5Content.tsx" />,
    subsections: [
    {
      id: '5.2.1',
      title: 'المحولات: رافع الجهد، المحول التلقائي',
      content: 'المحولات ترفع الجهد من المصدر المنخفض (220-440V) إلى الجهد العالي المطلوب (50-150 kV). نسبة التحويل تحدد بعدد اللفات: V2/V1 = N2/N1. المحول التلقائي يوفر تحكماً دقيقاً في الجهد.'
    },
    {
      id: '5.2.2',
      title: 'التصحيح: نصف الموجة، الموجة الكاملة',
      content: 'التصحيح يحول التيار المتردد إلى تيار مستمر. تصحيح نصف الموجة يستخدم نصف الدورة فقط، بينما تصحيح الموجة الكاملة يستخدم الدورة كاملة، مما يحسن الكفاءة ويقلل التموج.'
    }]

  },
  {
    id: '5.3',
    title: 'أنواع مولدات الأشعة السينية وأشكالها الموجية',
    icon: <BarChart3 className="w-5 h-5" data-id="mtelayt86" data-path="src/components/Chapter5Content.tsx" />,
    subsections: [
    {
      id: '5.3.1',
      title: 'المولدات أحادية الطور',
      content: 'المولدات أحادية الطور هي الأبسط والأقل تكلفة. تنتج جهداً متموجاً بنسبة تموج 100%. تستخدم في التطبيقات البسيطة والمحمولة، لكن كفاءتها منخفضة نسبياً.'
    },
    {
      id: '5.3.2',
      title: 'مولدات ثلاثية الطور (6 نبضات، 12 نبضة)',
      content: 'المولدات ثلاثية الطور توفر جهداً أكثر استقراراً. نظام 6 نبضات له تموج ~13%، بينما نظام 12 نبضة له تموج ~4%. هذا يحسن جودة الأشعة ويقلل وقت التعرض.'
    },
    {
      id: '5.3.3',
      title: 'مولدات العاكس عالية التردد',
      content: 'مولدات العاكس تستخدم ترددات عالية (20-100 kHz) لتقليل حجم المحولات وتحسين التحكم. تموجها أقل من 1% وتوفر استجابة سريعة وتحكماً دقيقاً.'
    },
    {
      id: '5.3.4',
      title: 'وحدات الجهد الثابت والوحدات التي تعمل بالبطارية',
      content: 'مولدات الجهد الثابت تنتج جهداً ثابتاً تماماً (تموج = 0%). الوحدات التي تعمل بالبطارية توفر حلولاً محمولة للتطبيقات الميدانية والطوارئ.'
    }]

  },
  {
    id: '5.4',
    title: 'تموج الجهد: التعريف، والتأثير على kVp الفعال، والطيف، والإخراج',
    icon: <BarChart3 className="w-5 h-5" data-id="n31y48oqf" data-path="src/components/Chapter5Content.tsx" />,
    content: 'تموج الجهد هو التغيير في الجهد عن القيمة المتوسطة. يُحسب كـ: التموج % = (Vmax - Vmin)/(Vmax + Vmin) × 100. التموج يؤثر على الطيف ويقلل kVp الفعال، مما يستلزم تعرضات أطول.'
  },
  {
    id: '5.5',
    title: 'التحكم في جهد الأنبوب (كيلوفولت)، وتيار الأنبوب (مللي أمبير)، ووقت التعرض (ثانية)',
    icon: <Settings className="w-5 h-5" data-id="svi68dah7" data-path="src/components/Chapter5Content.tsx" />,
    subsections: [
    {
      id: '5.5.1',
      title: 'تأثير kVp على جودة وكمية الشعاع',
      content: 'زيادة kVp تزيد الطاقة القصوى للفوتونات وتحسن قدرة النفاذ. الكمية تزداد تقريباً مع مربع kVp، بينما الجودة (الصلابة) تتحسن مع زيادة الطاقة المتوسطة.'
    },
    {
      id: '5.5.2',
      title: 'تأثير mA ووقت التعرض (mAs) على كمية الشعاع',
      content: 'mAs (mA × الوقت) يتحكم في كمية الأشعة السينية المنتجة دون تغيير جودة الشعاع. العلاقة خطية: مضاعفة mAs تضاعف كمية الإشعاع.'
    }]

  },
  {
    id: '5.6',
    title: 'أنظمة التحكم التلقائي في التعرض (AEC): المبادئ وجوانب المحاكاة',
    icon: <Cpu className="w-5 h-5" data-id="f0lxfcvmk" data-path="src/components/Chapter5Content.tsx" />,
    content: 'أنظمة AEC تقيس الإشعاع المار عبر المريض وتنهي التعرض عند الوصول للكمية المطلوبة. تتضمن كاشفات أيونية أو صلبة، دوائر تحكم، وخوارزميات لضبط المعاملات تلقائياً.'
  }];


  const progress = completedSections.length / sections.length * 100;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6" dir="rtl" data-id="ct5mvs9cl" data-path="src/components/Chapter5Content.tsx">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }} data-id="tiufd6ii0" data-path="src/components/Chapter5Content.tsx">
        
        <Card className="mb-6" data-id="tbe05v3p1" data-path="src/components/Chapter5Content.tsx">
          <CardHeader className="text-center" data-id="2cemzqvpe" data-path="src/components/Chapter5Content.tsx">
            <CardTitle className="text-2xl font-bold text-right" data-id="xc7ymsi3e" data-path="src/components/Chapter5Content.tsx">
              الفصل الخامس: مولدات الجهد العالي والتحكم في شعاع الأشعة السينية
            </CardTitle>
            <CardDescription className="text-right" data-id="atumslbh7" data-path="src/components/Chapter5Content.tsx">
              دراسة شاملة لأنواع المولدات وتأثيرها على خصائص الأشعة السينية
            </CardDescription>
            <div className="mt-4" data-id="xqepzy2tx" data-path="src/components/Chapter5Content.tsx">
              <div className="flex justify-between items-center mb-2" data-id="j2xbq7gxv" data-path="src/components/Chapter5Content.tsx">
                <span className="text-sm text-muted-foreground" data-id="boyhrtqf2" data-path="src/components/Chapter5Content.tsx">التقدم</span>
                <span className="text-sm font-medium" data-id="7rizkk2mr" data-path="src/components/Chapter5Content.tsx">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" data-id="gd3eibksl" data-path="src/components/Chapter5Content.tsx" />
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      <Tabs defaultValue="content" className="w-full" data-id="vmeqruebu" data-path="src/components/Chapter5Content.tsx">
        <TabsList className="grid w-full grid-cols-4" data-id="5dbbb935p" data-path="src/components/Chapter5Content.tsx">
          <TabsTrigger value="content" data-id="aecy4gjpq" data-path="src/components/Chapter5Content.tsx">المحتوى</TabsTrigger>
          <TabsTrigger value="types" data-id="4femao50j" data-path="src/components/Chapter5Content.tsx">أنواع المولدات</TabsTrigger>
          <TabsTrigger value="waveforms" data-id="5jxjxo8ib" data-path="src/components/Chapter5Content.tsx">الأشكال الموجية</TabsTrigger>
          <TabsTrigger value="control" data-id="on2zlbzpn" data-path="src/components/Chapter5Content.tsx">أنظمة التحكم</TabsTrigger>
        </TabsList>
        
        <TabsContent value="content" className="space-y-4" data-id="pu1pq662s" data-path="src/components/Chapter5Content.tsx">
          {sections.map((section, index) =>
          <motion.div
            key={section.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }} data-id="buv5tktvn" data-path="src/components/Chapter5Content.tsx">
              
              <Card className="overflow-hidden" data-id="cw09zwda4" data-path="src/components/Chapter5Content.tsx">
                <Collapsible
                open={openSections[section.id]}
                onOpenChange={() => toggleSection(section.id)} data-id="67n3qc2j7" data-path="src/components/Chapter5Content.tsx">
                  
                  <CollapsibleTrigger asChild data-id="qltjmr70m" data-path="src/components/Chapter5Content.tsx">
                    <CardHeader className="hover:bg-muted/50 cursor-pointer transition-colors" data-id="aumccpwyf" data-path="src/components/Chapter5Content.tsx">
                      <div className="flex items-center justify-between" data-id="k0mv9s1h2" data-path="src/components/Chapter5Content.tsx">
                        <div className="flex items-center gap-3" data-id="gjh5yut0v" data-path="src/components/Chapter5Content.tsx">
                          <div className="flex items-center gap-2" data-id="c2mmn7wjo" data-path="src/components/Chapter5Content.tsx">
                            {section.icon}
                            <Badge variant="outline" data-id="976i66z61" data-path="src/components/Chapter5Content.tsx">{section.id}</Badge>
                          </div>
                          <CardTitle className="text-lg text-right" data-id="3shrq75ym" data-path="src/components/Chapter5Content.tsx">{section.title}</CardTitle>
                        </div>
                        <div className="flex items-center gap-2" data-id="o5xedw25h" data-path="src/components/Chapter5Content.tsx">
                          {completedSections.includes(section.id) &&
                        <Badge variant="default" data-id="xqse5y5a2" data-path="src/components/Chapter5Content.tsx">مكتمل</Badge>
                        }
                          {openSections[section.id] ?
                        <ChevronDown className="w-4 h-4" data-id="lu73kxf21" data-path="src/components/Chapter5Content.tsx" /> :

                        <ChevronRight className="w-4 h-4" data-id="1wo9vlncv" data-path="src/components/Chapter5Content.tsx" />
                        }
                        </div>
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent data-id="hz0d4v6iu" data-path="src/components/Chapter5Content.tsx">
                    <CardContent className="pt-0" data-id="hgeq6afr1" data-path="src/components/Chapter5Content.tsx">
                      {section.subsections ?
                    <div className="space-y-4" data-id="dc6ukweu5" data-path="src/components/Chapter5Content.tsx">
                          {section.subsections.map((subsection) =>
                      <Card key={subsection.id} className="border-l-4 border-l-primary" data-id="gco95cxdo" data-path="src/components/Chapter5Content.tsx">
                              <CardHeader className="pb-2" data-id="06qphlron" data-path="src/components/Chapter5Content.tsx">
                                <div className="flex items-center gap-2" data-id="u3blncmr7" data-path="src/components/Chapter5Content.tsx">
                                  <Badge variant="secondary" data-id="tbvc7672a" data-path="src/components/Chapter5Content.tsx">{subsection.id}</Badge>
                                  <CardTitle className="text-base text-right" data-id="6si21b71l" data-path="src/components/Chapter5Content.tsx">
                                    {subsection.title}
                                  </CardTitle>
                                </div>
                              </CardHeader>
                              <CardContent data-id="0ujaeycrk" data-path="src/components/Chapter5Content.tsx">
                                <p className="text-muted-foreground text-right leading-relaxed" data-id="hwg1r26vi" data-path="src/components/Chapter5Content.tsx">
                                  {subsection.content}
                                </p>
                              </CardContent>
                            </Card>
                      )}
                        </div> :

                    <p className="text-muted-foreground text-right leading-relaxed" data-id="noqziwt9i" data-path="src/components/Chapter5Content.tsx">
                          {section.content}
                        </p>
                    }
                      
                      <div className="mt-4 flex justify-start" data-id="xm4lisgef" data-path="src/components/Chapter5Content.tsx">
                        <Button
                        onClick={() => markAsCompleted(section.id)}
                        disabled={completedSections.includes(section.id)}
                        size="sm" data-id="ho1rrz7w9" data-path="src/components/Chapter5Content.tsx">
                          {completedSections.includes(section.id) ? 'مكتمل' : 'وضع علامة كمكتمل'}
                        </Button>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            </motion.div>
          )}
        </TabsContent>

        <TabsContent value="types" className="space-y-4" data-id="lywmwgxk4" data-path="src/components/Chapter5Content.tsx">
          <div className="grid md:grid-cols-2 gap-4" data-id="b85gp2e7z" data-path="src/components/Chapter5Content.tsx">
            <Card data-id="sk2se0uaw" data-path="src/components/Chapter5Content.tsx">
              <CardHeader data-id="nggx7nd8c" data-path="src/components/Chapter5Content.tsx">
                <CardTitle className="text-right" data-id="rmfdijb21" data-path="src/components/Chapter5Content.tsx">المولدات التقليدية</CardTitle>
              </CardHeader>
              <CardContent data-id="qto2ima9e" data-path="src/components/Chapter5Content.tsx">
                <div className="space-y-3" data-id="dq05pz6af" data-path="src/components/Chapter5Content.tsx">
                  <div className="bg-blue-50 p-3 rounded" data-id="upk2mn8ua" data-path="src/components/Chapter5Content.tsx">
                    <h4 className="font-semibold text-right" data-id="5n9tufp0h" data-path="src/components/Chapter5Content.tsx">أحادي الطور</h4>
                    <ul className="text-sm mt-1 text-right" data-id="ejqj2sq5v" data-path="src/components/Chapter5Content.tsx">
                      <li data-id="o23bkipjq" data-path="src/components/Chapter5Content.tsx">• تموج 100%</li>
                      <li data-id="dr4p2y79y" data-path="src/components/Chapter5Content.tsx">• بسيط وغير مكلف</li>
                      <li data-id="esy45hp01" data-path="src/components/Chapter5Content.tsx">• كفاءة منخفضة</li>
                    </ul>
                  </div>
                  <div className="bg-green-50 p-3 rounded" data-id="dpmjmnxn5" data-path="src/components/Chapter5Content.tsx">
                    <h4 className="font-semibold text-right" data-id="h400bahpt" data-path="src/components/Chapter5Content.tsx">ثلاثي الطور - 6 نبضات</h4>
                    <ul className="text-sm mt-1 text-right" data-id="plhj3l8iz" data-path="src/components/Chapter5Content.tsx">
                      <li data-id="obfsbwutf" data-path="src/components/Chapter5Content.tsx">• تموج ~13%</li>
                      <li data-id="bgulxm9fq" data-path="src/components/Chapter5Content.tsx">• كفاءة أفضل</li>
                      <li data-id="z6rrhbluv" data-path="src/components/Chapter5Content.tsx">• أكثر تعقيداً</li>
                    </ul>
                  </div>
                  <div className="bg-orange-50 p-3 rounded" data-id="nzl64r662" data-path="src/components/Chapter5Content.tsx">
                    <h4 className="font-semibold text-right" data-id="r2whjzmc7" data-path="src/components/Chapter5Content.tsx">ثلاثي الطور - 12 نبضة</h4>
                    <ul className="text-sm mt-1 text-right" data-id="371q2dtsw" data-path="src/components/Chapter5Content.tsx">
                      <li data-id="wbjlmk2mg" data-path="src/components/Chapter5Content.tsx">• تموج ~4%</li>
                      <li data-id="czxpj68x0" data-path="src/components/Chapter5Content.tsx">• كفاءة عالية</li>
                      <li data-id="ku11rvt64" data-path="src/components/Chapter5Content.tsx">• معقد ومكلف</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card data-id="9a7f0yco9" data-path="src/components/Chapter5Content.tsx">
              <CardHeader data-id="estds5afj" data-path="src/components/Chapter5Content.tsx">
                <CardTitle className="text-right" data-id="owip6s4ao" data-path="src/components/Chapter5Content.tsx">المولدات المتقدمة</CardTitle>
              </CardHeader>
              <CardContent data-id="vy4c7k3p0" data-path="src/components/Chapter5Content.tsx">
                <div className="space-y-3" data-id="8aazx8vgj" data-path="src/components/Chapter5Content.tsx">
                  <div className="bg-purple-50 p-3 rounded" data-id="bv71r6blf" data-path="src/components/Chapter5Content.tsx">
                    <h4 className="font-semibold text-right" data-id="kc6x9jbjn" data-path="src/components/Chapter5Content.tsx">عاكس عالي التردد</h4>
                    <ul className="text-sm mt-1 text-right" data-id="09f0x3v3y" data-path="src/components/Chapter5Content.tsx">
                      <li data-id="px9h7jm0h" data-path="src/components/Chapter5Content.tsx">• تموج &lt;1%</li>
                      <li data-id="s4b9tq1fo" data-path="src/components/Chapter5Content.tsx">• حجم صغير</li>
                      <li data-id="ojml6q0kj" data-path="src/components/Chapter5Content.tsx">• تحكم دقيق</li>
                    </ul>
                  </div>
                  <div className="bg-red-50 p-3 rounded" data-id="me312grva" data-path="src/components/Chapter5Content.tsx">
                    <h4 className="font-semibold text-right" data-id="qj8o0gg6d" data-path="src/components/Chapter5Content.tsx">جهد ثابت</h4>
                    <ul className="text-sm mt-1 text-right" data-id="lhlmjgfh1" data-path="src/components/Chapter5Content.tsx">
                      <li data-id="icnn8br7c" data-path="src/components/Chapter5Content.tsx">• تموج = 0%</li>
                      <li data-id="u4le53xy1" data-path="src/components/Chapter5Content.tsx">• أفضل جودة طيف</li>
                      <li data-id="glmcdwiti" data-path="src/components/Chapter5Content.tsx">• أعلى كفاءة</li>
                    </ul>
                  </div>
                  <div className="bg-gray-50 p-3 rounded" data-id="qtzdff6pz" data-path="src/components/Chapter5Content.tsx">
                    <h4 className="font-semibold text-right" data-id="vn9l4p750" data-path="src/components/Chapter5Content.tsx">يعمل بالبطارية</h4>
                    <ul className="text-sm mt-1 text-right" data-id="7xmu78eso" data-path="src/components/Chapter5Content.tsx">
                      <li data-id="xeyxno0d1" data-path="src/components/Chapter5Content.tsx">• محمول</li>
                      <li data-id="ifakmflic" data-path="src/components/Chapter5Content.tsx">• مستقل عن الشبكة</li>
                      <li data-id="5cpgqq9w0" data-path="src/components/Chapter5Content.tsx">• قدرة محدودة</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="waveforms" className="space-y-4" data-id="3ibdl66h5" data-path="src/components/Chapter5Content.tsx">
          <Card data-id="6tmwv53uz" data-path="src/components/Chapter5Content.tsx">
            <CardHeader data-id="1ne6ugki3" data-path="src/components/Chapter5Content.tsx">
              <CardTitle className="text-right" data-id="rjzg7fryv" data-path="src/components/Chapter5Content.tsx">خصائص الأشكال الموجية</CardTitle>
            </CardHeader>
            <CardContent data-id="kixbz8wg2" data-path="src/components/Chapter5Content.tsx">
              <div className="space-y-4" data-id="qqsqxn3wz" data-path="src/components/Chapter5Content.tsx">
                <div className="bg-muted p-4 rounded-lg" data-id="6t82h4nt4" data-path="src/components/Chapter5Content.tsx">
                  <h4 className="font-semibold mb-2 text-right" data-id="g0lq8jy2y" data-path="src/components/Chapter5Content.tsx">حساب التموج:</h4>
                  <div className="font-mono text-center bg-white p-3 rounded border" data-id="u9isi3s7y" data-path="src/components/Chapter5Content.tsx">
                    التموج % = (Vmax - Vmin)/(Vmax + Vmin) × 100
                  </div>
                </div>
                
                <div className="grid md:grid-cols-3 gap-4" data-id="dbkw7kn22" data-path="src/components/Chapter5Content.tsx">
                  <Card data-id="sce0dqvr6" data-path="src/components/Chapter5Content.tsx">
                    <CardHeader data-id="ie7czadxm" data-path="src/components/Chapter5Content.tsx">
                      <CardTitle className="text-base text-right" data-id="aczs5fso8" data-path="src/components/Chapter5Content.tsx">أحادي الطور</CardTitle>
                    </CardHeader>
                    <CardContent data-id="6cyhmwvoa" data-path="src/components/Chapter5Content.tsx">
                      <div className="h-20 bg-gradient-to-r from-blue-200 to-blue-400 rounded flex items-center justify-center" data-id="ehv9fkmk7" data-path="src/components/Chapter5Content.tsx">
                        <span className="text-xs text-white" data-id="x7hjlvcm5" data-path="src/components/Chapter5Content.tsx">موجة جيبية مقطوعة</span>
                      </div>
                      <p className="text-xs mt-2 text-center" data-id="s9k41pf0d" data-path="src/components/Chapter5Content.tsx">تموج 100%</p>
                    </CardContent>
                  </Card>
                  
                  <Card data-id="9g0ohvg2i" data-path="src/components/Chapter5Content.tsx">
                    <CardHeader data-id="60g2b7mmu" data-path="src/components/Chapter5Content.tsx">
                      <CardTitle className="text-base text-right" data-id="xi5ul5zdl" data-path="src/components/Chapter5Content.tsx">ثلاثي الطور</CardTitle>
                    </CardHeader>
                    <CardContent data-id="xt2lgxebi" data-path="src/components/Chapter5Content.tsx">
                      <div className="h-20 bg-gradient-to-r from-green-200 to-green-400 rounded flex items-center justify-center" data-id="dmsaht7go" data-path="src/components/Chapter5Content.tsx">
                        <span className="text-xs text-white" data-id="jilcf52le" data-path="src/components/Chapter5Content.tsx">موجة أكثر نعومة</span>
                      </div>
                      <p className="text-xs mt-2 text-center" data-id="j4qjtnson" data-path="src/components/Chapter5Content.tsx">تموج 4-13%</p>
                    </CardContent>
                  </Card>
                  
                  <Card data-id="0vqz0fx43" data-path="src/components/Chapter5Content.tsx">
                    <CardHeader data-id="totztekui" data-path="src/components/Chapter5Content.tsx">
                      <CardTitle className="text-base text-right" data-id="euaz5mi8w" data-path="src/components/Chapter5Content.tsx">جهد ثابت</CardTitle>
                    </CardHeader>
                    <CardContent data-id="ob6dw0dje" data-path="src/components/Chapter5Content.tsx">
                      <div className="h-20 bg-gradient-to-r from-purple-200 to-purple-400 rounded flex items-center justify-center" data-id="erl05y6vj" data-path="src/components/Chapter5Content.tsx">
                        <span className="text-xs text-white" data-id="szpn8ef79" data-path="src/components/Chapter5Content.tsx">خط مستقيم</span>
                      </div>
                      <p className="text-xs mt-2 text-center" data-id="yoe2gzuhx" data-path="src/components/Chapter5Content.tsx">تموج 0%</p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="control" className="space-y-4" data-id="qokascjb8" data-path="src/components/Chapter5Content.tsx">
          <Card data-id="5y13xikgm" data-path="src/components/Chapter5Content.tsx">
            <CardHeader data-id="itab811tb" data-path="src/components/Chapter5Content.tsx">
              <CardTitle className="text-right" data-id="1ggb79fgl" data-path="src/components/Chapter5Content.tsx">معاملات التحكم وتأثيراتها</CardTitle>
            </CardHeader>
            <CardContent data-id="0wmgsmnq6" data-path="src/components/Chapter5Content.tsx">
              <div className="space-y-4" data-id="j3n395lav" data-path="src/components/Chapter5Content.tsx">
                <div className="grid md:grid-cols-2 gap-4" data-id="71ybcmqo6" data-path="src/components/Chapter5Content.tsx">
                  <Card data-id="ufipu6ce1" data-path="src/components/Chapter5Content.tsx">
                    <CardHeader data-id="nmilvjwzv" data-path="src/components/Chapter5Content.tsx">
                      <CardTitle className="text-base text-right" data-id="3ponw6udz" data-path="src/components/Chapter5Content.tsx">kVp (الجهد)</CardTitle>
                    </CardHeader>
                    <CardContent data-id="untxs7in6" data-path="src/components/Chapter5Content.tsx">
                      <ul className="text-sm space-y-1 text-right" data-id="2v2p174sv" data-path="src/components/Chapter5Content.tsx">
                        <li data-id="9igihqb75" data-path="src/components/Chapter5Content.tsx">• يتحكم في جودة الشعاع</li>
                        <li data-id="rndh2yamd" data-path="src/components/Chapter5Content.tsx">• يؤثر على قدرة النفاذ</li>
                        <li data-id="1jzgqt0w2" data-path="src/components/Chapter5Content.tsx">• العلاقة: الكمية ∝ kVp²</li>
                        <li data-id="84t9065i2" data-path="src/components/Chapter5Content.tsx">• يحدد الطاقة القصوى</li>
                      </ul>
                    </CardContent>
                  </Card>
                  
                  <Card data-id="vug2k0z3g" data-path="src/components/Chapter5Content.tsx">
                    <CardHeader data-id="p7mwll62r" data-path="src/components/Chapter5Content.tsx">
                      <CardTitle className="text-base text-right" data-id="gsvlzwlm5" data-path="src/components/Chapter5Content.tsx">mAs (التيار × الوقت)</CardTitle>
                    </CardHeader>
                    <CardContent data-id="ec6xnr4hn" data-path="src/components/Chapter5Content.tsx">
                      <ul className="text-sm space-y-1 text-right" data-id="fz1rn5ddo" data-path="src/components/Chapter5Content.tsx">
                        <li data-id="6de5jziy3" data-path="src/components/Chapter5Content.tsx">• يتحكم في كمية الشعاع</li>
                        <li data-id="4g1d6n78o" data-path="src/components/Chapter5Content.tsx">• لا يؤثر على الجودة</li>
                        <li data-id="w7y5hy8pl" data-path="src/components/Chapter5Content.tsx">• العلاقة خطية</li>
                        <li data-id="lo466y6vv" data-path="src/components/Chapter5Content.tsx">• يحدد عدد الفوتونات</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>
                
                <Card data-id="xd470qnli" data-path="src/components/Chapter5Content.tsx">
                  <CardHeader data-id="on6lsgae6" data-path="src/components/Chapter5Content.tsx">
                    <CardTitle className="text-base text-right" data-id="kor7slsmt" data-path="src/components/Chapter5Content.tsx">أنظمة التحكم التلقائي (AEC)</CardTitle>
                  </CardHeader>
                  <CardContent data-id="rueruhvks" data-path="src/components/Chapter5Content.tsx">
                    <div className="grid md:grid-cols-3 gap-3" data-id="ga884xruu" data-path="src/components/Chapter5Content.tsx">
                      <div className="bg-blue-50 p-3 rounded" data-id="bzrxbb7w4" data-path="src/components/Chapter5Content.tsx">
                        <h5 className="font-semibold text-right" data-id="vibltr0lw" data-path="src/components/Chapter5Content.tsx">كاشفات أيونية</h5>
                        <p className="text-xs mt-1 text-right" data-id="xzzt79544" data-path="src/components/Chapter5Content.tsx">تقيس التأين المباشر</p>
                      </div>
                      <div className="bg-green-50 p-3 rounded" data-id="l5tl2z17s" data-path="src/components/Chapter5Content.tsx">
                        <h5 className="font-semibold text-right" data-id="0rjgqfiti" data-path="src/components/Chapter5Content.tsx">كاشفات صلبة</h5>
                        <p className="text-xs mt-1 text-right" data-id="jq0qvqz8b" data-path="src/components/Chapter5Content.tsx">استجابة سريعة ودقيقة</p>
                      </div>
                      <div className="bg-orange-50 p-3 rounded" data-id="wlnm93x8w" data-path="src/components/Chapter5Content.tsx">
                        <h5 className="font-semibold text-right" data-id="qxl3mxwe4" data-path="src/components/Chapter5Content.tsx">تحكم رقمي</h5>
                        <p className="text-xs mt-1 text-right" data-id="96k6lxxci" data-path="src/components/Chapter5Content.tsx">خوارزميات متقدمة</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card className="mt-8" data-id="zakuufe2h" data-path="src/components/Chapter5Content.tsx">
        <CardHeader data-id="jjeem9joj" data-path="src/components/Chapter5Content.tsx">
          <CardTitle className="text-right" data-id="skm1apvb9" data-path="src/components/Chapter5Content.tsx">أهداف التعلم</CardTitle>
        </CardHeader>
        <CardContent data-id="sbkx99w3y" data-path="src/components/Chapter5Content.tsx">
          <ul className="list-disc list-inside space-y-2 text-right" data-id="8bxgnrv68" data-path="src/components/Chapter5Content.tsx">
            <li data-id="yxnxz0phq" data-path="src/components/Chapter5Content.tsx">فهم مبادئ توليد الجهد العالي ومكونات المولدات</li>
            <li data-id="qahlzj4xk" data-path="src/components/Chapter5Content.tsx">مقارنة أنواع المولدات المختلفة وخصائصها</li>
            <li data-id="woxlc77wq" data-path="src/components/Chapter5Content.tsx">تعلم تأثير التموج على جودة الأشعة السينية</li>
            <li data-id="w5cwhhpf7" data-path="src/components/Chapter5Content.tsx">إتقان العلاقات بين معاملات التحكم وخصائص الشعاع</li>
            <li data-id="cbeb74vh6" data-path="src/components/Chapter5Content.tsx">فهم أنظمة التحكم التلقائي ومبادئ عملها</li>
          </ul>
        </CardContent>
      </Card>
    </div>);

};

export default Chapter5Content;