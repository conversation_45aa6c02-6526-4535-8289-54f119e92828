import React from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  Cpu,
  Target,
  Zap,
  TrendingUp,
  Calculator,
  Image,
  FlaskConical,
  AlertCircle,
  ChevronDown,
  BookOpen,
  Key,
  FileText,
  HelpCircle,
  Dice1,
  BarChart3,
  Waves } from
'lucide-react';

const Chapter10MonteCarloSimulation = () => {
  const [openSections, setOpenSections] = React.useState<{[key: string]: boolean;}>({});

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50" dir="rtl" data-id="fjfi7o4xx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
      <Navigation data-id="lm8sxpkdk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl" data-id="5k5nc64s4" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
        {/* Header */}
        <div className="text-center mb-12" data-id="utceftb5q" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Badge variant="secondary" className="mb-4 text-lg px-6 py-2" data-id="lw3wvq8nu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            الفصل العاشر
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4" data-id="zg5wr3jhr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            محاكاة مونت كارلو لنقل الفوتونات
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed" data-id="o9arhg5sk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            دراسة شاملة لأساليب محاكاة مونت كارلو في نمذجة نقل الإشعاع الفوتوني وتطبيقاتها الطبية
          </p>
        </div>

        {/* Learning Objectives */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-purple-500" data-id="xl7zavukk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <CardHeader data-id="wnetgrkip" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <CardTitle className="flex items-center text-2xl text-purple-700" data-id="le7xrzbzt" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <Target className="ml-3 h-6 w-6" data-id="kw3bj6wkl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
              أهداف التعلم
            </CardTitle>
          </CardHeader>
          <CardContent data-id="phlwn10l8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <ul className="space-y-3 text-gray-700" data-id="nif351t4n" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <li className="flex items-start" data-id="6lo0uon86" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <span className="text-purple-500 ml-2" data-id="s0553c0lc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                فهم مبادئ محاكاة مونت كارلو التناظرية لتتبع الفوتونات
              </li>
              <li className="flex items-start" data-id="xcyma7icq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <span className="text-purple-500 ml-2" data-id="tioi5ay3f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                إتقان تقنيات أخذ العينات للتفاعلات والمسافات والجسيمات الثانوية
              </li>
              <li className="flex items-start" data-id="s9wzr4xb0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <span className="text-purple-500 ml-2" data-id="ga6chm65o" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                تطبيق تقنيات تقليل التباين لتحسين كفاءة المحاكاة
              </li>
              <li className="flex items-start" data-id="99jnr03w3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <span className="text-purple-500 ml-2" data-id="4has0fb4x" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                حساب وتسجيل الكميات الفيزيائية المختلفة (الجرعة، التدفق، ترسب الطاقة)
              </li>
              <li className="flex items-start" data-id="phacepk3j" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <span className="text-purple-500 ml-2" data-id="4555clmdb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                تحليل تأثير الإشعاع المبعثر على جودة الصورة الطبية
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Section 10.1 */}
        <Card className="mb-6 shadow-lg" data-id="mg5sjzusv" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section101']}
            onOpenChange={() => toggleSection('section101')} data-id="jptf66hub" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="9929wubpn" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="elfsdqcta" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="d2vqzwwj2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="silslt2z5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <Cpu className="ml-3 h-6 w-6 text-blue-600" data-id="4inpwn77g" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.1 مبادئ مونت كارلو التناظرية لتتبع الفوتونات
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section101'] ? 'rotate-180' : ''}`} data-id="7vam9tuxz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="62bskzxy6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  الأسس النظرية والتطبيقية لمحاكاة مسار الفوتونات باستخدام مونت كارلو
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="ucmnaa357" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="pcbw8i1ni" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="msyqdn593" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="border-l-4 border-l-blue-500" data-id="vb0uyjh4w" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="gjr48ilol" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="b489szzzc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <Dice1 className="ml-2 h-5 w-5 text-blue-600" data-id="uf0adqjos" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                        المبادئ الأساسية
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="bzz3kxxfm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <ul className="space-y-2 text-sm text-gray-600" data-id="autt8g5cx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <li data-id="iisn71w5m" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• استخدام الأرقام العشوائية لمحاكاة العمليات الفيزيائية</li>
                        <li data-id="w9nguqn8o" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تتبع كل فوتون على حدة من المصدر إلى الامتصاص</li>
                        <li data-id="tts7ipi6c" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• حساب احتمالية كل تفاعل وفقاً للمقاطع العرضية</li>
                        <li data-id="2c4lec507" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تراكم الإحصائيات لحساب الكميات المطلوبة</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-green-500" data-id="mucnxoall" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="wb4k8rjrg" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="pvolo1ix0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <Target className="ml-2 h-5 w-5 text-green-600" data-id="7avixsw4d" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                        خوارزمية التتبع
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="xbr4e6t5b" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-3" data-id="ncr7914gx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-green-50 p-3 rounded" data-id="vymimuarq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-green-800 mb-2" data-id="klp4xhb6c" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">خطوات المحاكاة:</h4>
                          <ol className="text-sm text-gray-700 list-decimal list-inside space-y-1" data-id="du6k0svps" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="jlx6k8vho" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تهيئة خصائص الفوتون (الموقع، الاتجاه، الطاقة)</li>
                            <li data-id="4i1301f53" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">حساب المسافة إلى التفاعل التالي</li>
                            <li data-id="c0yt2um38" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تحديد نوع التفاعل (امتصاص، تبعثر)</li>
                            <li data-id="zcrgjmi8k" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تحديث خصائص الفوتون بعد التفاعل</li>
                            <li data-id="3jfft5pll" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تسجيل المعلومات المطلوبة</li>
                            <li data-id="pvjm4wd0e" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تكرار العملية حتى امتصاص الفوتون</li>
                          </ol>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Alert data-id="yjpe0wtlg" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <AlertCircle className="h-4 w-4" data-id="o46g0sv7y" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                  <AlertDescription data-id="9bukjj5mj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    دقة المحاكاة تعتمد على عدد الفوتونات المحاكية وجودة مولد الأرقام العشوائية المستخدم.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 10.2 */}
        <Card className="mb-6 shadow-lg" data-id="x9iu9ar0p" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section102']}
            onOpenChange={() => toggleSection('section102')} data-id="u9l6kshgx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="4i2b45jnw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="w861gprqz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="g9q0id0e2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="uzyqanhpe" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <BarChart3 className="ml-3 h-6 w-6 text-purple-600" data-id="u9ljlaird" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.2 تقنيات أخذ العينات
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section102'] ? 'rotate-180' : ''}`} data-id="zsx6g18j2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="z5ghio3vg" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  طرق أخذ العينات للتفاعلات والمسافات والجسيمات الثانوية
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="fqrqcgf3g" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="cu1axjnat" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="grid md:grid-cols-3 gap-6" data-id="caopkci9h" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="bg-gradient-to-br from-purple-50 to-purple-100" data-id="g3kzhozna" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="riulqf84y" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg text-purple-800" data-id="1vxd8kj1o" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">أخذ العينات للمسافة</CardTitle>
                    </CardHeader>
                    <CardContent data-id="qd0lwabt6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-3" data-id="z0561rwem" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-white p-3 rounded shadow-sm" data-id="w4d8trueq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="qvsnnjrle" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">قانون بير-لامبرت:</h4>
                          <p className="text-sm text-gray-600 text-center font-mono bg-gray-100 p-2 rounded" data-id="wqkbfluih" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            s = -ln(ξ)/Σ
                          </p>
                        </div>
                        <ul className="text-sm text-gray-700" data-id="thuungaqo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="sflpf3q4k" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• s: المسافة إلى التفاعل</li>
                          <li data-id="zcdp1t66p" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• ξ: رقم عشوائي [0,1]</li>
                          <li data-id="i9s58i81h" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• Σ: المقطع العرضي الكلي</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-blue-50 to-blue-100" data-id="vzoxzd3mh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="80apvt67k" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg text-blue-800" data-id="dnkfomori" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">نوع التفاعل</CardTitle>
                    </CardHeader>
                    <CardContent data-id="noubt9d9c" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-3" data-id="fsp3misqn" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-white p-3 rounded shadow-sm" data-id="xeze9yfgb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="t8tief6fi" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الاختيار المرجح:</h4>
                          <ul className="text-sm text-gray-700 space-y-1" data-id="trha79nl8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="ihbsnhycw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• σₚₕ/Σ: التأثير الكهروضوئي</li>
                            <li data-id="nsqlwy2ss" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• σᶜ/Σ: تبعثر كومبتون</li>
                            <li data-id="8dfhtn2r0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• σᵣ/Σ: إنتاج الأزواج</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-green-50 to-green-100" data-id="4cass02nh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="625qp8kvc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg text-green-800" data-id="gydr2ms2o" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الجسيمات الثانوية</CardTitle>
                    </CardHeader>
                    <CardContent data-id="67lqcigay" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-3" data-id="zvisl6scm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-white p-3 rounded shadow-sm" data-id="u84axu01i" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="md8yebqj5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">توزيع الطاقة والزاوية:</h4>
                          <ul className="text-sm text-gray-700 space-y-1" data-id="1sgoa59vb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="wn5u9ivlh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• معادلة كلاين-نيشينا</li>
                            <li data-id="jd19k7kkz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• توزيع الإلكترونات الثانوية</li>
                            <li data-id="obp0dzciz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• أشعة X المميزة</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 10.3 */}
        <Card className="mb-6 shadow-lg" data-id="knb8v40pu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section103']}
            onOpenChange={() => toggleSection('section103')} data-id="e6yncxm6g" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="2qe97gr9b" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="nh0mqzoub" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="myyllz3aa" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="n136tysnz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <TrendingUp className="ml-3 h-6 w-6 text-orange-600" data-id="du6nvzn8f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.3 تقنيات تقليل التباين
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section103'] ? 'rotate-180' : ''}`} data-id="pisdid1ks" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="dgjwlalxr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  طرق تحسين كفاءة المحاكاة وتقليل الوقت الحاسوبي المطلوب
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="d17jssdfu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="ohn3utrbz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="grid md:grid-cols-3 gap-6" data-id="nb4h1frrd" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="border-t-4 border-t-red-500" data-id="djvm89ngm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="e1f649u61" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="u1cwtil90" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <Target className="ml-2 h-5 w-5 text-red-600" data-id="omzk5l14t" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                        العينة ذات الأهمية
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3" data-id="jpmq2wlvb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div data-id="irisykh35" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="tiqo09v55" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المبدأ:</h4>
                        <p className="text-sm text-gray-600" data-id="bwmojgxcz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          تركيز العينات في المناطق المهمة للكمية المقاسة
                        </p>
                      </div>
                      <div data-id="og72ftu4r" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="mt7b1owfd" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">التطبيقات:</h4>
                        <ul className="text-sm text-gray-600 space-y-1" data-id="hk1qdf0bm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="3dltpl3lb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تحيز الاتجاه نحو الكاشف</li>
                          <li data-id="uxtig2lcd" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تحيز الطاقة للفوتونات المهمة</li>
                          <li data-id="p18onu45i" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تحيز المسافة في المناطق الحرجة</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-t-4 border-t-blue-500" data-id="bcuegvd7l" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="65guvthms" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="l9hrq7xzu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <span className="text-2xl ml-2" data-id="hbmqc34l2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">🔄</span>
                        التقسيم
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3" data-id="xdg5e4qhc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div data-id="wh40et2jp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="4uca9st0b" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المبدأ:</h4>
                        <p className="text-sm text-gray-600" data-id="qvkh0orkk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          تقسيم الجسيم إلى عدة نسخ بأوزان مخفضة
                        </p>
                      </div>
                      <div data-id="35dwncl7u" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="y0ly469mw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الاستخدامات:</h4>
                        <ul className="text-sm text-gray-600 space-y-1" data-id="t43w9kgvf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="tu48guflc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• زيادة الإحصائيات في المناطق المهمة</li>
                          <li data-id="d3hzgiw38" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تحسين دقة القياس</li>
                          <li data-id="jfp7z3at1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تقليل التباين النسبي</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-t-4 border-t-green-500" data-id="vf172ir2v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="1elvui7ce" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="lyutvip6d" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <span className="text-2xl ml-2" data-id="ibsogmom3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">🎰</span>
                        الروليت الروسي
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3" data-id="z1vhwd2jz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div data-id="bws3zxwk6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="feh71nqti" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المبدأ:</h4>
                        <p className="text-sm text-gray-600" data-id="eokqp51rp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          إنهاء تتبع الجسيمات منخفضة الوزن عشوائياً
                        </p>
                      </div>
                      <div data-id="9bg6sius0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="5kccnonvj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الفوائد:</h4>
                        <ul className="text-sm text-gray-600 space-y-1" data-id="8ipv7irj7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="69xxpkjtz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• توفير الوقت الحاسوبي</li>
                          <li data-id="8ogsz6fy8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• التركيز على الجسيمات المهمة</li>
                          <li data-id="zoueofwl3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• منع تراكم الأوزان الصغيرة</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Alert data-id="6gzjclwoi" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <AlertCircle className="h-4 w-4" data-id="8adlxh3qu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                  <AlertDescription data-id="vd5ww2j6n" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    تقنيات تقليل التباين يجب أن تُطبق بحذر للحفاظ على دقة النتائج وعدم إدخال تحيز في المحاكاة.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 10.4 */}
        <Card className="mb-6 shadow-lg" data-id="swtwn5usz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section104']}
            onOpenChange={() => toggleSection('section104')} data-id="edgpvt3b9" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="rrdxpvxtb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="ax7yda4sy" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="aqqt9p8tu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="kdtajsijb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <Calculator className="ml-3 h-6 w-6 text-indigo-600" data-id="6b3syd37t" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.4 العد والتسجيل
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section104'] ? 'rotate-180' : ''}`} data-id="bl0b50wh7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="1rg4fkpch" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  تقدير الكميات الفيزيائية: الجرعة، التدفق، وترسب الطاقة
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="y1gsfmgab" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="cjuf07cjz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="qv83fp0z3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100" data-id="nvqe5phbq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="9x7uth4v0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg text-indigo-800" data-id="iuga7158o" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">أنواع العدادات (Tallies)</CardTitle>
                    </CardHeader>
                    <CardContent data-id="xt137eu59" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-4" data-id="52e89ymes" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-white p-3 rounded shadow-sm" data-id="1w667u83a" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="z3wpurk91" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">عداد التدفق:</h4>
                          <ul className="text-sm text-gray-700 space-y-1" data-id="b9yxjp6rq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="6expvb7wv" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• F1: التدفق السطحي</li>
                            <li data-id="0a21zx8cz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• F2: التدفق الزاوي</li>
                            <li data-id="dkjpl0nt8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• F4: التدفق الحجمي</li>
                          </ul>
                        </div>
                        <div className="bg-white p-3 rounded shadow-sm" data-id="cs67akc7d" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="c5p22s08k" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">عداد الطاقة:</h4>
                          <ul className="text-sm text-gray-700 space-y-1" data-id="2kysikfcn" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="k9g50re5v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• F6: ترسب الطاقة</li>
                            <li data-id="80bie2s4a" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• F8: توزيع الطاقة النبضي</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-cyan-50 to-cyan-100" data-id="0g00q0loc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="xe9pcxlo5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg text-cyan-800" data-id="b41mvtegd" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">حساب الجرعة</CardTitle>
                    </CardHeader>
                    <CardContent data-id="ely5y5xop" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div className="space-y-4" data-id="5tu9qc2zx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <div className="bg-white p-3 rounded shadow-sm" data-id="0pmxqeo5k" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="6tsqpo5wf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">معادلة الجرعة:</h4>
                          <p className="text-center font-mono bg-gray-100 p-2 rounded text-sm" data-id="r412vrn0a" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            D = E_dep / (ρ × V)
                          </p>
                          <ul className="text-xs text-gray-600 mt-2 space-y-1" data-id="15we8n12u" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            <li data-id="dt7f43bcs" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• D: الجرعة الممتصة (Gy)</li>
                            <li data-id="87hhdrkhh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• E_dep: الطاقة المترسبة (J)</li>
                            <li data-id="8howbdv2z" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• ρ: كثافة المادة (kg/m³)</li>
                            <li data-id="fkg33p4q8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• V: الحجم (m³)</li>
                          </ul>
                        </div>
                        <div className="bg-white p-3 rounded shadow-sm" data-id="djw70ax92" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <h4 className="font-semibold text-gray-800 mb-2" data-id="4pwizmutc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تحويل الطاقة إلى جرعة:</h4>
                          <p className="text-sm text-gray-700" data-id="lo5zyqiyf" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                            استخدام عوامل التحويل المعيارية لكل نسيج
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 10.5 */}
        <Card className="mb-6 shadow-lg" data-id="hqlp4kh4g" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section105']}
            onOpenChange={() => toggleSection('section105')} data-id="olhal918d" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="p8g7dvc9a" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="3j2wk6zyc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="kgcll8gzs" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="2bb83j3i0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <Waves className="ml-3 h-6 w-6 text-teal-600" data-id="ahzc2cuc1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.5 محاكاة الإشعاع المبعثر
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section105'] ? 'rotate-180' : ''}`} data-id="w2sx5onet" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="zyj3ijsx6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  تأثير الإشعاع المبعثر على جودة الصورة الطبية
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="55w7rz5jl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="qj2vjqpbl" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="uv3dkirau" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="border-l-4 border-l-teal-500" data-id="ghsv6w93d" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="bm4y78b2j" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg" data-id="prv0ztk1f" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مصادر الإشعاع المبعثر</CardTitle>
                    </CardHeader>
                    <CardContent data-id="ibo8jl4wx" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <ul className="space-y-2 text-gray-700" data-id="di5o3h7as" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <li className="flex items-start" data-id="is2c3r71z" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-teal-500 ml-2 mt-1" data-id="5hcumuyex" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="d6s50119u" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تبعثر كومبتون في المريض</span>
                        </li>
                        <li className="flex items-start" data-id="ghwbxfwyr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-teal-500 ml-2 mt-1" data-id="g9ajt6jew" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="58tvn91vo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تبعثر في أجهزة التصوير</span>
                        </li>
                        <li className="flex items-start" data-id="04bbz2o2q" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-teal-500 ml-2 mt-1" data-id="eirqef27w" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="ngsj7hzls" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تبعثر في الهواء المحيط</span>
                        </li>
                        <li className="flex items-start" data-id="ftbc4f3m0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-teal-500 ml-2 mt-1" data-id="ib2en50yq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="qmw9ia005" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">انتثار خلفي من الكاشف</span>
                        </li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-orange-500" data-id="t84j3yt9c" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="ywh7c5kxr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg" data-id="cf0sifjxq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تأثيرات على جودة الصورة</CardTitle>
                    </CardHeader>
                    <CardContent data-id="3s7tnpfcc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <ul className="space-y-2 text-gray-700" data-id="rle8s2n2g" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <li className="flex items-start" data-id="7dymjozla" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-orange-500 ml-2 mt-1" data-id="pp3gaklwm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="p914snkzq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تقليل التباين في الصورة</span>
                        </li>
                        <li className="flex items-start" data-id="ees7zyg0v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-orange-500 ml-2 mt-1" data-id="cuo30a30h" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="8j3k3gyw0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">زيادة الضوضاء الخلفية</span>
                        </li>
                        <li className="flex items-start" data-id="endzwtgmp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-orange-500 ml-2 mt-1" data-id="24aqsrqe0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="gp6ul9v8n" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تشويه التوزيع المكاني للطاقة</span>
                        </li>
                        <li className="flex items-start" data-id="d269p1tjh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <span className="text-orange-500 ml-2 mt-1" data-id="nggzp5if6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">•</span>
                          <span data-id="hhv8eexlo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تقليل الدقة التشخيصية</span>
                        </li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <Card className="bg-gradient-to-r from-blue-50 to-purple-50" data-id="mq5g7lyuz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <CardHeader data-id="5gzgqghwe" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardTitle className="text-lg" data-id="xwz814jwy" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">استراتيجيات تقليل التبعثر</CardTitle>
                  </CardHeader>
                  <CardContent data-id="bgq5lkao2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <div className="grid md:grid-cols-2 gap-4" data-id="shhannaz9" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <div data-id="ltxmmekcw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="r1tysj2u9" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الطرق الفيزيائية:</h4>
                        <ul className="text-sm text-gray-700 space-y-1" data-id="ehqt3goos" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="tsl0ptq1m" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• شبكات مضادة للتبعثر</li>
                          <li data-id="20pm8aj8l" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تقليل حجم الحزمة</li>
                          <li data-id="iknn9j0ws" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تحسين هندسة التصوير</li>
                          <li data-id="jrlvs6geu" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• استخدام مرشحات طاقية</li>
                        </ul>
                      </div>
                      <div data-id="913jgn4iq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="nlrgt1egt" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الطرق الحاسوبية:</h4>
                        <ul className="text-sm text-gray-700 space-y-1" data-id="qgv0wadqq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                          <li data-id="8xfuhiyp5" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تصحيح التبعثر بعد المعالجة</li>
                          <li data-id="w5r175f4v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• خوارزميات الطرح الطيفي</li>
                          <li data-id="424hk6du3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• النمذجة الحاسوبية للتبعثر</li>
                          <li data-id="ds8p7z3ds" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• تقنيات الذكاء الاصطناعي</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 10.6 */}
        <Card className="mb-6 shadow-lg" data-id="3m5citc53" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <Collapsible
            open={openSections['section106']}
            onOpenChange={() => toggleSection('section106')} data-id="8y9apgld8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">

            <CollapsibleTrigger className="w-full" data-id="icyx34zqm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="wzoqy0pp6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="f8pqh4dgq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <div className="flex items-center" data-id="nypywdawq" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <FlaskConical className="ml-3 h-6 w-6 text-green-600" data-id="iwuinxlxj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                    10.6 تمرين عملي: محاكاة توهين الشعاع
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section106'] ? 'rotate-180' : ''}`} data-id="phcructi1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
                </CardTitle>
                <CardDescription data-id="0n0eoh428" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  محاكاة عملية لتوهين شعاع الفوتونات خلال شبح الماء
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="yeyzhsij3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <CardContent className="space-y-6" data-id="40n87t4k8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg" data-id="bnyrmdx71" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h3 className="text-xl font-bold text-gray-800 mb-4" data-id="wxp03auwj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">هدف التمرين</h3>
                  <p className="text-gray-700 mb-4" data-id="htkkcvybs" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    محاكاة توهين حزمة فوتونات أحادية الطاقة (100 keV) خلال شبح مائي أسطواني 
                    وحساب معامل التوهين الخطي ومقارنته بالقيم النظرية.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-6" data-id="dwcnkjcif" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <Card className="border-t-4 border-t-blue-500" data-id="0hv44yfx2" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="rb9x0c3px" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg" data-id="3iibow98x" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المعطيات التجريبية</CardTitle>
                    </CardHeader>
                    <CardContent data-id="lh9wdbf8l" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <ul className="space-y-2 text-gray-700" data-id="zpv6a9dt0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <li data-id="va2956jbo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx"><strong data-id="im1jya2vm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">طاقة الفوتونات:</strong> 100 keV</li>
                        <li data-id="01i72kc4j" data-path="src/pages/Chapter10MonteCarloSimulation.tsx"><strong data-id="6vqlgusma" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">هندسة الشبح:</strong> أسطوانة قطرها 20 cm</li>
                        <li data-id="fmedr041r" data-path="src/pages/Chapter10MonteCarloSimulation.tsx"><strong data-id="h2azdl1mo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مادة الشبح:</strong> ماء (ρ = 1.0 g/cm³)</li>
                        <li data-id="sy6pyr7d3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx"><strong data-id="4fdiaps08" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">عدد الفوتونات:</strong> 10⁶</li>
                        <li data-id="jrtqyxoph" data-path="src/pages/Chapter10MonteCarloSimulation.tsx"><strong data-id="g9q9jwgzb" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">سماكات مختلفة:</strong> 1, 2, 5, 10, 15 cm</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="border-t-4 border-t-purple-500" data-id="nnfq8jswj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardHeader data-id="2arkqnu2g" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <CardTitle className="text-lg" data-id="kvt2rutr4" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الكميات المطلوب حسابها</CardTitle>
                    </CardHeader>
                    <CardContent data-id="xkxdltffc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <ul className="space-y-2 text-gray-700" data-id="6qlvw6dzt" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                        <li data-id="u28ejpt07" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• معامل التوهين الخطي μ</li>
                        <li data-id="x7mb36kvd" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• النسبة المئوية للفوتونات المنتقلة</li>
                        <li data-id="cz8ugrmnv" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• التوزيع الطيفي للفوتونات المبعثرة</li>
                        <li data-id="6yqnm684e" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• توزيع الجرعة المكانية</li>
                        <li data-id="zwdomfmmk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">• مقارنة مع القانون الأسي</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <Card className="bg-yellow-50 border-l-4 border-l-yellow-500" data-id="hiaq9hy8c" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <CardHeader data-id="l4sda5v67" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <CardTitle className="text-lg text-yellow-800" data-id="c7d5zafp8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">خطوات التنفيذ</CardTitle>
                  </CardHeader>
                  <CardContent data-id="uz366f9dj" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                    <ol className="list-decimal list-inside space-y-2 text-gray-700" data-id="e6rhl0pol" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                      <li data-id="5ypmyrd5y" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">إعداد الهندسة والمواد في برنامج المحاكاة</li>
                      <li data-id="7ox5vl9to" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تعريف مصدر الفوتونات (نقطي، اتجاه واحد)</li>
                      <li data-id="nxswv5104" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تشغيل المحاكاة لكل سماكة</li>
                      <li data-id="yas401n33" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تسجيل عدد الفوتونات المنتقلة والمبعثرة</li>
                      <li data-id="qlimlxndh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">حساب معامل التوهين من المعادلة: I = I₀e^(-μx)</li>
                      <li data-id="asn7hsy8v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مقارنة النتائج بالقيم النظرية</li>
                      <li data-id="l9negary3" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تحليل الأخطاء الإحصائية</li>
                    </ol>
                  </CardContent>
                </Card>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Key Terms */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-yellow-500" data-id="kr66m9y4d" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <CardHeader data-id="4q044lcfi" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <CardTitle className="flex items-center text-2xl text-yellow-700" data-id="kw18p7kuz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <Key className="ml-3 h-6 w-6" data-id="6tocm72gt" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
              المصطلحات الرئيسية
            </CardTitle>
          </CardHeader>
          <CardContent data-id="klct3pywe" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <div className="grid md:grid-cols-2 gap-6" data-id="1v77kdb0c" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <div className="space-y-3" data-id="b4sn0ls8m" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="border-r-4 border-r-blue-400 pr-4" data-id="wm4k6lyn6" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="y7w1k63l1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مونت كارلو التناظرية</h4>
                  <p className="text-sm text-gray-600" data-id="pfpamtges" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">محاكاة مباشرة للعمليات الفيزيائية دون تقريبات</p>
                </div>
                <div className="border-r-4 border-r-green-400 pr-4" data-id="e7h4yvudw" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="kipatg8yz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">أخذ العينات العكسي</h4>
                  <p className="text-sm text-gray-600" data-id="u384095js" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تقنية لتحويل التوزيع المنتظم إلى توزيع مطلوب</p>
                </div>
                <div className="border-r-4 border-r-purple-400 pr-4" data-id="8sa6exy4u" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="idjr34b0j" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تقليل التباين</h4>
                  <p className="text-sm text-gray-600" data-id="phgl33mu0" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">تقنيات لتحسين كفاءة المحاكاة الإحصائية</p>
                </div>
                <div className="border-r-4 border-r-red-400 pr-4" data-id="n65e7rgas" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="yo7bbc0xm" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">العداد (Tally)</h4>
                  <p className="text-sm text-gray-600" data-id="m6hgbw76q" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">أداة لقياس وتسجيل الكميات الفيزيائية</p>
                </div>
              </div>
              <div className="space-y-3" data-id="4j2h0qv3j" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <div className="border-r-4 border-r-indigo-400 pr-4" data-id="quqlifxyg" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="qq8rrised" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الوزن الإحصائي</h4>
                  <p className="text-sm text-gray-600" data-id="1eif1hm3m" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">عامل يمثل أهمية الجسيم في المحاكاة</p>
                </div>
                <div className="border-r-4 border-r-teal-400 pr-4" data-id="cewz99bsp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="wfgvgqyw7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المقطع العرضي الكلي</h4>
                  <p className="text-sm text-gray-600" data-id="zq6mwi1si" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مجموع احتماليات جميع التفاعلات الممكنة</p>
                </div>
                <div className="border-r-4 border-r-orange-400 pr-4" data-id="634mch99k" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="kff21966y" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الكيرما (KERMA)</h4>
                  <p className="text-sm text-gray-600" data-id="kudwhvz69" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">الطاقة الحركية المحررة في المادة</p>
                </div>
                <div className="border-r-4 border-r-pink-400 pr-4" data-id="8zh8a7m3h" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="mo4sknb43" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">نسبة التبعثر إلى الأولي</h4>
                  <p className="text-sm text-gray-600" data-id="l0pdb0f3y" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">مقياس جودة الصورة في التصوير الطبي</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* References */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-green-500" data-id="u9e569t8v" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <CardHeader data-id="n0b4c7fy1" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <CardTitle className="flex items-center text-2xl text-green-700" data-id="8fpm7eqds" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <FileText className="ml-3 h-6 w-6" data-id="gel1uxxs9" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
              المراجع
            </CardTitle>
          </CardHeader>
          <CardContent data-id="6kitt4bvp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <ul className="space-y-2 text-gray-700" data-id="oehvilqdi" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <li data-id="q3v32jwqr" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">1. Kalos, M.H., Whitlock, P.A.: Monte Carlo Methods. Wiley-VCH (2008)</li>
              <li data-id="3byurhqup" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">2. Rogers, D.W.O.: Fifty years of Monte Carlo simulations for medical physics. Phys Med Biol 51, R287-R301 (2006)</li>
              <li data-id="o6jpwq1t8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">3. Salvat, F.: PENELOPE-2018: A Code System for Monte Carlo Simulation (2019)</li>
              <li data-id="0yowgeisk" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">4. Kawrakow, I., et al.: The EGSnrc Code System: Monte Carlo Simulation of Electron and Photon Transport. NRCC Report PIRS-701 (2017)</li>
              <li data-id="uiu8hv2ie" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">5. Pelowitz, D.B., et al.: MCNP6 User's Manual. Los Alamos National Laboratory (2013)</li>
              <li data-id="jsc6idelh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">6. Verhaegen, F., Seuntjens, J.: Monte Carlo modelling of external radiotherapy photon beams. Phys Med Biol 48, R107-R164 (2003)</li>
            </ul>
          </CardContent>
        </Card>

        {/* Problems */}
        <Card className="shadow-lg border-t-4 border-t-orange-500" data-id="pcuw4krkn" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
          <CardHeader data-id="6r49s3doc" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <CardTitle className="flex items-center text-2xl text-orange-700" data-id="vklcgvs4x" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <HelpCircle className="ml-3 h-6 w-6" data-id="7xovdu8oo" data-path="src/pages/Chapter10MonteCarloSimulation.tsx" />
              المشكلات والتمارين
            </CardTitle>
          </CardHeader>
          <CardContent data-id="rphi2s2kz" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
            <div className="space-y-6" data-id="tmxoennnp" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
              <div className="bg-orange-50 p-4 rounded-lg" data-id="hyu56jld8" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <h4 className="font-semibold text-orange-800 mb-2" data-id="jb99wmg4w" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المشكلة 1: حساب المسافة إلى التفاعل</h4>
                <p className="text-gray-700" data-id="9xy85x16a" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  فوتون بطاقة 500 keV يسافر في الماء. المقطع العرضي الكلي هو 0.096 cm⁻¹. 
                  إذا كان الرقم العشوائي ξ = 0.3، احسب المسافة إلى التفاعل التالي.
                </p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg" data-id="voeyewn6z" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <h4 className="font-semibold text-blue-800 mb-2" data-id="uumeq4jde" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المشكلة 2: تحديد نوع التفاعل</h4>
                <p className="text-gray-700" data-id="6wptys430" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  لفوتون بطاقة 200 keV في العظم: σₚₕ = 0.02 cm⁻¹، σᶜ = 0.15 cm⁻¹، σᵣ = 0 cm⁻¹. 
                  إذا كان ξ = 0.7، حدد نوع التفاعل الذي سيحدث.
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg" data-id="nintg70mh" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <h4 className="font-semibold text-green-800 mb-2" data-id="4v6b76vxn" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المشكلة 3: حساب كفاءة التقسيم</h4>
                <p className="text-gray-700" data-id="81o5rn58l" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  تم تقسيم الفوتونات في منطقة معينة بعامل 4. إذا كان التباين الأصلي 0.05، 
                  احسب التباين الجديد والكسب في الكفاءة.
                </p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg" data-id="oi4zzm6k7" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                <h4 className="font-semibold text-purple-800 mb-2" data-id="xfzteqqea" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">المشكلة 4: تصميم تمرين محاكاة</h4>
                <p className="text-gray-700" data-id="0zgo3a5te" data-path="src/pages/Chapter10MonteCarloSimulation.tsx">
                  صمم تجربة محاكاة لحساب نسبة التبعثر إلى الأولي في صورة صدرية. 
                  حدد المعطيات المطلوبة والكميات التي ستقيسها.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default Chapter10MonteCarloSimulation;