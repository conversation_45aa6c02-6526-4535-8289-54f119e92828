import { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, RotateCcw, Settings, Zap, Thermometer } from 'lucide-react';

const XRaySimulator = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [voltage, setVoltage] = useState([80]); // kV
  const [current, setCurrent] = useState([200]); // mA
  const [time, setTime] = useState([100]); // ms
  const [anodeTemp, setAnodeTemp] = useState(20); // °C
  const [xrayIntensity, setXrayIntensity] = useState(0);

  useEffect(() => {
    if (isRunning) {
      const interval = setInterval(() => {
        // Simulate temperature increase
        setAnodeTemp((prev) => Math.min(prev + 10, 1200));
        // Calculate X-ray intensity based on parameters
        const intensity = voltage[0] * current[0] * time[0] / 10000;
        setXrayIntensity(Math.min(intensity, 100));
      }, 100);

      const timeout = setTimeout(() => {
        setIsRunning(false);
      }, time[0] * 10);

      return () => {
        clearInterval(interval);
        clearTimeout(timeout);
      };
    } else {
      // Cool down
      const coolInterval = setInterval(() => {
        setAnodeTemp((prev) => Math.max(prev - 5, 20));
        setXrayIntensity((prev) => Math.max(prev - 2, 0));
      }, 100);

      return () => clearInterval(coolInterval);
    }
  }, [isRunning, time, voltage, current]);

  const startSimulation = () => {
    setIsRunning(true);
  };

  const stopSimulation = () => {
    setIsRunning(false);
  };

  const resetSimulation = () => {
    setIsRunning(false);
    setAnodeTemp(20);
    setXrayIntensity(0);
  };

  const getTemperatureColor = (temp: number) => {
    if (temp < 100) return 'text-blue-600';
    if (temp < 500) return 'text-yellow-600';
    if (temp < 800) return 'text-orange-600';
    return 'text-red-600';
  };

  const getIntensityColor = (intensity: number) => {
    if (intensity < 25) return 'bg-green-500';
    if (intensity < 50) return 'bg-yellow-500';
    if (intensity < 75) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <div className="grid lg:grid-cols-2 gap-8" data-id="h5etu4txu" data-path="src/components/XRaySimulator.tsx">
      {/* Control Panel */}
      <motion.div
        initial={{ opacity: 0, x: -30 }}
        animate={{ opacity: 1, x: 0 }}
        className="space-y-6" data-id="jid2v9zke" data-path="src/components/XRaySimulator.tsx">

        <Card data-id="7q1dyyr54" data-path="src/components/XRaySimulator.tsx">
          <CardHeader data-id="2cxjhcj4q" data-path="src/components/XRaySimulator.tsx">
            <CardTitle className="flex items-center gap-2" data-id="1h6u6moqn" data-path="src/components/XRaySimulator.tsx">
              <Settings className="w-5 h-5 text-blue-600" data-id="tizsrnpv1" data-path="src/components/XRaySimulator.tsx" />
              لوحة التحكم
            </CardTitle>
            <CardDescription data-id="gncg7s1fk" data-path="src/components/XRaySimulator.tsx">
              اضبط معاملات التشغيل لمحاكاة إنتاج الأشعة السينية
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6" data-id="cp540ke26" data-path="src/components/XRaySimulator.tsx">
            {/* Voltage Control */}
            <div className="space-y-3" data-id="t5kikapot" data-path="src/components/XRaySimulator.tsx">
              <div className="flex justify-between items-center" data-id="mbiz5b68w" data-path="src/components/XRaySimulator.tsx">
                <label className="text-sm font-medium" data-id="nzjp2fvtf" data-path="src/components/XRaySimulator.tsx">الجهد (kV)</label>
                <Badge variant="outline" data-id="08u65sbqr" data-path="src/components/XRaySimulator.tsx">{voltage[0]} kV</Badge>
              </div>
              <Slider
                value={voltage}
                onValueChange={setVoltage}
                max={150}
                min={40}
                step={5}
                disabled={isRunning}
                className="w-full" data-id="ntowwxcmb" data-path="src/components/XRaySimulator.tsx" />

              <p className="text-xs text-gray-500" data-id="juoob03y0" data-path="src/components/XRaySimulator.tsx">
                يؤثر على طاقة الأشعة السينية وقدرتها على الاختراق
              </p>
            </div>

            {/* Current Control */}
            <div className="space-y-3" data-id="jr5tuup8e" data-path="src/components/XRaySimulator.tsx">
              <div className="flex justify-between items-center" data-id="b81pgvu2e" data-path="src/components/XRaySimulator.tsx">
                <label className="text-sm font-medium" data-id="78vsj08fu" data-path="src/components/XRaySimulator.tsx">التيار (mA)</label>
                <Badge variant="outline" data-id="2f9a8dt5r" data-path="src/components/XRaySimulator.tsx">{current[0]} mA</Badge>
              </div>
              <Slider
                value={current}
                onValueChange={setCurrent}
                max={500}
                min={50}
                step={25}
                disabled={isRunning}
                className="w-full" data-id="kh0y9t2gc" data-path="src/components/XRaySimulator.tsx" />

              <p className="text-xs text-gray-500" data-id="p55kj2j6o" data-path="src/components/XRaySimulator.tsx">
                يحدد كمية الأشعة السينية المنتجة
              </p>
            </div>

            {/* Time Control */}
            <div className="space-y-3" data-id="gilmm1dhb" data-path="src/components/XRaySimulator.tsx">
              <div className="flex justify-between items-center" data-id="tjivlmj9a" data-path="src/components/XRaySimulator.tsx">
                <label className="text-sm font-medium" data-id="k7eahlc6f" data-path="src/components/XRaySimulator.tsx">زمن التعرض (ms)</label>
                <Badge variant="outline" data-id="nvdcra9se" data-path="src/components/XRaySimulator.tsx">{time[0]} ms</Badge>
              </div>
              <Slider
                value={time}
                onValueChange={setTime}
                max={1000}
                min={10}
                step={10}
                disabled={isRunning}
                className="w-full" data-id="p0ib0mct9" data-path="src/components/XRaySimulator.tsx" />

              <p className="text-xs text-gray-500" data-id="bky4x8khv" data-path="src/components/XRaySimulator.tsx">
                مدة إنتاج الأشعة السينية
              </p>
            </div>

            {/* Control Buttons */}
            <div className="flex gap-3 pt-4" data-id="5hh27g7a9" data-path="src/components/XRaySimulator.tsx">
              <Button
                onClick={startSimulation}
                disabled={isRunning}
                className="flex-1" data-id="b21qa9due" data-path="src/components/XRaySimulator.tsx">

                <Play className="w-4 h-4 ml-2" data-id="383t7q9p1" data-path="src/components/XRaySimulator.tsx" />
                تشغيل
              </Button>
              <Button
                onClick={stopSimulation}
                disabled={!isRunning}
                variant="outline"
                className="flex-1" data-id="s7hfobcne" data-path="src/components/XRaySimulator.tsx">

                <Pause className="w-4 h-4 ml-2" data-id="8aah75zon" data-path="src/components/XRaySimulator.tsx" />
                إيقاف
              </Button>
              <Button
                onClick={resetSimulation}
                variant="outline"
                size="icon" data-id="gw8gzdvjv" data-path="src/components/XRaySimulator.tsx">

                <RotateCcw className="w-4 h-4" data-id="3d1y0iylv" data-path="src/components/XRaySimulator.tsx" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Calculated Values */}
        <Card data-id="6a6x314c3" data-path="src/components/XRaySimulator.tsx">
          <CardHeader data-id="13zntcpyk" data-path="src/components/XRaySimulator.tsx">
            <CardTitle className="text-lg" data-id="06cqtrlj4" data-path="src/components/XRaySimulator.tsx">القيم المحسوبة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4" data-id="5p513emcl" data-path="src/components/XRaySimulator.tsx">
            <div className="grid grid-cols-2 gap-4" data-id="9k5oqy29f" data-path="src/components/XRaySimulator.tsx">
              <div className="text-center p-3 bg-gray-50 rounded-lg" data-id="ys9fdzs2o" data-path="src/components/XRaySimulator.tsx">
                <div className="text-sm text-gray-600" data-id="mqjqif4mr" data-path="src/components/XRaySimulator.tsx">القدرة</div>
                <div className="text-xl font-bold" data-id="f485cxeh7" data-path="src/components/XRaySimulator.tsx">
                  {(voltage[0] * current[0] / 1000).toFixed(1)} kW
                </div>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg" data-id="suwmlr9ne" data-path="src/components/XRaySimulator.tsx">
                <div className="text-sm text-gray-600" data-id="3ewf72nl4" data-path="src/components/XRaySimulator.tsx">الطاقة</div>
                <div className="text-xl font-bold" data-id="8etmtxtuf" data-path="src/components/XRaySimulator.tsx">
                  {(voltage[0] * current[0] * time[0] / 1000000).toFixed(2)} kJ
                </div>
              </div>
            </div>
            <div className="text-xs text-gray-500 text-center" data-id="03i3y4k7z" data-path="src/components/XRaySimulator.tsx">
              الطاقة المنتجة تتحول إلى أشعة سينية (1%) وحرارة (99%)
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Visualization */}
      <motion.div
        initial={{ opacity: 0, x: 30 }}
        animate={{ opacity: 1, x: 0 }}
        className="space-y-6" data-id="l7e6eriqb" data-path="src/components/XRaySimulator.tsx">

        {/* Visual Display */}
        <Card data-id="lg5mw7qyj" data-path="src/components/XRaySimulator.tsx">
          <CardHeader data-id="8zpq7zg07" data-path="src/components/XRaySimulator.tsx">
            <CardTitle className="flex items-center gap-2" data-id="687g8l2pr" data-path="src/components/XRaySimulator.tsx">
              <Zap className="w-5 h-5 text-yellow-600" data-id="ojmed31q9" data-path="src/components/XRaySimulator.tsx" />
              المحاكاة المرئية
            </CardTitle>
          </CardHeader>
          <CardContent data-id="vy83e54me" data-path="src/components/XRaySimulator.tsx">
            <div className="relative bg-gray-900 rounded-lg p-6 min-h-[300px] overflow-hidden" data-id="g6hnahpab" data-path="src/components/XRaySimulator.tsx">
              {/* Tube Outline */}
              <svg width="100%" height="100%" className="absolute inset-0" data-id="pjtqyrgzz" data-path="src/components/XRaySimulator.tsx">
                {/* Cathode */}
                <rect x="20" y="120" width="30" height="30" fill="#ef4444" rx="5" data-id="07ixcu7ol" data-path="src/components/XRaySimulator.tsx" />
                <text x="35" y="110" textAnchor="middle" className="text-xs fill-white" data-id="suade1y9n" data-path="src/components/XRaySimulator.tsx">
                  الكاثود
                </text>

                {/* Anode */}
                <circle cx="280" cy="135" r="20" fill="#3b82f6" data-id="tr7o8wzo0" data-path="src/components/XRaySimulator.tsx" />
                <text x="280" y="170" textAnchor="middle" className="text-xs fill-white" data-id="szssbbpeg" data-path="src/components/XRaySimulator.tsx">
                  الأنود
                </text>

                {/* Electron Beam Animation */}
                {isRunning &&
                <motion.g data-id="s4ps2zeyo" data-path="src/components/XRaySimulator.tsx">
                    {[...Array(5)].map((_, i) =>
                  <motion.circle
                    key={i}
                    r="2"
                    fill="#fbbf24"
                    initial={{ cx: 50, cy: 135 }}
                    animate={{ cx: 260, cy: 135 }}
                    transition={{
                      duration: 0.5,
                      repeat: Infinity,
                      delay: i * 0.1
                    }} data-id="fgsd5nh5k" data-path="src/components/XRaySimulator.tsx" />

                  )}
                  </motion.g>
                }

                {/* X-ray Beam */}
                {xrayIntensity > 0 &&
                <motion.g data-id="kj4s96tyf" data-path="src/components/XRaySimulator.tsx">
                    {[...Array(3)].map((_, i) =>
                  <motion.line
                    key={i}
                    x1="280"
                    y1="135"
                    x2="350"
                    y2={115 + i * 20}
                    stroke="#10b981"
                    strokeWidth="2"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{
                      duration: 0.3,
                      repeat: Infinity,
                      delay: i * 0.1
                    }} data-id="1raxq18ll" data-path="src/components/XRaySimulator.tsx" />

                  )}
                  </motion.g>
                }
              </svg>

              {/* Status Indicator */}
              <div className="absolute top-4 right-4" data-id="5oxqpefmr" data-path="src/components/XRaySimulator.tsx">
                <Badge
                  variant={isRunning ? "default" : "secondary"}
                  className={isRunning ? "bg-green-500" : ""} data-id="qx3phj23w" data-path="src/components/XRaySimulator.tsx">

                  {isRunning ? "يعمل" : "متوقف"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status Monitors */}
        <div className="grid grid-cols-2 gap-4" data-id="ibbkkcqhp" data-path="src/components/XRaySimulator.tsx">
          <Card data-id="c1m0ay0hc" data-path="src/components/XRaySimulator.tsx">
            <CardHeader className="pb-3" data-id="h9th9kcqk" data-path="src/components/XRaySimulator.tsx">
              <CardTitle className="text-base flex items-center gap-2" data-id="14abx4omj" data-path="src/components/XRaySimulator.tsx">
                <Thermometer className="w-4 h-4" data-id="amqsb6krp" data-path="src/components/XRaySimulator.tsx" />
                حرارة الأنود
              </CardTitle>
            </CardHeader>
            <CardContent data-id="nmk9vcjwm" data-path="src/components/XRaySimulator.tsx">
              <div className="text-center" data-id="n9ha825vy" data-path="src/components/XRaySimulator.tsx">
                <div className={`text-2xl font-bold ${getTemperatureColor(anodeTemp)}`} data-id="r1olmcqhq" data-path="src/components/XRaySimulator.tsx">
                  {Math.round(anodeTemp)}°C
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2" data-id="hjpr0lb47" data-path="src/components/XRaySimulator.tsx">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                    anodeTemp > 800 ? 'bg-red-500' :
                    anodeTemp > 400 ? 'bg-orange-500' : 'bg-blue-500'}`
                    }
                    style={{ width: `${Math.min(anodeTemp / 1200 * 100, 100)}%` }} data-id="xpvd0jiew" data-path="src/components/XRaySimulator.tsx">
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1" data-id="xsuuzq4yy" data-path="src/components/XRaySimulator.tsx">
                  الحد الأقصى: 1200°C
                </div>
              </div>
            </CardContent>
          </Card>

          <Card data-id="2swbw8b8y" data-path="src/components/XRaySimulator.tsx">
            <CardHeader className="pb-3" data-id="hijtm95dm" data-path="src/components/XRaySimulator.tsx">
              <CardTitle className="text-base flex items-center gap-2" data-id="1ybl2glo6" data-path="src/components/XRaySimulator.tsx">
                <Zap className="w-4 h-4" data-id="3eqkai3gr" data-path="src/components/XRaySimulator.tsx" />
                شدة الأشعة
              </CardTitle>
            </CardHeader>
            <CardContent data-id="kr2epp4gm" data-path="src/components/XRaySimulator.tsx">
              <div className="text-center" data-id="phfup3z2l" data-path="src/components/XRaySimulator.tsx">
                <div className="text-2xl font-bold text-gray-900" data-id="e0ig43thx" data-path="src/components/XRaySimulator.tsx">
                  {Math.round(xrayIntensity)}%
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2" data-id="nmuglrtm3" data-path="src/components/XRaySimulator.tsx">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${getIntensityColor(xrayIntensity)}`}
                    style={{ width: `${xrayIntensity}%` }} data-id="ttv7q5tx2" data-path="src/components/XRaySimulator.tsx">
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1" data-id="7cjdc7qhh" data-path="src/components/XRaySimulator.tsx">
                  شدة الإنتاج النسبية
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>
    </div>);

};

export default XRaySimulator;