import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { BookOpen, Monitor, Cpu, Zap, Users, Microscope, Search, TrendingUp } from 'lucide-react';
import { motion } from 'motion/react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSwitcher from '@/components/LanguageSwitcher';

const CoursePage = () => {
  const [selectedChapter, setSelectedChapter] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const { dir } = useLanguage();
  const { t } = useTranslation();

  const part4Chapters = [
  {
    id: 'ch11',
    title: t('chapters.ch11.title'),
    description: t('chapters.ch11.description'),
    icon: <Monitor className="w-6 h-6" data-id="nybdpygx5" data-path="src/pages/CoursePage.tsx" />,
    topics: [
    dir === 'rtl' ? 'أنظمة الشاشة والفيلم' : 'Screen-Film Systems',
    dir === 'rtl' ? 'التصوير الشعاعي المحوسب' : 'Computed Radiography',
    dir === 'rtl' ? 'التصوير الرقمي' : 'Digital Imaging',
    dir === 'rtl' ? 'مقاييس الأداء' : 'Performance Metrics'],

    vrFeatures: [
    dir === 'rtl' ? 'نماذج ثلاثية الأبعاد للكاشفات' : '3D detector models',
    dir === 'rtl' ? 'محاكاة تفاعلية لعمليات الكشف' : 'Interactive detection process simulation']

  },
  {
    id: 'ch12',
    title: t('chapters.ch12.title'),
    description: t('chapters.ch12.description'),
    icon: <Cpu className="w-6 h-6" data-id="4iu2cqh82" data-path="src/pages/CoursePage.tsx" />,
    topics: [
    dir === 'rtl' ? 'نمذجة ترسب الطاقة' : 'Energy deposition modeling',
    dir === 'rtl' ? 'محاكاة انتشار الضوء' : 'Light propagation simulation',
    dir === 'rtl' ? 'تحليل الأنظمة الخطية' : 'Linear systems analysis',
    dir === 'rtl' ? 'معالجة الصور' : 'Image processing'],

    vrFeatures: [
    dir === 'rtl' ? 'تجارب افتراضية لمحاكاة الكاشف' : 'Virtual detector simulation experiments',
    dir === 'rtl' ? 'تصور ثلاثي الأبعاد لانتشار الضوء' : '3D visualization of light propagation']

  }];

  const part5Chapters = [
  {
    id: 'ch13',
    title: t('chapters.ch13.title'),
    description: t('chapters.ch13.description'),
    icon: <Zap className="w-6 h-6" data-id="kbhm1yuo6" data-path="src/pages/CoursePage.tsx" />,
    topics: [
    dir === 'rtl' ? 'تحسين معلمات المصدر' : 'Source parameter optimization',
    dir === 'rtl' ? 'تقييم مواد جديدة' : 'New material evaluation',
    dir === 'rtl' ? 'تصميم الشبكات' : 'Grid design',
    dir === 'rtl' ? 'دراسات حالة' : 'Case studies'],

    vrFeatures: [
    dir === 'rtl' ? 'بيئة افتراضية لتصميم الأنظمة' : 'Virtual environment for system design',
    dir === 'rtl' ? 'تجارب تفاعلية للتحسين' : 'Interactive optimization experiments']

  },
  {
    id: 'ch14',
    title: t('chapters.ch14.title'),
    description: t('chapters.ch14.description'),
    icon: <Users className="w-6 h-6" data-id="z8qnb9nk5" data-path="src/pages/CoursePage.tsx" />,
    topics: [
    dir === 'rtl' ? 'حساب جرعات الأعضاء' : 'Organ dose calculation',
    dir === 'rtl' ? 'تأثير معلمات التصوير' : 'Imaging parameters effect',
    dir === 'rtl' ? 'قياس الجرعات للأطفال' : 'Pediatric dosimetry',
    dir === 'rtl' ? 'التحسين السريري' : 'Clinical optimization'],

    vrFeatures: [
    dir === 'rtl' ? 'نماذج ثلاثية الأبعاد للجسم البشري' : '3D human body models',
    dir === 'rtl' ? 'محاكاة توزيع الجرعة بصرياً' : 'Visual dose distribution simulation']

  },
  {
    id: 'ch15',
    title: t('chapters.ch15.title'),
    description: t('chapters.ch15.description'),
    icon: <Microscope className="w-6 h-6" data-id="wpqatnw14" data-path="src/pages/CoursePage.tsx" />,
    topics: [
    dir === 'rtl' ? 'مقاييس جودة الصورة' : 'Image quality metrics',
    dir === 'rtl' ? 'تأثير التشتت' : 'Scatter effects',
    dir === 'rtl' ? 'معالجة الصور' : 'Image processing',
    dir === 'rtl' ? 'تقييم قائم على المهام' : 'Task-based evaluation'],

    vrFeatures: [
    dir === 'rtl' ? 'مقارنة تفاعلية لجودة الصور' : 'Interactive image quality comparison',
    dir === 'rtl' ? 'تصور ثلاثي الأبعاد للتشتت' : '3D scatter visualization']

  },
  {
    id: 'ch16',
    title: t('chapters.ch16.title'),
    description: t('chapters.ch16.description'),
    icon: <Search className="w-6 h-6" data-id="qwk1wi10i" data-path="src/pages/CoursePage.tsx" />,
    topics: [
    dir === 'rtl' ? 'تعريفات VVUQ' : 'VVUQ definitions',
    dir === 'rtl' ? 'التحقق من الكود' : 'Code verification',
    dir === 'rtl' ? 'تجارب التحقق' : 'Validation experiments',
    dir === 'rtl' ? 'تحليل الحساسية' : 'Sensitivity analysis'],

    vrFeatures: [
    dir === 'rtl' ? 'بيئة تجريبية افتراضية' : 'Virtual experimental environment',
    dir === 'rtl' ? 'مقارنة تفاعلية للنتائج' : 'Interactive results comparison']

  },
  {
    id: 'ch17',
    title: t('chapters.ch17.title'),
    description: t('chapters.ch17.description'),
    icon: <TrendingUp className="w-6 h-6" data-id="bm5e0x3gd" data-path="src/pages/CoursePage.tsx" />,
    topics: [
    dir === 'rtl' ? 'الذكاء الاصطناعي' : 'Artificial Intelligence',
    dir === 'rtl' ? 'الحوسبة عالية الأداء' : 'High Performance Computing',
    dir === 'rtl' ? 'المنصات السحابية' : 'Cloud Platforms',
    dir === 'rtl' ? 'التوائم الرقمية' : 'Digital Twins'],

    vrFeatures: [
    dir === 'rtl' ? 'استكشاف مستقبل التكنولوجيا' : 'Future technology exploration',
    dir === 'rtl' ? 'نماذج تفاعلية للتوائم الرقمية' : 'Interactive digital twin models']

  }];

  const ChapterCard = ({ chapter, partTitle }: {chapter: any;partTitle: string;}) =>
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="h-full" data-id="sz2mbevjl" data-path="src/pages/CoursePage.tsx">

      <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group" onClick={() => setSelectedChapter(chapter.id)} data-id="91sxt4sey" data-path="src/pages/CoursePage.tsx">
        <CardHeader data-id="ghfv8ucuc" data-path="src/pages/CoursePage.tsx">
          <div className="flex items-start justify-between" data-id="nhghjlf1f" data-path="src/pages/CoursePage.tsx">
            <div className={`flex items-center space-x-3 ${dir === 'rtl' ? 'rtl:space-x-reverse' : ''}`} data-id="7d4q4od7z" data-path="src/pages/CoursePage.tsx">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white group-hover:from-purple-600 group-hover:to-blue-500 transition-all" data-id="lis7xjh8d" data-path="src/pages/CoursePage.tsx">
                {chapter.icon}
              </div>
              <div data-id="3z23nfhcv" data-path="src/pages/CoursePage.tsx">
                <CardTitle className="text-lg leading-tight" data-id="x14iv6bu9" data-path="src/pages/CoursePage.tsx">{chapter.title}</CardTitle>
                <Badge variant="outline" className="mt-1" data-id="jhsbio1za" data-path="src/pages/CoursePage.tsx">{partTitle}</Badge>
              </div>
            </div>
          </div>
          <CardDescription className="mt-2" data-id="kyp1jv4iz" data-path="src/pages/CoursePage.tsx">{chapter.description}</CardDescription>
        </CardHeader>
        <CardContent data-id="bf2gbxn7n" data-path="src/pages/CoursePage.tsx">
          <div className="space-y-4" data-id="es54kzm29" data-path="src/pages/CoursePage.tsx">
            <div data-id="ltap0o0wa" data-path="src/pages/CoursePage.tsx">
              <h4 className={`font-semibold text-sm mb-2 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="fcny65epv" data-path="src/pages/CoursePage.tsx">
                {t('chapter11.main_topics')}
              </h4>
              <div className={`flex flex-wrap gap-1 ${dir === 'rtl' ? 'justify-end' : 'justify-start'}`} data-id="scbbo7nbr" data-path="src/pages/CoursePage.tsx">
                {chapter.topics.map((topic: string, index: number) =>
              <Badge key={index} variant="secondary" className="text-xs" data-id="hiimm3xeu" data-path="src/pages/CoursePage.tsx">{topic}</Badge>
              )}
              </div>
            </div>
            <div data-id="rthqec7b0" data-path="src/pages/CoursePage.tsx">
              <h4 className={`font-semibold text-sm mb-2 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="cxz43wrys" data-path="src/pages/CoursePage.tsx">
                {t('chapter11.vr_features')}
              </h4>
              <div className="space-y-1" data-id="y30wv13nv" data-path="src/pages/CoursePage.tsx">
                {chapter.vrFeatures.map((feature: string, index: number) =>
              <div key={index} className={`flex items-center space-x-2 text-xs text-muted-foreground ${dir === 'rtl' ? 'justify-end rtl:space-x-reverse' : 'justify-start'}`} data-id="74fmk52g2" data-path="src/pages/CoursePage.tsx">
                    {dir === 'rtl' && <span data-id="hhg918hh0" data-path="src/pages/CoursePage.tsx">{feature}</span>}
                    <div className="w-2 h-2 bg-green-500 rounded-full" data-id="9c4oxvojf" data-path="src/pages/CoursePage.tsx"></div>
                    {dir === 'ltr' && <span data-id="axxi3yqpa" data-path="src/pages/CoursePage.tsx">{feature}</span>}
                  </div>
              )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6" dir={dir} data-id="9mqwicfsm" data-path="src/pages/CoursePage.tsx">
      <div className="max-w-7xl mx-auto" data-id="ia7e0y9q6" data-path="src/pages/CoursePage.tsx">
        {/* Language Switcher */}
        <div className="fixed top-4 right-4 z-50" data-id="tpuu20ugl" data-path="src/pages/CoursePage.tsx">
          <LanguageSwitcher data-id="cbt0xgv8d" data-path="src/pages/CoursePage.tsx" />
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8" data-id="4lr30h4ep" data-path="src/pages/CoursePage.tsx">

          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4" data-id="dtkapwyan" data-path="src/pages/CoursePage.tsx">
            {t('course.title')}
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto" data-id="ktp37yn1a" data-path="src/pages/CoursePage.tsx">
            {t('course.subtitle')}
          </p>
          <div className={`flex items-center justify-center space-x-4 mt-4 ${dir === 'rtl' ? 'rtl:space-x-reverse' : ''}`} data-id="5j7o5ppnu" data-path="src/pages/CoursePage.tsx">
            <div className={`flex items-center space-x-2 ${dir === 'rtl' ? 'rtl:space-x-reverse' : ''}`} data-id="jn421ua5c" data-path="src/pages/CoursePage.tsx">
              <BookOpen className="w-5 h-5 text-blue-600" data-id="8x0hjspit" data-path="src/pages/CoursePage.tsx" />
              <span className="text-sm font-medium" data-id="chffiyzq5" data-path="src/pages/CoursePage.tsx">{t('course.chapters.specialized')}</span>
            </div>
            <div className={`flex items-center space-x-2 ${dir === 'rtl' ? 'rtl:space-x-reverse' : ''}`} data-id="ipxqouocd" data-path="src/pages/CoursePage.tsx">
              <Monitor className="w-5 h-5 text-purple-600" data-id="txeracyd7" data-path="src/pages/CoursePage.tsx" />
              <span className="text-sm font-medium" data-id="lexugni0t" data-path="src/pages/CoursePage.tsx">{t('course.interactive_vr')}</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <Card className="mb-8" data-id="voe3h87vt" data-path="src/pages/CoursePage.tsx">
          <CardHeader data-id="na4wwenmq" data-path="src/pages/CoursePage.tsx">
            <CardTitle className={dir === 'rtl' ? 'text-right' : 'text-left'} data-id="ti31lfvyy" data-path="src/pages/CoursePage.tsx">{t('course.progress.title')}</CardTitle>
          </CardHeader>
          <CardContent data-id="l54hc0rpe" data-path="src/pages/CoursePage.tsx">
            <div className="space-y-2" data-id="mcrmbjxpn" data-path="src/pages/CoursePage.tsx">
              <div className="flex justify-between text-sm" data-id="2bsrid9ry" data-path="src/pages/CoursePage.tsx">
                <span data-id="arkg2kj2j" data-path="src/pages/CoursePage.tsx">{progress}% {t('course.progress.completed')}</span>
                <span data-id="4hz30kmjc" data-path="src/pages/CoursePage.tsx">{t('course.progress.total')}</span>
              </div>
              <Progress value={progress} className="w-full" data-id="uiyrp45xa" data-path="src/pages/CoursePage.tsx" />
            </div>
          </CardContent>
        </Card>

        {/* Course Content */}
        <Tabs defaultValue="part4" className="w-full" data-id="qsh7hynkk" data-path="src/pages/CoursePage.tsx">
          <TabsList className="grid w-full grid-cols-2 mb-8" data-id="n4l8z243l" data-path="src/pages/CoursePage.tsx">
            <TabsTrigger value="part4" className="text-lg" data-id="8n2fiz5z8" data-path="src/pages/CoursePage.tsx">
              {t('course.part4.tab_title')}
            </TabsTrigger>
            <TabsTrigger value="part5" className="text-lg" data-id="z6ozn1v57" data-path="src/pages/CoursePage.tsx">
              {t('course.part5.tab_title')}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="part4" className="space-y-6" data-id="22fbqxed0" data-path="src/pages/CoursePage.tsx">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="grid md:grid-cols-2 gap-6" data-id="qy7uur4br" data-path="src/pages/CoursePage.tsx">

              {part4Chapters.map((chapter) =>
              <ChapterCard
                key={chapter.id}
                chapter={chapter}
                partTitle={dir === 'rtl' ? 'الجزء الرابع' : 'Part IV'} data-id="mp274txet" data-path="src/pages/CoursePage.tsx" />

              )}
            </motion.div>
          </TabsContent>

          <TabsContent value="part5" className="space-y-6" data-id="735b6vvng" data-path="src/pages/CoursePage.tsx">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-6" data-id="j69bzqtxi" data-path="src/pages/CoursePage.tsx">

              {part5Chapters.map((chapter) =>
              <ChapterCard
                key={chapter.id}
                chapter={chapter}
                partTitle={dir === 'rtl' ? 'الجزء الخامس' : 'Part V'} data-id="2htam80rb" data-path="src/pages/CoursePage.tsx" />

              )}
            </motion.div>
          </TabsContent>
        </Tabs>

        {/* VR Features Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-12" data-id="b6o4d0tnf" data-path="src/pages/CoursePage.tsx">

          <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white" data-id="4a8vx0opc" data-path="src/pages/CoursePage.tsx">
            <CardHeader data-id="oro3r6pel" data-path="src/pages/CoursePage.tsx">
              <CardTitle className={`text-2xl ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="a1zd1wrsr" data-path="src/pages/CoursePage.tsx">
                {t('course.vr_experience.title')}
              </CardTitle>
              <CardDescription className={`text-blue-100 ${dir === 'rtl' ? 'text-right' : 'text-left'}`} data-id="vgulfy032" data-path="src/pages/CoursePage.tsx">
                {t('course.vr_experience.subtitle')}
              </CardDescription>
            </CardHeader>
            <CardContent data-id="kq3gljtdf" data-path="src/pages/CoursePage.tsx">
              <div className="grid md:grid-cols-3 gap-6" data-id="wfcp32v59" data-path="src/pages/CoursePage.tsx">
                <div className="text-center" data-id="msuifaip0" data-path="src/pages/CoursePage.tsx">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3" data-id="rertp5u82" data-path="src/pages/CoursePage.tsx">
                    <Monitor className="w-8 h-8" data-id="tayztjlgi" data-path="src/pages/CoursePage.tsx" />
                  </div>
                  <h3 className="font-semibold mb-2" data-id="pniiutpsp" data-path="src/pages/CoursePage.tsx">{t('course.vr_experience.3d_models')}</h3>
                  <p className="text-sm text-blue-100" data-id="acz1mfl2y" data-path="src/pages/CoursePage.tsx">
                    {dir === 'rtl' ? 'تفاعل مع نماذج دقيقة لأجهزة الأشعة السينية' : 'Interact with precise X-ray device models'}
                  </p>
                </div>
                <div className="text-center" data-id="bp4rwzzsz" data-path="src/pages/CoursePage.tsx">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3" data-id="j13ud2v5y" data-path="src/pages/CoursePage.tsx">
                    <Cpu className="w-8 h-8" data-id="up6ioomsm" data-path="src/pages/CoursePage.tsx" />
                  </div>
                  <h3 className="font-semibold mb-2" data-id="kl7dlooms" data-path="src/pages/CoursePage.tsx">{t('course.vr_experience.interactive_simulation')}</h3>
                  <p className="text-sm text-blue-100" data-id="ravlt3p56" data-path="src/pages/CoursePage.tsx">
                    {dir === 'rtl' ? 'جرب المحاكاة في بيئة افتراضية واقعية' : 'Experience simulation in realistic virtual environment'}
                  </p>
                </div>
                <div className="text-center" data-id="rdrofvq9h" data-path="src/pages/CoursePage.tsx">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3" data-id="bve7lnfha" data-path="src/pages/CoursePage.tsx">
                    <Search className="w-8 h-8" data-id="fqnpw9b4i" data-path="src/pages/CoursePage.tsx" />
                  </div>
                  <h3 className="font-semibold mb-2" data-id="cakh77uwv" data-path="src/pages/CoursePage.tsx">{t('course.vr_experience.visual_analysis')}</h3>
                  <p className="text-sm text-blue-100" data-id="v99dt087u" data-path="src/pages/CoursePage.tsx">
                    {dir === 'rtl' ? 'تصور البيانات والنتائج بشكل تفاعلي' : 'Visualize data and results interactively'}
                  </p>
                </div>
              </div>
              <div className="text-center mt-6" data-id="loi2hvozi" data-path="src/pages/CoursePage.tsx">
                <Button variant="secondary" size="lg" className="text-blue-600" data-id="o84xepljj" data-path="src/pages/CoursePage.tsx">
                  {t('course.vr_experience.start_experience')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>);

};

export default CoursePage;