import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Languages } from 'lucide-react';

const LanguageToggle: React.FC = () => {
  const { language, setLanguage, t } = useLanguage();

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'es' : 'en');
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleLanguage}
      className="flex items-center gap-2 hover:bg-accent/50 transition-colors" data-id="0k5qm9db5" data-path="src/components/LanguageToggle.tsx">

      <Languages className="h-4 w-4" data-id="33cpjzz2m" data-path="src/components/LanguageToggle.tsx" />
      <Badge variant="secondary" className="text-xs font-medium" data-id="e53fuk7et" data-path="src/components/LanguageToggle.tsx">
        {language.toUpperCase()}
      </Badge>
      <span className="hidden sm:inline text-sm" data-id="nfqqah5w0" data-path="src/components/LanguageToggle.tsx">{t('nav.language')}</span>
    </Button>);

};

export default LanguageToggle;