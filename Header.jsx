import { useState } from 'react'
import { Menu, X } from 'lucide-react'
import { Button } from '@/components/ui/button.jsx'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <header className="bg-blue-900 text-white shadow-lg">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold">التصوير الطبي بالإشعاع المؤين</h1>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-6">
            <a href="#home" className="hover:text-blue-200 transition-colors">الرئيسية</a>
            <a href="#basics" className="hover:text-blue-200 transition-colors">المبادئ الأساسية</a>
            <a href="#components" className="hover:text-blue-200 transition-colors">المكونات</a>
            <a href="#circuits" className="hover:text-blue-200 transition-colors">الدوائر الكهربائية</a>
            <a href="#interactive" className="hover:text-blue-200 transition-colors">التفاعل الذكي</a>
          </nav>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden text-white hover:bg-blue-800"
            onClick={toggleMenu}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 pb-4">
            <div className="flex flex-col space-y-2">
              <a href="#home" className="hover:text-blue-200 transition-colors py-2">الرئيسية</a>
              <a href="#basics" className="hover:text-blue-200 transition-colors py-2">المبادئ الأساسية</a>
              <a href="#components" className="hover:text-blue-200 transition-colors py-2">المكونات</a>
              <a href="#circuits" className="hover:text-blue-200 transition-colors py-2">الدوائر الكهربائية</a>
              <a href="#interactive" className="hover:text-blue-200 transition-colors py-2">التفاعل الذكي</a>
            </div>
          </nav>
        )}
      </div>
    </header>
  )
}

export default Header

