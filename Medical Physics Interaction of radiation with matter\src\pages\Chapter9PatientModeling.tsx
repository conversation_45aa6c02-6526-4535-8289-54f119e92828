import React from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  User,
  Box,
  Cylinder,
  Circle,
  Activity,
  Database,
  Grid3X3,
  AlertCircle,
  ChevronDown,
  BookOpen,
  Target,
  Key,
  FileText,
  HelpCircle } from
'lucide-react';

const Chapter9PatientModeling = () => {
  const [openSections, setOpenSections] = React.useState<{[key: string]: boolean;}>({});

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50" dir="rtl" data-id="guctpxecb" data-path="src/pages/Chapter9PatientModeling.tsx">
      <Navigation data-id="f52l2rv55" data-path="src/pages/Chapter9PatientModeling.tsx" />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl" data-id="m6nlx3996" data-path="src/pages/Chapter9PatientModeling.tsx">
        {/* Header */}
        <div className="text-center mb-12" data-id="nr59nyjb7" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Badge variant="secondary" className="mb-4 text-lg px-6 py-2" data-id="sw59el4t3" data-path="src/pages/Chapter9PatientModeling.tsx">
            الفصل التاسع
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4" data-id="87i8tpv2d" data-path="src/pages/Chapter9PatientModeling.tsx">
            نمذجة هندسة المريض والوهم للمحاكاة
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed" data-id="a9xbh94db" data-path="src/pages/Chapter9PatientModeling.tsx">
            دراسة شاملة لأساليب النمذجة الهندسية للمرضى والأشباح المستخدمة في محاكاة النقل الإشعاعي
          </p>
        </div>

        {/* Learning Objectives */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-blue-500" data-id="azygmoils" data-path="src/pages/Chapter9PatientModeling.tsx">
          <CardHeader data-id="vc88xnj8v" data-path="src/pages/Chapter9PatientModeling.tsx">
            <CardTitle className="flex items-center text-2xl text-blue-700" data-id="etbmro5e5" data-path="src/pages/Chapter9PatientModeling.tsx">
              <Target className="ml-3 h-6 w-6" data-id="oedpkom51" data-path="src/pages/Chapter9PatientModeling.tsx" />
              أهداف التعلم
            </CardTitle>
          </CardHeader>
          <CardContent data-id="w44odfuon" data-path="src/pages/Chapter9PatientModeling.tsx">
            <ul className="space-y-3 text-gray-700" data-id="9sc8u2ey9" data-path="src/pages/Chapter9PatientModeling.tsx">
              <li className="flex items-start" data-id="kgof9ewi2" data-path="src/pages/Chapter9PatientModeling.tsx">
                <span className="text-blue-500 ml-2" data-id="47bdc99hr" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                فهم مبادئ الأشباح الهندسية البسيطة وتطبيقاتها في المحاكاة
              </li>
              <li className="flex items-start" data-id="j8nlh79v6" data-path="src/pages/Chapter9PatientModeling.tsx">
                <span className="text-blue-500 ml-2" data-id="626ko7ac5" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                التمييز بين الأشباح الفيزيائية والحسابية ومزايا كل منها
              </li>
              <li className="flex items-start" data-id="ywya6zwpr" data-path="src/pages/Chapter9PatientModeling.tsx">
                <span className="text-blue-500 ml-2" data-id="elllfb5ex" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                معرفة الأشباح المرجعية المعتمدة من ICRU/ICRP وخصائصها
              </li>
              <li className="flex items-start" data-id="wyrofbxzk" data-path="src/pages/Chapter9PatientModeling.tsx">
                <span className="text-blue-500 ml-2" data-id="ox0m8qmhq" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                فهم كيفية إنشاء الأشباح المجسمة من بيانات التصوير الطبي
              </li>
              <li className="flex items-start" data-id="d0mjerk7h" data-path="src/pages/Chapter9PatientModeling.tsx">
                <span className="text-blue-500 ml-2" data-id="kz9iool6h" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                إتقان تعريفات المواد وتركيبات الأنسجة حسب معايير ICRU
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Section 9.1 */}
        <Card className="mb-6 shadow-lg" data-id="4p1cvaaxv" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Collapsible
            open={openSections['section91']}
            onOpenChange={() => toggleSection('section91')} data-id="recd245vf" data-path="src/pages/Chapter9PatientModeling.tsx">

            <CollapsibleTrigger className="w-full" data-id="glfglh611" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="gntr4vm5j" data-path="src/pages/Chapter9PatientModeling.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="8ogns81sa" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="flex items-center" data-id="9wdmc7d0p" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <Box className="ml-3 h-6 w-6 text-green-600" data-id="vc2s9s2i6" data-path="src/pages/Chapter9PatientModeling.tsx" />
                    9.1 أشباح هندسية بسيطة
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section91'] ? 'rotate-180' : ''}`} data-id="q1tf261ay" data-path="src/pages/Chapter9PatientModeling.tsx" />
                </CardTitle>
                <CardDescription data-id="o0x7fwruz" data-path="src/pages/Chapter9PatientModeling.tsx">
                  الألواح، الأسطوانات، والكرات كأشباح أساسية للمحاكاة
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="wy0yue2xh" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardContent className="space-y-6" data-id="mtwyztgv3" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="grid md:grid-cols-3 gap-6" data-id="neju60jml" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <Card className="border-l-4 border-l-blue-500" data-id="tdrscagjr" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="tf79lgxht" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="y6c3t22rf" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center ml-2" data-id="bpnqfu8cq" data-path="src/pages/Chapter9PatientModeling.tsx">
                          📏
                        </span>
                        الألواح المسطحة
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="12cfdqbgm" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <ul className="space-y-2 text-sm text-gray-600" data-id="nymfpwzis" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <li data-id="fjyt2liah" data-path="src/pages/Chapter9PatientModeling.tsx">• هندسة بسيطة للحسابات التحليلية</li>
                        <li data-id="6arb8oc28" data-path="src/pages/Chapter9PatientModeling.tsx">• مناسبة لدراسة التوهين الخطي</li>
                        <li data-id="f8tm2d49v" data-path="src/pages/Chapter9PatientModeling.tsx">• سهولة في التعامل والمعايرة</li>
                        <li data-id="xvf9ek6ew" data-path="src/pages/Chapter9PatientModeling.tsx">• تطبيقات في الحماية الإشعاعية</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-green-500" data-id="rm05u0z3z" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="qx5ijqc6d" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="vavlpvnl7" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <Cylinder className="ml-2 h-5 w-5 text-green-600" data-id="or7t3a79b" data-path="src/pages/Chapter9PatientModeling.tsx" />
                        الأسطوانات
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="hchnrdnoh" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <ul className="space-y-2 text-sm text-gray-600" data-id="3lk8y6xnn" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <li data-id="9zm1hghys" data-path="src/pages/Chapter9PatientModeling.tsx">• تمثيل أفضل للجذع البشري</li>
                        <li data-id="zrgu1xi9r" data-path="src/pages/Chapter9PatientModeling.tsx">• مفيدة لدراسة التبعثر</li>
                        <li data-id="xzy04q2cy" data-path="src/pages/Chapter9PatientModeling.tsx">• حسابات أكثر تعقيداً من الألواح</li>
                        <li data-id="dgo5x4que" data-path="src/pages/Chapter9PatientModeling.tsx">• تطبيقات في الطب النووي</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-purple-500" data-id="brenay780" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="4rdijfxe3" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="s0vxgkggv" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <Circle className="ml-2 h-5 w-5 text-purple-600" data-id="772vo6cqc" data-path="src/pages/Chapter9PatientModeling.tsx" />
                        الكرات
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="e75hvnmpy" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <ul className="space-y-2 text-sm text-gray-600" data-id="iuffuc5qd" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <li data-id="59a011593" data-path="src/pages/Chapter9PatientModeling.tsx">• تناظر كروي مثالي</li>
                        <li data-id="kw4u25gs3" data-path="src/pages/Chapter9PatientModeling.tsx">• مفيدة للحسابات النظرية</li>
                        <li data-id="sqkoyre27" data-path="src/pages/Chapter9PatientModeling.tsx">• تطبيقات في فيزياء الجسيمات</li>
                        <li data-id="ehga2kh4g" data-path="src/pages/Chapter9PatientModeling.tsx">• نمذجة أعضاء كروية صغيرة</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <Alert data-id="0ii96mx3n" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <AlertCircle className="h-4 w-4" data-id="ro1d287om" data-path="src/pages/Chapter9PatientModeling.tsx" />
                  <AlertDescription data-id="2y2d7diks" data-path="src/pages/Chapter9PatientModeling.tsx">
                    الأشباح الهندسية البسيطة توفر نقطة انطلاق مثالية لفهم مبادئ النقل الإشعاعي قبل الانتقال إلى النماذج المعقدة.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 9.2 */}
        <Card className="mb-6 shadow-lg" data-id="fj5xw9a91" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Collapsible
            open={openSections['section92']}
            onOpenChange={() => toggleSection('section92')} data-id="pi34ct2yr" data-path="src/pages/Chapter9PatientModeling.tsx">

            <CollapsibleTrigger className="w-full" data-id="okbgiwdtx" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="wrhf2p3hc" data-path="src/pages/Chapter9PatientModeling.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="nu3spd1n5" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="flex items-center" data-id="lqbox1yz0" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <User className="ml-3 h-6 w-6 text-blue-600" data-id="qnzgy66zs" data-path="src/pages/Chapter9PatientModeling.tsx" />
                    9.2 الأشباح المجسمة: الفيزيائية والحسابية
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section92'] ? 'rotate-180' : ''}`} data-id="f9xpilii9" data-path="src/pages/Chapter9PatientModeling.tsx" />
                </CardTitle>
                <CardDescription data-id="rzbefx0ym" data-path="src/pages/Chapter9PatientModeling.tsx">
                  النماذج المتقدمة التي تحاكي التشريح البشري الحقيقي
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="rosailpxv" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardContent className="space-y-6" data-id="lcr9fj194" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="7z3njqrn2" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <Card className="border-t-4 border-t-orange-500" data-id="slyyez8i0" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="hig926ozd" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="q5din9jyu" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <Activity className="ml-2 h-5 w-5 text-orange-600" data-id="gt2tst8fr" data-path="src/pages/Chapter9PatientModeling.tsx" />
                        9.2.1 أشباح مرجعية ICRU/ICRP
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4" data-id="ov52cnl5g" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div data-id="2wtxlmhm2" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="ks0mubbx1" data-path="src/pages/Chapter9PatientModeling.tsx">الأشباح المرجعية الأساسية:</h4>
                        <ul className="space-y-2 text-sm text-gray-600" data-id="u8tn867dw" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <li data-id="u2dugrk5w" data-path="src/pages/Chapter9PatientModeling.tsx">• شبح الرجل المرجعي (70 كغ)</li>
                          <li data-id="evvle2wth" data-path="src/pages/Chapter9PatientModeling.tsx">• شبح المرأة المرجعية (60 كغ)</li>
                          <li data-id="f92o5rjpt" data-path="src/pages/Chapter9PatientModeling.tsx">• أشباح الأطفال (أعمار مختلفة)</li>
                          <li data-id="rwtkbu1a2" data-path="src/pages/Chapter9PatientModeling.tsx">• أشباح الحوامل</li>
                        </ul>
                      </div>
                      <div data-id="4b8rnxwz7" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="zom6dzrdr" data-path="src/pages/Chapter9PatientModeling.tsx">المواصفات المعيارية:</h4>
                        <ul className="space-y-2 text-sm text-gray-600" data-id="okindw6es" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <li data-id="rkamdtwtr" data-path="src/pages/Chapter9PatientModeling.tsx">• كثافات الأنسجة المحددة</li>
                          <li data-id="gsy5hs4k7" data-path="src/pages/Chapter9PatientModeling.tsx">• التركيب العنصري للأعضاء</li>
                          <li data-id="pfmz0q7bg" data-path="src/pages/Chapter9PatientModeling.tsx">• الأبعاد التشريحية المعيارية</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-t-4 border-t-cyan-500" data-id="dx5ivjomy" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="1xdoziy7b" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="qyw7y48ql" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <Database className="ml-2 h-5 w-5 text-cyan-600" data-id="p55xpeoe0" data-path="src/pages/Chapter9PatientModeling.tsx" />
                        9.2.2 الأشباح من بيانات التصوير الطبي
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4" data-id="26mwqw4vk" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div data-id="zk4pgdbg4" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="zzuz07gz8" data-path="src/pages/Chapter9PatientModeling.tsx">مصادر البيانات:</h4>
                        <ul className="space-y-2 text-sm text-gray-600" data-id="x09twxv09" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <li data-id="rxc139xwn" data-path="src/pages/Chapter9PatientModeling.tsx">• بيانات التصوير المقطعي المحوسب (CT)</li>
                          <li data-id="aq3rut00u" data-path="src/pages/Chapter9PatientModeling.tsx">• صور الرنين المغناطيسي (MRI)</li>
                          <li data-id="09g0pq6sx" data-path="src/pages/Chapter9PatientModeling.tsx">• صور PET/SPECT</li>
                        </ul>
                      </div>
                      <div data-id="lj2kics67" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="xj0zn08iy" data-path="src/pages/Chapter9PatientModeling.tsx">عملية التحويل:</h4>
                        <ul className="space-y-2 text-sm text-gray-600" data-id="e9p7wkl7c" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <li data-id="lzdl2r1dz" data-path="src/pages/Chapter9PatientModeling.tsx">• تحويل أرقام Hounsfield إلى كثافة</li>
                          <li data-id="tstr1e129" data-path="src/pages/Chapter9PatientModeling.tsx">• تصنيف الأنسجة والأعضاء</li>
                          <li data-id="9pmxa5xi0" data-path="src/pages/Chapter9PatientModeling.tsx">• إنشاء الشبكة ثلاثية الأبعاد</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Alert data-id="4gy3d8zdv" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <AlertCircle className="h-4 w-4" data-id="h7whydml0" data-path="src/pages/Chapter9PatientModeling.tsx" />
                  <AlertDescription data-id="1kqntq4vt" data-path="src/pages/Chapter9PatientModeling.tsx">
                    الأشباح المجسمة توفر دقة أكبر في المحاكاة لكنها تتطلب موارد حاسوبية أكثر وبيانات تشريحية مفصلة.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 9.3 */}
        <Card className="mb-6 shadow-lg" data-id="ejz94sl81" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Collapsible
            open={openSections['section93']}
            onOpenChange={() => toggleSection('section93')} data-id="er5u0neu3" data-path="src/pages/Chapter9PatientModeling.tsx">

            <CollapsibleTrigger className="w-full" data-id="shuva28r7" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="bsk0z39mh" data-path="src/pages/Chapter9PatientModeling.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="jcwrpfuxw" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="flex items-center" data-id="m8klvf7in" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <Activity className="ml-3 h-6 w-6 text-purple-600" data-id="tsh6to4pj" data-path="src/pages/Chapter9PatientModeling.tsx" />
                    9.3 تعريفات المواد وتركيبات الأنسجة
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section93'] ? 'rotate-180' : ''}`} data-id="8qqajguk4" data-path="src/pages/Chapter9PatientModeling.tsx" />
                </CardTitle>
                <CardDescription data-id="yjo1x9lq0" data-path="src/pages/Chapter9PatientModeling.tsx">
                  معايير ICRU-44 و ICRU-46 لتركيبات الأنسجة البشرية
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="gvjtewk7w" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardContent className="space-y-6" data-id="wd09t46tb" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="hm9r3xsqm" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <Card className="bg-gradient-to-br from-blue-50 to-blue-100" data-id="iad3rov7y" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="icu99ms92" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="text-lg text-blue-800" data-id="72mhtyea7" data-path="src/pages/Chapter9PatientModeling.tsx">ICRU-44: تركيبات الأنسجة</CardTitle>
                    </CardHeader>
                    <CardContent data-id="2emxrlce4" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div className="space-y-3" data-id="1q5dokd0y" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <div data-id="ddoxcsm2a" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="hdl6gzzi2" data-path="src/pages/Chapter9PatientModeling.tsx">الأنسجة الرخوة:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="bqdiowrfa" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="7d7n39hjw" data-path="src/pages/Chapter9PatientModeling.tsx">• العضلات: 76.2% H₂O, 12.3% البروتين</li>
                            <li data-id="jlslv4qnm" data-path="src/pages/Chapter9PatientModeling.tsx">• الدهون: 11.5% H₂O, 85.7% الدهون</li>
                            <li data-id="k23uyom3s" data-path="src/pages/Chapter9PatientModeling.tsx">• الدم: 83.2% H₂O, 16.0% البروتين</li>
                          </ul>
                        </div>
                        <div data-id="bww56scvv" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="i23b4mign" data-path="src/pages/Chapter9PatientModeling.tsx">الأنسجة الصلبة:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="yafbmin7q" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="hjr6nzsmy" data-path="src/pages/Chapter9PatientModeling.tsx">• العظام الكثيفة: كثافة 1.92 g/cm³</li>
                            <li data-id="c7jro6ejj" data-path="src/pages/Chapter9PatientModeling.tsx">• العظام الإسفنجية: كثافة 1.18 g/cm³</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-green-50 to-green-100" data-id="fy4xaxcsw" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="o97ep5rop" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="text-lg text-green-800" data-id="93of3lgi7" data-path="src/pages/Chapter9PatientModeling.tsx">ICRU-46: معاملات التفاعل</CardTitle>
                    </CardHeader>
                    <CardContent data-id="vaj5x8yk0" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div className="space-y-3" data-id="fhlen0rf7" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <div data-id="ho7xcab53" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="2dieea3xm" data-path="src/pages/Chapter9PatientModeling.tsx">معاملات التوهين:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="g4s3kgksb" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="f57xz02hi" data-path="src/pages/Chapter9PatientModeling.tsx">• معامل التوهين الكتلي</li>
                            <li data-id="khf2t9e4u" data-path="src/pages/Chapter9PatientModeling.tsx">• معامل التوهين الخطي</li>
                            <li data-id="akhyq35bc" data-path="src/pages/Chapter9PatientModeling.tsx">• معامل نقل الطاقة</li>
                          </ul>
                        </div>
                        <div data-id="lk71n1tlw" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="hwc9sbkqg" data-path="src/pages/Chapter9PatientModeling.tsx">خصائص الأنسجة:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="ynbl9bdxy" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="5yajys38u" data-path="src/pages/Chapter9PatientModeling.tsx">• العدد الذري الفعال</li>
                            <li data-id="3jr9p82yx" data-path="src/pages/Chapter9PatientModeling.tsx">• الكثافة الإلكترونية</li>
                            <li data-id="pmgvawcnf" data-path="src/pages/Chapter9PatientModeling.tsx">• طاقة الإثارة المتوسطة</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 9.4 */}
        <Card className="mb-6 shadow-lg" data-id="qj43j5xex" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Collapsible
            open={openSections['section94']}
            onOpenChange={() => toggleSection('section94')} data-id="qjp8puj5w" data-path="src/pages/Chapter9PatientModeling.tsx">

            <CollapsibleTrigger className="w-full" data-id="6em6qfkxj" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="x19bgx4xc" data-path="src/pages/Chapter9PatientModeling.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="y13lem1ci" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="flex items-center" data-id="prwja07qm" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <Grid3X3 className="ml-3 h-6 w-6 text-indigo-600" data-id="x8c2nfkjk" data-path="src/pages/Chapter9PatientModeling.tsx" />
                    9.4 هندسة التمثيل الشبكي والحدودي
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section94'] ? 'rotate-180' : ''}`} data-id="x8fs0ea2i" data-path="src/pages/Chapter9PatientModeling.tsx" />
                </CardTitle>
                <CardDescription data-id="acgihoal3" data-path="src/pages/Chapter9PatientModeling.tsx">
                  طرق تمثيل الهندسة في المحاكاة الحاسوبية
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="rt4o96d9j" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardContent className="space-y-6" data-id="y48ynyfwv" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="870d8gm4z" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <Card className="border-l-4 border-l-indigo-500" data-id="bwha1khif" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="him8cc5a8" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="text-lg" data-id="84bqy7mm0" data-path="src/pages/Chapter9PatientModeling.tsx">التمثيل الشبكي (Voxel-based)</CardTitle>
                    </CardHeader>
                    <CardContent data-id="1ry6j8dtj" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div className="space-y-3" data-id="l53onhmld" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <div data-id="av90o50qv" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="pbf7xtf1p" data-path="src/pages/Chapter9PatientModeling.tsx">المزايا:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="01ow9mar4" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="686it6ta7" data-path="src/pages/Chapter9PatientModeling.tsx">• سهولة في التنفيذ البرمجي</li>
                            <li data-id="d2rftbszv" data-path="src/pages/Chapter9PatientModeling.tsx">• تكامل مباشر مع بيانات التصوير</li>
                            <li data-id="fy6opdqjb" data-path="src/pages/Chapter9PatientModeling.tsx">• دقة في التفاصيل التشريحية</li>
                          </ul>
                        </div>
                        <div data-id="obsta797j" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="9wxr2epyz" data-path="src/pages/Chapter9PatientModeling.tsx">العيوب:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="j3oxwlxep" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="wp8gfhs27" data-path="src/pages/Chapter9PatientModeling.tsx">• استهلاك كبير للذاكرة</li>
                            <li data-id="tm1s0trif" data-path="src/pages/Chapter9PatientModeling.tsx">• أسطح متدرجة (stepwise)</li>
                            <li data-id="j0w72sc32" data-path="src/pages/Chapter9PatientModeling.tsx">• صعوبة في التشويه</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-teal-500" data-id="q5fl3qi7r" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="3pfyhip1a" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="text-lg" data-id="v8iqfw66c" data-path="src/pages/Chapter9PatientModeling.tsx">التمثيل الحدودي (Boundary-based)</CardTitle>
                    </CardHeader>
                    <CardContent data-id="8zv271y8z" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div className="space-y-3" data-id="y3dspsdqy" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <div data-id="2xim091oy" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="7njsxxiri" data-path="src/pages/Chapter9PatientModeling.tsx">المزايا:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="th4ovh0la" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="rdgchu23w" data-path="src/pages/Chapter9PatientModeling.tsx">• أسطح ناعمة ومستمرة</li>
                            <li data-id="lsyqb7yj3" data-path="src/pages/Chapter9PatientModeling.tsx">• مرونة في التشويه</li>
                            <li data-id="bpshcp39i" data-path="src/pages/Chapter9PatientModeling.tsx">• كفاءة في استخدام الذاكرة</li>
                          </ul>
                        </div>
                        <div data-id="d95ssp77h" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="bi22pn7z9" data-path="src/pages/Chapter9PatientModeling.tsx">العيوب:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="b6f931zl9" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="s6h6ghj5q" data-path="src/pages/Chapter9PatientModeling.tsx">• تعقيد في التنفيذ</li>
                            <li data-id="bnvzj314w" data-path="src/pages/Chapter9PatientModeling.tsx">• صعوبة في النمذجة المعقدة</li>
                            <li data-id="p3lgn6ude" data-path="src/pages/Chapter9PatientModeling.tsx">• حسابات هندسية معقدة</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 9.5 */}
        <Card className="mb-6 shadow-lg" data-id="90g3kully" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Collapsible
            open={openSections['section95']}
            onOpenChange={() => toggleSection('section95')} data-id="fg3365skh" data-path="src/pages/Chapter9PatientModeling.tsx">

            <CollapsibleTrigger className="w-full" data-id="qyke0ofuj" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="ejf08g6p2" data-path="src/pages/Chapter9PatientModeling.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="k27abxf69" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="flex items-center" data-id="buezgb9ti" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <AlertCircle className="ml-3 h-6 w-6 text-red-600" data-id="2k5q9nv84" data-path="src/pages/Chapter9PatientModeling.tsx" />
                    9.5 التحديات في النمذجة التشريحية الدقيقة
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section95'] ? 'rotate-180' : ''}`} data-id="l4sifmggm" data-path="src/pages/Chapter9PatientModeling.tsx" />
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="y3294hbuz" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardContent data-id="czwje4g9z" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="j2zttznhd" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="space-y-4" data-id="gys6q5klp" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <h3 className="text-lg font-semibold text-red-700" data-id="425k0klyd" data-path="src/pages/Chapter9PatientModeling.tsx">التحديات التقنية</h3>
                    <ul className="space-y-2 text-gray-700" data-id="y0dem7ct3" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <li className="flex items-start" data-id="uqwd11oqo" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-red-500 ml-2 mt-1" data-id="xhjfxcja8" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="tmx2bp1kh" data-path="src/pages/Chapter9PatientModeling.tsx">التباين بين الأفراد في الحجم والشكل</span>
                      </li>
                      <li className="flex items-start" data-id="8wojjw4w2" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-red-500 ml-2 mt-1" data-id="zyg7qz1ra" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="56puwqtgz" data-path="src/pages/Chapter9PatientModeling.tsx">حركة الأعضاء أثناء العلاج</span>
                      </li>
                      <li className="flex items-start" data-id="64hraf57y" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-red-500 ml-2 mt-1" data-id="gj9x0z6tm" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="th1qh5h55" data-path="src/pages/Chapter9PatientModeling.tsx">دقة بيانات التصوير المحدودة</span>
                      </li>
                      <li className="flex items-start" data-id="4pu2cuxw5" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-red-500 ml-2 mt-1" data-id="eo80mxpsa" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="iodlzsnb0" data-path="src/pages/Chapter9PatientModeling.tsx">التغيرات الفسيولوجية مع الوقت</span>
                      </li>
                    </ul>
                  </div>
                  <div className="space-y-4" data-id="92ut3x25h" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <h3 className="text-lg font-semibold text-blue-700" data-id="0fij180zh" data-path="src/pages/Chapter9PatientModeling.tsx">الحلول المقترحة</h3>
                    <ul className="space-y-2 text-gray-700" data-id="rh8hp0k2y" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <li className="flex items-start" data-id="wgszb03q8" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-blue-500 ml-2 mt-1" data-id="0vt4bntk5" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="za9m3jhvj" data-path="src/pages/Chapter9PatientModeling.tsx">الأشباح القابلة للتشويه</span>
                      </li>
                      <li className="flex items-start" data-id="jq2lfs85b" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-blue-500 ml-2 mt-1" data-id="cn8qvmiz1" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="rey0nw6lx" data-path="src/pages/Chapter9PatientModeling.tsx">التصوير المتتالي 4D</span>
                      </li>
                      <li className="flex items-start" data-id="0y5v25pld" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-blue-500 ml-2 mt-1" data-id="0l3isfc5g" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="54n34j3fq" data-path="src/pages/Chapter9PatientModeling.tsx">تقنيات الذكاء الاصطناعي</span>
                      </li>
                      <li className="flex items-start" data-id="7obdbe649" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-blue-500 ml-2 mt-1" data-id="pwlgqhpzu" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="91g82yh10" data-path="src/pages/Chapter9PatientModeling.tsx">المحاكاة التكيفية</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Key Terms */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-yellow-500" data-id="741ff30um" data-path="src/pages/Chapter9PatientModeling.tsx">
          <CardHeader data-id="qs77yj8fq" data-path="src/pages/Chapter9PatientModeling.tsx">
            <CardTitle className="flex items-center text-2xl text-yellow-700" data-id="yel8z5lin" data-path="src/pages/Chapter9PatientModeling.tsx">
              <Key className="ml-3 h-6 w-6" data-id="pyggpmway" data-path="src/pages/Chapter9PatientModeling.tsx" />
              المصطلحات الرئيسية
            </CardTitle>
          </CardHeader>
          <CardContent data-id="b0yr99rch" data-path="src/pages/Chapter9PatientModeling.tsx">
            <div className="grid md:grid-cols-2 gap-6" data-id="4dl2buuw0" data-path="src/pages/Chapter9PatientModeling.tsx">
              <div className="space-y-3" data-id="mjhhk5v6o" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="border-r-4 border-r-blue-400 pr-4" data-id="3r0xatara" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="ycl0t7h56" data-path="src/pages/Chapter9PatientModeling.tsx">الشبح (Phantom)</h4>
                  <p className="text-sm text-gray-600" data-id="t7ndngt3f" data-path="src/pages/Chapter9PatientModeling.tsx">نموذج يحاكي خصائص جسم الإنسان لأغراض المحاكاة</p>
                </div>
                <div className="border-r-4 border-r-green-400 pr-4" data-id="7cvyem8on" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="aque7d873" data-path="src/pages/Chapter9PatientModeling.tsx">الشبح المجسم (Anthropomorphic)</h4>
                  <p className="text-sm text-gray-600" data-id="jiitor0wb" data-path="src/pages/Chapter9PatientModeling.tsx">نموذج يحاكي الشكل والتركيب التشريحي البشري</p>
                </div>
                <div className="border-r-4 border-r-purple-400 pr-4" data-id="fpfpg2jy3" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="zps42xupi" data-path="src/pages/Chapter9PatientModeling.tsx">الفوكسل (Voxel)</h4>
                  <p className="text-sm text-gray-600" data-id="89bqxxa1b" data-path="src/pages/Chapter9PatientModeling.tsx">وحدة حجمية ثلاثية الأبعاد في الشبكة الحاسوبية</p>
                </div>
              </div>
              <div className="space-y-3" data-id="dg1zvkn4a" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="border-r-4 border-r-red-400 pr-4" data-id="boaw7kx4o" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="p6lbqoyy7" data-path="src/pages/Chapter9PatientModeling.tsx">رقم Hounsfield</h4>
                  <p className="text-sm text-gray-600" data-id="herxp4of8" data-path="src/pages/Chapter9PatientModeling.tsx">مقياس الكثافة في صور التصوير المقطعي المحوسب</p>
                </div>
                <div className="border-r-4 border-r-indigo-400 pr-4" data-id="hekct3q4t" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="cm3m8p7wd" data-path="src/pages/Chapter9PatientModeling.tsx">العدد الذري الفعال</h4>
                  <p className="text-sm text-gray-600" data-id="1w7d4ssns" data-path="src/pages/Chapter9PatientModeling.tsx">متوسط مرجح للعدد الذري في المواد المركبة</p>
                </div>
                <div className="border-r-4 border-r-teal-400 pr-4" data-id="8qodnmbdu" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="cnbp8tb0l" data-path="src/pages/Chapter9PatientModeling.tsx">الكثافة الإلكترونية</h4>
                  <p className="text-sm text-gray-600" data-id="d03z3hlkh" data-path="src/pages/Chapter9PatientModeling.tsx">عدد الإلكترونات لكل وحدة حجم في المادة</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* References */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-green-500" data-id="3tpxqf5fw" data-path="src/pages/Chapter9PatientModeling.tsx">
          <CardHeader data-id="8dprq2hss" data-path="src/pages/Chapter9PatientModeling.tsx">
            <CardTitle className="flex items-center text-2xl text-green-700" data-id="gyk8qtqxt" data-path="src/pages/Chapter9PatientModeling.tsx">
              <FileText className="ml-3 h-6 w-6" data-id="d3v5yo72f" data-path="src/pages/Chapter9PatientModeling.tsx" />
              المراجع
            </CardTitle>
          </CardHeader>
          <CardContent data-id="bjsiva1x3" data-path="src/pages/Chapter9PatientModeling.tsx">
            <ul className="space-y-2 text-gray-700" data-id="bpgzckpix" data-path="src/pages/Chapter9PatientModeling.tsx">
              <li data-id="miy845yau" data-path="src/pages/Chapter9PatientModeling.tsx">1. ICRU Report 44: Tissue Substitutes in Radiation Dosimetry and Measurement (1989)</li>
              <li data-id="ljedwn4fk" data-path="src/pages/Chapter9PatientModeling.tsx">2. ICRU Report 46: Photon, Electron, Proton and Neutron Interaction Data (1992)</li>
              <li data-id="rfvvtdjno" data-path="src/pages/Chapter9PatientModeling.tsx">3. ICRP Publication 89: Basic Anatomical and Physiological Data (2002)</li>
              <li data-id="5e3smk1a0" data-path="src/pages/Chapter9PatientModeling.tsx">4. Segars, W.P., et al.: 4D XCAT phantom for multimodality imaging research. Med Phys 37, 4902-4915 (2010)</li>
              <li data-id="s0unoz4m4" data-path="src/pages/Chapter9PatientModeling.tsx">5. Xu, X.G.: An exponential growth of computational phantom research. Phys Med Biol 59, R233-R302 (2014)</li>
            </ul>
          </CardContent>
        </Card>

        {/* Problems */}
        <Card className="shadow-lg border-t-4 border-t-orange-500" data-id="wx0xztn8x" data-path="src/pages/Chapter9PatientModeling.tsx">
          <CardHeader data-id="7vuwqo1qk" data-path="src/pages/Chapter9PatientModeling.tsx">
            <CardTitle className="flex items-center text-2xl text-orange-700" data-id="5b7cvrn6s" data-path="src/pages/Chapter9PatientModeling.tsx">
              <HelpCircle className="ml-3 h-6 w-6" data-id="nzjienhn9" data-path="src/pages/Chapter9PatientModeling.tsx" />
              المشكلات والتمارين
            </CardTitle>
          </CardHeader>
          <CardContent data-id="cbu9jl3zf" data-path="src/pages/Chapter9PatientModeling.tsx">
            <div className="space-y-6" data-id="hjkeoibok" data-path="src/pages/Chapter9PatientModeling.tsx">
              <div className="bg-orange-50 p-4 rounded-lg" data-id="2kjsxed64" data-path="src/pages/Chapter9PatientModeling.tsx">
                <h4 className="font-semibold text-orange-800 mb-2" data-id="cepalmg1c" data-path="src/pages/Chapter9PatientModeling.tsx">المشكلة 1:</h4>
                <p className="text-gray-700" data-id="52oq4hoqc" data-path="src/pages/Chapter9PatientModeling.tsx">
                  احسب معامل التوهين الخطي للعضلات عند طاقة 100 keV باستخدام بيانات ICRU-44. 
                  كثافة العضلات = 1.05 g/cm³، معامل التوهين الكتلي = 0.164 cm²/g.
                </p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg" data-id="53wv3ldex" data-path="src/pages/Chapter9PatientModeling.tsx">
                <h4 className="font-semibold text-blue-800 mb-2" data-id="7jhwp44st" data-path="src/pages/Chapter9PatientModeling.tsx">المشكلة 2:</h4>
                <p className="text-gray-700" data-id="dy9mgun28" data-path="src/pages/Chapter9PatientModeling.tsx">
                  صمم شبحاً أسطوانياً بقطر 30 cm وارتفاع 20 cm يحاكي الجذع البشري. 
                  حدد التركيب النسجي وحسب النسب الحجمية للأعضاء المختلفة.
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg" data-id="t5mpe838l" data-path="src/pages/Chapter9PatientModeling.tsx">
                <h4 className="font-semibold text-green-800 mb-2" data-id="5tchwuxu3" data-path="src/pages/Chapter9PatientModeling.tsx">المشكلة 3:</h4>
                <p className="text-gray-700" data-id="46xqtqrlf" data-path="src/pages/Chapter9PatientModeling.tsx">
                  قارن بين دقة النتائج عند استخدام شبح مكعبي بحجم فوكسل 1 mm³ 
                  مقابل شبح بحجم فوكسل 5 mm³ لنفس الهندسة.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default Chapter9PatientModeling;