
import React, { useState } from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Key, Search, BookOpen, Calculator, Target, Zap } from 'lucide-react';

const KeyTerms = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const termCategories = [
  {
    category: 'التفاعلات الأساسية',
    categoryEn: 'Basic Interactions',
    icon: <Zap className="w-5 h-5" data-id="p7xtn81dt" data-path="src/pages/KeyTerms.tsx" />,
    color: 'bg-blue-50 border-blue-200',
    terms: [
    {
      arabic: 'التشتت المتماسك',
      english: 'Coherent Scattering',
      definition: 'تشتت الفوتون دون فقدان طاقة، يحدث مع الإلكترونات المرتبطة بقوة',
      formula: 'E\' = E (طاقة ثابتة)'
    },
    {
      arabic: 'تشتت رايلي',
      english: 'Rayleigh Scattering',
      definition: 'اسم آخر للتشتت المتماسك، نسبة للعالم رايلي',
      formula: 'σ ∝ Z² / E²'
    },
    {
      arabic: 'التأثير الكهروضوئي',
      english: 'Photoelectric Effect',
      definition: 'امتصاص كامل للفوتون مع طرد إلكترون من مداره',
      formula: 'KE = E - BE'
    },
    {
      arabic: 'تشتت كومبتون',
      english: 'Compton Scattering',
      definition: 'تشتت الفوتون بفقدان جزئي للطاقة مع إلكترون حر أو مرتبط ضعيفاً',
      formula: 'E\' = E / [1 + (E/m₀c²)(1-cosθ)]'
    },
    {
      arabic: 'إنتاج الأزواج',
      english: 'Pair Production',
      definition: 'تحول فوتون عالي الطاقة إلى زوج إلكترون-بوزيترون',
      formula: 'E_threshold = 1.022 MeV'
    }]

  },
  {
    category: 'معاملات التوهين',
    categoryEn: 'Attenuation Coefficients',
    icon: <Calculator className="w-5 h-5" data-id="4usl20e17" data-path="src/pages/KeyTerms.tsx" />,
    color: 'bg-green-50 border-green-200',
    terms: [
    {
      arabic: 'المعامل الخطي',
      english: 'Linear Attenuation Coefficient (μ)',
      definition: 'احتمالية التفاعل لكل وحدة طول في المادة',
      formula: 'I = I₀e^(-μx)'
    },
    {
      arabic: 'معامل الكتلة',
      english: 'Mass Attenuation Coefficient',
      definition: 'المعامل الخطي مقسوماً على الكثافة',
      formula: 'μ/ρ (cm²/g)'
    },
    {
      arabic: 'المعامل الذري',
      english: 'Atomic Attenuation Coefficient',
      definition: 'المقطع العرضي لكل ذرة في المادة',
      formula: 'μₐ = (μ/ρ) × (A/Nₐ)'
    },
    {
      arabic: 'المعامل الإلكتروني',
      english: 'Electronic Attenuation Coefficient',
      definition: 'المقطع العرضي لكل إلكترون في الذرة',
      formula: 'μₑ = μₐ / Z'
    },
    {
      arabic: 'طبقة التخفيف النصفي',
      english: 'Half Value Layer (HVL)',
      definition: 'سماكة المادة التي تقل شدة الحزمة إلى النصف',
      formula: 'HVL = ln(2)/μ = 0.693/μ'
    }]

  },
  {
    category: 'الكميات الفيزيائية',
    categoryEn: 'Physical Quantities',
    icon: <Target className="w-5 h-5" data-id="ufjygkuaf" data-path="src/pages/KeyTerms.tsx" />,
    color: 'bg-purple-50 border-purple-200',
    terms: [
    {
      arabic: 'المقطع العرضي',
      english: 'Cross Section (σ)',
      definition: 'مقياس احتمالية حدوث تفاعل معين',
      formula: 'وحدة: بارن (10⁻²⁴ cm²)'
    },
    {
      arabic: 'طاقة الربط',
      english: 'Binding Energy (BE)',
      definition: 'الطاقة المطلوبة لإزالة إلكترون من مداره',
      formula: 'مختلفة لكل مدار (K, L, M...)'
    },
    {
      arabic: 'العدد الذري الفعال',
      english: 'Effective Atomic Number',
      definition: 'عدد ذري مكافئ للمواد المركبة',
      formula: 'Z_eff للأنسجة الرخوة ≈ 7.4'
    },
    {
      arabic: 'عامل الشكل',
      english: 'Form Factor',
      definition: 'تصحيح للتوزيع الإلكتروني في التشتت المتماسك',
      formula: 'F(q,Z) يعتمد على زاوية التشتت'
    },
    {
      arabic: 'زاوية التشتت',
      english: 'Scattering Angle (θ)',
      definition: 'الزاوية بين اتجاه الفوتون قبل وبعد التشتتت',
      formula: '0° ≤ θ ≤ 180°'
    }]

  },
  {
    category: 'التطبيقات السريرية',
    categoryEn: 'Clinical Applications',
    icon: <BookOpen className="w-5 h-5" data-id="n85rihx6y" data-path="src/pages/KeyTerms.tsx" />,
    color: 'bg-orange-50 border-orange-200',
    terms: [
    {
      arabic: 'التباين الإشعاعي',
      english: 'Radiographic Contrast',
      definition: 'الفرق في الكثافة الضوئية بين مناطق مختلفة في الصورة',
      formula: 'يعتمد على الاختلاف في التوهين'
    },
    {
      arabic: 'مواد التباين',
      english: 'Contrast Agents',
      definition: 'مواد عالية أو منخفضة العدد الذري لتحسين التباين',
      formula: 'مثل الباريوم (Z=56) واليود (Z=53)'
    },
    {
      arabic: 'الأشعة المتشتتة',
      english: 'Scattered Radiation',
      definition: 'إشعاع غير مرغوب فيه يقل من جودة الصورة',
      formula: 'يزداد مع تشتت كومبتون'
    },
    {
      arabic: 'الحماية الإشعاعية',
      english: 'Radiation Protection',
      definition: 'تدابير لتقليل التعرض للإشعاع',
      formula: 'تعتمد على حسابات التوهين'
    },
    {
      arabic: 'حساب الجرعة',
      english: 'Dose Calculation',
      definition: 'تحديد كمية الطاقة الممتصة في الأنسجة',
      formula: 'يعتمد على معاملات امتصاص الطاقة'
    }]

  }];


  const filteredCategories = termCategories.map((category) => ({
    ...category,
    terms: category.terms.filter((term) =>
    term.arabic.toLowerCase().includes(searchTerm.toLowerCase()) ||
    term.english.toLowerCase().includes(searchTerm.toLowerCase()) ||
    term.definition.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter((category) => category.terms.length > 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-slate-50 to-purple-50" data-id="ecj0ufw3a" data-path="src/pages/KeyTerms.tsx">
      <Navigation
        title="المصطلحات الرئيسية"
        titleEn="Key Terms" data-id="majqs47dj" data-path="src/pages/KeyTerms.tsx" />

      
      <div className="container mx-auto px-4 py-8" data-id="c732k57su" data-path="src/pages/KeyTerms.tsx">
        {/* Header */}
        <Card className="mb-8" data-id="boxvtmhrh" data-path="src/pages/KeyTerms.tsx">
          <CardHeader data-id="70bf9danq" data-path="src/pages/KeyTerms.tsx">
            <div className="flex items-center gap-3" data-id="q984mrj30" data-path="src/pages/KeyTerms.tsx">
              <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center" data-id="kp8pk6682" data-path="src/pages/KeyTerms.tsx">
                <Key className="w-5 h-5 text-indigo-600" data-id="wdsatp7hx" data-path="src/pages/KeyTerms.tsx" />
              </div>
              <div data-id="wolllbope" data-path="src/pages/KeyTerms.tsx">
                <CardTitle className="text-2xl text-right" data-id="jpif0j7wu" data-path="src/pages/KeyTerms.tsx">المصطلحات الرئيسية</CardTitle>
                <p className="text-gray-600 text-right" data-id="enohxo661" data-path="src/pages/KeyTerms.tsx">
                  المفاهيم والتعريفات الأساسية في تفاعل الأشعة السينية مع المادة
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent data-id="3yg1es5wz" data-path="src/pages/KeyTerms.tsx">
            <div className="relative" data-id="6iqp760ly" data-path="src/pages/KeyTerms.tsx">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" data-id="oq9f1kter" data-path="src/pages/KeyTerms.tsx" />
              <Input
                placeholder="البحث في المصطلحات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 text-right" data-id="dqcrl54oj" data-path="src/pages/KeyTerms.tsx" />

            </div>
          </CardContent>
        </Card>

        {/* Terms by Category */}
        <div className="space-y-8" data-id="xfcilfpa1" data-path="src/pages/KeyTerms.tsx">
          {filteredCategories.map((category, categoryIndex) =>
          <Card key={categoryIndex} className={category.color} data-id="bur4rm0pp" data-path="src/pages/KeyTerms.tsx">
              <CardHeader data-id="stizoao2z" data-path="src/pages/KeyTerms.tsx">
                <div className="flex items-center gap-3" data-id="h7qqvfj7d" data-path="src/pages/KeyTerms.tsx">
                  <div className="p-2 bg-white rounded-lg" data-id="49i5poufr" data-path="src/pages/KeyTerms.tsx">
                    {category.icon}
                  </div>
                  <div data-id="gzardirv5" data-path="src/pages/KeyTerms.tsx">
                    <CardTitle className="text-xl text-right" data-id="z3lzkvp1o" data-path="src/pages/KeyTerms.tsx">{category.category}</CardTitle>
                    <CardDescription className="italic" data-id="bus9t6pwr" data-path="src/pages/KeyTerms.tsx">{category.categoryEn}</CardDescription>
                  </div>
                  <Badge variant="outline" className="ml-auto" data-id="e9e16yida" data-path="src/pages/KeyTerms.tsx">
                    {category.terms.length} مصطلحات
                  </Badge>
                </div>
              </CardHeader>
              <CardContent data-id="7yc55ksko" data-path="src/pages/KeyTerms.tsx">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4" data-id="1ny5w9udq" data-path="src/pages/KeyTerms.tsx">
                  {category.terms.map((term, termIndex) =>
                <Card key={termIndex} className="bg-white border-gray-200 hover:shadow-md transition-shadow" data-id="tbp9bruna" data-path="src/pages/KeyTerms.tsx">
                      <CardHeader className="pb-3" data-id="z2h4mg0es" data-path="src/pages/KeyTerms.tsx">
                        <div className="flex justify-between items-start" data-id="jou8nsxc0" data-path="src/pages/KeyTerms.tsx">
                          <Badge variant="secondary" className="text-xs" data-id="wdi3ypegh" data-path="src/pages/KeyTerms.tsx">
                            {term.english}
                          </Badge>
                          <CardTitle className="text-base text-right" data-id="eh4oyljep" data-path="src/pages/KeyTerms.tsx">{term.arabic}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent data-id="2ykehl9nh" data-path="src/pages/KeyTerms.tsx">
                        <div className="space-y-3" data-id="3t6y7yczs" data-path="src/pages/KeyTerms.tsx">
                          <p className="text-sm text-gray-700 text-right leading-relaxed" data-id="in3fgtm1o" data-path="src/pages/KeyTerms.tsx">
                            {term.definition}
                          </p>
                          {term.formula &&
                      <div className="bg-gray-50 p-2 rounded text-center" data-id="qgpu1irlb" data-path="src/pages/KeyTerms.tsx">
                              <code className="text-sm font-mono" data-id="ua2akbm1n" data-path="src/pages/KeyTerms.tsx">{term.formula}</code>
                            </div>
                      }
                        </div>
                      </CardContent>
                    </Card>
                )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Summary Statistics */}
        <Card className="mt-8" data-id="2wz3571it" data-path="src/pages/KeyTerms.tsx">
          <CardHeader data-id="dyqel2j48" data-path="src/pages/KeyTerms.tsx">
            <CardTitle className="text-xl text-right" data-id="tymuljc12" data-path="src/pages/KeyTerms.tsx">إحصائيات المصطلحات</CardTitle>
          </CardHeader>
          <CardContent data-id="2ktz0vbn7" data-path="src/pages/KeyTerms.tsx">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4" data-id="fpxvdt27e" data-path="src/pages/KeyTerms.tsx">
              <div className="text-center" data-id="rugqvmdvl" data-path="src/pages/KeyTerms.tsx">
                <div className="text-2xl font-bold text-blue-600" data-id="ljt097630" data-path="src/pages/KeyTerms.tsx">
                  {termCategories.reduce((sum, cat) => sum + cat.terms.length, 0)}
                </div>
                <p className="text-sm text-gray-600" data-id="t2mbsdgt2" data-path="src/pages/KeyTerms.tsx">إجمالي المصطلحات</p>
              </div>
              <div className="text-center" data-id="mzi7zaw2t" data-path="src/pages/KeyTerms.tsx">
                <div className="text-2xl font-bold text-green-600" data-id="89telfkpr" data-path="src/pages/KeyTerms.tsx">
                  {termCategories.length}
                </div>
                <p className="text-sm text-gray-600" data-id="e04xwns4j" data-path="src/pages/KeyTerms.tsx">فئات المصطلحات</p>
              </div>
              <div className="text-center" data-id="fa181ixhf" data-path="src/pages/KeyTerms.tsx">
                <div className="text-2xl font-bold text-purple-600" data-id="c97azk028" data-path="src/pages/KeyTerms.tsx">
                  {termCategories[0].terms.length}
                </div>
                <p className="text-sm text-gray-600" data-id="o5zst3n3g" data-path="src/pages/KeyTerms.tsx">تفاعلات أساسية</p>
              </div>
              <div className="text-center" data-id="biw7cj3kv" data-path="src/pages/KeyTerms.tsx">
                <div className="text-2xl font-bold text-orange-600" data-id="u332c3ce2" data-path="src/pages/KeyTerms.tsx">
                  {termCategories[3].terms.length}
                </div>
                <p className="text-sm text-gray-600" data-id="q85aqh9ic" data-path="src/pages/KeyTerms.tsx">تطبيقات سريرية</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {searchTerm && filteredCategories.length === 0 &&
        <Card className="mt-8" data-id="91gu6ku99" data-path="src/pages/KeyTerms.tsx">
            <CardContent className="text-center py-12" data-id="gmpfutr2u" data-path="src/pages/KeyTerms.tsx">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" data-id="kdbx27a0o" data-path="src/pages/KeyTerms.tsx" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2" data-id="frp7ca3o1" data-path="src/pages/KeyTerms.tsx">لا توجد نتائج</h3>
              <p className="text-gray-500" data-id="1cwyom1ln" data-path="src/pages/KeyTerms.tsx">لم يتم العثور على مصطلحات تطابق بحثك: "{searchTerm}"</p>
            </CardContent>
          </Card>
        }
      </div>
    </div>);

};

export default KeyTerms;