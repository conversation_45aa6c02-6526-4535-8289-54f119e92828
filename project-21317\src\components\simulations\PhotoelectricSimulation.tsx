import { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';

const PhotoelectricSimulation = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  const [photonEnergy, setPhotonEnergy] = useState([50]); // keV
  const [atomicNumber, setAtomicNumber] = useState([20]); // Ca
  const [isRunning, setIsRunning] = useState(false);
  const [showTrajectory, setShowTrajectory] = useState(true);

  // Animation state
  const [photonPosition, setPhotonPosition] = useState({ x: 50, y: 250 });
  const [electronPosition, setElectronPosition] = useState({ x: 300, y: 250 });
  const [animationPhase, setAnimationPhase] = useState('ready'); // ready, collision, complete
  const [photoelectronAngle, setPhotoelectronAngle] = useState(45);

  // Calculate cross-section (simplified)
  const crossSection = Math.pow(atomicNumber[0], 4) / Math.pow(photonEnergy[0], 3) * 1000;
  const interactionProbability = Math.min(crossSection / 10000, 0.95);

  // Animation loop
  useEffect(() => {
    if (isRunning && canvasRef.current) {
      const animate = () => {
        drawSimulation();
        updateAnimation();
        animationRef.current = requestAnimationFrame(animate);
      };
      animate();
    } else if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRunning, photonPosition, electronPosition, animationPhase]);

  const updateAnimation = () => {
    if (animationPhase === 'ready' && photonPosition.x < 290) {
      setPhotonPosition((prev) => ({ ...prev, x: prev.x + 2 }));
    } else if (animationPhase === 'ready' && photonPosition.x >= 290) {
      setAnimationPhase('collision');
      setTimeout(() => {
        setAnimationPhase('complete');
        // Animate photoelectron
        const angle = photoelectronAngle * Math.PI / 180;
        const distance = photonEnergy[0] * 0.5;
        setElectronPosition({
          x: 300 + Math.cos(angle) * distance,
          y: 250 - Math.sin(angle) * distance
        });
      }, 500);
    }
  };

  const drawSimulation = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw atom
    ctx.fillStyle = '#3b82f6';
    ctx.beginPath();
    ctx.arc(300, 250, 30, 0, 2 * Math.PI);
    ctx.fill();

    // Draw nucleus
    ctx.fillStyle = '#1e40af';
    ctx.beginPath();
    ctx.arc(300, 250, 8, 0, 2 * Math.PI);
    ctx.fill();

    // Draw electron shells
    ctx.strokeStyle = '#60a5fa';
    ctx.lineWidth = 2;
    [15, 25, 35].forEach((radius) => {
      ctx.beginPath();
      ctx.arc(300, 250, radius, 0, 2 * Math.PI);
      ctx.stroke();
    });

    // Draw initial electrons
    if (animationPhase === 'ready' || animationPhase === 'collision') {
      ctx.fillStyle = '#ef4444';
      ctx.beginPath();
      ctx.arc(285, 250, 4, 0, 2 * Math.PI);
      ctx.fill();
    }

    // Draw photon
    if (animationPhase !== 'complete') {
      ctx.fillStyle = '#fbbf24';
      ctx.beginPath();
      ctx.arc(photonPosition.x, photonPosition.y, 6, 0, 2 * Math.PI);
      ctx.fill();

      // Draw photon wave
      ctx.strokeStyle = '#fbbf24';
      ctx.lineWidth = 2;
      ctx.beginPath();
      const waveLength = 20;
      const amplitude = 8;
      for (let x = 50; x < photonPosition.x; x += 2) {
        const y = 250 + amplitude * Math.sin((x - 50) / waveLength * 2 * Math.PI);
        if (x === 50) ctx.moveTo(x, y);else
        ctx.lineTo(x, y);
      }
      ctx.stroke();
    }

    // Draw photoelectron
    if (animationPhase === 'complete') {
      ctx.fillStyle = '#ef4444';
      ctx.beginPath();
      ctx.arc(electronPosition.x, electronPosition.y, 5, 0, 2 * Math.PI);
      ctx.fill();

      // Draw trajectory
      if (showTrajectory) {
        ctx.strokeStyle = '#ef4444';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(300, 250);
        ctx.lineTo(electronPosition.x, electronPosition.y);
        ctx.stroke();
        ctx.setLineDash([]);
      }
    }

    // Draw collision effects
    if (animationPhase === 'collision') {
      ctx.strokeStyle = '#fbbf24';
      ctx.lineWidth = 3;
      for (let i = 0; i < 8; i++) {
        const angle = i * 45 * Math.PI / 180;
        const length = 20;
        ctx.beginPath();
        ctx.moveTo(300, 250);
        ctx.lineTo(300 + Math.cos(angle) * length, 250 + Math.sin(angle) * length);
        ctx.stroke();
      }
    }

    // Draw labels
    ctx.fillStyle = '#374151';
    ctx.font = '12px Arial';
    ctx.fillText('Incoming Photon', 80, 230);
    ctx.fillText('Atom', 320, 280);
    if (animationPhase === 'complete') {
      ctx.fillText('Photoelectron', electronPosition.x + 10, electronPosition.y - 10);
    }
  };

  const resetAnimation = () => {
    setPhotonPosition({ x: 50, y: 250 });
    setElectronPosition({ x: 300, y: 250 });
    setAnimationPhase('ready');
    setPhotoelectronAngle(Math.random() * 90 + 30);
    setIsRunning(false);
  };

  const toggleAnimation = () => {
    if (animationPhase === 'complete') {
      resetAnimation();
    } else {
      setIsRunning(!isRunning);
    }
  };

  return (
    <div className="space-y-6" data-id="57omimpzr" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
      {/* Canvas */}
      <div className="relative bg-white rounded-lg border" data-id="rywh54rwo" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
        <canvas
          ref={canvasRef}
          width={600}
          height={400}
          className="w-full h-80 rounded-lg" data-id="4lwu2jwon" data-path="src/components/simulations/PhotoelectricSimulation.tsx" />

        
        {/* Control Buttons */}
        <div className="absolute top-4 right-4 flex gap-2" data-id="xall8hj7v" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
          <Button size="sm" onClick={toggleAnimation} data-id="142p4yv74" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
            {isRunning ? <Pause className="w-4 h-4" data-id="zbsemf4ff" data-path="src/components/simulations/PhotoelectricSimulation.tsx" /> : <Play className="w-4 h-4" data-id="q16gcth17" data-path="src/components/simulations/PhotoelectricSimulation.tsx" />}
          </Button>
          <Button size="sm" variant="outline" onClick={resetAnimation} data-id="3q339kvjq" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
            <RotateCcw className="w-4 h-4" data-id="t76dmc5l4" data-path="src/components/simulations/PhotoelectricSimulation.tsx" />
          </Button>
        </div>
      </div>

      {/* Controls */}
      <div className="grid md:grid-cols-2 gap-6" data-id="8gals158g" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
        <Card data-id="7x6mf5dqb" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
          <CardContent className="pt-6 space-y-4" data-id="zobi4dnp5" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
            <div data-id="oj48ynpvh" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
              <Label className="text-sm font-medium" data-id="xxq8d1t9t" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Photon Energy (keV)</Label>
              <Slider
                value={photonEnergy}
                onValueChange={setPhotonEnergy}
                max={100}
                min={10}
                step={5}
                className="mt-2" data-id="5bklq0oij" data-path="src/components/simulations/PhotoelectricSimulation.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="30nq6jd75" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
                <span data-id="3d0kitrj6" data-path="src/components/simulations/PhotoelectricSimulation.tsx">10</span>
                <span className="font-medium" data-id="joueuljhb" data-path="src/components/simulations/PhotoelectricSimulation.tsx">{photonEnergy[0]} keV</span>
                <span data-id="7mu6oank8" data-path="src/components/simulations/PhotoelectricSimulation.tsx">100</span>
              </div>
            </div>

            <div data-id="ohhoildya" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
              <Label className="text-sm font-medium" data-id="pbe91q0e9" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Atomic Number (Z)</Label>
              <Slider
                value={atomicNumber}
                onValueChange={setAtomicNumber}
                max={82}
                min={6}
                step={1}
                className="mt-2" data-id="n5r7h1kfk" data-path="src/components/simulations/PhotoelectricSimulation.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="pmbwq6hmk" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
                <span data-id="cf0mvkdlz" data-path="src/components/simulations/PhotoelectricSimulation.tsx">C (6)</span>
                <span className="font-medium" data-id="k4kgsqszd" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Z = {atomicNumber[0]}</span>
                <span data-id="wmtbibtlu" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Pb (82)</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="igw3o3lo4" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
          <CardContent className="pt-6 space-y-4" data-id="cvof7k6mf" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
            <div data-id="gp2sy5tgp" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="plvdnas8y" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Interaction Probability</Label>
              <div className="flex items-center gap-2" data-id="va30930wc" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
                <div className="flex-1 bg-gray-200 rounded-full h-3" data-id="pyam8yc8d" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
                  <div
                    className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${interactionProbability * 100}%` }} data-id="htnljmh4c" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
                  </div>
                </div>
                <Badge variant="outline" data-id="lm4iokwzn" data-path="src/components/simulations/PhotoelectricSimulation.tsx">{(interactionProbability * 100).toFixed(1)}%</Badge>
              </div>
            </div>

            <div data-id="ct34bw9qr" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="g7pc5lh0j" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Cross-Section (relative)</Label>
              <Badge variant="secondary" data-id="ddfg0f2vf" data-path="src/components/simulations/PhotoelectricSimulation.tsx">{crossSection.toFixed(2)} units</Badge>
            </div>

            <div data-id="v2bc26kwg" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="1ltjfabv4" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Photoelectron Energy</Label>
              <Badge variant="outline" data-id="uvxtk9npv" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
                {Math.max(0, photonEnergy[0] - 7.1).toFixed(1)} keV
              </Badge>
              <p className="text-xs text-gray-500 mt-1" data-id="6ikt36chr" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
                (Photon energy - binding energy)
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Information Panel */}
      <Card data-id="1tu0fbqsh" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
        <CardContent className="pt-6" data-id="xo0we33g4" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
          <h3 className="font-semibold text-gray-900 mb-3" data-id="34hwmqfyj" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Photoelectric Effect</h3>
          <div className="grid md:grid-cols-3 gap-4 text-sm text-gray-600" data-id="ebbbde15b" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
            <div data-id="qaln8bdeg" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
              <h4 className="font-medium text-gray-900 mb-1" data-id="49t4dlrv6" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Process</h4>
              <p data-id="eu0qcq71l" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Incident photon completely absorbed by inner shell electron, which is ejected as photoelectron.</p>
            </div>
            <div data-id="88uiyzimf" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
              <h4 className="font-medium text-gray-900 mb-1" data-id="sm9ihhadc" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Energy Conservation</h4>
              <p data-id="xctlp4l84" data-path="src/components/simulations/PhotoelectricSimulation.tsx">E_photon = E_binding + E_kinetic + E_recoil</p>
            </div>
            <div data-id="2144azn5i" data-path="src/components/simulations/PhotoelectricSimulation.tsx">
              <h4 className="font-medium text-gray-900 mb-1" data-id="0mxqwqjc0" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Characteristics</h4>
              <p data-id="p9ve4p5qa" data-path="src/components/simulations/PhotoelectricSimulation.tsx">Dominant at low energies (&lt;100 keV) and high-Z materials. Cross-section ∝ Z⁴/E³</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>);

};

export default PhotoelectricSimulation;