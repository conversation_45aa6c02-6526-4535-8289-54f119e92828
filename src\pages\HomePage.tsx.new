import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ChevronDown, 
  ChevronRight, 
  Zap, 
  Target, 
  Shield, 
  Settings, 
  BookOpen, 
  Key, 
  Globe, 
  Layers, 
  BookOpenCheck, 
  Atom, 
  Microscope, 
  Lightbulb, 
  GraduationCap
} from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import XRaySimulationPreview from '@/components/XRaySimulationPreview';

const HomePage = () => {
  const { language, setLanguage } = useLanguage();
  const [openSections, setOpenSections] = useState<{[key: string]: boolean;}>({});

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  // Bilingual content
  const content = {
    ar: {
      title: "تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية",
      subtitle: "دليل شامل للمهندسين الطبيين وعلماء الأشعة",
      languageButton: "English",
      mainModules: [
        {
          title: "الفيزياء الطبية",
          description: "أساسيات تفاعل الإشعاع مع المادة",
          icon: <Atom className="h-8 w-8 text-white" />,
          color: "from-blue-500 to-blue-700",
          path: "/الفيزياء الطبية"
        },
        {
          title: "التصوير الطبي",
          description: "مبادئ وتقنيات التصوير بالأشعة السينية",
          icon: <Microscope className="h-8 w-8 text-white" />,
          color: "from-purple-500 to-purple-700",
          path: "/التصوير الطبي"
        },
        {
          title: "محاكاة الأشعة السينية",
          description: "نمذجة ومحاكاة أنظمة التصوير المتقدمة",
          icon: <Layers className="h-8 w-8 text-white" />,
          color: "from-green-500 to-green-700",
          path: "/X-Ray Imaging Simulation"
        },
        {
          title: "الواقع الافتراضي",
          description: "تطبيقات الواقع الافتراضي في التعليم الطبي",
          icon: <Lightbulb className="h-8 w-8 text-white" />,
          color: "from-amber-500 to-amber-700",
          path: "/Medical Radiation Physics and virtual Reality"
        }
      ],
      featuredChapters: [
        {
          title: "الفصل 3: أنبوب الأشعة السينية",
          description: "التصميم والمكونات والوظيفة",
          path: "/تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية 3.html"
        },
        {
          title: "الفصل 6: ترشيح الأشعة السينية",
          description: "تحسين جودة الحزمة وتقليل الجرعة",
          path: "/xray-filtration.html"
        },
        {
          title: "الفصل 7: محاكاة مونت كارلو",
          description: "تقنيات متقدمة لمحاكاة تفاعلات الأشعة السينية",
          path: "/monte-carlo-simulation.html"
        }
      ],
      about: {
        title: "عن المنصة التعليمية",
        description: "منصة تعليمية تفاعلية متكاملة لدراسة فيزياء الإشعاع الطبي وتقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة. تجمع المنصة بين المحتوى النظري العميق والتطبيقات العملية التفاعلية، مما يتيح للطلاب والباحثين والمهنيين فهماً شاملاً للمبادئ الفيزيائية والتقنية لأنظمة التصوير الطبي.",
        features: [
          "محتوى ثنائي اللغة (العربية والإنجليزية)",
          "محاكاة تفاعلية لأنظمة الأشعة السينية",
          "تجارب افتراضية لفهم تفاعلات الإشعاع",
          "اختبارات ذاتية وتمارين تطبيقية",
          "مكتبة شاملة للمراجع والموارد العلمية"
        ]
      },
      author: {
        name: "د. محمد يعقوب إسماعيل",
        title: "أستاذ مشارك في الهندسة الطبية الحيوية",
        affiliation: "جامعة السودان للعلوم والتكنولوجيا، كلية الهندسة، قسم الهندسة الطبية الحيوية",
        email: "<EMAIL>",
        phone: "+249912867327, +966538076790"
      },
      footer: {
        copyright: "© 2024 جميع الحقوق محفوظة",
        links: [
          { title: "الصفحة الرئيسية", path: "/" },
          { title: "المحتوى العلمي", path: "/content" },
          { title: "المحاكاة التفاعلية", path: "/simulations" },
          { title: "عن المؤلف", path: "/author" },
          { title: "اتصل بنا", path: "/contact" }
        ]
      }
    },
    en: {
      title: "Advanced Simulation Techniques for X-Ray Imaging Systems",
      subtitle: "A Comprehensive Guide for Medical Engineers and Radiologists",
      languageButton: "العربية",
      mainModules: [
        {
          title: "Medical Physics",
          description: "Fundamentals of radiation interaction with matter",
          icon: <Atom className="h-8 w-8 text-white" />,
          color: "from-blue-500 to-blue-700",
          path: "/الفيزياء الطبية"
        },
        {
          title: "Medical Imaging",
          description: "Principles and techniques of X-ray imaging",
          icon: <Microscope className="h-8 w-8 text-white" />,
          color: "from-purple-500 to-purple-700",
          path: "/التصوير الطبي"
        },
        {
          title: "X-Ray Simulation",
          description: "Modeling and simulation of advanced imaging systems",
          icon: <Layers className="h-8 w-8 text-white" />,
          color: "from-green-500 to-green-700",
          path: "/X-Ray Imaging Simulation"
        },
        {
          title: "Virtual Reality",
          description: "VR applications in medical education",
          icon: <Lightbulb className="h-8 w-8 text-white" />,
          color: "from-amber-500 to-amber-700",
          path: "/Medical Radiation Physics and virtual Reality"
        }
      ],
      featuredChapters: [
        {
          title: "Chapter 3: X-Ray Tube",
          description: "Design, Components, and Function",
          path: "/تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية 3.html"
        },
        {
          title: "Chapter 6: X-Ray Filtration",
          description: "Beam Quality Improvement and Dose Reduction",
          path: "/xray-filtration.html"
        },
        {
          title: "Chapter 7: Monte Carlo Simulation",
          description: "Advanced Techniques for X-Ray Interaction Simulation",
          path: "/monte-carlo-simulation.html"
        }
      ],
      about: {
        title: "About the Learning Platform",
        description: "An integrated interactive educational platform for studying medical radiation physics and advanced simulation techniques for X-ray imaging systems. The platform combines in-depth theoretical content with interactive practical applications, allowing students, researchers, and professionals to gain a comprehensive understanding of the physical and technical principles of medical imaging systems.",
        features: [
          "Bilingual content (Arabic and English)",
          "Interactive simulation of X-ray systems",
          "Virtual experiments for understanding radiation interactions",
          "Self-assessment tests and practical exercises",
          "Comprehensive library of references and scientific resources"
        ]
      },
      author: {
        name: "Dr. Mohammed Yagoub Esmail",
        title: "Associate Professor of Biomedical Engineering",
        affiliation: "Sudan University of Science and Technology, Faculty of Engineering, Department of Biomedical Engineering",
        email: "<EMAIL>",
        phone: "+249912867327, +966538076790"
      },
      footer: {
        copyright: "© 2024 All Rights Reserved",
        links: [
          { title: "Home", path: "/" },
          { title: "Scientific Content", path: "/content" },
          { title: "Interactive Simulations", path: "/simulations" },
          { title: "About the Author", path: "/author" },
          { title: "Contact Us", path: "/contact" }
        ]
      }
    }
  };

  // Get current language content
  const currentContent = content[language];
  const dir = language === 'ar' ? 'rtl' : 'ltr';

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50`} dir={dir}>
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg">
                <BookOpenCheck className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{currentContent.title}</h1>
                <p className="text-sm text-gray-600">{currentContent.subtitle}</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <LanguageSwitcher 
                initialLanguage={language} 
                onLanguageChange={setLanguage}
              />
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* Hero Section */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-12">
          <div className="md:flex">
            <div className="md:w-3/5 p-8">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{currentContent.title}</h2>
              <p className="text-gray-600 mb-6 leading-relaxed">
                {currentContent.subtitle}
              </p>
              <div className="flex flex-wrap gap-3 mb-6">
                <span className="bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-sm">
                  {language === 'ar' ? 'الهندسة الطبية' : 'Biomedical Engineering'}
                </span>
                <span className="bg-green-50 text-green-600 px-3 py-1 rounded-full text-sm">
                  {language === 'ar' ? 'الفيزياء الطبية' : 'Medical Physics'}
                </span>
                <span className="bg-purple-50 text-purple-600 px-3 py-1 rounded-full text-sm">
                  {language === 'ar' ? 'علوم الأشعة' : 'Radiological Sciences'}
                </span>
                <span className="bg-amber-50 text-amber-600 px-3 py-1 rounded-full text-sm">
                  {language === 'ar' ? 'المحاكاة الحاسوبية' : 'Computational Simulation'}
                </span>
              </div>
              <div className="flex flex-wrap gap-3">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  {language === 'ar' ? 'ابدأ التعلم' : 'Start Learning'}
                </Button>
                <Button variant="outline">
                  {language === 'ar' ? 'عن المنصة' : 'About Platform'}
                </Button>
              </div>
            </div>
            <div className="md:w-2/5 bg-gradient-to-br from-indigo-500 to-purple-600 p-8 flex items-center justify-center">
              <div className="text-center">
                <div className="w-24 h-24 mx-auto bg-white/20 rounded-full flex items-center justify-center mb-4">
                  <Zap className="h-12 w-12 text-white" />
                </div>
                <h3 className="text-white text-xl font-bold mb-2">
                  {language === 'ar' ? 'تعلم تفاعلي' : 'Interactive Learning'}
                </h3>
                <p className="text-white/80 text-sm">
                  {language === 'ar' 
                    ? 'محاكاة متقدمة، تجارب افتراضية، ومحتوى ثنائي اللغة' 
                    : 'Advanced simulations, virtual experiments, and bilingual content'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* X-Ray Simulation Preview */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            {language === 'ar' ? 'محاكاة الأشعة السينية التفاعلية' : 'Interactive X-Ray Simulation'}
          </h2>
          <XRaySimulationPreview language={language} />
        </section>

        {/* Main Modules Section */}
        <section className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-800">
              {language === 'ar' ? 'الوحدات التعليمية الرئيسية' : 'Main Learning Modules'}
            </h2>
            <Button variant="ghost" size="sm">
              {language === 'ar' ? 'عرض الكل' : 'View All'} →
            </Button>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {currentContent.mainModules.map((module, index) => (
              <a 
                key={index} 
                href={module.path}
                className="module-card hover-lift bg-white rounded-xl shadow-md overflow-hidden"
              >
                <div className={`h-32 bg-gradient-to-r ${module.color} p-6 flex items-center justify-center`}>
                  {module.icon}
                </div>
                <div className="p-6">
                  <h3 className="font-bold text-lg text-gray-800 mb-2">{module.title}</h3>
                  <p className="text-gray-600 text-sm">{module.description}</p>
                </div>
              </a>
            ))}
          </div>
        </section>

        {/* Featured Chapters */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            {language === 'ar' ? 'فصول مختارة' : 'Featured Chapters'}
          </h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            {currentContent.featuredChapters.map((chapter, index) => (
              <a 
                key={index} 
                href={chapter.path}
                className="bg-white rounded-xl shadow-md p-6 hover-lift"
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                    <BookOpen className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800">{chapter.title}</h3>
                    <p className="text-sm text-gray-500">{chapter.description}</p>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-100 flex justify-end">
                  <span className="text-blue-600 text-sm font-medium">
                    {language === 'ar' ? 'قراءة المزيد' : 'Read More'} →
                  </span>
                </div>
              </a>
            ))}
          </div>
        </section>

        {/* About Section */}
        <section className="bg-white rounded-xl shadow-lg p-8 mb-12">
          <div className="md:flex">
            <div className="md:w-2/3 md:pr-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">{currentContent.about.title}</h2>
              <p className="text-gray-600 mb-6 leading-relaxed">{currentContent.about.description}</p>
              
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                {language === 'ar' ? 'المميزات الرئيسية' : 'Key Features'}
              </h3>
              <ul className="space-y-2 mb-6">
                {currentContent.about.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <div className="mt-1 mr-2">
                      <div className="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      </div>
                    </div>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="md:w-1/3 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 flex items-center justify-center">
              <div className="text-center">
                <GraduationCap className="h-16 w-16 text-indigo-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  {language === 'ar' ? 'تعلم بطريقتك' : 'Learn Your Way'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {language === 'ar' 
                    ? 'محتوى تفاعلي مصمم لتناسب أسلوب تعلمك الفريد' 
                    : 'Interactive content designed to fit your unique learning style'}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Author Section */}
        <section className="bg-white rounded-xl shadow-lg overflow-hidden mb-12">
          <div className="p-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
              <i className="fas fa-user-graduate mr-2 text-blue-500"></i>
              {language === 'ar' ? 'عن المؤلف' : 'About the Author'}
            </h2>
            <div className="flex flex-col md:flex-row items-start">
              <div className="md:w-1/4 mb-4 md:mb-0 flex justify-center">
                <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 overflow-hidden border-4 border-blue-50 flex items-center justify-center">
                  <GraduationCap className="h-12 w-12 text-indigo-400" />
                </div>
              </div>
              <div className="md:w-3/4 md:pl-4">
                <h3 className="text-lg font-semibold text-gray-800">{currentContent.author.name}</h3>
                <p className="text-blue-500 text-sm mb-2">{currentContent.author.title}</p>
                <p className="text-gray-600 mb-2">{currentContent.author.affiliation}</p>
                <p className="text-gray-600 mb-1">
                  <span className="font-medium">{language === 'ar' ? 'البريد الإلكتروني:' : 'Email:'}</span> {currentContent.author.email}
                </p>
                <p className="text-gray-600 mb-3">
                  <span className="font-medium">{language === 'ar' ? 'الهاتف:' : 'Phone:'}</span> {currentContent.author.phone}
                </p>
                <div className="flex space-x-3 rtl:space-x-reverse text-gray-500">
                  <a href="#" className="hover:text-blue-500"><i className="fab fa-google-scholar"></i></a>
                  <a href="#" className="hover:text-blue-500"><i className="fab fa-researchgate"></i></a>
                  <a href="#" className="hover:text-blue-500"><i className="fab fa-linkedin"></i></a>
                  <a href="#" className="hover:text-blue-500"><i className="fas fa-envelope"></i></a>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-6">
          <div className="md:flex md:justify-between">
            <div className="mb-6 md:mb-0">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-white/10 rounded-lg">
                  <BookOpenCheck className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-bold">{language === 'ar' ? 'منصة التعلم الطبي' : 'Medical Learning Platform'}</h2>
                </div>
              </div>
              <p className="text-gray-400 text-sm max-w-md">
                {language === 'ar' 
                  ? 'منصة تعليمية متخصصة في مجال الفيزياء الطبية وتقنيات التصوير بالأشعة السينية، تجمع بين المحتوى العلمي العميق والتطبيقات التفاعلية.' 
                  : 'A specialized educational platform in the field of medical physics and X-ray imaging techniques, combining in-depth scientific content with interactive applications.'}
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider mb-4">
                {language === 'ar' ? 'روابط سريعة' : 'Quick Links'}
              </h3>
              <ul className="space-y-2">
                {currentContent.footer.links.map((link, index) => (
                  <li key={index}>
                    <a href={link.path} className="text-gray-400 hover:text-white transition-colors">
                      {link.title}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-gray-700 flex flex-col md:flex-row md:justify-between md:items-center">
            <p className="text-gray-400 text-sm">
              {currentContent.footer.copyright}
            </p>
            <div className="flex space-x-6 rtl:space-x-reverse mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white">
                <i className="fab fa-twitter"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <i className="fab fa-linkedin"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <i className="fab fa-github"></i>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;