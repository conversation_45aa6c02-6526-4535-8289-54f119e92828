<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفيزياء الطبية | Medical Physics</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal for Arabic, Inter for English -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- MathJax for mathematical equations -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        /* Base Styles */
        :root {
            --primary-color: #4f46e5;
            --primary-light: #e0e7ff;
            --primary-dark: #312e81;
            --secondary-color: #0ea5e9;
            --secondary-light: #e0f2fe;
            --secondary-dark: #0369a1;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8fafc;
        }
        
        .en {
            font-family: 'Inter', sans-serif;
            direction: ltr;
            text-align: left;
        }
        
        /* Interactive elements */
        .interactive-control {
            transition: all 0.3s ease;
        }
        
        .interactive-control:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        /* Equation styling */
        .equation {
            background-color: #f8fafc;
            border-left: 4px solid #4f46e5;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
            overflow-x: auto;
            font-family: 'Times New Roman', serif;
            direction: ltr;
            text-align: center;
        }
        
        /* Interactive visualization */
        .visualization-container {
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem;
            background-color: #f1f5f9;
            border: 1px solid #e2e8f0;
        }
        
        /* Progress tracker */
        .progress-step {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background-color: #e2e8f0;
            color: #64748b;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .progress-step.active {
            background-color: #4f46e5;
            color: white;
        }
        
        .progress-step.completed {
            background-color: #10b981;
            color: white;
        }
        
        .progress-line {
            flex-grow: 1;
            height: 2px;
            background-color: #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .progress-line.active {
            background-color: #4f46e5;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-4 rtl:space-x-reverse">
                <a href="../landing.html" class="text-gray-600 hover:text-blue-600">
                    <i class="fas fa-arrow-right rtl:hidden"></i>
                    <i class="fas fa-arrow-left hidden rtl:inline"></i>
                    <span class="ml-2 rtl:mr-2" id="back-link">العودة للرئيسية</span>
                </a>
            </div>
            <div>
                <button id="language-toggle" class="text-gray-600 hover:text-blue-600 px-3 py-1 rounded-full border border-gray-200 text-sm flex items-center">
                    <i class="fas fa-globe mr-2 rtl:ml-2 rtl:mr-0"></i>
                    <span id="language-text">English</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Title Section -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div class="md:flex">
                <div class="md:w-2/3 p-8">
                    <h1 class="text-3xl font-bold text-gray-800 mb-4" id="page-title">الفيزياء الطبية</h1>
                    <p class="text-gray-600 mb-4 leading-relaxed" id="page-subtitle">أساسيات تفاعل الإشعاع مع المادة</p>
                    <div class="flex flex-wrap gap-3">
                        <span class="bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-sm">الوحدة الأولى</span>
                        <span class="bg-green-50 text-green-600 px-3 py-1 rounded-full text-sm" id="physics-tag">الفيزياء الإشعاعية</span>
                        <span class="bg-purple-50 text-purple-600 px-3 py-1 rounded-full text-sm" id="interaction-tag">تفاعلات الإشعاع</span>
                    </div>
                </div>
                <div class="md:w-1/3 bg-gradient-to-br from-blue-500 to-indigo-600 p-8 flex items-center justify-center">
                    <div class="text-center">
                        <div class="w-20 h-20 mx-auto bg-white/20 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-atom text-4xl text-white"></i>
                        </div>
                        <h3 class="text-white text-xl font-bold mb-2" id="interactive-title">محتوى تفاعلي</h3>
                        <p class="text-white/80 text-sm" id="interactive-desc">استكشاف تفاعلات الإشعاع مع المادة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Introduction Section -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center" id="intro-title">
                <i class="fas fa-info-circle text-blue-500 ml-2 rtl:ml-2 rtl:mr-0"></i>
                مقدمة في الفيزياء الطبية
            </h2>
            <p class="text-gray-700 mb-4 leading-relaxed" id="intro-p1">
                تُعد الفيزياء الطبية أحد الفروع الأساسية في العلوم الطبية التطبيقية، حيث تهتم بدراسة تطبيقات مبادئ وأساليب الفيزياء في تشخيص وعلاج الأمراض البشرية. تشكل الفيزياء الطبية الأساس العلمي لفهم وتطوير تقنيات التصوير الطبي، والعلاج الإشعاعي، والطب النووي، وغيرها من التطبيقات الطبية المتقدمة.
            </p>
            <p class="text-gray-700 leading-relaxed" id="intro-p2">
                يعتبر فهم تفاعل الإشعاع مع المادة من الركائز الأساسية في الفيزياء الطبية، حيث يمكّننا من تصميم أنظمة تصوير أكثر كفاءة، وتطوير بروتوكولات علاجية أكثر فعالية، وتحسين إجراءات الوقاية الإشعاعية. في هذه الوحدة، سنستكشف المبادئ الفيزيائية الأساسية لتفاعل الإشعاع مع الأنسجة البيولوجية، مع التركيز على الأشعة السينية وتطبيقاتها في التصوير الطبي.
            </p>
        </div>

        <!-- Interactive Learning Module -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div class="bg-gradient-to-r from-blue-600 to-indigo-700 p-4">
                <h2 class="text-xl font-bold text-white flex items-center" id="interactive-module-title">
                    <i class="fas fa-laptop-code mr-2 rtl:ml-2 rtl:mr-0"></i>
                    وحدة تعليمية تفاعلية: تفاعل الإشعاع مع المادة
                </h2>
            </div>
            
            <div class="p-6">
                <!-- Progress Tracker -->
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center flex-grow">
                        <div class="progress-step active">1</div>
                        <div class="progress-line"></div>
                        <div class="progress-step">2</div>
                        <div class="progress-line"></div>
                        <div class="progress-step">3</div>
                        <div class="progress-line"></div>
                        <div class="progress-step">4</div>
                    </div>
                </div>
                
                <!-- Content Sections (Only first one visible initially) -->
                <div id="section-1" class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4" id="section-1-title">أنواع الإشعاع وخصائصه</h3>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <p class="text-gray-700 mb-4" id="section-1-p1">
                                يمكن تصنيف الإشعاع إلى نوعين رئيسيين: الإشعاع المؤين وغير المؤين. الإشعاع المؤين هو الذي يمتلك طاقة كافية لتحرير الإلكترونات من الذرات، مما يؤدي إلى تكوين أيونات. تشمل الأشعة السينية وأشعة جاما والجسيمات المشحونة مثل الإلكترونات والبروتونات أمثلة للإشعاع المؤين.
                            </p>
                            
                            <div class="bg-blue-50 rounded-lg p-4 border border-blue-100 mb-4">
                                <h4 class="font-semibold text-blue-800 mb-2" id="ionizing-title">الإشعاع المؤين</h4>
                                <ul class="list-disc list-inside text-gray-700 space-y-1">
                                    <li id="ionizing-1">الأشعة السينية (X-Rays)</li>
                                    <li id="ionizing-2">أشعة جاما (Gamma Rays)</li>
                                    <li id="ionizing-3">الجسيمات ألفا (Alpha Particles)</li>
                                    <li id="ionizing-4">الجسيمات بيتا (Beta Particles)</li>
                                    <li id="ionizing-5">النيوترونات (Neutrons)</li>
                                </ul>
                            </div>
                            
                            <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                                <h4 class="font-semibold text-green-800 mb-2" id="non-ionizing-title">الإشعاع غير المؤين</h4>
                                <ul class="list-disc list-inside text-gray-700 space-y-1">
                                    <li id="non-ionizing-1">الأشعة فوق البنفسجية (Ultraviolet)</li>
                                    <li id="non-ionizing-2">الضوء المرئي (Visible Light)</li>
                                    <li id="non-ionizing-3">الأشعة تحت الحمراء (Infrared)</li>
                                    <li id="non-ionizing-4">موجات الراديو (Radio Waves)</li>
                                    <li id="non-ionizing-5">الميكروويف (Microwaves)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="visualization-container h-80 flex items-center justify-center">
                            <div class="text-center p-4">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4" id="spectrum-title">الطيف الكهرومغناطيسي</h4>
                                <img src="../assets/images/electromagnetic-spectrum.png" alt="الطيف الكهرومغناطيسي" class="max-w-full h-auto mb-4" id="spectrum-img">
                                <p class="text-sm text-gray-600" id="spectrum-desc">
                                    الطيف الكهرومغناطيسي يوضح أنواع الإشعاع المختلفة وطاقاتها
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end">
                        <button id="next-to-section-2" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center">
                            <span id="next-button-text">التالي</span>
                            <i class="fas fa-arrow-left mr-2 rtl:ml-2 rtl:mr-0"></i>
                        </button>
                    </div>
                </div>
                
                <div id="section-2" class="mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4" id="section-2-title">تفاعل الفوتونات مع المادة</h3>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <p class="text-gray-700 mb-4" id="section-2-p1">
                                عند اختراق الفوتونات (مثل الأشعة السينية) للمادة، يمكن أن تحدث عدة أنواع من التفاعلات. تعتمد احتمالية حدوث كل نوع من التفاعلات على طاقة الفوتون والعدد الذري للمادة. التفاعلات الرئيسية للفوتونات في نطاق الطاقة المستخدم في التصوير الطبي هي:
                            </p>
                            
                            <div class="space-y-4">
                                <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                                    <h4 class="font-semibold text-blue-800 mb-2" id="photoelectric-title">التأثير الكهروضوئي</h4>
                                    <p class="text-gray-700 text-sm" id="photoelectric-desc">
                                        يحدث عندما يمتص إلكترون في الذرة كامل طاقة الفوتون، مما يؤدي إلى تحرير الإلكترون من الذرة. يهيمن هذا التأثير عند الطاقات المنخفضة وفي المواد ذات العدد الذري العالي.
                                    </p>
                                </div>
                                
                                <div class="bg-purple-50 rounded-lg p-4 border border-purple-100">
                                    <h4 class="font-semibold text-purple-800 mb-2" id="compton-title">تشتت كومبتون</h4>
                                    <p class="text-gray-700 text-sm" id="compton-desc">
                                        يحدث عندما يتفاعل الفوتون مع إلكترون حر أو مرتبط ضعيفاً، مما يؤدي إلى تشتت الفوتون بطاقة أقل وإطلاق إلكترون. يهيمن هذا التأثير في نطاق الطاقات المتوسطة.
                                    </p>
                                </div>
                                
                                <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                                    <h4 class="font-semibold text-green-800 mb-2" id="rayleigh-title">تشتت رايلي</h4>
                                    <p class="text-gray-700 text-sm" id="rayleigh-desc">
                                        تشتت مرن للفوتون دون فقدان للطاقة. يحدث هذا التفاعل مع الذرة ككل ويؤدي فقط إلى تغيير اتجاه الفوتون. يكون أكثر أهمية عند الطاقات المنخفضة.
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="visualization-container h-80 flex items-center justify-center p-4">
                            <div class="text-center">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4" id="interaction-title">محاكاة تفاعلات الفوتون</h4>
                                <div id="interaction-simulation" class="bg-gray-900 rounded-lg h-48 mb-4 relative">
                                    <!-- Interactive simulation will be added with JavaScript -->
                                </div>
                                <div class="flex justify-center space-x-2 rtl:space-x-reverse">
                                    <button id="photoelectric-btn" class="bg-blue-100 text-blue-800 px-3 py-1 rounded-md text-sm">
                                        التأثير الكهروضوئي
                                    </button>
                                    <button id="compton-btn" class="bg-purple-100 text-purple-800 px-3 py-1 rounded-md text-sm">
                                        تشتت كومبتون
                                    </button>
                                    <button id="rayleigh-btn" class="bg-green-100 text-green-800 px-3 py-1 rounded-md text-sm">
                                        تشتت رايلي
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-between">
                        <button id="back-to-section-1" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md flex items-center">
                            <i class="fas fa-arrow-right ml-2 rtl:mr-2 rtl:ml-0"></i>
                            <span id="back-button-text">السابق</span>
                        </button>
                        <button id="next-to-section-3" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center">
                            <span id="next-button-text-2">التالي</span>
                            <i class="fas fa-arrow-left mr-2 rtl:ml-2 rtl:mr-0"></i>
                        </button>
                    </div>
                </div>
                
                <div id="section-3" class="mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4" id="section-3-title">التوهين الإشعاعي والامتصاص</h3>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <p class="text-gray-700 mb-4" id="section-3-p1">
                                عند مرور حزمة من الفوتونات خلال مادة، تنخفض شدة الحزمة بسبب التفاعلات المختلفة. يُعرف هذا الانخفاض بالتوهين الإشعاعي، ويمكن وصفه رياضياً بقانون بير-لامبرت (Beer-Lambert Law).
                            </p>
                            
                            <div class="equation">
                                <span id="equation-label">قانون بير-لامبرت:</span><br>
                                <span class="text-lg">I = I₀ e<sup>-μx</sup></span>
                            </div>
                            <p class="text-sm text-gray-500 text-center mb-4" id="equation-desc">
                                حيث I هي شدة الإشعاع بعد المرور، I₀ هي الشدة الأولية، μ هو معامل التوهين الخطي، و x هو سمك المادة
                            </p>
                            
                            <p class="text-gray-700 mb-4" id="section-3-p2">
                                يعتمد معامل التوهين الخطي (μ) على:
                            </p>
                            <ul class="list-disc list-inside text-gray-700 space-y-1 mb-4">
                                <li id="attenuation-1">طاقة الفوتونات</li>
                                <li id="attenuation-2">العدد الذري للمادة</li>
                                <li id="attenuation-3">كثافة المادة</li>
                                <li id="attenuation-4">التركيب الكيميائي للمادة</li>
                            </ul>
                            
                            <p class="text-gray-700" id="section-3-p3">
                                يُستخدم مفهوم نصف قيمة الطبقة (HVL) لوصف خصائص التوهين للمواد، وهو سمك المادة اللازم لتقليل شدة الإشعاع إلى النصف.
                            </p>
                        </div>
                        
                        <div class="visualization-container h-80 p-4">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4 text-center" id="attenuation-title">محاكاة التوهين الإشعاعي</h4>
                            
                            <div class="mb-4">
                                <div class="flex justify-between mb-1">
                                    <label class="text-sm font-medium" id="material-label">المادة</label>
                                    <span class="text-sm text-gray-500" id="material-value">الماء (Water)</span>
                                </div>
                                <select id="material-select" class="w-full p-2 border border-gray-300 rounded-md bg-white">
                                    <option value="water">الماء (Water)</option>
                                    <option value="bone">العظم (Bone)</option>
                                    <option value="lead">الرصاص (Lead)</option>
                                    <option value="aluminum">الألومنيوم (Aluminum)</option>
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <div class="flex justify-between mb-1">
                                    <label class="text-sm font-medium" id="energy-label">طاقة الفوتون (keV)</label>
                                    <span class="text-sm text-gray-500"><span id="energy-value">80</span> keV</span>
                                </div>
                                <input 
                                    type="range" 
                                    id="energy-slider" 
                                    min="20" 
                                    max="150" 
                                    value="80" 
                                    class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                                >
                            </div>
                            
                            <div class="mb-4">
                                <div class="flex justify-between mb-1">
                                    <label class="text-sm font-medium" id="thickness-label">سمك المادة (cm)</label>
                                    <span class="text-sm text-gray-500"><span id="thickness-value">5</span> cm</span>
                                </div>
                                <input 
                                    type="range" 
                                    id="thickness-slider" 
                                    min="0" 
                                    max="20" 
                                    value="5" 
                                    class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                                >
                            </div>
                            
                            <div class="bg-white rounded-lg p-4 border border-gray-200">
                                <h5 class="text-sm font-semibold text-gray-800 mb-3" id="results-title">النتائج</h5>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-xs text-gray-500 mb-1" id="transmission-label">نسبة النفاذية</p>
                                        <p class="text-lg font-semibold text-blue-700" id="transmission-value">36.8%</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500 mb-1" id="hvl-label">نصف قيمة الطبقة (HVL)</p>
                                        <p class="text-lg font-semibold text-green-700" id="hvl-value">2.3 cm</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-between">
                        <button id="back-to-section-2" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md flex items-center">
                            <i class="fas fa-arrow-right ml-2 rtl:mr-2 rtl:ml-0"></i>
                            <span id="back-button-text-2">السابق</span>
                        </button>
                        <button id="next-to-section-4" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center">
                            <span id="next-button-text-3">التالي</span>
                            <i class="fas fa-arrow-left mr-2 rtl:ml-2 rtl:mr-0"></i>
                        </button>
                    </div>
                </div>
                
                <div id="section-4" class="mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4" id="section-4-title">تطبيقات في التصوير الطبي</h3>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <p class="text-gray-700 mb-4" id="section-4-p1">
                                تعتمد تقنيات التصوير الطبي المختلفة على مبادئ تفاعل الإشعاع مع الأنسجة البيولوجية. يعتمد التباين في صور الأشعة السينية على الاختلافات في معاملات التوهين للأنسجة المختلفة.
                            </p>
                            
                            <div class="bg-blue-50 rounded-lg p-4 border border-blue-100 mb-4">
                                <h4 class="font-semibold text-blue-800 mb-2" id="contrast-title">التباين في التصوير الإشعاعي</h4>
                                <p class="text-gray-700 text-sm" id="contrast-desc">
                                    يعتمد التباين في صور الأشعة السينية على الاختلافات في معاملات التوهين للأنسجة المختلفة:
                                </p>
                                <ul class="list-disc list-inside text-gray-700 space-y-1 mt-2">
                                    <li id="contrast-1">العظام: معامل توهين مرتفع (تظهر بيضاء)</li>
                                    <li id="contrast-2">الأنسجة الرخوة: معامل توهين متوسط (تظهر رمادية)</li>
                                    <li id="contrast-3">الهواء: معامل توهين منخفض (يظهر أسود)</li>
                                </ul>
                            </div>
                            
                            <div class="bg-purple-50 rounded-lg p-4 border border-purple-100">
                                <h4 class="font-semibold text-purple-800 mb-2" id="modalities-title">تقنيات التصوير المختلفة</h4>
                                <ul class="list-disc list-inside text-gray-700 space-y-1">
                                    <li id="modality-1">التصوير الشعاعي التقليدي (X-Ray Radiography)</li>
                                    <li id="modality-2">التصوير المقطعي المحوسب (CT Scan)</li>
                                    <li id="modality-3">التصوير بالرنين المغناطيسي (MRI)</li>
                                    <li id="modality-4">التصوير بالموجات فوق الصوتية (Ultrasound)</li>
                                    <li id="modality-5">التصوير بالطب النووي (Nuclear Medicine Imaging)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="bg-white rounded-lg p-4 border border-gray-200">
                                <h4 class="text-lg font-semibold text-gray-800 mb-3 text-center" id="comparison-title">مقارنة بين تقنيات التصوير</h4>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead>
                                            <tr>
                                                <th class="px-3 py-2 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" id="table-technique">التقنية</th>
                                                <th class="px-3 py-2 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" id="table-principle">المبدأ الفيزيائي</th>
                                                <th class="px-3 py-2 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" id="table-resolution">الدقة المكانية</th>
                                                <th class="px-3 py-2 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" id="table-dose">الجرعة الإشعاعية</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">X-Ray</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">توهين الأشعة السينية</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">متوسطة</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">منخفضة-متوسطة</td>
                                            </tr>
                                            <tr>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">CT</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">توهين الأشعة السينية</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">عالية</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">متوسطة-عالية</td>
                                            </tr>
                                            <tr>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">MRI</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">الرنين المغناطيسي النووي</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">عالية</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">لا يوجد</td>
                                            </tr>
                                            <tr>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">Ultrasound</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">انعكاس الموجات الصوتية</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">متوسطة</td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">لا يوجد</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="bg-white rounded-lg p-4 border border-gray-200">
                                <h4 class="text-sm font-semibold text-gray-800 mb-3" id="applications-title">تطبيقات عملية</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-xs text-gray-500 mb-1" id="diagnostic-label">التشخيص الطبي</p>
                                        <p class="text-sm text-gray-700" id="diagnostic-value">
                                            تحديد الكسور، الكشف عن الأورام، تقييم وظائف الأعضاء
                                        </p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500 mb-1" id="therapeutic-label">العلاج الإشعاعي</p>
                                        <p class="text-sm text-gray-700" id="therapeutic-value">
                                            علاج السرطان، تصوير موجه للتدخلات الجراحية
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-between">
                        <button id="back-to-section-3" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md flex items-center">
                            <i class="fas fa-arrow-right ml-2 rtl:mr-2 rtl:ml-0"></i>
                            <span id="back-button-text-3">السابق</span>
                        </button>
                        <button id="complete-module" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center">
                            <span id="complete-button-text">إنهاء الوحدة</span>
                            <i class="fas fa-check ml-2 rtl:mr-2 rtl:ml-0"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Resources -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center" id="resources-title">
                <i class="fas fa-book text-blue-500 ml-2 rtl:ml-2 rtl:mr-0"></i>
                مصادر إضافية
            </h2>
            
            <div class="grid md:grid-cols-3 gap-6">
                <a href="#" class="block bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-blue-300 transition-colors">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3 rtl:ml-3 rtl:mr-0">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800" id="resource-1-title">كتاب الفيزياء الإشعاعية</h3>
                    </div>
                    <p class="text-sm text-gray-600" id="resource-1-desc">
                        مرجع شامل في أساسيات الفيزياء الإشعاعية وتطبيقاتها الطبية
                    </p>
                </a>
                
                <a href="#" class="block bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-blue-300 transition-colors">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-3 rtl:ml-3 rtl:mr-0">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800" id="resource-2-title">محاضرات فيديو</h3>
                    </div>
                    <p class="text-sm text-gray-600" id="resource-2-desc">
                        سلسلة محاضرات مرئية حول تفاعل الإشعاع مع المادة
                    </p>
                </a>
                
                <a href="#" class="block bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-blue-300 transition-colors">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 rtl:ml-3 rtl:mr-0">
                            <i class="fas fa-flask"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800" id="resource-3-title">تجارب تفاعلية</h3>
                    </div>
                    <p class="text-sm text-gray-600" id="resource-3-desc">
                        مجموعة من التجارب الافتراضية لاستكشاف مفاهيم الفيزياء الإشعاعية
                    </p>
                </a>
            </div>
        </div>

        <!-- Related Chapters -->
        <div class="bg-white rounded-xl shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center" id="related-title">
                <i class="fas fa-link text-blue-500 ml-2 rtl:ml-2 rtl:mr-0"></i>
                فصول ذات صلة
            </h2>
            
            <div class="grid md:grid-cols-3 gap-6">
                <a href="../xray-filtration.html" class="block bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                    <div class="h-32 bg-gradient-to-r from-blue-500 to-blue-700 p-6 flex items-center justify-center">
                        <i class="fas fa-filter text-3xl text-white"></i>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800 mb-1" id="related-1-title">ترشيح الأشعة السينية</h3>
                        <p class="text-sm text-gray-600" id="related-1-desc">
                            تحسين جودة الحزمة وتقليل الجرعة الإشعاعية
                        </p>
                    </div>
                </a>
                
                <a href="../monte-carlo-simulation.html" class="block bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                    <div class="h-32 bg-gradient-to-r from-purple-500 to-purple-700 p-6 flex items-center justify-center">
                        <i class="fas fa-dice text-3xl text-white"></i>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800 mb-1" id="related-2-title">محاكاة مونت كارلو</h3>
                        <p class="text-sm text-gray-600" id="related-2-desc">
                            تقنيات متقدمة لمحاكاة تفاعلات الأشعة السينية
                        </p>
                    </div>
                </a>
                
                <a href="#" class="block bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                    <div class="h-32 bg-gradient-to-r from-green-500 to-green-700 p-6 flex items-center justify-center">
                        <i class="fas fa-radiation text-3xl text-white"></i>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-800 mb-1" id="related-3-title">الوقاية الإشعاعية</h3>
                        <p class="text-sm text-gray-600" id="related-3-desc">
                            مبادئ وممارسات الحماية من الإشعاع في البيئة الطبية
                        </p>
                    </div>
                </a>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4">
            <div class="md:flex md:justify-between">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center gap-3 mb-4">
                        <div class="p-2 bg-white/10 rounded-lg">
                            <i class="fas fa-atom text-white"></i>
                        </div>
                        <div>
                            <h2 class="text-lg font-bold" id="footer-title">الفيزياء الطبية</h2>
                        </div>
                    </div>
                    <p class="text-gray-400 text-sm max-w-md" id="footer-desc">
                        وحدة تعليمية متخصصة في أساسيات الفيزياء الطبية وتفاعل الإشعاع مع المادة، تجمع بين المحتوى العلمي العميق والتطبيقات التفاعلية.
                    </p>
                </div>
                
                <div>
                    <h3 class="text-sm font-semibold uppercase tracking-wider mb-4" id="quick-links-title">
                        روابط سريعة
                    </h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="../landing.html" class="text-gray-400 hover:text-white transition-colors" id="home-link">
                                الصفحة الرئيسية
                            </a>
                        </li>
                        <li>
                            <a href="../index.html" class="text-gray-400 hover:text-white transition-colors" id="interactive-link">
                                الواجهة التفاعلية
                            </a>
                        </li>
                        <li>
                            <a href="../xray-filtration.html" class="text-gray-400 hover:text-white transition-colors" id="filtration-link">
                                ترشيح الأشعة السينية
                            </a>
                        </li>
                        <li>
                            <a href="../monte-carlo-simulation.html" class="text-gray-400 hover:text-white transition-colors" id="monte-carlo-link">
                                محاكاة مونت كارلو
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-8 pt-8 border-t border-gray-700 flex flex-col md:flex-row md:justify-between md:items-center">
                <p class="text-gray-400 text-sm" id="copyright">
                    © 2024 جميع الحقوق محفوظة
                </p>
                <div class="mt-4 md:mt-0">
                    <p class="text-gray-400 text-sm mb-1">إعداد: د. محمد يعقوب إسماعيل</p>
                    <p class="text-gray-400 text-sm mb-1">أستاذ مشارك في الهندسة الطبية الحيوية</p>
                    <p class="text-gray-400 text-sm mb-1">جامعة السودان للعلوم والتكنولوجيا، كلية الهندسة، قسم الهندسة الطبية الحيوية</p>
                    <p class="text-gray-400 text-sm">
                        <a href="mailto:<EMAIL>" class="hover:text-white"><EMAIL></a> | 
                        <span dir="ltr">+249912867327, +966538076790</span>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Language Switcher Script -->
    <script src="../js/language-switcher.js"></script>
    
    <!-- Interactive Module Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation between sections
            const section1 = document.getElementById('section-1');
            const section2 = document.getElementById('section-2');
            const section3 = document.getElementById('section-3');
            const section4 = document.getElementById('section-4');
            
            const progressSteps = document.querySelectorAll('.progress-step');
            const progressLines = document.querySelectorAll('.progress-line');
            
            // Next buttons
            document.getElementById('next-to-section-2').addEventListener('click', function() {
                section1.classList.add('hidden');
                section2.classList.remove('hidden');
                updateProgress(2);
            });
            
            document.getElementById('next-to-section-3').addEventListener('click', function() {
                section2.classList.add('hidden');
                section3.classList.remove('hidden');
                updateProgress(3);
            });
            
            document.getElementById('next-to-section-4').addEventListener('click', function() {
                section3.classList.add('hidden');
                section4.classList.remove('hidden');
                updateProgress(4);
            });
            
            // Back buttons
            document.getElementById('back-to-section-1').addEventListener('click', function() {
                section2.classList.add('hidden');
                section1.classList.remove('hidden');
                updateProgress(1);
            });
            
            document.getElementById('back-to-section-2').addEventListener('click', function() {
                section3.classList.add('hidden');
                section2.classList.remove('hidden');
                updateProgress(2);
            });
            
            document.getElementById('back-to-section-3').addEventListener('click', function() {
                section4.classList.add('hidden');
                section3.classList.remove('hidden');
                updateProgress(3);
            });
            
            // Complete module button
            document.getElementById('complete-module').addEventListener('click', function() {
                alert(document.documentElement.lang === 'ar' ? 'تهانينا! لقد أكملت وحدة الفيزياء الطبية بنجاح.' : 'Congratulations! You have successfully completed the Medical Physics module.');
                updateProgress(4, true);
            });
            
            // Update progress tracker
            function updateProgress(step, completed = false) {
                // Update steps
                progressSteps.forEach((stepEl, index) => {
                    if (index + 1 < step) {
                        stepEl.classList.add('completed');
                        stepEl.classList.remove('active');
                    } else if (index + 1 === step) {
                        stepEl.classList.add('active');
                        stepEl.classList.remove('completed');
                    } else {
                        stepEl.classList.remove('active', 'completed');
                    }
                });
                
                // Update lines
                progressLines.forEach((lineEl, index) => {
                    if (index + 1 < step) {
                        lineEl.classList.add('active');
                    } else {
                        lineEl.classList.remove('active');
                    }
                });
                
                // If completed, mark all as completed
                if (completed) {
                    progressSteps.forEach(stepEl => {
                        stepEl.classList.add('completed');
                        stepEl.classList.remove('active');
                    });
                    
                    progressLines.forEach(lineEl => {
                        lineEl.classList.add('active');
                    });
                }
            }
            
            // Attenuation simulation
            const materialSelect = document.getElementById('material-select');
            const materialValue = document.getElementById('material-value');
            const energySlider = document.getElementById('energy-slider');
            const energyValue = document.getElementById('energy-value');
            const thicknessSlider = document.getElementById('thickness-slider');
            const thicknessValue = document.getElementById('thickness-value');
            const transmissionValue = document.getElementById('transmission-value');
            const hvlValue = document.getElementById('hvl-value');
            
            // Material properties (simplified for demonstration)
            const materials = {
                water: { name: 'الماء (Water)', density: 1.0, z: 7.4 },
                bone: { name: 'العظم (Bone)', density: 1.85, z: 13.8 },
                lead: { name: 'الرصاص (Lead)', density: 11.34, z: 82 },
                aluminum: { name: 'الألومنيوم (Aluminum)', density: 2.7, z: 13 }
            };
            
            // Event listeners for controls
            materialSelect.addEventListener('change', function() {
                materialValue.textContent = materials[this.value].name;
                updateAttenuationResults();
            });
            
            energySlider.addEventListener('input', function() {
                energyValue.textContent = this.value;
                updateAttenuationResults();
            });
            
            thicknessSlider.addEventListener('input', function() {
                thicknessValue.textContent = this.value;
                updateAttenuationResults();
            });
            
            // Calculate and update results
            function updateAttenuationResults() {
                const material = materials[materialSelect.value];
                const energy = parseInt(energySlider.value);
                const thickness = parseFloat(thicknessSlider.value);
                
                // Simplified calculation of linear attenuation coefficient
                // In reality, this would be based on complex physics and lookup tables
                const mu = calculateMu(material, energy);
                
                // Calculate transmission percentage using Beer-Lambert law
                const transmission = Math.exp(-mu * thickness) * 100;
                
                // Calculate HVL
                const hvl = Math.log(2) / mu;
                
                // Update display
                transmissionValue.textContent = transmission.toFixed(1) + '%';
                hvlValue.textContent = hvl.toFixed(1) + ' cm';
            }
            
            // Calculate linear attenuation coefficient (simplified model)
            function calculateMu(material, energy) {
                // This is a simplified model for educational purposes
                // Real calculations would use detailed physics models or lookup tables
                const baseAttenuation = 0.5 * material.density * Math.pow(material.z, 3) / Math.pow(energy, 2.5);
                return Math.max(0.01, Math.min(5, baseAttenuation));
            }
            
            // Initialize
            updateAttenuationResults();
            
            // Interaction simulation buttons
            document.getElementById('photoelectric-btn').addEventListener('click', function() {
                simulateInteraction('photoelectric');
            });
            
            document.getElementById('compton-btn').addEventListener('click', function() {
                simulateInteraction('compton');
            });
            
            document.getElementById('rayleigh-btn').addEventListener('click', function() {
                simulateInteraction('rayleigh');
            });
            
            // Simulate different interaction types
            function simulateInteraction(type) {
                const container = document.getElementById('interaction-simulation');
                
                // Clear previous simulation
                container.innerHTML = '';
                
                // Create photon
                const photon = document.createElement('div');
                photon.className = 'absolute w-3 h-3 bg-yellow-400 rounded-full';
                photon.style.top = '20px';
                photon.style.left = '50%';
                photon.style.transform = 'translateX(-50%)';
                container.appendChild(photon);
                
                // Create atom
                const atom = document.createElement('div');
                atom.className = 'absolute w-8 h-8 bg-gray-600 rounded-full';
                atom.style.top = '50%';
                atom.style.left = '50%';
                atom.style.transform = 'translate(-50%, -50%)';
                container.appendChild(atom);
                
                // Animate based on interaction type
                if (type === 'photoelectric') {
                    // Photoelectric effect - photon is absorbed, electron is ejected
                    setTimeout(() => {
                        // Move photon to atom
                        photon.style.transition = 'all 1s ease';
                        photon.style.top = '50%';
                        
                        // After photon reaches atom
                        setTimeout(() => {
                            // Photon disappears
                            photon.style.opacity = '0';
                            
                            // Electron is ejected
                            const electron = document.createElement('div');
                            electron.className = 'absolute w-2 h-2 bg-blue-400 rounded-full';
                            electron.style.top = '50%';
                            electron.style.left = '50%';
                            container.appendChild(electron);
                            
                            // Animate electron ejection
                            setTimeout(() => {
                                electron.style.transition = 'all 0.8s ease';
                                electron.style.top = '20px';
                                electron.style.left = '75%';
                            }, 100);
                        }, 1000);
                    }, 100);
                } else if (type === 'compton') {
                    // Compton scattering - photon changes direction, electron is ejected
                    setTimeout(() => {
                        // Move photon to atom
                        photon.style.transition = 'all 1s ease';
                        photon.style.top = '50%';
                        
                        // After photon reaches atom
                        setTimeout(() => {
                            // Photon changes direction
                            photon.style.transition = 'all 1s ease';
                            photon.style.top = '80%';
                            photon.style.left = '75%';
                            photon.style.backgroundColor = 'rgba(234, 179, 8, 0.7)'; // Lower energy
                            
                            // Electron is ejected
                            const electron = document.createElement('div');
                            electron.className = 'absolute w-2 h-2 bg-blue-400 rounded-full';
                            electron.style.top = '50%';
                            electron.style.left = '50%';
                            container.appendChild(electron);
                            
                            // Animate electron ejection
                            setTimeout(() => {
                                electron.style.transition = 'all 0.8s ease';
                                electron.style.top = '80%';
                                electron.style.left = '25%';
                            }, 100);
                        }, 1000);
                    }, 100);
                } else if (type === 'rayleigh') {
                    // Rayleigh scattering - photon only changes direction
                    setTimeout(() => {
                        // Move photon to atom
                        photon.style.transition = 'all 1s ease';
                        photon.style.top = '50%';
                        
                        // After photon reaches atom
                        setTimeout(() => {
                            // Photon changes direction
                            photon.style.transition = 'all 1s ease';
                            photon.style.top = '80%';
                            photon.style.left = '25%';
                            // No change in color as energy remains the same
                        }, 1000);
                    }, 100);
                }
            }
        });
    </script>
</body>
</html>