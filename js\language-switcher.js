/**
 * Language Switcher for Bilingual Medical Radiation Physics and Imaging Simulation
 * 
 * This script handles language switching between Arabic and English
 * for the static HTML pages of the application.
 */

// Language translations for common UI elements
const translations = {
    // Navigation
    'back-link': {
        ar: 'العودة للرئيسية',
        en: 'Back to Home'
    },
    'language-text': {
        ar: 'English',
        en: 'العربية'
    },
    
    // Common page elements
    'page-title': {
        'xray-filtration': {
            ar: 'ترشيح الأشعة السينية',
            en: 'X-Ray Filtration'
        },
        'monte-carlo': {
            ar: 'محاكاة مونت كارلو للأشعة السينية',
            en: 'Monte Carlo X-Ray Simulation'
        }
    },
    'page-subtitle': {
        'xray-filtration': {
            ar: 'تحسين جودة الحزمة وتقليل الجرعة الإشعاعية',
            en: 'Beam Quality Improvement and Dose Reduction'
        },
        'monte-carlo': {
            ar: 'تقنيات متقدمة لمحاكاة تفاعلات الأشعة السينية مع المادة',
            en: 'Advanced Techniques for X-Ray Interaction Simulation'
        }
    },
    
    // Tags
    'physics-tag': {
        ar: 'فيزياء الأشعة',
        en: 'Radiation Physics'
    },
    'protection-tag': {
        ar: 'الوقاية الإشعاعية',
        en: 'Radiation Protection'
    },
    'simulation-tag': {
        ar: 'المحاكاة الحاسوبية',
        en: 'Computational Simulation'
    },
    
    // Interactive elements
    'interactive-title': {
        ar: 'محتوى تفاعلي',
        en: 'Interactive Content'
    },
    'interactive-desc': {
        'xray-filtration': {
            ar: 'تجربة تأثير الترشيح على حزمة الأشعة السينية',
            en: 'Experience the effect of filtration on X-ray beam'
        },
        'monte-carlo': {
            ar: 'تجربة محاكاة مونت كارلو لتفاعلات الأشعة السينية',
            en: 'Experience Monte Carlo simulation of X-ray interactions'
        }
    }
};

// Initialize language based on HTML lang attribute
function initializeLanguage() {
    const currentLang = document.documentElement.lang;
    updateUILanguage(currentLang);
}

// Update UI elements based on selected language
function updateUILanguage(lang) {
    // Get the current page type
    const pageType = window.location.pathname.includes('xray-filtration') 
        ? 'xray-filtration' 
        : 'monte-carlo';
    
    // Update common elements
    for (const [elementId, translations] of Object.entries(translations)) {
        const element = document.getElementById(elementId);
        if (element) {
            // Handle nested translations
            if (typeof translations[lang] === 'object') {
                element.textContent = translations[lang][pageType] || '';
            } else {
                element.textContent = translations[lang] || '';
            }
        }
    }
    
    // Update document direction
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    
    // Update page title
    if (pageType === 'xray-filtration') {
        document.title = lang === 'ar' 
            ? 'ترشيح الأشعة السينية | X-Ray Filtration' 
            : 'X-Ray Filtration | ترشيح الأشعة السينية';
    } else {
        document.title = lang === 'ar' 
            ? 'محاكاة مونت كارلو للأشعة السينية | Monte Carlo X-Ray Simulation' 
            : 'Monte Carlo X-Ray Simulation | محاكاة مونت كارلو للأشعة السينية';
    }
}

// Toggle between Arabic and English
function toggleLanguage() {
    const html = document.documentElement;
    const newLang = html.lang === 'ar' ? 'en' : 'ar';
    
    // Update HTML lang attribute
    html.lang = newLang;
    
    // Update UI elements
    updateUILanguage(newLang);
}

// Add event listener to language toggle button
document.addEventListener('DOMContentLoaded', function() {
    const languageToggle = document.getElementById('language-toggle');
    if (languageToggle) {
        languageToggle.addEventListener('click', toggleLanguage);
    }
    
    // Initialize language
    initializeLanguage();
});