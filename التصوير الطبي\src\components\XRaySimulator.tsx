import { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, RotateCcw, Settings, Zap, Thermometer } from 'lucide-react';

const XRaySimulator = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [voltage, setVoltage] = useState([80]); // kV
  const [current, setCurrent] = useState([200]); // mA
  const [time, setTime] = useState([100]); // ms
  const [anodeTemp, setAnodeTemp] = useState(20); // °C
  const [xrayIntensity, setXrayIntensity] = useState(0);

  useEffect(() => {
    if (isRunning) {
      const interval = setInterval(() => {
        // Simulate temperature increase
        setAnodeTemp((prev) => Math.min(prev + 10, 1200));
        // Calculate X-ray intensity based on parameters
        const intensity = voltage[0] * current[0] * time[0] / 10000;
        setXrayIntensity(Math.min(intensity, 100));
      }, 100);

      const timeout = setTimeout(() => {
        setIsRunning(false);
      }, time[0] * 10);

      return () => {
        clearInterval(interval);
        clearTimeout(timeout);
      };
    } else {
      // Cool down
      const coolInterval = setInterval(() => {
        setAnodeTemp((prev) => Math.max(prev - 5, 20));
        setXrayIntensity((prev) => Math.max(prev - 2, 0));
      }, 100);

      return () => clearInterval(coolInterval);
    }
  }, [isRunning, time, voltage, current]);

  const startSimulation = () => {
    setIsRunning(true);
  };

  const stopSimulation = () => {
    setIsRunning(false);
  };

  const resetSimulation = () => {
    setIsRunning(false);
    setAnodeTemp(20);
    setXrayIntensity(0);
  };

  const getTemperatureColor = (temp: number) => {
    if (temp < 100) return 'text-blue-600';
    if (temp < 500) return 'text-yellow-600';
    if (temp < 800) return 'text-orange-600';
    return 'text-red-600';
  };

  const getIntensityColor = (intensity: number) => {
    if (intensity < 25) return 'bg-green-500';
    if (intensity < 50) return 'bg-yellow-500';
    if (intensity < 75) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <div className="grid lg:grid-cols-2 gap-8" data-id="2f4bg8fc3" data-path="src/components/XRaySimulator.tsx">
      {/* Control Panel */}
      <motion.div
        initial={{ opacity: 0, x: -30 }}
        animate={{ opacity: 1, x: 0 }}
        className="space-y-6" data-id="zz8ry296q" data-path="src/components/XRaySimulator.tsx">

        <Card data-id="gllynvckc" data-path="src/components/XRaySimulator.tsx">
          <CardHeader data-id="3bk3321kv" data-path="src/components/XRaySimulator.tsx">
            <CardTitle className="flex items-center gap-2" data-id="0dgbqqsye" data-path="src/components/XRaySimulator.tsx">
              <Settings className="w-5 h-5 text-blue-600" data-id="iq717b7p4" data-path="src/components/XRaySimulator.tsx" />
              لوحة التحكم
            </CardTitle>
            <CardDescription data-id="d9b0v1feb" data-path="src/components/XRaySimulator.tsx">
              اضبط معاملات التشغيل لمحاكاة إنتاج الأشعة السينية
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6" data-id="jzd5l0ekw" data-path="src/components/XRaySimulator.tsx">
            {/* Voltage Control */}
            <div className="space-y-3" data-id="u2eo6ukd7" data-path="src/components/XRaySimulator.tsx">
              <div className="flex justify-between items-center" data-id="26x2813a0" data-path="src/components/XRaySimulator.tsx">
                <label className="text-sm font-medium" data-id="5hdbp5sk8" data-path="src/components/XRaySimulator.tsx">الجهد (kV)</label>
                <Badge variant="outline" data-id="uz6h2xp82" data-path="src/components/XRaySimulator.tsx">{voltage[0]} kV</Badge>
              </div>
              <Slider
                value={voltage}
                onValueChange={setVoltage}
                max={150}
                min={40}
                step={5}
                disabled={isRunning}
                className="w-full" data-id="f4y32sj54" data-path="src/components/XRaySimulator.tsx" />

              <p className="text-xs text-gray-500" data-id="j3i9sbu4c" data-path="src/components/XRaySimulator.tsx">
                يؤثر على طاقة الأشعة السينية وقدرتها على الاختراق
              </p>
            </div>

            {/* Current Control */}
            <div className="space-y-3" data-id="5qtcv0sxy" data-path="src/components/XRaySimulator.tsx">
              <div className="flex justify-between items-center" data-id="u9ij9o762" data-path="src/components/XRaySimulator.tsx">
                <label className="text-sm font-medium" data-id="frswaa6hs" data-path="src/components/XRaySimulator.tsx">التيار (mA)</label>
                <Badge variant="outline" data-id="wao6d1dsw" data-path="src/components/XRaySimulator.tsx">{current[0]} mA</Badge>
              </div>
              <Slider
                value={current}
                onValueChange={setCurrent}
                max={500}
                min={50}
                step={25}
                disabled={isRunning}
                className="w-full" data-id="50x2e9crn" data-path="src/components/XRaySimulator.tsx" />

              <p className="text-xs text-gray-500" data-id="29yo6ax0a" data-path="src/components/XRaySimulator.tsx">
                يحدد كمية الأشعة السينية المنتجة
              </p>
            </div>

            {/* Time Control */}
            <div className="space-y-3" data-id="f693jip7o" data-path="src/components/XRaySimulator.tsx">
              <div className="flex justify-between items-center" data-id="ghc43wjzl" data-path="src/components/XRaySimulator.tsx">
                <label className="text-sm font-medium" data-id="j8ccj8yf2" data-path="src/components/XRaySimulator.tsx">زمن التعرض (ms)</label>
                <Badge variant="outline" data-id="8kuvezyax" data-path="src/components/XRaySimulator.tsx">{time[0]} ms</Badge>
              </div>
              <Slider
                value={time}
                onValueChange={setTime}
                max={1000}
                min={10}
                step={10}
                disabled={isRunning}
                className="w-full" data-id="yrnitv9et" data-path="src/components/XRaySimulator.tsx" />

              <p className="text-xs text-gray-500" data-id="j5n1puvid" data-path="src/components/XRaySimulator.tsx">
                مدة إنتاج الأشعة السينية
              </p>
            </div>

            {/* Control Buttons */}
            <div className="flex gap-3 pt-4" data-id="557xm309w" data-path="src/components/XRaySimulator.tsx">
              <Button
                onClick={startSimulation}
                disabled={isRunning}
                className="flex-1" data-id="pn4hz8vgt" data-path="src/components/XRaySimulator.tsx">

                <Play className="w-4 h-4 ml-2" data-id="822y5ktos" data-path="src/components/XRaySimulator.tsx" />
                تشغيل
              </Button>
              <Button
                onClick={stopSimulation}
                disabled={!isRunning}
                variant="outline"
                className="flex-1" data-id="2q0q78j8s" data-path="src/components/XRaySimulator.tsx">

                <Pause className="w-4 h-4 ml-2" data-id="ivg9wpxuq" data-path="src/components/XRaySimulator.tsx" />
                إيقاف
              </Button>
              <Button
                onClick={resetSimulation}
                variant="outline"
                size="icon" data-id="x44h269rp" data-path="src/components/XRaySimulator.tsx">

                <RotateCcw className="w-4 h-4" data-id="eftn902cs" data-path="src/components/XRaySimulator.tsx" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Calculated Values */}
        <Card data-id="62muijczx" data-path="src/components/XRaySimulator.tsx">
          <CardHeader data-id="wsexrw07i" data-path="src/components/XRaySimulator.tsx">
            <CardTitle className="text-lg" data-id="q7ghkn3b0" data-path="src/components/XRaySimulator.tsx">القيم المحسوبة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4" data-id="c86itv8s1" data-path="src/components/XRaySimulator.tsx">
            <div className="grid grid-cols-2 gap-4" data-id="ujw86uhby" data-path="src/components/XRaySimulator.tsx">
              <div className="text-center p-3 bg-gray-50 rounded-lg" data-id="ncm26hj65" data-path="src/components/XRaySimulator.tsx">
                <div className="text-sm text-gray-600" data-id="la0bof65y" data-path="src/components/XRaySimulator.tsx">القدرة</div>
                <div className="text-xl font-bold" data-id="xz11oqbyj" data-path="src/components/XRaySimulator.tsx">
                  {(voltage[0] * current[0] / 1000).toFixed(1)} kW
                </div>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg" data-id="ik41dlaj4" data-path="src/components/XRaySimulator.tsx">
                <div className="text-sm text-gray-600" data-id="38vbpmcu7" data-path="src/components/XRaySimulator.tsx">الطاقة</div>
                <div className="text-xl font-bold" data-id="x4z5i940p" data-path="src/components/XRaySimulator.tsx">
                  {(voltage[0] * current[0] * time[0] / 1000000).toFixed(2)} kJ
                </div>
              </div>
            </div>
            <div className="text-xs text-gray-500 text-center" data-id="7zp2ac5rh" data-path="src/components/XRaySimulator.tsx">
              الطاقة المنتجة تتحول إلى أشعة سينية (1%) وحرارة (99%)
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Visualization */}
      <motion.div
        initial={{ opacity: 0, x: 30 }}
        animate={{ opacity: 1, x: 0 }}
        className="space-y-6" data-id="omvk2ijmk" data-path="src/components/XRaySimulator.tsx">

        {/* Visual Display */}
        <Card data-id="8zgzk4ybt" data-path="src/components/XRaySimulator.tsx">
          <CardHeader data-id="f26zqjqwg" data-path="src/components/XRaySimulator.tsx">
            <CardTitle className="flex items-center gap-2" data-id="b7edojkv6" data-path="src/components/XRaySimulator.tsx">
              <Zap className="w-5 h-5 text-yellow-600" data-id="ptj7u9vy3" data-path="src/components/XRaySimulator.tsx" />
              المحاكاة المرئية
            </CardTitle>
          </CardHeader>
          <CardContent data-id="wov32ffyu" data-path="src/components/XRaySimulator.tsx">
            <div className="relative bg-gray-900 rounded-lg p-6 min-h-[300px] overflow-hidden" data-id="j09c659dh" data-path="src/components/XRaySimulator.tsx">
              {/* Tube Outline */}
              <svg width="100%" height="100%" className="absolute inset-0" data-id="dbv59rlvk" data-path="src/components/XRaySimulator.tsx">
                {/* Cathode */}
                <rect x="20" y="120" width="30" height="30" fill="#ef4444" rx="5" data-id="0uo306236" data-path="src/components/XRaySimulator.tsx" />
                <text x="35" y="110" textAnchor="middle" className="text-xs fill-white" data-id="z5zo9fpnm" data-path="src/components/XRaySimulator.tsx">
                  الكاثود
                </text>

                {/* Anode */}
                <circle cx="280" cy="135" r="20" fill="#3b82f6" data-id="wujysghnl" data-path="src/components/XRaySimulator.tsx" />
                <text x="280" y="170" textAnchor="middle" className="text-xs fill-white" data-id="fxfhpwset" data-path="src/components/XRaySimulator.tsx">
                  الأنود
                </text>

                {/* Electron Beam Animation */}
                {isRunning &&
                <motion.g data-id="rtcmnpnw5" data-path="src/components/XRaySimulator.tsx">
                    {[...Array(5)].map((_, i) =>
                  <motion.circle
                    key={i}
                    r="2"
                    fill="#fbbf24"
                    initial={{ cx: 50, cy: 135 }}
                    animate={{ cx: 260, cy: 135 }}
                    transition={{
                      duration: 0.5,
                      repeat: Infinity,
                      delay: i * 0.1
                    }} data-id="ft4q9p178" data-path="src/components/XRaySimulator.tsx" />

                  )}
                  </motion.g>
                }

                {/* X-ray Beam */}
                {xrayIntensity > 0 &&
                <motion.g data-id="3hepncyba" data-path="src/components/XRaySimulator.tsx">
                    {[...Array(3)].map((_, i) =>
                  <motion.line
                    key={i}
                    x1="280"
                    y1="135"
                    x2="350"
                    y2={115 + i * 20}
                    stroke="#10b981"
                    strokeWidth="2"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{
                      duration: 0.3,
                      repeat: Infinity,
                      delay: i * 0.1
                    }} data-id="gh0k1tmr7" data-path="src/components/XRaySimulator.tsx" />

                  )}
                  </motion.g>
                }
              </svg>

              {/* Status Indicator */}
              <div className="absolute top-4 right-4" data-id="24vgg1qyz" data-path="src/components/XRaySimulator.tsx">
                <Badge
                  variant={isRunning ? "default" : "secondary"}
                  className={isRunning ? "bg-green-500" : ""} data-id="kvyzd4r1t" data-path="src/components/XRaySimulator.tsx">

                  {isRunning ? "يعمل" : "متوقف"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status Monitors */}
        <div className="grid grid-cols-2 gap-4" data-id="lt9q1vmgg" data-path="src/components/XRaySimulator.tsx">
          <Card data-id="1bvp6z30k" data-path="src/components/XRaySimulator.tsx">
            <CardHeader className="pb-3" data-id="qeumbfxtk" data-path="src/components/XRaySimulator.tsx">
              <CardTitle className="text-base flex items-center gap-2" data-id="q6035pfkl" data-path="src/components/XRaySimulator.tsx">
                <Thermometer className="w-4 h-4" data-id="i7afr8cro" data-path="src/components/XRaySimulator.tsx" />
                حرارة الأنود
              </CardTitle>
            </CardHeader>
            <CardContent data-id="53gbzf8kd" data-path="src/components/XRaySimulator.tsx">
              <div className="text-center" data-id="25ggcqsf7" data-path="src/components/XRaySimulator.tsx">
                <div className={`text-2xl font-bold ${getTemperatureColor(anodeTemp)}`} data-id="5ihtr7aoo" data-path="src/components/XRaySimulator.tsx">
                  {Math.round(anodeTemp)}°C
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2" data-id="dkvag8dvl" data-path="src/components/XRaySimulator.tsx">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                    anodeTemp > 800 ? 'bg-red-500' :
                    anodeTemp > 400 ? 'bg-orange-500' : 'bg-blue-500'}`
                    }
                    style={{ width: `${Math.min(anodeTemp / 1200 * 100, 100)}%` }} data-id="gem5yjp0r" data-path="src/components/XRaySimulator.tsx">
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1" data-id="r79akqnkl" data-path="src/components/XRaySimulator.tsx">
                  الحد الأقصى: 1200°C
                </div>
              </div>
            </CardContent>
          </Card>

          <Card data-id="pqrxaoc37" data-path="src/components/XRaySimulator.tsx">
            <CardHeader className="pb-3" data-id="2f01gaqdf" data-path="src/components/XRaySimulator.tsx">
              <CardTitle className="text-base flex items-center gap-2" data-id="m55fnx8dh" data-path="src/components/XRaySimulator.tsx">
                <Zap className="w-4 h-4" data-id="9eyq8hkfh" data-path="src/components/XRaySimulator.tsx" />
                شدة الأشعة
              </CardTitle>
            </CardHeader>
            <CardContent data-id="gzzrhynba" data-path="src/components/XRaySimulator.tsx">
              <div className="text-center" data-id="oc0blsddk" data-path="src/components/XRaySimulator.tsx">
                <div className="text-2xl font-bold text-gray-900" data-id="eyh11u88d" data-path="src/components/XRaySimulator.tsx">
                  {Math.round(xrayIntensity)}%
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2" data-id="n1teg15xf" data-path="src/components/XRaySimulator.tsx">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${getIntensityColor(xrayIntensity)}`}
                    style={{ width: `${xrayIntensity}%` }} data-id="v92prtyhb" data-path="src/components/XRaySimulator.tsx">
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1" data-id="c59jzwid0" data-path="src/components/XRaySimulator.tsx">
                  شدة الإنتاج النسبية
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>
    </div>);

};

export default XRaySimulator;