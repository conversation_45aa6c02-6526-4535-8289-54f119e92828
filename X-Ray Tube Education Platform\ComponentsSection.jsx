import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Button } from '@/components/ui/button.jsx'
import { useState } from 'react'
import xRayTube from '../assets/x_ray_tube.png'
import blockDiagram from '../assets/x_ray_machine_block_diagram.png'

const ComponentsSection = () => {
  const [selectedComponent, setSelectedComponent] = useState('tube')

  const components = {
    tube: {
      title: 'أنبوب الأشعة السينية',
      description: 'المكون الأساسي المسؤول عن توليد الأشعة السينية',
      image: xRayTube,
      details: [
        'الكاثود: مصدر الإلكترونات المسؤول عن انبعاث الإلكترونات عند التسخين',
        'الأنود: الهدف المصنوع من التنغستن الذي تصطدم به الإلكترونات لإنتاج الأشعة السينية',
        'الحاوية الزجاجية: بيئة مفرغة من الهواء تسمح بحركة الإلكترونات بحرية',
        'نظام التبريد: يمنع ارتفاع درجة الحرارة المفرط في الأنود'
      ]
    },
    detector: {
      title: 'الكاشف الرقمي',
      description: 'يحول الأشعة السينية إلى إشارات رقمية',
      image: blockDiagram,
      details: [
        'يستقبل الأشعة السينية التي تمر عبر جسم المريض',
        'يحول الإشعاع إلى إشارات كهربائية',
        'يوفر صورًا فورية عالية الجودة',
        'يقلل من جرعة الإشعاع المطلوبة مقارنة بالأفلام التقليدية'
      ]
    },
    control: {
      title: 'لوحة التحكم',
      description: 'تتحكم في معاملات التصوير والتشغيل',
      image: blockDiagram,
      details: [
        'ضبط الجهد الكهربائي (kVp) للتحكم في جودة الأشعة',
        'تحديد شدة التيار (mA) للتحكم في كمية الأشعة',
        'ضبط وقت التعرض للحصول على الصورة المطلوبة',
        'مراقبة حالة الجهاز وأنظمة الأمان'
      ]
    },
    generator: {
      title: 'مولد الجهد العالي',
      description: 'يوفر الطاقة الكهربائية اللازمة لتشغيل أنبوب الأشعة السينية',
      image: blockDiagram,
      details: [
        'يرفع الجهد من 220 فولت إلى عشرات الآلاف من الفولتات',
        'يوفر تيارًا مستقرًا لضمان جودة الصورة',
        'يحتوي على دوائر حماية لمنع التلف',
        'يتضمن أنظمة تبريد للحفاظ على درجة حرارة مناسبة'
      ]
    }
  }

  return (
    <section id="components" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">مكونات جهاز الأشعة السينية</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            تعرف على المكونات الأساسية لجهاز الأشعة السينية وكيفية عمل كل منها في إنتاج صور طبية عالية الجودة
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Component Selection */}
          <div className="lg:col-span-1">
            <div className="space-y-4">
              {Object.entries(components).map(([key, component]) => (
                <Button
                  key={key}
                  variant={selectedComponent === key ? "default" : "outline"}
                  className={`w-full text-right justify-start h-auto p-4 ${
                    selectedComponent === key ? 'bg-blue-600 text-white' : 'hover:bg-blue-50'
                  }`}
                  onClick={() => setSelectedComponent(key)}
                >
                  <div>
                    <div className="font-semibold">{component.title}</div>
                    <div className="text-sm opacity-80">{component.description}</div>
                  </div>
                </Button>
              ))}
            </div>
          </div>

          {/* Component Details */}
          <div className="lg:col-span-2">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-2xl text-blue-600">
                  {components[selectedComponent].title}
                </CardTitle>
                <CardDescription className="text-lg">
                  {components[selectedComponent].description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <img
                      src={components[selectedComponent].image}
                      alt={components[selectedComponent].title}
                      className="w-full rounded-lg shadow-md"
                    />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold mb-4 text-gray-800">التفاصيل التقنية:</h4>
                    <ul className="space-y-3">
                      {components[selectedComponent].details.map((detail, index) => (
                        <li key={index} className="flex items-start">
                          <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          <span className="text-gray-700">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ComponentsSection

