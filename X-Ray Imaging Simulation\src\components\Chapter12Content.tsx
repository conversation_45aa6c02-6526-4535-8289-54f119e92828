import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Cpu, Lightbulb, Zap, Activity, Image, Grid, PlayCircle, Calculator } from 'lucide-react';
import { motion } from 'motion/react';

const Chapter12Content = () => {
  const [activeSection, setActiveSection] = useState('introduction');
  const [simulationMode, setSimulationMode] = useState(false);
  const [simulationProgress, setSimulationProgress] = useState(0);

  const sections = [
    {
      id: 'introduction',
      title: 'مقدمة - نمذجة استجابة الكاشف',
      icon: <Cpu className="w-5 h-5" />,
      content: {
        theory: `
          محاكاة استجابة الكاشف وتكوين الصورة تتطلب نمذجة دقيقة لجميع العمليات الفيزيائية:
          
          **المراحل الأساسية للنمذجة:**
          1. **نمذجة ترسب الطاقة**: كيف تتفاعل الأشعة السينية مع مادة الكاشف
          2. **محاكاة انتشار الضوء**: انتشار الفوتونات الضوئية في المواد الومضة
          3. **نمذجة جمع الشحنات**: تحويل الإشارة الضوئية إلى إشارة كهربائية
          4. **تحليل الأنظمة الخطية**: وصف استجابة النظام باستخدام نظرية الأنظمة الخطية
          5. **تكوين صورة الإسقاط**: محاكاة تكوين الصورة الشعاعية النهائية
          
          **أهمية المحاكاة:**
          - تحسين تصميم أجهزة الكشف الجديدة
          - فهم تأثير المعلمات المختلفة على جودة الصورة
          - تطوير تقنيات معالجة الصور المتقدمة
          - التنبؤ بأداء النظام قبل التصنيع
        `,
        vrContent: 'نموذج ثلاثي الأبعاد شامل يوضح جميع مراحل تكوين الصورة',
        keyEquations: [
          'E_deposited = ∫ μ(E,x) × Φ(E,x) dE dx',
          'Light_output = η × E_deposited',
          'Signal = ∫ QE(λ) × Light(λ) dλ'
        ]
      }
    },
    {
      id: 'energy-deposition',
      title: '12.1 نمذجة ترسب الطاقة',
      icon: <Zap className="w-5 h-5" />,
      content: {
        theory: `
          **نمذجة ترسب الطاقة في مواد الكاشف:**
          
          ترسب الطاقة هو الخطوة الأولى في عملية الكشف، حيث تتفاعل الأشعة السينية مع ذرات المادة.
          
          **التفاعلات الأساسية:**
          
          **1. التأثير الكهروضوئي (Photoelectric Effect):**
          - يحدث عند الطاقات المنخفضة (< 100 keV)
          - الفوتون يُمتص كلياً ويطلق إلكترون ضوئي
          - طاقة الإلكترون: E_e = E_photon - B_k
          
          **2. تشتت كومبتون (Compton Scattering):**
          - يحدث عند الطاقات المتوسطة (100-1000 keV)
          - الفوتون يفقد جزء من طاقته للإلكترون
          - طاقة الفوتون المتشتت: E' = E / (1 + E/m_e c² (1-cos θ))
          
          **3. إنتاج الأزواج (Pair Production):**
          - يحدث عند الطاقات العالية (> 1.022 MeV)
          - الفوتون يتحول إلى زوج إلكترون-بوزيترون
          
          **نمذجة Monte Carlo:**
          
          محاكاة Monte Carlo تتبع كل فوتون فردياً:
          
          ```
          for each photon:
              while photon.energy > cutoff:
                  distance = sample_interaction_distance()
                  interaction_type = sample_interaction_type()
                  deposit_energy(interaction_site, energy_transfer)
                  update_photon_direction_and_energy()
          ```
          
          **معادلات أساسية:**
          
          معامل التوهين الكلي:
          μ_total = μ_photoelectric + μ_compton + μ_pair
          
          احتمالية التفاعل:
          P(x) = 1 - exp(-μ × x)
          
          توزيع ترسب الطاقة:
          dE/dx = ρ × Σ σ_i × N_i × E_i
        `,
        vrContent: 'محاكاة ثلاثية الأبعاد لمسارات الفوتونات وترسب الطاقة في الكاشف',
        materialProperties: {
          'CsI': { density: '4.51 g/cm³', Z_eff: '54', mu_photoelectric: 'عالي عند 60 keV' },
          'a-Se': { density: '4.28 g/cm³', Z_eff: '34', mu_photoelectric: 'متوسط عند 60 keV' },
          'Gd2O2S': { density: '7.32 g/cm³', Z_eff: '61', mu_photoelectric: 'عالي جداً عند 60 keV' }
        }
      }
    },
    {
      id: 'light-transport',
      title: '12.2 محاكاة انتشار ضوء الومض',
      icon: <Lightbulb className="w-5 h-5" />,
      content: {
        theory: `
          **محاكاة انتشار ضوء الومض والتداخل البصري:**
          
          بعد ترسب الطاقة، تحتاج المواد الومضة إلى تحويل هذه الطاقة إلى ضوء مرئي.
          
          **عملية الومض (Scintillation Process):**
          
          **1. الإثارة الأولية:**
          - الإلكترونات المثارة بالأشعة السينية تنقل طاقتها للشبكة البلورية
          - تكوين أزواج إلكترون-ثقب أو إثارة مراكز الإضاءة
          
          **2. الإصدار الضوئي:**
          - الإلكترونات المثارة تعود لحالتها الأساسية مصدرة فوتونات ضوئية
          - طيف الإصدار يعتمد على نوع المادة الومضة
          
          **3. انتشار الضوء:**
          الضوء المنبعث ينتشر في المادة ويخضع لعدة عمليات:
          
          **الامتصاص الذاتي (Self-absorption):**
          I(x) = I_0 × exp(-α × x)
          
          حيث α معامل الامتصاص للمادة الومضة.
          
          **التشتت (Scattering):**
          - تشتت رايلي: يعتمد على λ^-4
          - تشتت مي: يحدث مع الجسيمات الكبيرة
          
          **الانعكاس والانكسار:**
          - عند الحدود بين المواد المختلفة
          - قانون سنيل: n₁ sin θ₁ = n₂ sin θ₂
          
          **نمذجة Monte Carlo للانتشار الضوئي:**
          
          ```
          for each light_photon:
              while photon.active:
                  step_size = sample_step_size()
                  if absorption_event():
                      photon.active = false
                  elif scattering_event():
                      new_direction = sample_scattering_angle()
                      update_photon_direction(new_direction)
                  update_photon_position(step_size)
          ```
          
          **التداخل البصري (Optical Crosstalk):**
          
          الضوء المنتشر يمكن أن يصل إلى بكسل مجاور، مما يسبب:
          - تدهور الدقة المكانية
          - انخفاض التباين
          - تشويش في الصورة
          
          معامل التداخل البصري:
          OCF = (Signal_adjacent)/(Signal_central)
          
          **تحسين التصميم:**
          - استخدام عاكسات بصرية بين البكسل
          - تحسين سماكة طبقة الومض
          - اختيار مواد ذات معامل انكسار مناسب
        `,
        vrContent: 'تصور ثلاثي الأبعاد لانتشار الضوء داخل المادة الومضة مع إظهار التداخل البصري',
        scintillatorProperties: {
          'CsI:Tl': { 
            lightYield: '54,000 photons/MeV', 
            peakWavelength: '550 nm', 
            decayTime: '1 μs',
            refractiveIndex: '1.79'
          },
          'Gd2O2S:Tb': { 
            lightYield: '60,000 photons/MeV', 
            peakWavelength: '545 nm', 
            decayTime: '1 ms',
            refractiveIndex: '1.95'
          }
        }
      }
    },
    {
      id: 'charge-collection',
      title: '12.3 نمذجة جمع الشحنات',
      icon: <Activity className="w-5 h-5" />,
      content: {
        theory: `
          **نمذجة جمع الشحنات والضوضاء الإلكترونية:**
          
          المرحلة الأخيرة في عملية الكشف هي تحويل الإشارة الضوئية إلى إشارة كهربائية.
          
          **عملية جمع الشحنات:**
          
          **1. التحويل الضوئي-الكهربائي:**
          في أجهزة الكشف الرقمية، يتم تحويل الضوء إلى شحنات كهربائية:
          
          **للكاشفات غير المباشرة:**
          - الثنائيات الضوئية تحول الفوتونات إلى أزواج إلكترون-ثقب
          - كفاءة التحويل: QE(λ) = η_quantum × (1 - R(λ)) × (1 - exp(-α(λ)d))
          
          **للكاشفات المباشرة:**
          - الأشعة السينية تولد الشحنات مباشرة
          - عدد أزواج الشحنة: N = E_photon / W_pair
          
          **2. انجراف الشحنات:**
          الشحنات المتولدة تنجرف تحت تأثير المجال الكهربائي:
          
          سرعة الانجراف: v_drift = μ × E_field
          
          حيث μ هي حركية الحاملات (electron/hole mobility).
          
          **3. جمع الشحنات في البكسل:**
          الشحنات تُجمع في مكثفات البكسل:
          
          الجهد المتكون: V = Q / C_pixel
          
          **مصادر الضوضاء الإلكترونية:**
          
          **1. الضوضاء الكمية (Shot Noise):**
          σ_shot² = N_electrons
          
          **2. ضوضاء الحرارة (Thermal Noise):**
          σ_thermal² = 4kT × BW / R
          
          **3. ضوضاء التيار المظلم (Dark Current Noise):**
          σ_dark² = I_dark × t_integration
          
          **4. ضوضاء المضخم (Amplifier Noise):**
          σ_amp² = (V_noise / Gain)²
          
          **إجمالي الضوضاء:**
          σ_total² = σ_shot² + σ_thermal² + σ_dark² + σ_amp²
          
          **نمذجة جمع الشحنات:**
          
          ```
          for each pixel:
              charge_generated = light_signal × quantum_efficiency
              charge_collected = charge_generated × collection_efficiency
              thermal_noise = generate_thermal_noise()
              dark_current = generate_dark_current()
              amplifier_noise = generate_amplifier_noise()
              
              total_signal = charge_collected + thermal_noise + dark_current + amplifier_noise
              digital_value = ADC_conversion(total_signal)
          ```
          
          **عوامل تؤثر على جمع الشحنات:**
          - سماكة طبقة الكشف
          - قوة المجال الكهربائي
          - درجة الحرارة
          - عيوب في البلورة أو الشوائب
          - زمن التكامل (Integration Time)
          
          **تحسين عملية جمع الشحنات:**
          - زيادة الجهد المطبق (للكاشفات المباشرة)
          - تقليل درجة الحرارة
          - استخدام مواد عالية النقاء
          - تحسين تصميم الدوائر الإلكترونية
        `,
        vrContent: 'نموذج تفاعلي يوضح انجراف الشحنات وعملية جمعها في البكسل مع تأثيرات الضوضاء',
        noiseAnalysis: {
          'Shot Noise': 'متناسب مع جذر عدد الفوتونات',
          'Thermal Noise': 'متناسب مع جذر درجة الحرارة والتردد',
          'Dark Current': 'يزداد مع الوقت ودرجة الحرارة',
          'Read Noise': 'ثابت لكل قراءة'
        }
      }
    },
    {
      id: 'linear-systems',
      title: '12.4 تحليل الأنظمة الخطية',
      icon: <Calculator className="w-5 h-5" />,
      content: {
        theory: `
          **تحليل الأنظمة الخطية المتتالية لنمذجة الكاشف:**
          
          نظرية الأنظمة الخطية توفر إطار عمل رياضي لتحليل أداء أجهزة الكشف.
          
          **مبادئ النظام الخطي:**
          
          **1. الخطية (Linearity):**
          إذا كان f(x) يعطي y(x)، فإن a×f(x) يعطي a×y(x)
          
          **2. عدم التغير المكاني (Shift Invariance):**
          إذا كان f(x) يعطي y(x)، فإن f(x-a) يعطي y(x-a)
          
          **وصف النظام بالدوال:**
          
          **دالة الاستجابة النقطية (Point Spread Function - PSF):**
          تصف استجابة النظام لنقطة ضوء واحدة.
          
          **دالة الاستجابة الخطية (Line Spread Function - LSF):**
          تصف استجابة النظام لخط رفيع من الضوء.
          
          العلاقة: LSF(x) = ∫ PSF(x,y) dy
          
          **دالة نقل التعديل (Modulation Transfer Function - MTF):**
          MTF(f) = |FT[LSF(x)]| / |FT[LSF(x)]|_{f=0}
          
          **النمذجة المتتالية (Cascade Model):**
          
          كاشف الأشعة السينية يمكن نمذجته كسلسلة من المراحل:
          
          **المرحلة 1: امتصاص الأشعة السينية**
          - عدد الفوتونات الممتصة: N₁ = N₀ × η_abs
          - MTF₁(f) = sinc(πfd₁) حيث d₁ سماكة طبقة الامتصاص
          
          **المرحلة 2: تحويل الأشعة إلى ضوء**
          - عدد الفوتونات الضوئية: N₂ = N₁ × g حيث g معامل التكثيف
          - MTF₂(f) يعتمد على انتشار الضوء
          
          **المرحلة 3: كشف الضوء**
          - عدد الإلكترونات: N₃ = N₂ × QE
          - MTF₃(f) يعتمد على حجم البكسل
          
          **الاستجابة الإجمالية:**
          MTF_total(f) = MTF₁(f) × MTF₂(f) × MTF₃(f)
          
          **طيف قدرة الضوضاء (Noise Power Spectrum - NPS):**
          
          NPS يصف التوزيع التردي للضوضاء:
          NPS(f) = |FT[noise_autocorrelation(x)]|
          
          **للضوضاء الكمية:**
          NPS_quantum(f) = constant (أبيض)
          
          **للضوضاء المترابطة:**
          NPS_correlated(f) = NPS_quantum(f) × |MTF_noise(f)|²
          
          **كفاءة الكم الاستقصائية (Detective Quantum Efficiency - DQE):**
          
          DQE تجمع بين MTF وNPS:
          DQE(f) = [MTF(f)]² × QE / [NPS(f) / (q₀ × Δx²)]
          
          حيث:
          - q₀: عدد الفوتونات الساقطة لكل وحدة مساحة
          - Δx: حجم البكسل
          
          **نمذجة رياضية:**
          
          ```python
          def cascade_model(stages):
              mtf_total = 1.0
              nps_total = 0.0
              
              for stage in stages:
                  mtf_total *= stage.mtf
                  nps_total = stage.gain² × (nps_total + stage.added_noise)
              
              dqe = mtf_total² / (nps_total / quantum_input)
              return mtf_total, nps_total, dqe
          ```
          
          **تطبيقات التحليل:**
          - تحسين تصميم الكاشف
          - مقارنة أداء أنواع مختلفة من الكاشفات
          - التنبؤ بجودة الصورة
          - تصميم فلاتر معالجة الصور المثلى
        `,
        vrContent: 'تصور تفاعلي للدوال MTF وNPS وDQE مع إمكانية تغيير معلمات النظام',
        cascadeStages: {
          'X-ray absorption': { gain: 'η_abs', mtf: 'sinc function', noise: 'quantum' },
          'Light conversion': { gain: 'light_yield', mtf: 'spread function', noise: 'conversion' },
          'Light detection': { gain: 'QE', mtf: 'pixel aperture', noise: 'electronic' }
        }
      }
    },
    {
      id: 'projection-imaging',
      title: '12.5 محاكاة تكوين صورة الإسقاط',
      icon: <Image className="w-5 h-5" />,
      content: {
        theory: `
          **محاكاة تكوين صورة الإسقاط الشعاعي:**
          
          تكوين صورة الإسقاط هو العملية النهائية في التصوير الشعاعي، حيث تتحول الأشعة المخترقة للمريض إلى صورة مرئية.
          
          **مبادئ الإسقاط الشعاعي:**
          
          **قانون بير-لامبرت (Beer-Lambert Law):**
          الأساس الرياضي لتوهين الأشعة السينية:
          
          I(x,y) = I₀ × exp(-∫ μ(E,z) dz)
          
          حيث:
          - I₀: شدة الشعاع الساقط
          - μ(E,z): معامل التوهين كدالة للطاقة والموقع
          - z: عمق النفاذ في الجسم
          
          **الهندسة الإسقاطية:**
          
          **تكبير هندسي:**
          M = (SID) / (SOD)
          
          حيث:
          - SID: المسافة من المصدر إلى الكاشف
          - SOD: المسافة من المصدر إلى الجسم
          
          **عدم الوضوح الهندسي:**
          U_g = f × (SID - SOD) / SOD
          
          حيث f حجم البؤرة الفعالة.
          
          **نمذجة المريض:**
          
          **الأشباح الرقمية (Digital Phantoms):**
          تمثيل رقمي لتشريح الإنسان:
          
          ```
          Phantom = {
              organs: [
                  {name: 'lung', density: 0.3, composition: tissue_lung},
                  {name: 'bone', density: 1.8, composition: tissue_bone},
                  {name: 'soft_tissue', density: 1.0, composition: tissue_soft}
              ],
              geometry: voxel_array[512][512][200]
          }
          ```
          
          **محاكاة Ray Tracing:**
          
          لكل بكسل في الكاشف:
          
          ```python
          def ray_trace(source_point, detector_pixel):
              ray_direction = normalize(detector_pixel - source_point)
              intensity = initial_intensity
              
              for step in ray_path:
                  material = phantom.get_material_at(step.position)
                  attenuation = material.mu * step.length
                  intensity *= exp(-attenuation)
              
              return intensity
          ```
          
          **تأثيرات التشويه:**
          
          **1. عدم الوضوح الحركي:**
          بسبب حركة المريض أثناء التعرض:
          MTF_motion(f) = sinc(π × f × v × t_exposure)
          
          **2. عدم الوضوح بسبب حجم البؤرة:**
          PSF_focal(x,y) = rect(x/f_x) × rect(y/f_y)
          
          **3. التشتت:**
          الأشعة المتشتتة تقلل تباين الصورة:
          SPR = (Scattered Radiation) / (Primary Radiation)
          
          **خوارزمية المحاكاة الشاملة:**
          
          ```python
          def simulate_projection_imaging():
              # 1. إعداد الهندسة
              setup_geometry(source, patient, detector)
              
              # 2. محاكاة الطيف
              spectrum = simulate_xray_spectrum(kVp, filtration)
              
              # 3. تتبع الأشعة
              for pixel in detector:
                  primary_intensity = 0
                  scattered_intensity = 0
                  
                  for energy in spectrum:
                      for ray in pixel_rays:
                          primary = trace_primary_ray(ray, energy)
                          scattered = monte_carlo_scatter(ray, energy)
                          
                          primary_intensity += primary
                          scattered_intensity += scattered
                  
                  pixel.value = detector_response(primary_intensity + scattered_intensity)
              
              # 4. إضافة ضوضاء الكاشف
              add_detector_noise()
              
              # 5. معالجة الصورة
              processed_image = image_processing(raw_image)
              
              return processed_image
          ```
          
          **معايير جودة الصورة:**
          
          **نسبة الإشارة إلى الضوضاء (SNR):**
          SNR = (Signal_mean) / (Noise_std)
          
          **نسبة التباين إلى الضوضاء (CNR):**
          CNR = |Signal_object - Signal_background| / Noise_std
          
          **دقة التباين المنخفض:**
          LCR = ΔI / I_background
          
          **تطبيقات المحاكاة:**
          - تحسين بروتوكولات التصوير
          - تطوير خوارزميات إعادة البناء
          - تقييم جرعة المريض
          - تدريب الأطباء وتقنيي الأشعة
          - تطوير أنظمة الذكاء الاصطناعي
        `,
        vrContent: 'محاكاة كاملة لعملية التصوير مع إمكانية تغيير معلمات الإعداد ومشاهدة تأثيرها على الصورة',
        imagingParameters: {
          'kVp': { range: '80-140 kV', effect: 'يؤثر على نفاذية الأشعة وتباين الصورة' },
          'mAs': { range: '1-1000 mAs', effect: 'يحدد كمية الأشعة وضوضاء الصورة' },
          'Filtration': { types: 'Al, Cu, Mo', effect: 'يحسن جودة الطيف ويقلل الجرعة' },
          'Grid ratio': { range: '8:1 - 16:1', effect: 'يقلل التشتت ويحسن التباين' }
        }
      }
    },
    {
      id: 'scatter-grids',
      title: '12.6 دمج شبكات مكافحة التشتت',
      icon: <Grid className="w-5 h-5" />,
      content: {
        theory: `
          **دمج شبكات مكافحة التشتت في عمليات المحاكاة:**
          
          شبكات مكافحة التشتت هي أجهزة مهمة لتحسين جودة الصورة الشعاعية عبر تقليل تأثير الأشعة المتشتتة.
          
          **12.6.1 معلمات تصميم الشبكة:**
          
          **النسبة (Grid Ratio):**
          R = h / D
          
          حيث:
          - h: ارتفاع الألواح الرصاصية
          - D: المسافة بين الألواح
          
          النسب الشائعة: 5:1, 8:1, 10:1, 12:1, 16:1
          
          **التردد (Grid Frequency):**
          عدد الألواح الرصاصية لكل بوصة أو سنتيمتر:
          - تردد منخفض: 25-35 خط/بوصة
          - تردد عالي: 60-80 خط/بوصة
          
          **مادة الألواح:**
          - **الرصاص**: الأكثر شيوعاً، كفاءة عالية في امتصاص الأشعة
          - **التنغستن**: كثافة أعلى، مناسب للطاقات العالية
          - **ألياف الكربون**: وزن أخف، شفافية أكبر للأشعة الأولية
          
          **مادة الفواصل:**
          - **الألومنيوم**: خفيف، شفافية جيدة للأشعة الأولية
          - **ألياف الكربون**: شفافية ممتازة، مقاومة للإشعاع
          - **البلاستيك**: اقتصادي، مناسب للتطبيقات منخفضة الطاقة
          
          **12.6.2 نمذجة التوهين الشبكي ورفض التشتت:**
          
          **كفاءة انتقال الأشعة الأولية:**
          
          للأشعة الساقطة عمودياً:
          T_primary = (D / (D + t_lead)) × exp(-μ_interspace × t_interspace)
          
          للأشعة المائلة بزاوية θ:
          T_primary(θ) = T_primary(0) × exp(-μ_lead × t_lead × tan(θ) / sin(θ))
          
          **كفاءة رفض التشتت:**
          
          نسبة البكي-غريدي (Bucky Factor):
          BF = Exposure_with_grid / Exposure_without_grid
          
          نسبة تحسين التباين (Contrast Improvement Factor):
          CIF = (C_with_grid) / (C_without_grid)
          
          حيث C هو التباين.
          
          **كفاءة الانتقائية:**
          Σ = (T_primary) / (T_scattered)
          
          **نمذجة Monte Carlo للشبكة:**
          
          ```python
          def grid_interaction(photon_position, photon_direction, grid_parameters):
              # تحديد موقع الفوتون بالنسبة للشبكة
              x_grid = photon_position.x % grid_parameters.spacing
              
              # تحديد ما إذا كان الفوتون في منطقة الرصاص أم الفاصل
              if x_grid < grid_parameters.lead_width:
                  # في منطقة الرصاص
                  path_length = calculate_path_in_lead(photon_direction, grid_parameters)
                  attenuation_prob = 1 - exp(-μ_lead * path_length)
                  
                  if random() < attenuation_prob:
                      photon.absorbed = True
                      return False  # الفوتون مُمتص
              
              # في منطقة الفاصل
              path_length = calculate_path_in_interspace(photon_direction, grid_parameters)
              attenuation_prob = 1 - exp(-μ_interspace * path_length)
              
              if random() < attenuation_prob:
                  photon.absorbed = True
                  return False
              
              return True  # الفوتون مُرَّ
          ```
          
          **تأثير زاوية الإمالة:**
          
          عندما لا تكون الأشعة عمودية على الشبكة:
          
          **Cut-off للأشعة الأولية:**
          Cut-off يحدث عند زاوية:
          θ_cutoff = arctan(D / h)
          
          **نمذجة نمط الشبكة:**
          Grid_pattern(x) = sinc²(π × x × frequency) × rect(x / aperture)
          
          **التأثير على MTF:**
          MTF_grid(f) = |sinc(π × f × D)| × MTF_detector(f)
          
          **خوارزمية المحاكاة الشاملة:**
          
          ```python
          def simulate_with_grid():
              for photon in photon_beam:
                  # تتبع الفوتون حتى الوصول للشبكة
                  photon_at_grid = trace_to_grid(photon)
                  
                  # تحديد نوع الفوتون (أولي أم متشتت)
                  if photon.is_primary():
                      transmission_prob = calculate_primary_transmission(photon_at_grid)
                  else:
                      transmission_prob = calculate_scatter_transmission(photon_at_grid)
                  
                  if random() < transmission_prob:
                      # الفوتون مُرَّ عبر الشبكة
                      photon_at_detector = trace_to_detector(photon_at_grid)
                      detector.record(photon_at_detector)
          ```
          
          **تحسين أداء الشبكة:**
          
          **تصميم الشبكة المثلى:**
          - توازن بين رفض التشتت وانتقال الأشعة الأولية
          - اختيار النسبة والتردد المناسبين للتطبيق
          - تحسين مواد الألواح والفواصل
          
          **الشبكات المتحركة:**
          - تقلل ظهور خطوط الشبكة في الصورة
          - تتطلب تزامن حركة الشبكة مع التعرض
          
          **الشبكات الرقمية:**
          - إزالة نمط الشبكة بالمعالجة الرقمية
          - تحسين موحد لجودة الصورة
        `,
        vrContent: 'نموذج ثلاثي الأبعاد تفاعلي للشبكة يوضح مسار الأشعة الأولية والمتشتتة',
        gridComparison: {
          '8:1': { primary_transmission: '85%', scatter_rejection: '90%', contrast_improvement: '2.5x' },
          '12:1': { primary_transmission: '75%', scatter_rejection: '95%', contrast_improvement: '3.5x' },
          '16:1': { primary_transmission: '65%', scatter_rejection: '98%', contrast_improvement: '4.5x' }
        }
      }
    },
    {
      id: 'image-processing',
      title: '12.7 معالجة الصور بعد الاستحواذ',
      icon: <Image className="w-5 h-5" />,
      content: {
        theory: `
          **خطوات معالجة الصور بعد الاستحواذ (نظرة عامة موجزة):**
          
          معالجة الصور الشعاعية بعد الاستحواذ تهدف إلى تحسين جودة الصورة وجعلها مناسبة للتشخيص الطبي.
          
          **المراحل الأساسية للمعالجة:**
          
          **1. التصحيح والمعايرة (Correction and Calibration):**
          
          **تصحيح الأوفست (Offset Correction):**
          I_corrected = I_raw - I_dark
          
          **تصحيح المكسب (Gain Correction):**
          I_corrected = I_offset / Gain_map
          
          **تصحيح البكسل المعيب (Bad Pixel Correction):**
          استبدال قيم البكسل المعيبة بمتوسط البكسل المجاورة.
          
          **2. تقليل الضوضاء (Noise Reduction):**
          
          **المرشحات الخطية:**
          - مرشح جاوس: G(x,y) = exp(-(x²+y²)/(2σ²))
          - مرشح متوسط: Average filter
          - مرشح ويينر: يحسن نسبة الإشارة إلى الضوضاء
          
          **المرشحات غير الخطية:**
          - مرشح الوسيط (Median Filter): يحافظ على الحواف
          - مرشح ثنائي (Bilateral Filter): يحافظ على التفاصيل
          - Non-local means: يستخدم التشابه في الصورة
          
          **3. تحسين التباين (Contrast Enhancement):**
          
          **تمديد النطاق (Histogram Stretching):**
          I_enhanced = (I - I_min) × (255) / (I_max - I_min)
          
          **تعديل المنحنى (Histogram Equalization):**
          يوزع الكثافات بالتساوي عبر النطاق الديناميكي.
          
          **CLAHE (Contrast Limited Adaptive Histogram Equalization):**
          تحسين تباين محلي مع تجنب التضخيم المفرط للضوضاء.
          
          **4. تحسين الحواف (Edge Enhancement):**
          
          **Unsharp Masking:**
          I_enhanced = I_original + α × (I_original - I_blurred)
          
          **مرشحات الحواف:**
          - مرشح سوبل (Sobel)
          - مرشح لابلاسيان (Laplacian)
          - مرشح كاني (Canny)
          
          **5. المعالجة المتقدمة:**
          
          **إزالة الآثار (Artifact Removal):**
          - إزالة خطوط الشبكة
          - تصحيح آثار التشتت
          - إزالة الحلقات والخطوط
          
          **Multiscale Processing:**
          تحليل الصورة على مقاييس متعددة:
          ```python
          def multiscale_enhancement(image, scales):
              enhanced = image.copy()
              
              for scale in scales:
                  # تطبيق مرشح جاوس بمقياس محدد
                  smoothed = gaussian_filter(image, scale)
                  detail = image - smoothed
                  
                  # تضخيم التفاصيل بشكل انتقائي
                  enhanced_detail = enhance_details(detail, scale)
                  enhanced += enhanced_detail
              
              return enhanced
          ```
          
          **6. معالجة خاصة بالتطبيق:**
          
          **للصدر:**
          - تحسين تباين الرئتين
          - تقليل ضوضاء القلب
          - تحسين وضوح الأوعية الدموية
          
          **للعظام:**
          - تحسين حواف العظام
          - تقليل تأثير الأنسجة الرخوة
          - تحسين الكسور الدقيقة
          
          **لتصوير الثدي:**
          - تحسين تباين الأنسجة الغدية
          - الكشف عن الكتل الصغيرة
          - تحسين وضوح الكلسيوم المجهري
          
          **7. التحويل للعرض (Display Processing):**
          
          **LUT (Look-Up Table) Processing:**
          تحويل القيم الخام إلى قيم مناسبة للعرض:
          Display_value = LUT[Raw_value]
          
          **Window/Level Processing:**
          - Window Width: نطاق القيم المعروضة
          - Window Level: مركز النطاق
          
          **8. ضغط الصور:**
          
          **DICOM Compression:**
          - Lossless: لا يفقد أي معلومات
          - Lossy: يقلل الحجم مع فقدان مقبول للجودة
          
          **خوارزمية المعالجة الشاملة:**
          
          ```python
          def comprehensive_image_processing(raw_image, processing_params):
              # 1. التصحيح الأساسي
              corrected = basic_corrections(raw_image)
              
              # 2. تقليل الضوضاء
              denoised = adaptive_noise_reduction(corrected, processing_params.noise_level)
              
              # 3. تحسين التباين
              contrast_enhanced = multiscale_contrast_enhancement(denoised)
              
              # 4. تحسين الحواف
              edge_enhanced = selective_edge_enhancement(contrast_enhanced)
              
              # 5. المعالجة الخاصة بالتطبيق
              app_specific = application_specific_processing(edge_enhanced, processing_params.exam_type)
              
              # 6. التحضير للعرض
              display_ready = display_processing(app_specific, processing_params.display_params)
              
              return display_ready
          ```
          
          **معايير تقييم جودة المعالجة:**
          - وضوح التفاصيل التشريحية
          - مستوى الضوضاء
          - جودة التباين
          - الحفاظ على المعلومات التشخيصية
          - سرعة المعالجة
        `,
        vrContent: 'تطبيق تفاعلي يظهر تأثير مختلف خطوات المعالجة على جودة الصورة',
        processingTechniques: {
          'Noise Reduction': 'يقلل الضوضاء مع الحفاظ على التفاصيل',
          'Contrast Enhancement': 'يحسن وضوح الهياكل المختلفة',
          'Edge Sharpening': 'يبرز الحدود والتفاصيل الدقيقة',
          'Artifact Removal': 'يزيل الآثار غير المرغوبة'
        }
      }
    }
  ];

  const SimulationVisualization = ({ section }: { section: any }) => {
    const startSimulation = () => {
      setSimulationMode(true);
      setSimulationProgress(0);
      
      const interval = setInterval(() => {
        setSimulationProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setTimeout(() => setSimulationMode(false), 2000);
            return 100;
          }
          return prev + 5;
        });
      }, 200);
    };

    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-gradient-to-br from-slate-800 to-slate-900 p-6 rounded-lg text-white"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-right">محاكاة تفاعلية</h3>
          <Button
            variant="secondary"
            size="sm"
            onClick={startSimulation}
            disabled={simulationMode}
          >
            <PlayCircle className="w-4 h-4 ml-2" />
            {simulationMode ? 'جاري المحاكاة...' : 'بدء المحاكاة'}
          </Button>
        </div>
        
        <div className="aspect-video bg-slate-700 rounded-lg flex items-center justify-center mb-4">
          {simulationMode ? (
            <motion.div className="text-center">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"
              />
              <p className="text-lg mb-2">{section.content.vrContent}</p>
              <Progress value={simulationProgress} className="max-w-sm mx-auto" />
              <p className="text-sm mt-2">{simulationProgress}% مكتمل</p>
            </motion.div>
          ) : (
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                {section.icon}
              </div>
              <p>انقر على "بدء المحاكاة" لرؤية العرض التفاعلي</p>
            </div>
          )}
        </div>
        
        {section.content.keyEquations && (
          <div className="bg-slate-600 p-4 rounded-lg">
            <h4 className="font-semibold mb-2 text-right">المعادلات الرئيسية:</h4>
            {section.content.keyEquations.map((eq: string, index: number) => (
              <div key={index} className="text-sm font-mono bg-slate-700 p-2 rounded mb-2 text-center">
                {eq}
              </div>
            ))}
          </div>
        )}
      </motion.div>
    );
  };

  const currentSection = sections.find(s => s.id === activeSection);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6" dir="rtl">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            الفصل الثاني عشر: محاكاة استجابة الكاشف وتكوين الصورة
          </h1>
          <p className="text-xl text-muted-foreground">
            نمذجة متقدمة وتحليل شامل لعمليات تكوين الصورة الشعاعية
          </p>
          <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mt-4">
            <Badge variant="outline">محاكاة Monte Carlo</Badge>
            <Badge variant="outline">تحليل الأنظمة الخطية</Badge>
            <Badge variant="outline">معالجة الصور</Badge>
          </div>
        </motion.div>

        {/* Navigation */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-right">محتويات الفصل</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {sections.map((section) => (
                <Button
                  key={section.id}
                  variant={activeSection === section.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveSection(section.id)}
                  className="justify-end text-xs p-2 h-auto"
                >
                  <span className="mr-2 text-right leading-tight">{section.title}</span>
                  {section.icon}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Theory Content */}
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-right flex items-center space-x-3 rtl:space-x-reverse">
                  {currentSection?.icon}
                  <span>{currentSection?.title}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 max-h-[600px] overflow-y-auto">
                <div className="prose prose-sm max-w-none text-right" style={{ direction: 'rtl' }}>
                  <div className="whitespace-pre-line text-sm leading-relaxed">
                    {currentSection?.content.theory}
                  </div>
                </div>

                {/* Technical Data Tables */}
                {currentSection?.content.materialProperties && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-800 text-right mb-3">خصائص المواد:</h4>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b border-blue-200">
                            <th className="text-right p-2">معامل التوهين</th>
                            <th className="text-right p-2">العدد الذري الفعال</th>
                            <th className="text-right p-2">الكثافة</th>
                            <th className="text-right p-2">المادة</th>
                          </tr>
                        </thead>
                        <tbody>
                          {Object.entries(currentSection.content.materialProperties).map(([material, props]: [string, any]) => (
                            <tr key={material} className="border-b border-blue-100">
                              <td className="text-right p-2">{props.mu_photoelectric}</td>
                              <td className="text-right p-2">{props.Z_eff}</td>
                              <td className="text-right p-2">{props.density}</td>
                              <td className="text-right p-2 font-medium">{material}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {currentSection?.content.cascadeStages && (
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-green-800 text-right mb-3">مراحل النموذج المتتالي:</h4>
                    <div className="space-y-3">
                      {Object.entries(currentSection.content.cascadeStages).map(([stage, props]: [string, any]) => (
                        <div key={stage} className="bg-white p-3 rounded border">
                          <h5 className="font-medium text-right mb-2">{stage}</h5>
                          <div className="grid grid-cols-3 gap-2 text-xs">
                            <div><span className="font-medium">المكسب:</span> {props.gain}</div>
                            <div><span className="font-medium">MTF:</span> {props.mtf}</div>
                            <div><span className="font-medium">الضوضاء:</span> {props.noise}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Simulation Visualization */}
          <motion.div
            key={`${activeSection}-sim`}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            {currentSection && <SimulationVisualization section={currentSection} />}
          </motion.div>
        </div>

        {/* Learning Objectives and Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8 space-y-6"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-right">الأهداف التعليمية والمخرجات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold text-right mb-3 text-blue-600">المعرفة النظرية:</h4>
                  <ul className="text-sm space-y-1 text-right">
                    <li>• فهم الأسس الفيزيائية لعمليات الكشف</li>
                    <li>• تحليل انتشار الضوء في المواد الومضة</li>
                    <li>• نمذجة عمليات جمع الشحنات</li>
                    <li>• تطبيق نظرية الأنظمة الخطية</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-right mb-3 text-green-600">المهارات التطبيقية:</h4>
                  <ul className="text-sm space-y-1 text-right">
                    <li>• تصميم نماذج محاكاة Monte Carlo</li>
                    <li>• حساب MTF وNPS وDQE</li>
                    <li>• تحليل تأثير المعلمات على الأداء</li>
                    <li>• تطوير خوارزميات معالجة الصور</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-right mb-3 text-purple-600">التطبيقات العملية:</h4>
                  <ul className="text-sm space-y-1 text-right">
                    <li>• تحسين تصميم أجهزة الكشف</li>
                    <li>• تطوير بروتوكولات التصوير</li>
                    <li>• تقييم جودة الصورة</li>
                    <li>• حل مشاكل الأداء السريرية</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Chapter12Content;