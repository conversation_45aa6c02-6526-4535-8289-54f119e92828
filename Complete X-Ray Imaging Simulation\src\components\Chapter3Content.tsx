import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Zap, Target, Settings, Thermometer, Shield, Cpu } from 'lucide-react';
import { motion } from 'motion/react';

const Chapter3Content = () => {
  const [openSections, setOpenSections] = useState<{[key: string]: boolean;}>({});
  const [completedSections, setCompletedSections] = useState<string[]>([]);

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const markAsCompleted = (sectionId: string) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections((prev) => [...prev, sectionId]);
    }
  };

  const sections = [
  {
    id: '3.1',
    title: 'مجموعة الكاثود',
    icon: <Zap className="w-5 h-5" data-id="trss107dq" data-path="src/components/Chapter3Content.tsx" />,
    subsections: [
    {
      id: '3.1.1',
      title: 'الخيوط: المواد والانبعاث الحراري (معادلة ريتشاردسون-دوشمان)',
      content: 'الخيوط المصنوعة من التنغستن تقوم بإصدار الإلكترونات عند تسخينها. معادلة ريتشاردسون-دوشمان تحدد كمية الإلكترونات المنبعثة: J = AT²e^(-W/kT) حيث J هو كثافة التيار، A ثابت ريتشاردسون، W طاقة العمل، k ثابت بولتزمان، T درجة الحرارة.'
    },
    {
      id: '3.1.2',
      title: 'كوب التركيز: التركيز الكهروستاتيكي وتشكيل الشعاع',
      content: 'كوب التركيز يحيط بالخيط ويحمل شحنة سالبة لتركيز الإلكترونات وتوجيهها نحو الأنود. يؤثر شكل الكوب وجهده على حجم وشكل البقعة البؤرية.'
    },
    {
      id: '3.1.3',
      title: 'الكاثودات ثنائية التركيز',
      content: 'تحتوي على خيطين منفصلين لإنتاج بقعتين بؤريتين مختلفتي الحجم - كبيرة للتيارات العالية وصغيرة للحصول على دقة أفضل.'
    }]

  },
  {
    id: '3.2',
    title: 'مجموعة الأنود',
    icon: <Target className="w-5 h-5" data-id="n99yowhrh" data-path="src/components/Chapter3Content.tsx" />,
    subsections: [
    {
      id: '3.2.1',
      title: 'المواد المستهدفة: التنغستن، الموليبدينوم، الروديوم',
      content: 'التنغستن (Z=74): مادة مثلى للأشعة العامة بسبب نقطة انصهارها العالية وكفاءة إنتاج الأشعة. الموليبدينوم (Z=42): مستخدم في تصوير الثدي. الروديوم (Z=45): يوفر طيف طاقة مناسب للأنسجة الرخوة.'
    },
    {
      id: '3.2.2',
      title: 'الأنودات الثابتة مقابل الأنودات الدوارة',
      content: 'الأنودات الثابتة: بسيطة ومناسبة للتطبيقات منخفضة الطاقة. الأنودات الدوارة: توزع الحرارة على مساحة أكبر، مما يسمح بتيارات وأوقات تعرض أطول.'
    },
    {
      id: '3.2.3',
      title: 'زاوية الأنود ومبدأ التركيز الخطي',
      content: 'زاوية الأنود تؤثر على حجم البقعة البؤرية الفعالة. التركيز الخطي يقلل البقعة البؤرية الظاهرة مقارنة بالحقيقية حسب: حجم البؤرة الفعالة = حجم البؤرة الحقيقية × sin(زاوية الأنود)'
    },
    {
      id: '3.2.4',
      title: 'تأثير كعب الأنود وتأثيره على اتساق الشعاع',
      content: 'تأثير كعب الأنود يؤدي إلى عدم انتظام شدة الشعاع عبر الحقل، حيث تكون الشدة أقل في جهة الأنود مقارنة بجهة الكاثود بسبب الامتصاص الذاتي.'
    }]

  },
  {
    id: '3.3',
    title: 'غلاف التفريغ وغطاء الأنبوب',
    icon: <Shield className="w-5 h-5" data-id="xvnc3kpxj" data-path="src/components/Chapter3Content.tsx" />,
    subsections: [
    {
      id: '3.3.1',
      title: 'الأغلفة الزجاجية والمعدنية والسيراميكية',
      content: 'الزجاج: تقليدي ومرئي للفحص. المعدن: أقوى ومقاوم أكثر للحرارة. السيراميك: يجمع مزايا كلا النوعين مع خصائص عزل ممتازة.'
    },
    {
      id: '3.3.2',
      title: 'الزيت العازل والدروع ومنفاخ التمدد',
      content: 'الزيت العازل يوفر العزل الكهربائي والتبريد. الدروع الرصاصية تحمي من التسرب الإشعاعي. منفاخ التمدد يتعامل مع التمدد الحراري للزيت.'
    }]

  },
  {
    id: '3.4',
    title: 'التحديات الهندسية في تصميم أنبوب الأشعة السينية',
    icon: <Settings className="w-5 h-5" data-id="t2jjokxa9" data-path="src/components/Chapter3Content.tsx" />,
    subsections: [
    {
      id: '3.4.1',
      title: 'إدارة الحرارة وتقنيات التبريد',
      content: 'إدارة الحرارة أمر بالغ الأهمية حيث أن 99% من طاقة الإلكترونات تتحول إلى حرارة. تقنيات التبريد تشمل: التبريد بالزيت، الأنودات الدوارة، والمواد عالية التوصيل الحراري.'
    },
    {
      id: '3.4.2',
      title: 'سلامة الفراغ وعمر الأنبوب',
      content: 'الحفاظ على فراغ عالي ضروري لمنع تصادم الإلكترونات مع جزيئات الغاز. تآكل الخيط والأنود يحدد عمر الأنبوب الذي يتراوح بين 20,000-100,000 تعرض.'
    }]

  }];


  const progress = completedSections.length / sections.length * 100;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6" dir="rtl" data-id="oqzs2oh1l" data-path="src/components/Chapter3Content.tsx">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }} data-id="c5j5klgei" data-path="src/components/Chapter3Content.tsx">
        
        <Card className="mb-6" data-id="1unuu0rda" data-path="src/components/Chapter3Content.tsx">
          <CardHeader className="text-center" data-id="9msjxx5ca" data-path="src/components/Chapter3Content.tsx">
            <CardTitle className="text-2xl font-bold text-right" data-id="ytfwxuwhf" data-path="src/components/Chapter3Content.tsx">
              الفصل الثالث: أنبوب الأشعة السينية - التصميم والمكونات والوظيفة
            </CardTitle>
            <CardDescription className="text-right" data-id="lrtid3z08" data-path="src/components/Chapter3Content.tsx">
              دراسة شاملة لمكونات أنبوب الأشعة السينية والتحديات الهندسية في تصميمه
            </CardDescription>
            <div className="mt-4" data-id="6c5ud0m8h" data-path="src/components/Chapter3Content.tsx">
              <div className="flex justify-between items-center mb-2" data-id="plk3vb4fl" data-path="src/components/Chapter3Content.tsx">
                <span className="text-sm text-muted-foreground" data-id="vac1g68vs" data-path="src/components/Chapter3Content.tsx">التقدم</span>
                <span className="text-sm font-medium" data-id="08svy6fep" data-path="src/components/Chapter3Content.tsx">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" data-id="5ccxrteja" data-path="src/components/Chapter3Content.tsx" />
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      <Tabs defaultValue="content" className="w-full" data-id="00uya9nco" data-path="src/components/Chapter3Content.tsx">
        <TabsList className="grid w-full grid-cols-3" data-id="hpyvlx4f7" data-path="src/components/Chapter3Content.tsx">
          <TabsTrigger value="content" data-id="uyaw4u83f" data-path="src/components/Chapter3Content.tsx">المحتوى</TabsTrigger>
          <TabsTrigger value="equations" data-id="2nddcfe5y" data-path="src/components/Chapter3Content.tsx">المعادلات</TabsTrigger>
          <TabsTrigger value="applications" data-id="obc6dsywi" data-path="src/components/Chapter3Content.tsx">التطبيقات</TabsTrigger>
        </TabsList>
        
        <TabsContent value="content" className="space-y-4" data-id="m05cbz1o6" data-path="src/components/Chapter3Content.tsx">
          {sections.map((section, index) =>
          <motion.div
            key={section.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }} data-id="iufh80ytm" data-path="src/components/Chapter3Content.tsx">
              
              <Card className="overflow-hidden" data-id="dlvay9y8z" data-path="src/components/Chapter3Content.tsx">
                <Collapsible
                open={openSections[section.id]}
                onOpenChange={() => toggleSection(section.id)} data-id="wc7wz4mco" data-path="src/components/Chapter3Content.tsx">
                  
                  <CollapsibleTrigger asChild data-id="t13siol62" data-path="src/components/Chapter3Content.tsx">
                    <CardHeader className="hover:bg-muted/50 cursor-pointer transition-colors" data-id="vx0dhy5ed" data-path="src/components/Chapter3Content.tsx">
                      <div className="flex items-center justify-between" data-id="ubkudz3m1" data-path="src/components/Chapter3Content.tsx">
                        <div className="flex items-center gap-3" data-id="hx69iz2yw" data-path="src/components/Chapter3Content.tsx">
                          <div className="flex items-center gap-2" data-id="lp83s1305" data-path="src/components/Chapter3Content.tsx">
                            {section.icon}
                            <Badge variant="outline" data-id="1t1l253nd" data-path="src/components/Chapter3Content.tsx">{section.id}</Badge>
                          </div>
                          <CardTitle className="text-lg text-right" data-id="i9e65dba6" data-path="src/components/Chapter3Content.tsx">{section.title}</CardTitle>
                        </div>
                        <div className="flex items-center gap-2" data-id="9vlykayql" data-path="src/components/Chapter3Content.tsx">
                          {completedSections.includes(section.id) &&
                        <Badge variant="default" data-id="mveqvoxi0" data-path="src/components/Chapter3Content.tsx">مكتمل</Badge>
                        }
                          {openSections[section.id] ?
                        <ChevronDown className="w-4 h-4" data-id="tnmb7nn3g" data-path="src/components/Chapter3Content.tsx" /> :

                        <ChevronRight className="w-4 h-4" data-id="e1ujfezsw" data-path="src/components/Chapter3Content.tsx" />
                        }
                        </div>
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent data-id="ujdjvcyu5" data-path="src/components/Chapter3Content.tsx">
                    <CardContent className="pt-0" data-id="nsexgls95" data-path="src/components/Chapter3Content.tsx">
                      <div className="space-y-4" data-id="s21kr3yhx" data-path="src/components/Chapter3Content.tsx">
                        {section.subsections.map((subsection) =>
                      <Card key={subsection.id} className="border-l-4 border-l-primary" data-id="hjin4dplv" data-path="src/components/Chapter3Content.tsx">
                            <CardHeader className="pb-2" data-id="tbwixf0fd" data-path="src/components/Chapter3Content.tsx">
                              <div className="flex items-center gap-2" data-id="scodtyx0z" data-path="src/components/Chapter3Content.tsx">
                                <Badge variant="secondary" data-id="cxue6wipa" data-path="src/components/Chapter3Content.tsx">{subsection.id}</Badge>
                                <CardTitle className="text-base text-right" data-id="tmzyinois" data-path="src/components/Chapter3Content.tsx">
                                  {subsection.title}
                                </CardTitle>
                              </div>
                            </CardHeader>
                            <CardContent data-id="00hsmtw4o" data-path="src/components/Chapter3Content.tsx">
                              <p className="text-muted-foreground text-right leading-relaxed" data-id="nla4wrym8" data-path="src/components/Chapter3Content.tsx">
                                {subsection.content}
                              </p>
                            </CardContent>
                          </Card>
                      )}
                      </div>
                      
                      <div className="mt-4 flex justify-start" data-id="vci3uxy3c" data-path="src/components/Chapter3Content.tsx">
                        <Button
                        onClick={() => markAsCompleted(section.id)}
                        disabled={completedSections.includes(section.id)}
                        size="sm" data-id="cmccr2azq" data-path="src/components/Chapter3Content.tsx">
                          {completedSections.includes(section.id) ? 'مكتمل' : 'وضع علامة كمكتمل'}
                        </Button>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            </motion.div>
          )}
        </TabsContent>

        <TabsContent value="equations" className="space-y-4" data-id="1o6oa8wft" data-path="src/components/Chapter3Content.tsx">
          <Card data-id="qxk70qq62" data-path="src/components/Chapter3Content.tsx">
            <CardHeader data-id="9tqana310" data-path="src/components/Chapter3Content.tsx">
              <CardTitle className="text-right" data-id="rdxaec8xq" data-path="src/components/Chapter3Content.tsx">المعادلات الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4" data-id="cxt4uvsup" data-path="src/components/Chapter3Content.tsx">
              <div className="bg-muted p-4 rounded-lg" data-id="dw7qdi3vh" data-path="src/components/Chapter3Content.tsx">
                <h4 className="font-semibold mb-2 text-right" data-id="4mnqmu6b8" data-path="src/components/Chapter3Content.tsx">معادلة ريتشاردسون-دوشمان للانبعاث الحراري:</h4>
                <div className="font-mono text-center bg-white p-3 rounded border" data-id="7q1f3xrx1" data-path="src/components/Chapter3Content.tsx">
                  J = AT²e^(-W/kT)
                </div>
                <p className="text-sm text-muted-foreground mt-2 text-right" data-id="i4cc2d1pv" data-path="src/components/Chapter3Content.tsx">
                  حيث J = كثافة التيار، A = ثابت ريتشاردسون، T = درجة الحرارة، W = طاقة العمل
                </p>
              </div>
              
              <div className="bg-muted p-4 rounded-lg" data-id="q5g7r99pk" data-path="src/components/Chapter3Content.tsx">
                <h4 className="font-semibold mb-2 text-right" data-id="gg45z18wi" data-path="src/components/Chapter3Content.tsx">حجم البقعة البؤرية الفعالة:</h4>
                <div className="font-mono text-center bg-white p-3 rounded border" data-id="rt4cknc1m" data-path="src/components/Chapter3Content.tsx">
                  حجم البؤرة الفعالة = حجم البؤرة الحقيقية × sin(θ)
                </div>
                <p className="text-sm text-muted-foreground mt-2 text-right" data-id="en9w3ob33" data-path="src/components/Chapter3Content.tsx">
                  حيث θ = زاوية الأنود
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="applications" className="space-y-4" data-id="bzg494ybw" data-path="src/components/Chapter3Content.tsx">
          <Card data-id="iqz66degd" data-path="src/components/Chapter3Content.tsx">
            <CardHeader data-id="gy1g1mbbe" data-path="src/components/Chapter3Content.tsx">
              <CardTitle className="text-right" data-id="8ao0bpv6d" data-path="src/components/Chapter3Content.tsx">التطبيقات العملية</CardTitle>
            </CardHeader>
            <CardContent data-id="fpz6c9bde" data-path="src/components/Chapter3Content.tsx">
              <div className="grid md:grid-cols-2 gap-4" data-id="46aiqr54f" data-path="src/components/Chapter3Content.tsx">
                <Card data-id="9ea0piyle" data-path="src/components/Chapter3Content.tsx">
                  <CardHeader data-id="hdxskx805" data-path="src/components/Chapter3Content.tsx">
                    <CardTitle className="text-base text-right" data-id="zsisapdz6" data-path="src/components/Chapter3Content.tsx">التصوير العام</CardTitle>
                  </CardHeader>
                  <CardContent data-id="ormx3mwyx" data-path="src/components/Chapter3Content.tsx">
                    <ul className="text-sm space-y-1 text-right" data-id="2jjbo9xqv" data-path="src/components/Chapter3Content.tsx">
                      <li data-id="rb7pv86iq" data-path="src/components/Chapter3Content.tsx">• أنودات التنغستن الدوارة</li>
                      <li data-id="w0kr6zkn4" data-path="src/components/Chapter3Content.tsx">• بقع بؤرية متعددة الأحجام</li>
                      <li data-id="93nblw185" data-path="src/components/Chapter3Content.tsx">• قدرة حرارية عالية</li>
                    </ul>
                  </CardContent>
                </Card>
                
                <Card data-id="m5mrrra5g" data-path="src/components/Chapter3Content.tsx">
                  <CardHeader data-id="yxd6cughp" data-path="src/components/Chapter3Content.tsx">
                    <CardTitle className="text-base text-right" data-id="e9mwsphfh" data-path="src/components/Chapter3Content.tsx">تصوير الثدي</CardTitle>
                  </CardHeader>
                  <CardContent data-id="1n9eqp93u" data-path="src/components/Chapter3Content.tsx">
                    <ul className="text-sm space-y-1 text-right" data-id="m7nwa4jim" data-path="src/components/Chapter3Content.tsx">
                      <li data-id="ta064ga6c" data-path="src/components/Chapter3Content.tsx">• أنود الموليبدينوم/الروديوم</li>
                      <li data-id="n3vw697x5" data-path="src/components/Chapter3Content.tsx">• بقعة بؤرية صغيرة</li>
                      <li data-id="0dzfj7szo" data-path="src/components/Chapter3Content.tsx">• طيف طاقة منخفضة</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card className="mt-8" data-id="u8bkbfyx9" data-path="src/components/Chapter3Content.tsx">
        <CardHeader data-id="5cehfg8gq" data-path="src/components/Chapter3Content.tsx">
          <CardTitle className="text-right" data-id="wtm67z463" data-path="src/components/Chapter3Content.tsx">أهداف التعلم</CardTitle>
        </CardHeader>
        <CardContent data-id="1y83egpkd" data-path="src/components/Chapter3Content.tsx">
          <ul className="list-disc list-inside space-y-2 text-right" data-id="j4xapf0pu" data-path="src/components/Chapter3Content.tsx">
            <li data-id="ld1pr3tv1" data-path="src/components/Chapter3Content.tsx">فهم مكونات وعمل مجموعة الكاثود</li>
            <li data-id="2cljo6wf8" data-path="src/components/Chapter3Content.tsx">تعلم خصائص ومواد الأنود المختلفة</li>
            <li data-id="lwhqdu45d" data-path="src/components/Chapter3Content.tsx">إدراك أهمية إدارة الحرارة في تصميم الأنبوب</li>
            <li data-id="solfmh9lu" data-path="src/components/Chapter3Content.tsx">فهم تأثير زاوية الأنود على البقعة البؤرية</li>
            <li data-id="ek2swibxj" data-path="src/components/Chapter3Content.tsx">التعرف على التحديات الهندسية في التصميم</li>
          </ul>
        </CardContent>
      </Card>
    </div>);

};

export default Chapter3Content;