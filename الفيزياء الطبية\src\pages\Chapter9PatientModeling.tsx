import React from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  User,
  Box,
  Cylinder,
  Circle,
  Activity,
  Database,
  Grid3X3,
  AlertCircle,
  ChevronDown,
  BookOpen,
  Target,
  Key,
  FileText,
  HelpCircle } from
'lucide-react';

const Chapter9PatientModeling = () => {
  const [openSections, setOpenSections] = React.useState<{[key: string]: boolean;}>({});

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50" dir="rtl" data-id="dabh5dd22" data-path="src/pages/Chapter9PatientModeling.tsx">
      <Navigation data-id="nm339l3g4" data-path="src/pages/Chapter9PatientModeling.tsx" />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl" data-id="r27828gp3" data-path="src/pages/Chapter9PatientModeling.tsx">
        {/* Header */}
        <div className="text-center mb-12" data-id="ie9y8wnt9" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Badge variant="secondary" className="mb-4 text-lg px-6 py-2" data-id="nhubrrm10" data-path="src/pages/Chapter9PatientModeling.tsx">
            الفصل التاسع
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4" data-id="hfemkk8zc" data-path="src/pages/Chapter9PatientModeling.tsx">
            نمذجة هندسة المريض والوهم للمحاكاة
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed" data-id="la2411cke" data-path="src/pages/Chapter9PatientModeling.tsx">
            دراسة شاملة لأساليب النمذجة الهندسية للمرضى والأشباح المستخدمة في محاكاة النقل الإشعاعي
          </p>
        </div>

        {/* Learning Objectives */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-blue-500" data-id="qi3lapzv6" data-path="src/pages/Chapter9PatientModeling.tsx">
          <CardHeader data-id="7hur2s8rx" data-path="src/pages/Chapter9PatientModeling.tsx">
            <CardTitle className="flex items-center text-2xl text-blue-700" data-id="ze08jhbv8" data-path="src/pages/Chapter9PatientModeling.tsx">
              <Target className="ml-3 h-6 w-6" data-id="icg926dqx" data-path="src/pages/Chapter9PatientModeling.tsx" />
              أهداف التعلم
            </CardTitle>
          </CardHeader>
          <CardContent data-id="6atu88ejm" data-path="src/pages/Chapter9PatientModeling.tsx">
            <ul className="space-y-3 text-gray-700" data-id="06b55ras0" data-path="src/pages/Chapter9PatientModeling.tsx">
              <li className="flex items-start" data-id="ymks4nb8g" data-path="src/pages/Chapter9PatientModeling.tsx">
                <span className="text-blue-500 ml-2" data-id="8qaj611ia" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                فهم مبادئ الأشباح الهندسية البسيطة وتطبيقاتها في المحاكاة
              </li>
              <li className="flex items-start" data-id="xdwu3j8id" data-path="src/pages/Chapter9PatientModeling.tsx">
                <span className="text-blue-500 ml-2" data-id="x2e69y5i9" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                التمييز بين الأشباح الفيزيائية والحسابية ومزايا كل منها
              </li>
              <li className="flex items-start" data-id="cexoddqjl" data-path="src/pages/Chapter9PatientModeling.tsx">
                <span className="text-blue-500 ml-2" data-id="ccytssxf9" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                معرفة الأشباح المرجعية المعتمدة من ICRU/ICRP وخصائصها
              </li>
              <li className="flex items-start" data-id="2s8syafi9" data-path="src/pages/Chapter9PatientModeling.tsx">
                <span className="text-blue-500 ml-2" data-id="106abviu0" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                فهم كيفية إنشاء الأشباح المجسمة من بيانات التصوير الطبي
              </li>
              <li className="flex items-start" data-id="je9je9qjh" data-path="src/pages/Chapter9PatientModeling.tsx">
                <span className="text-blue-500 ml-2" data-id="ahpk299e3" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                إتقان تعريفات المواد وتركيبات الأنسجة حسب معايير ICRU
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Section 9.1 */}
        <Card className="mb-6 shadow-lg" data-id="kqtrirfn6" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Collapsible
            open={openSections['section91']}
            onOpenChange={() => toggleSection('section91')} data-id="apjna2j39" data-path="src/pages/Chapter9PatientModeling.tsx">

            <CollapsibleTrigger className="w-full" data-id="dofkya1y2" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="00t0evopq" data-path="src/pages/Chapter9PatientModeling.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="c0yhpi7k2" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="flex items-center" data-id="2sew16338" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <Box className="ml-3 h-6 w-6 text-green-600" data-id="38moqttso" data-path="src/pages/Chapter9PatientModeling.tsx" />
                    9.1 أشباح هندسية بسيطة
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section91'] ? 'rotate-180' : ''}`} data-id="yr8aqzoz7" data-path="src/pages/Chapter9PatientModeling.tsx" />
                </CardTitle>
                <CardDescription data-id="mqqu8geh4" data-path="src/pages/Chapter9PatientModeling.tsx">
                  الألواح، الأسطوانات، والكرات كأشباح أساسية للمحاكاة
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="6gzqgs5y8" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardContent className="space-y-6" data-id="7vdek2ceo" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="grid md:grid-cols-3 gap-6" data-id="aes4hs81y" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <Card className="border-l-4 border-l-blue-500" data-id="bepizabww" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="xb2h3u4zp" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="s11ynqoqx" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center ml-2" data-id="azoc1t1zs" data-path="src/pages/Chapter9PatientModeling.tsx">
                          📏
                        </span>
                        الألواح المسطحة
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="8rgugz5cj" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <ul className="space-y-2 text-sm text-gray-600" data-id="bt1cbemdd" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <li data-id="j3tpea62h" data-path="src/pages/Chapter9PatientModeling.tsx">• هندسة بسيطة للحسابات التحليلية</li>
                        <li data-id="rlomwqlm5" data-path="src/pages/Chapter9PatientModeling.tsx">• مناسبة لدراسة التوهين الخطي</li>
                        <li data-id="x6a9g9tzo" data-path="src/pages/Chapter9PatientModeling.tsx">• سهولة في التعامل والمعايرة</li>
                        <li data-id="juhn483zj" data-path="src/pages/Chapter9PatientModeling.tsx">• تطبيقات في الحماية الإشعاعية</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-green-500" data-id="stwm9r7xj" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="cajmhyp5s" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="io1erglun" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <Cylinder className="ml-2 h-5 w-5 text-green-600" data-id="u2r4wa8v4" data-path="src/pages/Chapter9PatientModeling.tsx" />
                        الأسطوانات
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="ukjo7fjgk" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <ul className="space-y-2 text-sm text-gray-600" data-id="qa899tmuj" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <li data-id="nt9919sgw" data-path="src/pages/Chapter9PatientModeling.tsx">• تمثيل أفضل للجذع البشري</li>
                        <li data-id="vab9xahxp" data-path="src/pages/Chapter9PatientModeling.tsx">• مفيدة لدراسة التبعثر</li>
                        <li data-id="jo6ck4g5a" data-path="src/pages/Chapter9PatientModeling.tsx">• حسابات أكثر تعقيداً من الألواح</li>
                        <li data-id="5r1wjwvfz" data-path="src/pages/Chapter9PatientModeling.tsx">• تطبيقات في الطب النووي</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-purple-500" data-id="8cir76082" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="ozby3sujn" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="ujwt1bxxn" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <Circle className="ml-2 h-5 w-5 text-purple-600" data-id="5pi7fzcm4" data-path="src/pages/Chapter9PatientModeling.tsx" />
                        الكرات
                      </CardTitle>
                    </CardHeader>
                    <CardContent data-id="nb0qvwrgj" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <ul className="space-y-2 text-sm text-gray-600" data-id="r6d0rbdq6" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <li data-id="wtn8jzrfe" data-path="src/pages/Chapter9PatientModeling.tsx">• تناظر كروي مثالي</li>
                        <li data-id="mjebcl1cr" data-path="src/pages/Chapter9PatientModeling.tsx">• مفيدة للحسابات النظرية</li>
                        <li data-id="udx0uj9id" data-path="src/pages/Chapter9PatientModeling.tsx">• تطبيقات في فيزياء الجسيمات</li>
                        <li data-id="qoq127pwj" data-path="src/pages/Chapter9PatientModeling.tsx">• نمذجة أعضاء كروية صغيرة</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <Alert data-id="c2o5ep38k" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <AlertCircle className="h-4 w-4" data-id="x70plicg7" data-path="src/pages/Chapter9PatientModeling.tsx" />
                  <AlertDescription data-id="i2swqprbw" data-path="src/pages/Chapter9PatientModeling.tsx">
                    الأشباح الهندسية البسيطة توفر نقطة انطلاق مثالية لفهم مبادئ النقل الإشعاعي قبل الانتقال إلى النماذج المعقدة.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 9.2 */}
        <Card className="mb-6 shadow-lg" data-id="7bzucxicg" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Collapsible
            open={openSections['section92']}
            onOpenChange={() => toggleSection('section92')} data-id="umk7xisf1" data-path="src/pages/Chapter9PatientModeling.tsx">

            <CollapsibleTrigger className="w-full" data-id="4l0odfntg" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="wkrv3n85v" data-path="src/pages/Chapter9PatientModeling.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="wp12j9128" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="flex items-center" data-id="jhdb46kj8" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <User className="ml-3 h-6 w-6 text-blue-600" data-id="5c73snee1" data-path="src/pages/Chapter9PatientModeling.tsx" />
                    9.2 الأشباح المجسمة: الفيزيائية والحسابية
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section92'] ? 'rotate-180' : ''}`} data-id="ieyqid7lp" data-path="src/pages/Chapter9PatientModeling.tsx" />
                </CardTitle>
                <CardDescription data-id="0wflqgjki" data-path="src/pages/Chapter9PatientModeling.tsx">
                  النماذج المتقدمة التي تحاكي التشريح البشري الحقيقي
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="ozhig2lmv" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardContent className="space-y-6" data-id="sggpzj3ue" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="ftevjbaj4" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <Card className="border-t-4 border-t-orange-500" data-id="299u4rjds" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="mj4tt3661" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="o3d1o3uj7" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <Activity className="ml-2 h-5 w-5 text-orange-600" data-id="e6z8lkup1" data-path="src/pages/Chapter9PatientModeling.tsx" />
                        9.2.1 أشباح مرجعية ICRU/ICRP
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4" data-id="wg8h2pizd" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div data-id="qssqpxzki" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="tkdixpjb9" data-path="src/pages/Chapter9PatientModeling.tsx">الأشباح المرجعية الأساسية:</h4>
                        <ul className="space-y-2 text-sm text-gray-600" data-id="iirn0q501" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <li data-id="xlw0ogd49" data-path="src/pages/Chapter9PatientModeling.tsx">• شبح الرجل المرجعي (70 كغ)</li>
                          <li data-id="9j80c1d6l" data-path="src/pages/Chapter9PatientModeling.tsx">• شبح المرأة المرجعية (60 كغ)</li>
                          <li data-id="88agtceut" data-path="src/pages/Chapter9PatientModeling.tsx">• أشباح الأطفال (أعمار مختلفة)</li>
                          <li data-id="0gu5ph1a5" data-path="src/pages/Chapter9PatientModeling.tsx">• أشباح الحوامل</li>
                        </ul>
                      </div>
                      <div data-id="fzr1ncjs3" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="ptkdizcrg" data-path="src/pages/Chapter9PatientModeling.tsx">المواصفات المعيارية:</h4>
                        <ul className="space-y-2 text-sm text-gray-600" data-id="s7i2edl9a" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <li data-id="uhozgmc09" data-path="src/pages/Chapter9PatientModeling.tsx">• كثافات الأنسجة المحددة</li>
                          <li data-id="xcmg6lxh5" data-path="src/pages/Chapter9PatientModeling.tsx">• التركيب العنصري للأعضاء</li>
                          <li data-id="t3a2189vo" data-path="src/pages/Chapter9PatientModeling.tsx">• الأبعاد التشريحية المعيارية</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-t-4 border-t-cyan-500" data-id="qicwxrgkn" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="qkgvnqbi3" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="flex items-center text-lg" data-id="p2n4r9mgg" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <Database className="ml-2 h-5 w-5 text-cyan-600" data-id="ajyt29dow" data-path="src/pages/Chapter9PatientModeling.tsx" />
                        9.2.2 الأشباح من بيانات التصوير الطبي
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4" data-id="mu9x0iz98" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div data-id="qyeqcbwjn" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="uphf9u7nk" data-path="src/pages/Chapter9PatientModeling.tsx">مصادر البيانات:</h4>
                        <ul className="space-y-2 text-sm text-gray-600" data-id="qygwqesko" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <li data-id="1zp4abqm0" data-path="src/pages/Chapter9PatientModeling.tsx">• بيانات التصوير المقطعي المحوسب (CT)</li>
                          <li data-id="cm55td3fa" data-path="src/pages/Chapter9PatientModeling.tsx">• صور الرنين المغناطيسي (MRI)</li>
                          <li data-id="uz8fofvx2" data-path="src/pages/Chapter9PatientModeling.tsx">• صور PET/SPECT</li>
                        </ul>
                      </div>
                      <div data-id="ba632q9sk" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <h4 className="font-semibold text-gray-800 mb-2" data-id="5qzf2z97f" data-path="src/pages/Chapter9PatientModeling.tsx">عملية التحويل:</h4>
                        <ul className="space-y-2 text-sm text-gray-600" data-id="cx47whpei" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <li data-id="o1otdddk1" data-path="src/pages/Chapter9PatientModeling.tsx">• تحويل أرقام Hounsfield إلى كثافة</li>
                          <li data-id="80f45bldl" data-path="src/pages/Chapter9PatientModeling.tsx">• تصنيف الأنسجة والأعضاء</li>
                          <li data-id="4qw853gox" data-path="src/pages/Chapter9PatientModeling.tsx">• إنشاء الشبكة ثلاثية الأبعاد</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Alert data-id="jsao3mgky" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <AlertCircle className="h-4 w-4" data-id="sz3kel7ms" data-path="src/pages/Chapter9PatientModeling.tsx" />
                  <AlertDescription data-id="oe1412w3o" data-path="src/pages/Chapter9PatientModeling.tsx">
                    الأشباح المجسمة توفر دقة أكبر في المحاكاة لكنها تتطلب موارد حاسوبية أكثر وبيانات تشريحية مفصلة.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 9.3 */}
        <Card className="mb-6 shadow-lg" data-id="od1qd80gi" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Collapsible
            open={openSections['section93']}
            onOpenChange={() => toggleSection('section93')} data-id="c6fzr5ijt" data-path="src/pages/Chapter9PatientModeling.tsx">

            <CollapsibleTrigger className="w-full" data-id="xya51vrwa" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="mkxhhlosy" data-path="src/pages/Chapter9PatientModeling.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="2jic7qo54" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="flex items-center" data-id="0k69fe0h6" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <Activity className="ml-3 h-6 w-6 text-purple-600" data-id="tnje7cm3n" data-path="src/pages/Chapter9PatientModeling.tsx" />
                    9.3 تعريفات المواد وتركيبات الأنسجة
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section93'] ? 'rotate-180' : ''}`} data-id="rv0ncvugl" data-path="src/pages/Chapter9PatientModeling.tsx" />
                </CardTitle>
                <CardDescription data-id="4bodd5nby" data-path="src/pages/Chapter9PatientModeling.tsx">
                  معايير ICRU-44 و ICRU-46 لتركيبات الأنسجة البشرية
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="af4g3wlol" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardContent className="space-y-6" data-id="e53ooxze5" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="aokn7yqy9" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <Card className="bg-gradient-to-br from-blue-50 to-blue-100" data-id="h0micxlgd" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="zfhad22a5" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="text-lg text-blue-800" data-id="5etmpg8hb" data-path="src/pages/Chapter9PatientModeling.tsx">ICRU-44: تركيبات الأنسجة</CardTitle>
                    </CardHeader>
                    <CardContent data-id="hp0wa6ctr" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div className="space-y-3" data-id="jdcjwcg3q" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <div data-id="6rj0bpldd" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="7n0uq09ro" data-path="src/pages/Chapter9PatientModeling.tsx">الأنسجة الرخوة:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="ogyknmk1s" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="r8bzg84tl" data-path="src/pages/Chapter9PatientModeling.tsx">• العضلات: 76.2% H₂O, 12.3% البروتين</li>
                            <li data-id="4c7dtg98i" data-path="src/pages/Chapter9PatientModeling.tsx">• الدهون: 11.5% H₂O, 85.7% الدهون</li>
                            <li data-id="ditrk396w" data-path="src/pages/Chapter9PatientModeling.tsx">• الدم: 83.2% H₂O, 16.0% البروتين</li>
                          </ul>
                        </div>
                        <div data-id="gq0teedb5" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="1k9fv31ad" data-path="src/pages/Chapter9PatientModeling.tsx">الأنسجة الصلبة:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="8uxp1t0xp" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="k3r8vr785" data-path="src/pages/Chapter9PatientModeling.tsx">• العظام الكثيفة: كثافة 1.92 g/cm³</li>
                            <li data-id="uoczyiq8v" data-path="src/pages/Chapter9PatientModeling.tsx">• العظام الإسفنجية: كثافة 1.18 g/cm³</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-green-50 to-green-100" data-id="mgtt8uf9f" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="a7cnz8xgc" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="text-lg text-green-800" data-id="sxwztieqq" data-path="src/pages/Chapter9PatientModeling.tsx">ICRU-46: معاملات التفاعل</CardTitle>
                    </CardHeader>
                    <CardContent data-id="y9gthj4dw" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div className="space-y-3" data-id="c4db4vm3t" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <div data-id="tt0uymq4q" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="06y5nshm2" data-path="src/pages/Chapter9PatientModeling.tsx">معاملات التوهين:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="97g67cp3i" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="udoqsjdpx" data-path="src/pages/Chapter9PatientModeling.tsx">• معامل التوهين الكتلي</li>
                            <li data-id="56dk6hl4j" data-path="src/pages/Chapter9PatientModeling.tsx">• معامل التوهين الخطي</li>
                            <li data-id="lzgtl2yw3" data-path="src/pages/Chapter9PatientModeling.tsx">• معامل نقل الطاقة</li>
                          </ul>
                        </div>
                        <div data-id="gm68q85nl" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="o85uip83j" data-path="src/pages/Chapter9PatientModeling.tsx">خصائص الأنسجة:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="zt20a0isb" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="gy5039cy9" data-path="src/pages/Chapter9PatientModeling.tsx">• العدد الذري الفعال</li>
                            <li data-id="u838crvts" data-path="src/pages/Chapter9PatientModeling.tsx">• الكثافة الإلكترونية</li>
                            <li data-id="dqf5gyvz3" data-path="src/pages/Chapter9PatientModeling.tsx">• طاقة الإثارة المتوسطة</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 9.4 */}
        <Card className="mb-6 shadow-lg" data-id="ka0y4pu4c" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Collapsible
            open={openSections['section94']}
            onOpenChange={() => toggleSection('section94')} data-id="bg4r5fjbr" data-path="src/pages/Chapter9PatientModeling.tsx">

            <CollapsibleTrigger className="w-full" data-id="xp5rjpc9w" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="f7cj1eczb" data-path="src/pages/Chapter9PatientModeling.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="vueh7dozh" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="flex items-center" data-id="cnqrjep2f" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <Grid3X3 className="ml-3 h-6 w-6 text-indigo-600" data-id="k46klnmch" data-path="src/pages/Chapter9PatientModeling.tsx" />
                    9.4 هندسة التمثيل الشبكي والحدودي
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section94'] ? 'rotate-180' : ''}`} data-id="gg7m6fy61" data-path="src/pages/Chapter9PatientModeling.tsx" />
                </CardTitle>
                <CardDescription data-id="58q31danm" data-path="src/pages/Chapter9PatientModeling.tsx">
                  طرق تمثيل الهندسة في المحاكاة الحاسوبية
                </CardDescription>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="peu7tq00j" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardContent className="space-y-6" data-id="yslwgblyt" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="cof9l6esb" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <Card className="border-l-4 border-l-indigo-500" data-id="3wcmwbt86" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="frzqhmr8q" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="text-lg" data-id="ohdga74rh" data-path="src/pages/Chapter9PatientModeling.tsx">التمثيل الشبكي (Voxel-based)</CardTitle>
                    </CardHeader>
                    <CardContent data-id="b95axcmr1" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div className="space-y-3" data-id="t3jrvciqg" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <div data-id="0131wr3yu" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="7cc3dsvcl" data-path="src/pages/Chapter9PatientModeling.tsx">المزايا:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="13h4qrk0x" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="1o86a26hr" data-path="src/pages/Chapter9PatientModeling.tsx">• سهولة في التنفيذ البرمجي</li>
                            <li data-id="iu69tb9h8" data-path="src/pages/Chapter9PatientModeling.tsx">• تكامل مباشر مع بيانات التصوير</li>
                            <li data-id="vtvquoz7e" data-path="src/pages/Chapter9PatientModeling.tsx">• دقة في التفاصيل التشريحية</li>
                          </ul>
                        </div>
                        <div data-id="hjc2ntzpn" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="2lkjhg2jt" data-path="src/pages/Chapter9PatientModeling.tsx">العيوب:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="4x5uf7x7g" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="7rhgjc53v" data-path="src/pages/Chapter9PatientModeling.tsx">• استهلاك كبير للذاكرة</li>
                            <li data-id="vgr0qc1aw" data-path="src/pages/Chapter9PatientModeling.tsx">• أسطح متدرجة (stepwise)</li>
                            <li data-id="trl5cuk6q" data-path="src/pages/Chapter9PatientModeling.tsx">• صعوبة في التشويه</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-teal-500" data-id="ssiogy589" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <CardHeader data-id="iunoj6sib" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <CardTitle className="text-lg" data-id="i0ipbft30" data-path="src/pages/Chapter9PatientModeling.tsx">التمثيل الحدودي (Boundary-based)</CardTitle>
                    </CardHeader>
                    <CardContent data-id="frkx53lmm" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <div className="space-y-3" data-id="4ooyy4d7d" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <div data-id="wlrc25p1g" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="sh644njum" data-path="src/pages/Chapter9PatientModeling.tsx">المزايا:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="3t62y26e6" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="uy75nzyor" data-path="src/pages/Chapter9PatientModeling.tsx">• أسطح ناعمة ومستمرة</li>
                            <li data-id="fqj7g7bpo" data-path="src/pages/Chapter9PatientModeling.tsx">• مرونة في التشويه</li>
                            <li data-id="n7gk8esmm" data-path="src/pages/Chapter9PatientModeling.tsx">• كفاءة في استخدام الذاكرة</li>
                          </ul>
                        </div>
                        <div data-id="x5paht93k" data-path="src/pages/Chapter9PatientModeling.tsx">
                          <h4 className="font-semibold text-gray-800" data-id="b19s3qlin" data-path="src/pages/Chapter9PatientModeling.tsx">العيوب:</h4>
                          <ul className="text-sm text-gray-600 mr-4" data-id="cjax2vx9o" data-path="src/pages/Chapter9PatientModeling.tsx">
                            <li data-id="atrhee7lr" data-path="src/pages/Chapter9PatientModeling.tsx">• تعقيد في التنفيذ</li>
                            <li data-id="dj8a38p9q" data-path="src/pages/Chapter9PatientModeling.tsx">• صعوبة في النمذجة المعقدة</li>
                            <li data-id="f7estexif" data-path="src/pages/Chapter9PatientModeling.tsx">• حسابات هندسية معقدة</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Section 9.5 */}
        <Card className="mb-6 shadow-lg" data-id="xzxk96dki" data-path="src/pages/Chapter9PatientModeling.tsx">
          <Collapsible
            open={openSections['section95']}
            onOpenChange={() => toggleSection('section95')} data-id="r1nxb12ws" data-path="src/pages/Chapter9PatientModeling.tsx">

            <CollapsibleTrigger className="w-full" data-id="0dmskg1er" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardHeader className="hover:bg-gray-50 transition-colors cursor-pointer" data-id="yyivv7eis" data-path="src/pages/Chapter9PatientModeling.tsx">
                <CardTitle className="flex items-center justify-between text-2xl" data-id="b1w5cp34x" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="flex items-center" data-id="ee58r199t" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <AlertCircle className="ml-3 h-6 w-6 text-red-600" data-id="1wr7bxbgi" data-path="src/pages/Chapter9PatientModeling.tsx" />
                    9.5 التحديات في النمذجة التشريحية الدقيقة
                  </div>
                  <ChevronDown className={`h-5 w-5 transform transition-transform ${openSections['section95'] ? 'rotate-180' : ''}`} data-id="heixf8be8" data-path="src/pages/Chapter9PatientModeling.tsx" />
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent data-id="u7od0hbm5" data-path="src/pages/Chapter9PatientModeling.tsx">
              <CardContent data-id="0zm770eks" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="grid md:grid-cols-2 gap-6" data-id="ibuzswdex" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <div className="space-y-4" data-id="9szpbu3so" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <h3 className="text-lg font-semibold text-red-700" data-id="0ghcughle" data-path="src/pages/Chapter9PatientModeling.tsx">التحديات التقنية</h3>
                    <ul className="space-y-2 text-gray-700" data-id="w07tcviq6" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <li className="flex items-start" data-id="s6p21i5xj" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-red-500 ml-2 mt-1" data-id="op4bcsa38" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="sokh2i65q" data-path="src/pages/Chapter9PatientModeling.tsx">التباين بين الأفراد في الحجم والشكل</span>
                      </li>
                      <li className="flex items-start" data-id="882s9aowa" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-red-500 ml-2 mt-1" data-id="66wzte57l" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="agq9qy3gu" data-path="src/pages/Chapter9PatientModeling.tsx">حركة الأعضاء أثناء العلاج</span>
                      </li>
                      <li className="flex items-start" data-id="j3yes3wkj" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-red-500 ml-2 mt-1" data-id="pu429n5d5" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="seoeh0sfg" data-path="src/pages/Chapter9PatientModeling.tsx">دقة بيانات التصوير المحدودة</span>
                      </li>
                      <li className="flex items-start" data-id="4fad42874" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-red-500 ml-2 mt-1" data-id="twxc6ak1n" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="rgtbs9lbr" data-path="src/pages/Chapter9PatientModeling.tsx">التغيرات الفسيولوجية مع الوقت</span>
                      </li>
                    </ul>
                  </div>
                  <div className="space-y-4" data-id="srovgxkoj" data-path="src/pages/Chapter9PatientModeling.tsx">
                    <h3 className="text-lg font-semibold text-blue-700" data-id="22go1ndfz" data-path="src/pages/Chapter9PatientModeling.tsx">الحلول المقترحة</h3>
                    <ul className="space-y-2 text-gray-700" data-id="grx4ibm0c" data-path="src/pages/Chapter9PatientModeling.tsx">
                      <li className="flex items-start" data-id="owem9bw57" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-blue-500 ml-2 mt-1" data-id="hibniae5z" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="khz46wlja" data-path="src/pages/Chapter9PatientModeling.tsx">الأشباح القابلة للتشويه</span>
                      </li>
                      <li className="flex items-start" data-id="ybmd5uku0" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-blue-500 ml-2 mt-1" data-id="9gs05knph" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="vogsx44ko" data-path="src/pages/Chapter9PatientModeling.tsx">التصوير المتتالي 4D</span>
                      </li>
                      <li className="flex items-start" data-id="i5j8ptlsq" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-blue-500 ml-2 mt-1" data-id="sc11xrjjd" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="g3d1prtr3" data-path="src/pages/Chapter9PatientModeling.tsx">تقنيات الذكاء الاصطناعي</span>
                      </li>
                      <li className="flex items-start" data-id="aoh0zeeuw" data-path="src/pages/Chapter9PatientModeling.tsx">
                        <span className="text-blue-500 ml-2 mt-1" data-id="p7l95q5w3" data-path="src/pages/Chapter9PatientModeling.tsx">•</span>
                        <span data-id="aq5wt3vd6" data-path="src/pages/Chapter9PatientModeling.tsx">المحاكاة التكيفية</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Key Terms */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-yellow-500" data-id="43g7pizb6" data-path="src/pages/Chapter9PatientModeling.tsx">
          <CardHeader data-id="idz1l9lex" data-path="src/pages/Chapter9PatientModeling.tsx">
            <CardTitle className="flex items-center text-2xl text-yellow-700" data-id="eo9yl9e1n" data-path="src/pages/Chapter9PatientModeling.tsx">
              <Key className="ml-3 h-6 w-6" data-id="40j6gk16s" data-path="src/pages/Chapter9PatientModeling.tsx" />
              المصطلحات الرئيسية
            </CardTitle>
          </CardHeader>
          <CardContent data-id="otkzbu2z1" data-path="src/pages/Chapter9PatientModeling.tsx">
            <div className="grid md:grid-cols-2 gap-6" data-id="bj0famep1" data-path="src/pages/Chapter9PatientModeling.tsx">
              <div className="space-y-3" data-id="lf88k9fw9" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="border-r-4 border-r-blue-400 pr-4" data-id="3zosefbvf" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="vfytassvw" data-path="src/pages/Chapter9PatientModeling.tsx">الشبح (Phantom)</h4>
                  <p className="text-sm text-gray-600" data-id="75arw1k7x" data-path="src/pages/Chapter9PatientModeling.tsx">نموذج يحاكي خصائص جسم الإنسان لأغراض المحاكاة</p>
                </div>
                <div className="border-r-4 border-r-green-400 pr-4" data-id="wq70eld11" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="98hfx963b" data-path="src/pages/Chapter9PatientModeling.tsx">الشبح المجسم (Anthropomorphic)</h4>
                  <p className="text-sm text-gray-600" data-id="4vj1onrje" data-path="src/pages/Chapter9PatientModeling.tsx">نموذج يحاكي الشكل والتركيب التشريحي البشري</p>
                </div>
                <div className="border-r-4 border-r-purple-400 pr-4" data-id="6cbx0olal" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="193jbrz99" data-path="src/pages/Chapter9PatientModeling.tsx">الفوكسل (Voxel)</h4>
                  <p className="text-sm text-gray-600" data-id="zo72ijvxm" data-path="src/pages/Chapter9PatientModeling.tsx">وحدة حجمية ثلاثية الأبعاد في الشبكة الحاسوبية</p>
                </div>
              </div>
              <div className="space-y-3" data-id="f1f8c6erl" data-path="src/pages/Chapter9PatientModeling.tsx">
                <div className="border-r-4 border-r-red-400 pr-4" data-id="seah6ouau" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="eyh099x72" data-path="src/pages/Chapter9PatientModeling.tsx">رقم Hounsfield</h4>
                  <p className="text-sm text-gray-600" data-id="ykcwzcuqj" data-path="src/pages/Chapter9PatientModeling.tsx">مقياس الكثافة في صور التصوير المقطعي المحوسب</p>
                </div>
                <div className="border-r-4 border-r-indigo-400 pr-4" data-id="qitzc0dcx" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="kbuf4my6s" data-path="src/pages/Chapter9PatientModeling.tsx">العدد الذري الفعال</h4>
                  <p className="text-sm text-gray-600" data-id="r49y8hstr" data-path="src/pages/Chapter9PatientModeling.tsx">متوسط مرجح للعدد الذري في المواد المركبة</p>
                </div>
                <div className="border-r-4 border-r-teal-400 pr-4" data-id="9sf0r6wgy" data-path="src/pages/Chapter9PatientModeling.tsx">
                  <h4 className="font-semibold text-gray-800" data-id="6zj97t9v9" data-path="src/pages/Chapter9PatientModeling.tsx">الكثافة الإلكترونية</h4>
                  <p className="text-sm text-gray-600" data-id="hxmbxi6jm" data-path="src/pages/Chapter9PatientModeling.tsx">عدد الإلكترونات لكل وحدة حجم في المادة</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* References */}
        <Card className="mb-8 shadow-lg border-t-4 border-t-green-500" data-id="smslugqf7" data-path="src/pages/Chapter9PatientModeling.tsx">
          <CardHeader data-id="pxky1wfeh" data-path="src/pages/Chapter9PatientModeling.tsx">
            <CardTitle className="flex items-center text-2xl text-green-700" data-id="nlxrqsms1" data-path="src/pages/Chapter9PatientModeling.tsx">
              <FileText className="ml-3 h-6 w-6" data-id="shlxbtk2x" data-path="src/pages/Chapter9PatientModeling.tsx" />
              المراجع
            </CardTitle>
          </CardHeader>
          <CardContent data-id="hfv26fbyw" data-path="src/pages/Chapter9PatientModeling.tsx">
            <ul className="space-y-2 text-gray-700" data-id="zx17pzluq" data-path="src/pages/Chapter9PatientModeling.tsx">
              <li data-id="9anqby9ya" data-path="src/pages/Chapter9PatientModeling.tsx">1. ICRU Report 44: Tissue Substitutes in Radiation Dosimetry and Measurement (1989)</li>
              <li data-id="0msfnokqr" data-path="src/pages/Chapter9PatientModeling.tsx">2. ICRU Report 46: Photon, Electron, Proton and Neutron Interaction Data (1992)</li>
              <li data-id="wekmxsxwe" data-path="src/pages/Chapter9PatientModeling.tsx">3. ICRP Publication 89: Basic Anatomical and Physiological Data (2002)</li>
              <li data-id="bnziue120" data-path="src/pages/Chapter9PatientModeling.tsx">4. Segars, W.P., et al.: 4D XCAT phantom for multimodality imaging research. Med Phys 37, 4902-4915 (2010)</li>
              <li data-id="emm450pxc" data-path="src/pages/Chapter9PatientModeling.tsx">5. Xu, X.G.: An exponential growth of computational phantom research. Phys Med Biol 59, R233-R302 (2014)</li>
            </ul>
          </CardContent>
        </Card>

        {/* Problems */}
        <Card className="shadow-lg border-t-4 border-t-orange-500" data-id="2od7w8izq" data-path="src/pages/Chapter9PatientModeling.tsx">
          <CardHeader data-id="70mgxcok7" data-path="src/pages/Chapter9PatientModeling.tsx">
            <CardTitle className="flex items-center text-2xl text-orange-700" data-id="21j9itwpa" data-path="src/pages/Chapter9PatientModeling.tsx">
              <HelpCircle className="ml-3 h-6 w-6" data-id="m9h89pvff" data-path="src/pages/Chapter9PatientModeling.tsx" />
              المشكلات والتمارين
            </CardTitle>
          </CardHeader>
          <CardContent data-id="h7omzrtt9" data-path="src/pages/Chapter9PatientModeling.tsx">
            <div className="space-y-6" data-id="eqr24hsy3" data-path="src/pages/Chapter9PatientModeling.tsx">
              <div className="bg-orange-50 p-4 rounded-lg" data-id="mfkjrcbs3" data-path="src/pages/Chapter9PatientModeling.tsx">
                <h4 className="font-semibold text-orange-800 mb-2" data-id="lzp2q8vn8" data-path="src/pages/Chapter9PatientModeling.tsx">المشكلة 1:</h4>
                <p className="text-gray-700" data-id="2ojixjvdb" data-path="src/pages/Chapter9PatientModeling.tsx">
                  احسب معامل التوهين الخطي للعضلات عند طاقة 100 keV باستخدام بيانات ICRU-44. 
                  كثافة العضلات = 1.05 g/cm³، معامل التوهين الكتلي = 0.164 cm²/g.
                </p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg" data-id="xehjdxib9" data-path="src/pages/Chapter9PatientModeling.tsx">
                <h4 className="font-semibold text-blue-800 mb-2" data-id="e0hrr73wo" data-path="src/pages/Chapter9PatientModeling.tsx">المشكلة 2:</h4>
                <p className="text-gray-700" data-id="gwtdbddgc" data-path="src/pages/Chapter9PatientModeling.tsx">
                  صمم شبحاً أسطوانياً بقطر 30 cm وارتفاع 20 cm يحاكي الجذع البشري. 
                  حدد التركيب النسجي وحسب النسب الحجمية للأعضاء المختلفة.
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg" data-id="k1j87bvcy" data-path="src/pages/Chapter9PatientModeling.tsx">
                <h4 className="font-semibold text-green-800 mb-2" data-id="8qnop90kn" data-path="src/pages/Chapter9PatientModeling.tsx">المشكلة 3:</h4>
                <p className="text-gray-700" data-id="72us8bqff" data-path="src/pages/Chapter9PatientModeling.tsx">
                  قارن بين دقة النتائج عند استخدام شبح مكعبي بحجم فوكسل 1 mm³ 
                  مقابل شبح بحجم فوكسل 5 mm³ لنفس الهندسة.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default Chapter9PatientModeling;