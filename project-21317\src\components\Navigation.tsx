import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import {
  Menu,
  Atom,
  Camera,
  Shield,
  Image,
  BookOpen,
  Info,
  Activity,
  Zap } from
'lucide-react';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();

  const navigationItems = [
  { path: '/', label: 'Home', icon: Activity },
  { path: '/radiation-physics', label: 'Radiation Physics', icon: Atom },
  { path: '/imaging-modalities', label: 'Imaging Modalities', icon: Camera },
  { path: '/dosimetry-protection', label: 'Dosimetry & Protection', icon: Shield },
  { path: '/image-quality', label: 'Image Quality', icon: Image },
  { path: '/glossary', label: 'Glossary', icon: BookO<PERSON> },
  { path: '/about', label: 'About', icon: Info }];


  const isActive = (path: string) => location.pathname === path;

  const NavLink = ({ item, mobile = false }: {item: typeof navigationItems[0];mobile?: boolean;}) => {
    const Icon = item.icon;
    return (
      <Link
        to={item.path}
        onClick={() => mobile && setIsOpen(false)}
        className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
        isActive(item.path) ?
        'bg-blue-600 text-white shadow-lg' :
        'text-gray-600 hover:text-blue-600 hover:bg-blue-50'} ${
        mobile ? 'w-full justify-start' : ''}`} data-id="0mlihtm2p" data-path="src/components/Navigation.tsx">

        <Icon size={18} data-id="c9pgzvvzs" data-path="src/components/Navigation.tsx" />
        <span className={mobile ? 'text-base' : 'text-sm font-medium'} data-id="fzdpoe80n" data-path="src/components/Navigation.tsx">{item.label}</span>
      </Link>);

  };

  return (
    <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b shadow-sm" data-id="bkvn96os4" data-path="src/components/Navigation.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="2u788ehy8" data-path="src/components/Navigation.tsx">
        <div className="flex justify-between items-center h-16" data-id="ue0twze9k" data-path="src/components/Navigation.tsx">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-2" data-id="bwt1bh90b" data-path="src/components/Navigation.tsx">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center" data-id="8njh4k729" data-path="src/components/Navigation.tsx">
              <Zap className="w-6 h-6 text-white" data-id="wpvr1m09d" data-path="src/components/Navigation.tsx" />
            </div>
            <div className="hidden sm:block" data-id="jfdjz0ota" data-path="src/components/Navigation.tsx">
              <h1 className="text-xl font-bold text-gray-900" data-id="yywn3lqfv" data-path="src/components/Navigation.tsx">MedPhys Sim</h1>
              <p className="text-xs text-gray-500" data-id="qq58r6zv8" data-path="src/components/Navigation.tsx">Interactive Medical Physics</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center gap-1" data-id="bio8raied" data-path="src/components/Navigation.tsx">
            {navigationItems.map((item) =>
            <NavLink key={item.path} item={item} data-id="3y6t15yv2" data-path="src/components/Navigation.tsx" />
            )}
          </div>

          {/* Mobile Navigation */}
          <Sheet open={isOpen} onOpenChange={setIsOpen} data-id="yaki5jhqg" data-path="src/components/Navigation.tsx">
            <SheetTrigger asChild className="lg:hidden" data-id="kqmqo4iqs" data-path="src/components/Navigation.tsx">
              <Button variant="outline" size="icon" data-id="p0k9jia32" data-path="src/components/Navigation.tsx">
                <Menu size={20} data-id="ih4ccojmn" data-path="src/components/Navigation.tsx" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-80" data-id="dtt44h4i6" data-path="src/components/Navigation.tsx">
              <div className="flex flex-col gap-4 pt-6" data-id="5kkbbq5d6" data-path="src/components/Navigation.tsx">
                <div className="flex items-center gap-2 mb-4" data-id="ja5am4fb8" data-path="src/components/Navigation.tsx">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center" data-id="d5ba1nrpx" data-path="src/components/Navigation.tsx">
                    <Zap className="w-5 h-5 text-white" data-id="uqg8xy544" data-path="src/components/Navigation.tsx" />
                  </div>
                  <div data-id="o3n8waun9" data-path="src/components/Navigation.tsx">
                    <h2 className="font-bold text-gray-900" data-id="c2xml0ett" data-path="src/components/Navigation.tsx">MedPhys Sim</h2>
                    <p className="text-xs text-gray-500" data-id="du2ukues8" data-path="src/components/Navigation.tsx">Interactive Medical Physics</p>
                  </div>
                </div>
                
                <div className="flex flex-col gap-2" data-id="q3l54rmd1" data-path="src/components/Navigation.tsx">
                  {navigationItems.map((item) =>
                  <NavLink key={item.path} item={item} mobile data-id="9j114t7ak" data-path="src/components/Navigation.tsx" />
                  )}
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>);

};

export default Navigation;