import { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';

const ComptonSimulation = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  const [photonEnergy, setPhotonEnergy] = useState([250]); // keV
  const [scatteringAngle, setScatteringAngle] = useState([60]); // degrees
  const [isRunning, setIsRunning] = useState(false);

  // Animation state
  const [photonPosition, setPhotonPosition] = useState({ x: 50, y: 250 });
  const [scatteredPhotonPos, setScatteredPhotonPos] = useState({ x: 300, y: 250 });
  const [electronPosition, setElectronPosition] = useState({ x: 300, y: 250 });
  const [animationPhase, setAnimationPhase] = useState('ready'); // ready, collision, complete

  // Calculate Compton scattering parameters
  const electronRestEnergy = 511; // keV
  const scatteredPhotonEnergy = photonEnergy[0] / (1 + photonEnergy[0] / electronRestEnergy * (1 - Math.cos(scatteringAngle[0] * Math.PI / 180)));
  const electronKineticEnergy = photonEnergy[0] - scatteredPhotonEnergy;

  // Calculate electron scattering angle using conservation of momentum
  const electronAngle = Math.atan(
    Math.sin(scatteringAngle[0] * Math.PI / 180) / (
    photonEnergy[0] / electronRestEnergy + 1 - Math.cos(scatteringAngle[0] * Math.PI / 180))
  ) * 180 / Math.PI;

  // Animation loop
  useEffect(() => {
    if (isRunning && canvasRef.current) {
      const animate = () => {
        drawSimulation();
        updateAnimation();
        animationRef.current = requestAnimationFrame(animate);
      };
      animate();
    } else if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRunning, photonPosition, scatteredPhotonPos, electronPosition, animationPhase, scatteringAngle]);

  const updateAnimation = () => {
    if (animationPhase === 'ready' && photonPosition.x < 290) {
      setPhotonPosition((prev) => ({ ...prev, x: prev.x + 2 }));
    } else if (animationPhase === 'ready' && photonPosition.x >= 290) {
      setAnimationPhase('collision');
      setTimeout(() => {
        setAnimationPhase('complete');

        // Animate scattered photon
        const photonAngleRad = scatteringAngle[0] * Math.PI / 180;
        const photonDistance = 100;
        setScatteredPhotonPos({
          x: 300 + Math.cos(photonAngleRad) * photonDistance,
          y: 250 - Math.sin(photonAngleRad) * photonDistance
        });

        // Animate recoil electron
        const electronAngleRad = -electronAngle * Math.PI / 180;
        const electronDistance = electronKineticEnergy * 0.3;
        setElectronPosition({
          x: 300 + Math.cos(electronAngleRad) * electronDistance,
          y: 250 - Math.sin(electronAngleRad) * electronDistance
        });
      }, 500);
    }
  };

  const drawSimulation = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw free electron
    if (animationPhase === 'ready' || animationPhase === 'collision') {
      ctx.fillStyle = '#ef4444';
      ctx.beginPath();
      ctx.arc(300, 250, 8, 0, 2 * Math.PI);
      ctx.fill();
    }

    // Draw incident photon
    if (animationPhase !== 'complete') {
      ctx.fillStyle = '#fbbf24';
      ctx.beginPath();
      ctx.arc(photonPosition.x, photonPosition.y, 6, 0, 2 * Math.PI);
      ctx.fill();

      // Draw incident photon wave
      ctx.strokeStyle = '#fbbf24';
      ctx.lineWidth = 2;
      ctx.beginPath();
      const waveLength = 15;
      const amplitude = 6;
      for (let x = 50; x < photonPosition.x; x += 2) {
        const y = 250 + amplitude * Math.sin((x - 50) / waveLength * 2 * Math.PI);
        if (x === 50) ctx.moveTo(x, y);else
        ctx.lineTo(x, y);
      }
      ctx.stroke();
    }

    // Draw scattered photon
    if (animationPhase === 'complete') {
      ctx.fillStyle = '#f97316';
      ctx.beginPath();
      ctx.arc(scatteredPhotonPos.x, scatteredPhotonPos.y, 5, 0, 2 * Math.PI);
      ctx.fill();

      // Draw scattered photon wave
      ctx.strokeStyle = '#f97316';
      ctx.lineWidth = 2;
      ctx.beginPath();
      const distance = Math.sqrt(Math.pow(scatteredPhotonPos.x - 300, 2) + Math.pow(scatteredPhotonPos.y - 250, 2));
      const angle = Math.atan2(250 - scatteredPhotonPos.y, scatteredPhotonPos.x - 300);
      const waveLength = 20;
      const amplitude = 4;

      for (let d = 0; d < distance; d += 3) {
        const x = 300 + Math.cos(angle) * d;
        const y = 250 - Math.sin(angle) * d;
        const waveY = y + amplitude * Math.sin(d / waveLength * 2 * Math.PI);
        if (d === 0) ctx.moveTo(x, waveY);else
        ctx.lineTo(x, waveY);
      }
      ctx.stroke();
    }

    // Draw recoil electron
    if (animationPhase === 'complete') {
      ctx.fillStyle = '#dc2626';
      ctx.beginPath();
      ctx.arc(electronPosition.x, electronPosition.y, 6, 0, 2 * Math.PI);
      ctx.fill();

      // Draw electron trajectory
      ctx.strokeStyle = '#dc2626';
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.moveTo(300, 250);
      ctx.lineTo(electronPosition.x, electronPosition.y);
      ctx.stroke();
      ctx.setLineDash([]);
    }

    // Draw collision effects
    if (animationPhase === 'collision') {
      ctx.strokeStyle = '#fbbf24';
      ctx.lineWidth = 3;
      for (let i = 0; i < 6; i++) {
        const angle = i * 60 * Math.PI / 180;
        const length = 15;
        ctx.beginPath();
        ctx.moveTo(300, 250);
        ctx.lineTo(300 + Math.cos(angle) * length, 250 + Math.sin(angle) * length);
        ctx.stroke();
      }
    }

    // Draw scattering angle arc
    if (animationPhase === 'complete') {
      ctx.strokeStyle = '#6b7280';
      ctx.lineWidth = 1;
      ctx.setLineDash([3, 3]);
      ctx.beginPath();
      ctx.arc(300, 250, 40, 0, -scatteringAngle[0] * Math.PI / 180, true);
      ctx.stroke();
      ctx.setLineDash([]);

      // Angle label
      ctx.fillStyle = '#6b7280';
      ctx.font = '12px Arial';
      ctx.fillText(`θ = ${scatteringAngle[0]}°`, 350, 230);
    }

    // Draw labels
    ctx.fillStyle = '#374151';
    ctx.font = '12px Arial';
    ctx.fillText('Incident Photon', 80, 230);
    ctx.fillText(`${photonEnergy[0]} keV`, 80, 245);

    if (animationPhase === 'complete') {
      ctx.fillText('Scattered Photon', scatteredPhotonPos.x + 10, scatteredPhotonPos.y - 10);
      ctx.fillText(`${scatteredPhotonEnergy.toFixed(1)} keV`, scatteredPhotonPos.x + 10, scatteredPhotonPos.y + 5);
      ctx.fillText('Recoil Electron', electronPosition.x + 10, electronPosition.y + 20);
      ctx.fillText(`${electronKineticEnergy.toFixed(1)} keV`, electronPosition.x + 10, electronPosition.y + 35);
    }
  };

  const resetAnimation = () => {
    setPhotonPosition({ x: 50, y: 250 });
    setScatteredPhotonPos({ x: 300, y: 250 });
    setElectronPosition({ x: 300, y: 250 });
    setAnimationPhase('ready');
    setIsRunning(false);
  };

  const toggleAnimation = () => {
    if (animationPhase === 'complete') {
      resetAnimation();
    } else {
      setIsRunning(!isRunning);
    }
  };

  return (
    <div className="space-y-6" data-id="28ck6xs3s" data-path="src/components/simulations/ComptonSimulation.tsx">
      {/* Canvas */}
      <div className="relative bg-white rounded-lg border" data-id="4w1a8negx" data-path="src/components/simulations/ComptonSimulation.tsx">
        <canvas
          ref={canvasRef}
          width={600}
          height={400}
          className="w-full h-80 rounded-lg" data-id="s6ak77v7u" data-path="src/components/simulations/ComptonSimulation.tsx" />

        
        {/* Control Buttons */}
        <div className="absolute top-4 right-4 flex gap-2" data-id="m7y8sw3bt" data-path="src/components/simulations/ComptonSimulation.tsx">
          <Button size="sm" onClick={toggleAnimation} data-id="sz4y3oyxh" data-path="src/components/simulations/ComptonSimulation.tsx">
            {isRunning ? <Pause className="w-4 h-4" data-id="dnxxbolew" data-path="src/components/simulations/ComptonSimulation.tsx" /> : <Play className="w-4 h-4" data-id="6emptlcre" data-path="src/components/simulations/ComptonSimulation.tsx" />}
          </Button>
          <Button size="sm" variant="outline" onClick={resetAnimation} data-id="5pee47l65" data-path="src/components/simulations/ComptonSimulation.tsx">
            <RotateCcw className="w-4 h-4" data-id="q0p7jqn87" data-path="src/components/simulations/ComptonSimulation.tsx" />
          </Button>
        </div>
      </div>

      {/* Controls */}
      <div className="grid md:grid-cols-2 gap-6" data-id="6e5lklw0w" data-path="src/components/simulations/ComptonSimulation.tsx">
        <Card data-id="fbyqf15ly" data-path="src/components/simulations/ComptonSimulation.tsx">
          <CardContent className="pt-6 space-y-4" data-id="szg2qvryu" data-path="src/components/simulations/ComptonSimulation.tsx">
            <div data-id="9yi9mbj44" data-path="src/components/simulations/ComptonSimulation.tsx">
              <Label className="text-sm font-medium" data-id="5upvxudxv" data-path="src/components/simulations/ComptonSimulation.tsx">Incident Photon Energy (keV)</Label>
              <Slider
                value={photonEnergy}
                onValueChange={setPhotonEnergy}
                max={1000}
                min={50}
                step={50}
                className="mt-2" data-id="61dsly4np" data-path="src/components/simulations/ComptonSimulation.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="aoh1nhppw" data-path="src/components/simulations/ComptonSimulation.tsx">
                <span data-id="yd6t5fzm4" data-path="src/components/simulations/ComptonSimulation.tsx">50</span>
                <span className="font-medium" data-id="29laegf2t" data-path="src/components/simulations/ComptonSimulation.tsx">{photonEnergy[0]} keV</span>
                <span data-id="6gpjik4sq" data-path="src/components/simulations/ComptonSimulation.tsx">1000</span>
              </div>
            </div>

            <div data-id="2cv430l0g" data-path="src/components/simulations/ComptonSimulation.tsx">
              <Label className="text-sm font-medium" data-id="607zocawk" data-path="src/components/simulations/ComptonSimulation.tsx">Scattering Angle (degrees)</Label>
              <Slider
                value={scatteringAngle}
                onValueChange={setScatteringAngle}
                max={180}
                min={0}
                step={10}
                className="mt-2" data-id="rmfhl2plj" data-path="src/components/simulations/ComptonSimulation.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="abwhr218x" data-path="src/components/simulations/ComptonSimulation.tsx">
                <span data-id="u9vckd3kl" data-path="src/components/simulations/ComptonSimulation.tsx">0°</span>
                <span className="font-medium" data-id="tkp8rhrkm" data-path="src/components/simulations/ComptonSimulation.tsx">{scatteringAngle[0]}°</span>
                <span data-id="28wvojqsq" data-path="src/components/simulations/ComptonSimulation.tsx">180°</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="xgg47oab4" data-path="src/components/simulations/ComptonSimulation.tsx">
          <CardContent className="pt-6 space-y-4" data-id="iqpjitiw9" data-path="src/components/simulations/ComptonSimulation.tsx">
            <div data-id="p0xt3j4xj" data-path="src/components/simulations/ComptonSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="bxjzkgmpx" data-path="src/components/simulations/ComptonSimulation.tsx">Scattered Photon Energy</Label>
              <Badge variant="outline" data-id="4d1fjm8jx" data-path="src/components/simulations/ComptonSimulation.tsx">{scatteredPhotonEnergy.toFixed(1)} keV</Badge>
            </div>

            <div data-id="vf5cjqev3" data-path="src/components/simulations/ComptonSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="b9ywxhupk" data-path="src/components/simulations/ComptonSimulation.tsx">Electron Kinetic Energy</Label>
              <Badge variant="outline" data-id="p1w8dgsgq" data-path="src/components/simulations/ComptonSimulation.tsx">{electronKineticEnergy.toFixed(1)} keV</Badge>
            </div>

            <div data-id="4qflvp3sr" data-path="src/components/simulations/ComptonSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="lvy8dk2cg" data-path="src/components/simulations/ComptonSimulation.tsx">Electron Scattering Angle</Label>
              <Badge variant="secondary" data-id="ncydo3yk7" data-path="src/components/simulations/ComptonSimulation.tsx">{electronAngle.toFixed(1)}°</Badge>
            </div>

            <div data-id="zjq75yigo" data-path="src/components/simulations/ComptonSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="eh9klglem" data-path="src/components/simulations/ComptonSimulation.tsx">Energy Conservation</Label>
              <div className="text-xs text-gray-600" data-id="njch1dpj2" data-path="src/components/simulations/ComptonSimulation.tsx">
                <p data-id="4ngz68orj" data-path="src/components/simulations/ComptonSimulation.tsx">Initial: {photonEnergy[0]} keV</p>
                <p data-id="twjoheo3e" data-path="src/components/simulations/ComptonSimulation.tsx">Final: {(scatteredPhotonEnergy + electronKineticEnergy).toFixed(1)} keV</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Information Panel */}
      <Card data-id="wxaklob3u" data-path="src/components/simulations/ComptonSimulation.tsx">
        <CardContent className="pt-6" data-id="gu5u3nnok" data-path="src/components/simulations/ComptonSimulation.tsx">
          <h3 className="font-semibold text-gray-900 mb-3" data-id="ifqi84x9y" data-path="src/components/simulations/ComptonSimulation.tsx">Compton Scattering</h3>
          <div className="grid md:grid-cols-3 gap-4 text-sm text-gray-600" data-id="jcshtfnbf" data-path="src/components/simulations/ComptonSimulation.tsx">
            <div data-id="b11k81h5e" data-path="src/components/simulations/ComptonSimulation.tsx">
              <h4 className="font-medium text-gray-900 mb-1" data-id="16bx8llgb" data-path="src/components/simulations/ComptonSimulation.tsx">Process</h4>
              <p data-id="t5hye0v4c" data-path="src/components/simulations/ComptonSimulation.tsx">Inelastic scattering of photon with loosely bound electron. Photon loses energy and changes direction.</p>
            </div>
            <div data-id="26bmn62ca" data-path="src/components/simulations/ComptonSimulation.tsx">
              <h4 className="font-medium text-gray-900 mb-1" data-id="30dhkx0uo" data-path="src/components/simulations/ComptonSimulation.tsx">Compton Formula</h4>
              <p data-id="k9gur4evi" data-path="src/components/simulations/ComptonSimulation.tsx">E' = E / [1 + (E/m₀c²)(1 - cos θ)]</p>
              <p className="text-xs mt-1" data-id="bze0oybub" data-path="src/components/simulations/ComptonSimulation.tsx">Where m₀c² = 511 keV</p>
            </div>
            <div data-id="7u1to5m5t" data-path="src/components/simulations/ComptonSimulation.tsx">
              <h4 className="font-medium text-gray-900 mb-1" data-id="tom2yltk7" data-path="src/components/simulations/ComptonSimulation.tsx">Characteristics</h4>
              <p data-id="dsdpoge85" data-path="src/components/simulations/ComptonSimulation.tsx">Dominant at intermediate energies (100 keV - 10 MeV). Cross-section independent of Z.</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>);

};

export default ComptonSimulation;