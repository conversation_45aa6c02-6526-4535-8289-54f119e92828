import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Atom,
  Camera,
  Shield,
  Image,
  BookOpen,
  Play,
  Users,
  Award,
  Zap,
  Brain,
  Target,
  Lightbulb } from
'lucide-react';
import { motion } from 'motion/react';

const HomePage = () => {
  const features = [
  {
    icon: Atom,
    title: 'Radiation Physics',
    description: 'Interactive simulations of photoelectric effect, Compton scattering, and pair production',
    path: '/radiation-physics',
    color: 'from-blue-500 to-blue-600'
  },
  {
    icon: Camera,
    title: 'Imaging Modalities',
    description: 'Explore X-ray, CT, MRI, PET, SPECT, and Ultrasound imaging principles',
    path: '/imaging-modalities',
    color: 'from-green-500 to-green-600'
  },
  {
    icon: Shield,
    title: 'Dosimetry & Protection',
    description: 'Calculate doses and design shielding with interactive tools',
    path: '/dosimetry-protection',
    color: 'from-red-500 to-red-600'
  },
  {
    icon: Image,
    title: 'Image Quality',
    description: 'Understand image processing, artifacts, and quality optimization',
    path: '/image-quality',
    color: 'from-purple-500 to-purple-600'
  }];


  const audience = [
  { icon: Users, title: 'Medical Physics Students', description: 'Undergraduate and postgraduate learners' },
  { icon: Award, title: 'Radiology Professionals', description: 'Residents and technologists' },
  { icon: Brain, title: 'Healthcare Professionals', description: 'Continuing education and skills refresh' },
  { icon: Target, title: 'Educators', description: 'Interactive teaching tools and resources' }];


  return (
    <div className="min-h-screen" data-id="3w8gg88sm" data-path="src/pages/HomePage.tsx">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 text-white" data-id="ecqi7i3da" data-path="src/pages/HomePage.tsx">
        <div className="absolute inset-0 bg-black/10" data-id="wh2qe52bd" data-path="src/pages/HomePage.tsx"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32" data-id="hfrltlnpa" data-path="src/pages/HomePage.tsx">
          <div className="text-center" data-id="vjht2wgx9" data-path="src/pages/HomePage.tsx">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }} data-id="ybs5nqio6" data-path="src/pages/HomePage.tsx">

              <div className="flex justify-center mb-6" data-id="rae9ypx2b" data-path="src/pages/HomePage.tsx">
                <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center" data-id="ee33ri88h" data-path="src/pages/HomePage.tsx">
                  <Zap className="w-12 h-12 text-white" data-id="1p44v4sef" data-path="src/pages/HomePage.tsx" />
                </div>
              </div>
              
              <h1 className="text-4xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent" data-id="d8nddewis" data-path="src/pages/HomePage.tsx">
                Interactive Medical Physics
              </h1>
              
              <p className="text-xl lg:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto" data-id="x0r2dka6y" data-path="src/pages/HomePage.tsx">
                Master radiation physics and medical imaging through hands-on simulations and interactive learning experiences
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center" data-id="3uz3haz68" data-path="src/pages/HomePage.tsx">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50" asChild data-id="od57f3zr6" data-path="src/pages/HomePage.tsx">
                  <Link to="/radiation-physics" data-id="ju97hsg14" data-path="src/pages/HomePage.tsx">
                    <Play className="w-5 h-5 mr-2" data-id="dan5qud6g" data-path="src/pages/HomePage.tsx" />
                    Start Learning
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10" asChild data-id="o9ld200cu" data-path="src/pages/HomePage.tsx">
                  <Link to="/about" data-id="ftovfo9hh" data-path="src/pages/HomePage.tsx">
                    <Lightbulb className="w-5 h-5 mr-2" data-id="a0iavx1op" data-path="src/pages/HomePage.tsx" />
                    Learn More
                  </Link>
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white" data-id="fu96evrn3" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="zjz74zmrp" data-path="src/pages/HomePage.tsx">
          <div className="text-center mb-16" data-id="8qpd5gyfx" data-path="src/pages/HomePage.tsx">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4" data-id="iwdm6jkg8" data-path="src/pages/HomePage.tsx">
              Interactive Learning Modules
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="fr9fxu24u" data-path="src/pages/HomePage.tsx">
              Explore complex medical physics concepts through engaging simulations and real-time visualizations
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6" data-id="dafzabxqj" data-path="src/pages/HomePage.tsx">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }} data-id="1myztvyla" data-path="src/pages/HomePage.tsx">

                  <Card className="h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group" data-id="3jmdenwqi" data-path="src/pages/HomePage.tsx">
                    <CardHeader className="text-center" data-id="t6rw4bmld" data-path="src/pages/HomePage.tsx">
                      <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${feature.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`} data-id="elbq8032d" data-path="src/pages/HomePage.tsx">
                        <Icon className="w-8 h-8 text-white" data-id="7i9d63z82" data-path="src/pages/HomePage.tsx" />
                      </div>
                      <CardTitle className="text-xl font-bold" data-id="nogov6lxp" data-path="src/pages/HomePage.tsx">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent data-id="5v9xkhno9" data-path="src/pages/HomePage.tsx">
                      <CardDescription className="text-center mb-4" data-id="f3st2kqv9" data-path="src/pages/HomePage.tsx">
                        {feature.description}
                      </CardDescription>
                      <Button className="w-full" asChild data-id="dxtvkcy9a" data-path="src/pages/HomePage.tsx">
                        <Link to={feature.path} data-id="f681iaik9" data-path="src/pages/HomePage.tsx">
                          Explore
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>);

            })}
          </div>
        </div>
      </section>

      {/* Target Audience */}
      <section className="py-20 bg-gray-50" data-id="fv0dr82jq" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="1xjkvdzkx" data-path="src/pages/HomePage.tsx">
          <div className="text-center mb-16" data-id="6dstroes0" data-path="src/pages/HomePage.tsx">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4" data-id="3zukikic7" data-path="src/pages/HomePage.tsx">
              Who This Platform Is For
            </h2>
            <p className="text-xl text-gray-600" data-id="fw31vrvop" data-path="src/pages/HomePage.tsx">
              Designed for learners and professionals at every stage of their medical physics journey
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6" data-id="hh8techpr" data-path="src/pages/HomePage.tsx">
            {audience.map((item, index) => {
              const Icon = item.icon;
              return (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }} data-id="txwgdal8k" data-path="src/pages/HomePage.tsx">

                  <Card className="text-center h-full" data-id="axn0df3qt" data-path="src/pages/HomePage.tsx">
                    <CardHeader data-id="r63adf1ui" data-path="src/pages/HomePage.tsx">
                      <div className="w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-lg flex items-center justify-center" data-id="au9aswwhy" data-path="src/pages/HomePage.tsx">
                        <Icon className="w-6 h-6 text-blue-600" data-id="wubh3wdvw" data-path="src/pages/HomePage.tsx" />
                      </div>
                      <CardTitle className="text-lg" data-id="3cb9szl56" data-path="src/pages/HomePage.tsx">{item.title}</CardTitle>
                    </CardHeader>
                    <CardContent data-id="brt98gimf" data-path="src/pages/HomePage.tsx">
                      <p className="text-gray-600" data-id="w254ruxsl" data-path="src/pages/HomePage.tsx">{item.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>);

            })}
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-20 bg-white" data-id="qrnbeh8e0" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="sex9r7k8u" data-path="src/pages/HomePage.tsx">
          <div className="text-center mb-16" data-id="x7uxjgfj1" data-path="src/pages/HomePage.tsx">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4" data-id="e9wbfjkrw" data-path="src/pages/HomePage.tsx">
              Platform Highlights
            </h2>
          </div>

          <div className="grid lg:grid-cols-3 gap-8" data-id="8mgr5o0xf" data-path="src/pages/HomePage.tsx">
            <div className="space-y-6" data-id="7nfbhf0m7" data-path="src/pages/HomePage.tsx">
              <h3 className="text-2xl font-bold text-gray-900 mb-4" data-id="u65xg8i31" data-path="src/pages/HomePage.tsx">Interactive Simulations</h3>
              <div className="space-y-4" data-id="tw2ykwufm" data-path="src/pages/HomePage.tsx">
                <div className="flex items-start gap-3" data-id="ff379ycpi" data-path="src/pages/HomePage.tsx">
                  <Badge variant="secondary" data-id="rc13vr4nf" data-path="src/pages/HomePage.tsx">Physics</Badge>
                  <p className="text-gray-600" data-id="pesserjz8" data-path="src/pages/HomePage.tsx">Real-time radiation interaction models with adjustable parameters</p>
                </div>
                <div className="flex items-start gap-3" data-id="mt6yq7nlp" data-path="src/pages/HomePage.tsx">
                  <Badge variant="secondary" data-id="ci9xl01j4" data-path="src/pages/HomePage.tsx">Imaging</Badge>
                  <p className="text-gray-600" data-id="e68tbe83f" data-path="src/pages/HomePage.tsx">Virtual imaging systems for X-ray, CT, MRI, and nuclear medicine</p>
                </div>
                <div className="flex items-start gap-3" data-id="uj58txmx4" data-path="src/pages/HomePage.tsx">
                  <Badge variant="secondary" data-id="s1etzcrzo" data-path="src/pages/HomePage.tsx">Dosimetry</Badge>
                  <p className="text-gray-600" data-id="nmr3ifglj" data-path="src/pages/HomePage.tsx">Dose calculation tools and shielding design simulators</p>
                </div>
              </div>
            </div>

            <div className="space-y-6" data-id="nc466go5d" data-path="src/pages/HomePage.tsx">
              <h3 className="text-2xl font-bold text-gray-900 mb-4" data-id="tqp1fu58g" data-path="src/pages/HomePage.tsx">Learning Tools</h3>
              <div className="space-y-4" data-id="qnirpuvgb" data-path="src/pages/HomePage.tsx">
                <div className="flex items-start gap-3" data-id="65cpt8z1m" data-path="src/pages/HomePage.tsx">
                  <Badge variant="secondary" data-id="7e3nhtob9" data-path="src/pages/HomePage.tsx">Quizzes</Badge>
                  <p className="text-gray-600" data-id="pnb7rgshf" data-path="src/pages/HomePage.tsx">Interactive assessments integrated with simulations</p>
                </div>
                <div className="flex items-start gap-3" data-id="i87w7tbkx" data-path="src/pages/HomePage.tsx">
                  <Badge variant="secondary" data-id="2klulfys5" data-path="src/pages/HomePage.tsx">Glossary</Badge>
                  <p className="text-gray-600" data-id="y0lfi354q" data-path="src/pages/HomePage.tsx">Comprehensive medical physics terminology reference</p>
                </div>
                <div className="flex items-start gap-3" data-id="hb056jtze" data-path="src/pages/HomePage.tsx">
                  <Badge variant="secondary" data-id="e2mkc5ax3" data-path="src/pages/HomePage.tsx">Resources</Badge>
                  <p className="text-gray-600" data-id="g4dxur01n" data-path="src/pages/HomePage.tsx">Curated external links and recommended readings</p>
                </div>
              </div>
            </div>

            <div className="space-y-6" data-id="2aa8ol5zm" data-path="src/pages/HomePage.tsx">
              <h3 className="text-2xl font-bold text-gray-900 mb-4" data-id="37fy1bwev" data-path="src/pages/HomePage.tsx">User Experience</h3>
              <div className="space-y-4" data-id="ssrltv41d" data-path="src/pages/HomePage.tsx">
                <div className="flex items-start gap-3" data-id="5v1hvj23o" data-path="src/pages/HomePage.tsx">
                  <Badge variant="secondary" data-id="xafxekyc7" data-path="src/pages/HomePage.tsx">Responsive</Badge>
                  <p className="text-gray-600" data-id="yc4rxfjx0" data-path="src/pages/HomePage.tsx">Fully functional on desktop, tablet, and mobile devices</p>
                </div>
                <div className="flex items-start gap-3" data-id="8auknwxog" data-path="src/pages/HomePage.tsx">
                  <Badge variant="secondary" data-id="hcezo1ydy" data-path="src/pages/HomePage.tsx">Accessible</Badge>
                  <p className="text-gray-600" data-id="ixhyirvsx" data-path="src/pages/HomePage.tsx">WCAG compliant design for all users</p>
                </div>
                <div className="flex items-start gap-3" data-id="ib4mrm4xo" data-path="src/pages/HomePage.tsx">
                  <Badge variant="secondary" data-id="0g8qjqokh" data-path="src/pages/HomePage.tsx">Modern</Badge>
                  <p className="text-gray-600" data-id="xlnj9tg0n" data-path="src/pages/HomePage.tsx">Clean, professional interface with smooth animations</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-indigo-600 text-white" data-id="ga89tyk5r" data-path="src/pages/HomePage.tsx">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center" data-id="aldamj3xv" data-path="src/pages/HomePage.tsx">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6" data-id="sq6g2vwyt" data-path="src/pages/HomePage.tsx">
            Ready to Master Medical Physics?
          </h2>
          <p className="text-xl mb-8 text-blue-100" data-id="o8xp11ww2" data-path="src/pages/HomePage.tsx">
            Start your interactive learning journey today and discover the fascinating world of medical radiation physics
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center" data-id="nczpuuodr" data-path="src/pages/HomePage.tsx">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50" asChild data-id="lyzrqgwot" data-path="src/pages/HomePage.tsx">
              <Link to="/radiation-physics" data-id="u0uhhp9gh" data-path="src/pages/HomePage.tsx">
                Begin with Radiation Physics
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10" asChild data-id="u64vk50ot" data-path="src/pages/HomePage.tsx">
              <Link to="/glossary" data-id="9tmnrk4sl" data-path="src/pages/HomePage.tsx">
                <BookOpen className="w-5 h-5 mr-2" data-id="fftto375k" data-path="src/pages/HomePage.tsx" />
                Browse Glossary
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>);

};

export default HomePage;