import { Button } from '@/components/ui/button.jsx'
import { Arrow<PERSON><PERSON>, <PERSON>ap, BookO<PERSON>, Brain } from 'lucide-react'

const Hero = () => {
  return (
    <section id="home" className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-4xl mx-auto">
          <h2 className="text-5xl font-bold text-gray-800 mb-6">
            تعلم مبادئ التصوير الطبي بالإشعاع المؤين
          </h2>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed">
            اكتشف عالم أجهزة التصوير الطبي التي تستخدم الإشعاع المؤين من خلال شروحات تفاعلية ورسومات توضيحية مفصلة. 
            تعلم كيفية عمل أنابيب الأشعة السينية والدوائر الكهربائية المعقدة بطريقة سهلة ومبسطة.
          </p>
          
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              ابدأ التعلم الآن
              <ArrowDown className="ml-2" size={20} />
            </Button>
            <Button variant="outline" size="lg" className="border-blue-600 text-blue-600 hover:bg-blue-50">
              استكشف المحتوى
            </Button>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="text-blue-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold mb-3">الإشعاع المؤين</h3>
              <p className="text-gray-600">
                فهم طبيعة الإشعاع المؤين وخصائصه الفيزيائية وتطبيقاته في التصوير الطبي
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="text-green-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold mb-3">المكونات التقنية</h3>
              <p className="text-gray-600">
                تعلم تفاصيل مكونات أجهزة الأشعة السينية من الأنابيب إلى الدوائر الكهربائية
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Brain className="text-purple-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold mb-3">التعلم التفاعلي</h3>
              <p className="text-gray-600">
                استخدم تقنيات الذكاء الاصطناعي للحصول على تجربة تعليمية تفاعلية ومخصصة
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero

