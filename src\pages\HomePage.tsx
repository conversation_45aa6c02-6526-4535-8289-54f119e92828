
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ChevronDown, 
  ChevronRight, 
  Zap, 
  Target, 
  Shield, 
  Settings, 
  BookOpen, 
  Key, 
  Globe, 
  Layers, 
  BookOpenCheck, 
  Atom, 
  Microscope, 
  Lightbulb, 
  GraduationCap
} from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import XRaySimulationPreview from '@/components/XRaySimulationPreview';

const HomePage = () => {
  const { language, setLanguage } = useLanguage();
  const [openSections, setOpenSections] = useState<{[key: string]: boolean;}>({});

  // Bilingual content
  const content = {
    ar: {
      title: "تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية",
      subtitle: "دليل شامل للمهندسين الطبيين وعلماء الأشعة",
      languageButton: "English",
      mainModules: [
        {
          title: "الفيزياء الطبية",
          description: "أساسيات تفاعل الإشعاع مع المادة",
          icon: <Atom className="h-8 w-8 text-white" />,
          color: "from-blue-500 to-blue-700",
          path: "/الفيزياء الطبية"
        },
        {
          title: "التصوير الطبي",
          description: "مبادئ وتقنيات التصوير بالأشعة السينية",
          icon: <Microscope className="h-8 w-8 text-white" />,
          color: "from-purple-500 to-purple-700",
          path: "/التصوير الطبي"
        },
        {
          title: "محاكاة الأشعة السينية",
          description: "نمذجة ومحاكاة أنظمة التصوير المتقدمة",
          icon: <Layers className="h-8 w-8 text-white" />,
          color: "from-green-500 to-green-700",
          path: "/X-Ray Imaging Simulation"
        },
        {
          title: "الواقع الافتراضي",
          description: "تطبيقات الواقع الافتراضي في التعليم الطبي",
          icon: <Lightbulb className="h-8 w-8 text-white" />,
          color: "from-amber-500 to-amber-700",
          path: "/Medical Radiation Physics and virtual Reality"
        }
      ],
      featuredChapters: [
        {
          title: "الفصل 3: أنبوب الأشعة السينية",
          description: "التصميم والمكونات والوظيفة",
          path: "/تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية 3.html"
        },
        {
          title: "الفصل 6: ترشيح الأشعة السينية",
          description: "تحسين جودة الحزمة وتقليل الجرعة",
          path: "/xray-filtration.html"
        },
        {
          title: "الفصل 7: محاكاة مونت كارلو",
          description: "تقنيات متقدمة لمحاكاة تفاعلات الأشعة السينية",
          path: "/monte-carlo-simulation.html"
        }
      ],
      about: {
        title: "عن المنصة التعليمية",
        description: "منصة تعليمية تفاعلية متكاملة لدراسة فيزياء الإشعاع الطبي وتقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة. تجمع المنصة بين المحتوى النظري العميق والتطبيقات العملية التفاعلية، مما يتيح للطلاب والباحثين والمهنيين فهماً شاملاً للمبادئ الفيزيائية والتقنية لأنظمة التصوير الطبي.",
        features: [
          "محتوى ثنائي اللغة (العربية والإنجليزية)",
          "محاكاة تفاعلية لأنظمة الأشعة السينية",
          "تجارب افتراضية لفهم تفاعلات الإشعاع",
          "اختبارات ذاتية وتمارين تطبيقية",
          "مكتبة شاملة للمراجع والموارد العلمية"
        ]
      },
      author: {
        name: "د. محمد يعقوب إسماعيل",
        title: "أستاذ مشارك في الهندسة الطبية الحيوية",
        affiliation: "جامعة السودان للعلوم والتكنولوجيا، كلية الهندسة، قسم الهندسة الطبية الحيوية",
        email: "<EMAIL>",
        phone: "+249912867327, +966538076790"
      },
      footer: {
        copyright: "© 2024 جميع الحقوق محفوظة",
        links: [
          { title: "الصفحة الرئيسية", path: "/" },
          { title: "المحتوى العلمي", path: "/content" },
          { title: "المحاكاة التفاعلية", path: "/simulations" },
          { title: "عن المؤلف", path: "/author" },
          { title: "اتصل بنا", path: "/contact" }
        ]
      }
    },
    en: {
      title: "Advanced Simulation Techniques for X-Ray Imaging Systems",
      subtitle: "A Comprehensive Guide for Medical Engineers and Radiologists",
      languageButton: "العربية",
      mainModules: [
        {
          title: "Medical Physics",
          description: "Fundamentals of radiation interaction with matter",
          icon: <Atom className="h-8 w-8 text-white" />,
          color: "from-blue-500 to-blue-700",
          path: "/الفيزياء الطبية"
        },
        {
          title: "Medical Imaging",
          description: "Principles and techniques of X-ray imaging",
          icon: <Microscope className="h-8 w-8 text-white" />,
          color: "from-purple-500 to-purple-700",
          path: "/التصوير الطبي"
        },
        {
          title: "X-Ray Simulation",
          description: "Modeling and simulation of advanced imaging systems",
          icon: <Layers className="h-8 w-8 text-white" />,
          color: "from-green-500 to-green-700",
          path: "/X-Ray Imaging Simulation"
        },
        {
          title: "Virtual Reality",
          description: "VR applications in medical education",
          icon: <Lightbulb className="h-8 w-8 text-white" />,
          color: "from-amber-500 to-amber-700",
          path: "/Medical Radiation Physics and virtual Reality"
        }
      ],
      featuredChapters: [
        {
          title: "Chapter 3: X-Ray Tube",
          description: "Design, Components, and Function",
          path: "/تقنيات المحاكاة المتقدمة لأنظمة التصوير بالأشعة السينية 3.html"
        },
        {
          title: "Chapter 6: X-Ray Filtration",
          description: "Beam Quality Improvement and Dose Reduction",
          path: "/xray-filtration.html"
        },
        {
          title: "Chapter 7: Monte Carlo Simulation",
          description: "Advanced Techniques for X-Ray Interaction Simulation",
          path: "/monte-carlo-simulation.html"
        }
      ],
      about: {
        title: "About the Learning Platform",
        description: "An integrated interactive educational platform for studying medical radiation physics and advanced simulation techniques for X-ray imaging systems. The platform combines in-depth theoretical content with interactive practical applications, allowing students, researchers, and professionals to gain a comprehensive understanding of the physical and technical principles of medical imaging systems.",
        features: [
          "Bilingual content (Arabic and English)",
          "Interactive simulation of X-ray systems",
          "Virtual experiments for understanding radiation interactions",
          "Self-assessment tests and practical exercises",
          "Comprehensive library of references and scientific resources"
        ]
      },
      author: {
        name: "Dr. Mohammed Yagoub Esmail",
        title: "Associate Professor of Biomedical Engineering",
        affiliation: "Sudan University of Science and Technology, Faculty of Engineering, Department of Biomedical Engineering",
        email: "<EMAIL>",
        phone: "+249912867327, +966538076790"
      },
      footer: {
        copyright: "© 2024 All Rights Reserved",
        links: [
          { title: "Home", path: "/" },
          { title: "Scientific Content", path: "/content" },
          { title: "Interactive Simulations", path: "/simulations" },
          { title: "About the Author", path: "/author" },
          { title: "Contact Us", path: "/contact" }
        ]
      }
    }
  };

  // Get current language content
  const currentContent = content[language];
  const dir = language === 'ar' ? 'rtl' : 'ltr';

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50`} dir={dir}>
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg">
                <BookOpenCheck className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{currentContent.title}</h1>
                <p className="text-sm text-gray-600">{currentContent.subtitle}</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <LanguageSwitcher 
                initialLanguage={language} 
                onLanguageChange={setLanguage}
              />
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* Hero Section */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-12">
          <div className="md:flex">
            <div className="md:w-3/5 p-8">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{currentContent.title}</h2>
              <p className="text-gray-600 mb-6 leading-relaxed">
                {currentContent.subtitle}
              </p>
              <div className="flex flex-wrap gap-3 mb-6">
                <span className="bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-sm">
                  {language === 'ar' ? 'الهندسة الطبية' : 'Biomedical Engineering'}
                </span>
                <span className="bg-green-50 text-green-600 px-3 py-1 rounded-full text-sm">
                  {language === 'ar' ? 'الفيزياء الطبية' : 'Medical Physics'}
                </span>
                <span className="bg-purple-50 text-purple-600 px-3 py-1 rounded-full text-sm">
                  {language === 'ar' ? 'علوم الأشعة' : 'Radiological Sciences'}
                </span>
                <span className="bg-amber-50 text-amber-600 px-3 py-1 rounded-full text-sm">
                  {language === 'ar' ? 'المحاكاة الحاسوبية' : 'Computational Simulation'}
                </span>
              </div>
              <div className="flex flex-wrap gap-3">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  {language === 'ar' ? 'ابدأ التعلم' : 'Start Learning'}
                </Button>
                <Button variant="outline">
                  {language === 'ar' ? 'عن المنصة' : 'About Platform'}
                </Button>
              </div>
            </div>
            <div className="md:w-2/5 bg-gradient-to-br from-indigo-500 to-purple-600 p-8 flex items-center justify-center">
              <div className="text-center">
                <div className="w-24 h-24 mx-auto bg-white/20 rounded-full flex items-center justify-center mb-4">
                  <Zap className="h-12 w-12 text-white" />
                </div>
                <h3 className="text-white text-xl font-bold mb-2">
                  {language === 'ar' ? 'تعلم تفاعلي' : 'Interactive Learning'}
                </h3>
                <p className="text-white/80 text-sm">
                  {language === 'ar' 
                    ? 'محاكاة متقدمة، تجارب افتراضية، ومحتوى ثنائي اللغة' 
                    : 'Advanced simulations, virtual experiments, and bilingual content'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* X-Ray Simulation Preview */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            {language === 'ar' ? 'محاكاة الأشعة السينية التفاعلية' : 'Interactive X-Ray Simulation'}
          </h2>
          <XRaySimulationPreview language={language} />
        </section>

        {/* Main Modules Section */}
        <section className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-800">
              {language === 'ar' ? 'الوحدات التعليمية الرئيسية' : 'Main Learning Modules'}
            </h2>
            <Button variant="ghost" size="sm">
              {language === 'ar' ? 'عرض الكل' : 'View All'} →
            </Button>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {currentContent.mainModules.map((module, index) => (
              <a 
                key={index} 
                href={module.path}
                className="module-card hover-lift bg-white rounded-xl shadow-md overflow-hidden"
              >
                <div className={`h-32 bg-gradient-to-r ${module.color} p-6 flex items-center justify-center`}>
                  {module.icon}
                </div>
                <div className="p-6">
                  <h3 className="font-bold text-lg text-gray-800 mb-2">{module.title}</h3>
                  <p className="text-gray-600 text-sm">{module.description}</p>
                </div>
              </a>
            ))}
          </div>
        </section>

        {/* Featured Chapters */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            {language === 'ar' ? 'فصول مختارة' : 'Featured Chapters'}
          </h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            {currentContent.featuredChapters.map((chapter, index) => (
              <a 
                key={index} 
                href={chapter.path}
                className="bg-white rounded-xl shadow-md p-6 hover-lift"
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                    <BookOpen className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800">{chapter.title}</h3>
                    <p className="text-sm text-gray-500">{chapter.description}</p>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-100 flex justify-end">
                  <span className="text-blue-600 text-sm font-medium">
                    {language === 'ar' ? 'قراءة المزيد' : 'Read More'} →
                  </span>
                </div>
              </a>
            ))}
          </div>
        </section>

        {/* About Section */}
        <section className="bg-white rounded-xl shadow-lg p-8 mb-12">
          <div className="md:flex">
            <div className="md:w-2/3 md:pr-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">{currentContent.about.title}</h2>
              <p className="text-gray-600 mb-6 leading-relaxed">{currentContent.about.description}</p>
              
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                {language === 'ar' ? 'المميزات الرئيسية' : 'Key Features'}
              </h3>
              <ul className="space-y-2 mb-6">
                {currentContent.about.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <div className="mt-1 mr-2">
                      <div className="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      </div>
                    </div>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="md:w-1/3 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 flex items-center justify-center">
              <div className="text-center">
                <GraduationCap className="h-16 w-16 text-indigo-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  {language === 'ar' ? 'تعلم بطريقتك' : 'Learn Your Way'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {language === 'ar' 
                    ? 'محتوى تفاعلي مصمم لتناسب أسلوب تعلمك الفريد' 
                    : 'Interactive content designed to fit your unique learning style'}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Author Section */}
        <section className="bg-white rounded-xl shadow-lg overflow-hidden mb-12">
          <div className="p-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
              <i className="fas fa-user-graduate mr-2 text-blue-500"></i>
              {language === 'ar' ? 'عن المؤلف' : 'About the Author'}
            </h2>
            <div className="flex flex-col md:flex-row items-start">
              <div className="md:w-1/4 mb-4 md:mb-0 flex justify-center">
                <div className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 overflow-hidden border-4 border-blue-50 flex items-center justify-center">
                  <GraduationCap className="h-12 w-12 text-indigo-400" />
                </div>
              </div>
              <div className="md:w-3/4 md:pl-4">
                <h3 className="text-lg font-semibold text-gray-800">{currentContent.author.name}</h3>
                <p className="text-blue-500 text-sm mb-2">{currentContent.author.title}</p>
                <p className="text-gray-600 mb-2">{currentContent.author.affiliation}</p>
                <p className="text-gray-600 mb-1">
                  <span className="font-medium">{language === 'ar' ? 'البريد الإلكتروني:' : 'Email:'}</span> {currentContent.author.email}
                </p>
                <p className="text-gray-600 mb-3">
                  <span className="font-medium">{language === 'ar' ? 'الهاتف:' : 'Phone:'}</span> {currentContent.author.phone}
                </p>
                <div className="flex space-x-3 rtl:space-x-reverse text-gray-500">
                  <a href="#" className="hover:text-blue-500"><i className="fab fa-google-scholar"></i></a>
                  <a href="#" className="hover:text-blue-500"><i className="fab fa-researchgate"></i></a>
                  <a href="#" className="hover:text-blue-500"><i className="fab fa-linkedin"></i></a>
                  <a href="#" className="hover:text-blue-500"><i className="fas fa-envelope"></i></a>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-6">
          <div className="md:flex md:justify-between">
            <div className="mb-6 md:mb-0">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-white/10 rounded-lg">
                  <BookOpenCheck className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-bold">{language === 'ar' ? 'منصة التعلم الطبي' : 'Medical Learning Platform'}</h2>
                </div>
              </div>
              <p className="text-gray-400 text-sm max-w-md">
                {language === 'ar' 
                  ? 'منصة تعليمية متخصصة في مجال الفيزياء الطبية وتقنيات التصوير بالأشعة السينية، تجمع بين المحتوى العلمي العميق والتطبيقات التفاعلية.' 
                  : 'A specialized educational platform in the field of medical physics and X-ray imaging techniques, combining in-depth scientific content with interactive applications.'}
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider mb-4">
                {language === 'ar' ? 'روابط سريعة' : 'Quick Links'}
              </h3>
              <ul className="space-y-2">
                {currentContent.footer.links.map((link, index) => (
                  <li key={index}>
                    <a href={link.path} className="text-gray-400 hover:text-white transition-colors">
                      {link.title}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-gray-700 flex flex-col md:flex-row md:justify-between md:items-center">
            <p className="text-gray-400 text-sm">
              {currentContent.footer.copyright}
            </p>
            <div className="flex space-x-6 rtl:space-x-reverse mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white">
                <i className="fab fa-twitter"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <i className="fab fa-linkedin"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <i className="fab fa-github"></i>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;
                              <h6 className="font-medium text-purple-700 mb-2" data-id="92ir8au3f" data-path="src/pages/HomePage.tsx">النقطة البؤرية الصغيرة</h6>
                              <ul className="text-xs text-gray-600 space-y-1" data-id="9xzhf76p3" data-path="src/pages/HomePage.tsx">
                                <li data-id="tkdyt8zyp" data-path="src/pages/HomePage.tsx">• دقة مكانية عالية</li>
                                <li data-id="m3g64z3y1" data-path="src/pages/HomePage.tsx">• تفاصيل أفضل للصورة</li>
                                <li data-id="2u52dfpv1" data-path="src/pages/HomePage.tsx">• مناسبة للفحوصات الدقيقة</li>
                              </ul>
                            </div>
                            <div className="bg-white rounded-lg p-3" data-id="xkwhd8rss" data-path="src/pages/HomePage.tsx">
                              <h6 className="font-medium text-purple-700 mb-2" data-id="215oq7p5l" data-path="src/pages/HomePage.tsx">النقطة البؤرية الكبيرة</h6>
                              <ul className="text-xs text-gray-600 space-y-1" data-id="gr7tghqgb" data-path="src/pages/HomePage.tsx">
                                <li data-id="xfm3o7w5m" data-path="src/pages/HomePage.tsx">• تحمل حراري أكبر</li>
                                <li data-id="tyslkeqso" data-path="src/pages/HomePage.tsx">• أوقات تعرض أقصر</li>
                                <li data-id="lb0wbhl5y" data-path="src/pages/HomePage.tsx">• مناسبة للفحوصات السريعة</li>
                              </ul>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                <Separator data-id="x8xmq9au2" data-path="src/pages/HomePage.tsx" />

                {/* Focusing Cup Section */}
                <Collapsible open={openSections['focusing-cup']} onOpenChange={() => toggleSection('focusing-cup')} data-id="signqxnyi" data-path="src/pages/HomePage.tsx">
                  <CollapsibleTrigger asChild data-id="c6ntw48ut" data-path="src/pages/HomePage.tsx">
                    <Button variant="outline" className="w-full justify-between p-4 h-auto" data-id="2hdvkg7bx" data-path="src/pages/HomePage.tsx">
                      <span className="font-semibold text-lg" data-id="orychydsc" data-path="src/pages/HomePage.tsx">3.1.2 كأس التركيز</span>
                      {openSections['focusing-cup'] ? <ChevronDown className="h-4 w-4" data-id="84ymr665w" data-path="src/pages/HomePage.tsx" /> : <ChevronRight className="h-4 w-4" data-id="2jmr29kbj" data-path="src/pages/HomePage.tsx" />}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="mt-4 space-y-4" data-id="brb4uv00v" data-path="src/pages/HomePage.tsx">
                    <div className="grid gap-4" data-id="9vanjvm1b" data-path="src/pages/HomePage.tsx">
                      <Card className="bg-orange-50 border-orange-200" data-id="u4x80kadi" data-path="src/pages/HomePage.tsx">
                        <CardHeader className="pb-3" data-id="ytpdggamp" data-path="src/pages/HomePage.tsx">
                          <CardTitle className="text-sm font-semibold text-orange-800" data-id="2hqceg16l" data-path="src/pages/HomePage.tsx">المواد والتصميم</CardTitle>
                        </CardHeader>
                        <CardContent data-id="13qhgj3bt" data-path="src/pages/HomePage.tsx">
                          <div className="grid md:grid-cols-2 gap-3" data-id="c832c2n8s" data-path="src/pages/HomePage.tsx">
                            <div className="bg-white rounded p-3" data-id="yjt3mzjrv" data-path="src/pages/HomePage.tsx">
                              <h6 className="font-medium text-orange-700" data-id="yj9l6og5t" data-path="src/pages/HomePage.tsx">الموليبدينوم</h6>
                              <p className="text-xs text-gray-600" data-id="bamdfl6nc" data-path="src/pages/HomePage.tsx">مقاومة حرارية عالية</p>
                            </div>
                            <div className="bg-white rounded p-3" data-id="m8xddue3h" data-path="src/pages/HomePage.tsx">
                              <h6 className="font-medium text-orange-700" data-id="mq4o65mad" data-path="src/pages/HomePage.tsx">النيكل</h6>
                              <p className="text-xs text-gray-600" data-id="441p8chzz" data-path="src/pages/HomePage.tsx">خصائص كهربائية ممتازة</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-teal-50 border-teal-200" data-id="837tl1ln8" data-path="src/pages/HomePage.tsx">
                        <CardHeader className="pb-3" data-id="kugnidg41" data-path="src/pages/HomePage.tsx">
                          <CardTitle className="text-sm font-semibold text-teal-800" data-id="bynui7h5v" data-path="src/pages/HomePage.tsx">التركيز الكهروستاتيكي</CardTitle>
                        </CardHeader>
                        <CardContent data-id="79m8sxp2v" data-path="src/pages/HomePage.tsx">
                          <p className="text-sm text-gray-700 mb-3" data-id="5roug5rld" data-path="src/pages/HomePage.tsx">تشكيل سحابة الإلكترونات في شعاع ضيق ومركز</p>
                          <div className="bg-white rounded-lg p-3 border" data-id="vkhcf93zu" data-path="src/pages/HomePage.tsx">
                            <h6 className="font-medium text-teal-700" data-id="63qad1v09" data-path="src/pages/HomePage.tsx">جهد التحيز</h6>
                            <p className="text-xs text-gray-600" data-id="m54wgpgjx" data-path="src/pages/HomePage.tsx">أنابيب التحكم بالشبكة للتطبيقات المتخصصة مثل التنظير الفلوري النبضي</p>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Continue with remaining tabs... */}
          <TabsContent value="anode" data-id="uirql9r1r" data-path="src/pages/HomePage.tsx">
            <Card data-id="ko53i0mag" data-path="src/pages/HomePage.tsx">
              <CardHeader data-id="vrg5jljy3" data-path="src/pages/HomePage.tsx">
                <CardTitle className="flex items-center gap-2" data-id="7z55zgedy" data-path="src/pages/HomePage.tsx">
                  <Target className="h-5 w-5 text-red-600" data-id="dkf4hcx9n" data-path="src/pages/HomePage.tsx" />
                  مجموعة الأنود: الهدف لإنتاج الأشعة السينية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6" data-id="vo70eeuhe" data-path="src/pages/HomePage.tsx">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4" data-id="of3cfhhx9" data-path="src/pages/HomePage.tsx">
                  <h4 className="font-semibold text-red-900 mb-2" data-id="8dhk6pfu3" data-path="src/pages/HomePage.tsx">وظيفة الأنود</h4>
                  <div className="grid md:grid-cols-3 gap-3" data-id="zipl0ecc0" data-path="src/pages/HomePage.tsx">
                    <div className="bg-white rounded p-3" data-id="1dcl3hxz0" data-path="src/pages/HomePage.tsx">
                      <h6 className="font-medium text-red-700" data-id="cp616wnqz" data-path="src/pages/HomePage.tsx">تباطؤ الإلكترون</h6>
                    </div>
                    <div className="bg-white rounded p-3" data-id="4vlpkm15r" data-path="src/pages/HomePage.tsx">
                      <h6 className="font-medium text-red-700" data-id="pj8unc7d7" data-path="src/pages/HomePage.tsx">توليد الأشعة السينية</h6>
                    </div>
                    <div className="bg-white rounded p-3" data-id="hdtlytrc1" data-path="src/pages/HomePage.tsx">
                      <h6 className="font-medium text-red-700" data-id="qdwssxy56" data-path="src/pages/HomePage.tsx">تبديد الحرارة</h6>
                    </div>
                  </div>
                </div>

                {/* Target Materials */}
                <Collapsible open={openSections['target-materials']} onOpenChange={() => toggleSection('target-materials')} data-id="hsuuphel8" data-path="src/pages/HomePage.tsx">
                  <CollapsibleTrigger asChild data-id="gb26v6qal" data-path="src/pages/HomePage.tsx">
                    <Button variant="outline" className="w-full justify-between p-4 h-auto" data-id="61izsp7mb" data-path="src/pages/HomePage.tsx">
                      <span className="font-semibold text-lg" data-id="grt0fpj14" data-path="src/pages/HomePage.tsx">المواد المستهدفة</span>
                      {openSections['target-materials'] ? <ChevronDown className="h-4 w-4" data-id="fzwc1hptl" data-path="src/pages/HomePage.tsx" /> : <ChevronRight className="h-4 w-4" data-id="8i5gr8xlv" data-path="src/pages/HomePage.tsx" />}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="mt-4 space-y-4" data-id="fq2k2hf7r" data-path="src/pages/HomePage.tsx">
                    <div className="grid gap-4" data-id="uymx0ag6j" data-path="src/pages/HomePage.tsx">
                      <Card className="bg-gray-50 border-gray-200" data-id="rlhqpkhb9" data-path="src/pages/HomePage.tsx">
                        <CardHeader data-id="69bvetzc7" data-path="src/pages/HomePage.tsx">
                          <CardTitle className="text-sm text-gray-800" data-id="bw7g2hk2x" data-path="src/pages/HomePage.tsx">التنغستن (W)</CardTitle>
                        </CardHeader>
                        <CardContent data-id="cux2w0loa" data-path="src/pages/HomePage.tsx">
                          <p className="text-sm text-gray-700 mb-3" data-id="60j9y0r2d" data-path="src/pages/HomePage.tsx">الخيار الأساسي للتصوير الشعاعي العام</p>
                          <div className="grid md:grid-cols-3 gap-2" data-id="irg8betm9" data-path="src/pages/HomePage.tsx">
                            <Badge variant="secondary" data-id="i6ogeegmg" data-path="src/pages/HomePage.tsx">Z عالية</Badge>
                            <Badge variant="secondary" data-id="5hv21rhbk" data-path="src/pages/HomePage.tsx">نقطة انصهار عالية</Badge>
                            <Badge variant="secondary" data-id="tuc6a2xcu" data-path="src/pages/HomePage.tsx">موصلية حرارية جيدة</Badge>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-blue-50 border-blue-200" data-id="sh9twywh7" data-path="src/pages/HomePage.tsx">
                        <CardHeader data-id="pvpkmyixu" data-path="src/pages/HomePage.tsx">
                          <CardTitle className="text-sm text-blue-800" data-id="hxfk22d9w" data-path="src/pages/HomePage.tsx">الرينيوم (Re)</CardTitle>
                        </CardHeader>
                        <CardContent data-id="rvia21mbl" data-path="src/pages/HomePage.tsx">
                          <p className="text-sm text-gray-700" data-id="b8gflja0p" data-path="src/pages/HomePage.tsx">يُخلط مع التنغستن لتحسين اللدونة وتقليل التشقق السطحي</p>
                        </CardContent>
                      </Card>

                      <Card className="bg-pink-50 border-pink-200" data-id="nw8g964ul" data-path="src/pages/HomePage.tsx">
                        <CardHeader data-id="lkpln1rr8" data-path="src/pages/HomePage.tsx">
                          <CardTitle className="text-sm text-pink-800" data-id="phaujsnc4" data-path="src/pages/HomePage.tsx">الموليبدينوم والروديوم</CardTitle>
                        </CardHeader>
                        <CardContent data-id="h3pias8f7" data-path="src/pages/HomePage.tsx">
                          <p className="text-sm text-gray-700" data-id="9g9g0l89b" data-path="src/pages/HomePage.tsx">مواد متخصصة لتصوير الثدي بالأشعة السينية</p>
                          <p className="text-xs text-gray-600 mt-2" data-id="ir9deaknu" data-path="src/pages/HomePage.tsx">طاقات الأشعة السينية المميزة المناسبة لتباين أنسجة الثدي</p>
                        </CardContent>
                      </Card>
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                {/* Anode Types */}
                <div className="grid md:grid-cols-2 gap-4" data-id="4r7efq9wg" data-path="src/pages/HomePage.tsx">
                  <Card className="bg-green-50 border-green-200" data-id="ohw73jbgv" data-path="src/pages/HomePage.tsx">
                    <CardHeader data-id="rnphihe90" data-path="src/pages/HomePage.tsx">
                      <CardTitle className="text-sm text-green-800" data-id="xbjfpfp2b" data-path="src/pages/HomePage.tsx">الأنودات الثابتة</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3" data-id="atovwg9mw" data-path="src/pages/HomePage.tsx">
                      <div data-id="2c821t8pv" data-path="src/pages/HomePage.tsx">
                        <h6 className="font-medium text-green-700 mb-1" data-id="3nhg8z8eu" data-path="src/pages/HomePage.tsx">التطبيقات</h6>
                        <ul className="text-xs text-gray-600 space-y-1" data-id="t2ycinxlz" data-path="src/pages/HomePage.tsx">
                          <li data-id="o7rwe73xp" data-path="src/pages/HomePage.tsx">• وحدات الأشعة السينية للأسنان</li>
                          <li data-id="xigxubgho" data-path="src/pages/HomePage.tsx">• الوحدات المحمولة منخفضة الطاقة</li>
                        </ul>
                      </div>
                      <div className="bg-white rounded p-2" data-id="ux9bloe8l" data-path="src/pages/HomePage.tsx">
                        <h6 className="font-medium text-red-600 text-xs" data-id="gxbzxv2ly" data-path="src/pages/HomePage.tsx">القيود</h6>
                        <p className="text-xs text-gray-600" data-id="hvue3im80" data-path="src/pages/HomePage.tsx">تبديد الحرارة محدود وتحميل طاقة منخفض</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-purple-50 border-purple-200" data-id="nv2a9562z" data-path="src/pages/HomePage.tsx">
                    <CardHeader data-id="l1k7lnm9n" data-path="src/pages/HomePage.tsx">
                      <CardTitle className="text-sm text-purple-800" data-id="j3btfmfpr" data-path="src/pages/HomePage.tsx">الأنودات الدوارة</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3" data-id="cyj4eibdi" data-path="src/pages/HomePage.tsx">
                      <div data-id="3sh9ui2ld" data-path="src/pages/HomePage.tsx">
                        <h6 className="font-medium text-purple-700 mb-1" data-id="bi1p1j0vh" data-path="src/pages/HomePage.tsx">المزايا</h6>
                        <ul className="text-xs text-gray-600 space-y-1" data-id="m7rz83cnx" data-path="src/pages/HomePage.tsx">
                          <li data-id="lxj1cfgn4" data-path="src/pages/HomePage.tsx">• تحسين كبير في تبديد الحرارة</li>
                          <li data-id="798twtcqv" data-path="src/pages/HomePage.tsx">• أحمال طاقة أعلى</li>
                          <li data-id="l4kpsx9hn" data-path="src/pages/HomePage.tsx">• أوقات تعرض أقصر</li>
                        </ul>
                      </div>
                      <div className="bg-white rounded p-2" data-id="tqaxzil67" data-path="src/pages/HomePage.tsx">
                        <h6 className="font-medium text-purple-700 text-xs" data-id="ojsluo2s5" data-path="src/pages/HomePage.tsx">آلية الدوران</h6>
                        <p className="text-xs text-gray-600" data-id="vzz2730vv" data-path="src/pages/HomePage.tsx">المحرك الحثي (الجزء الثابت خارج الفراغ)</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Continue with remaining content... */}
          <TabsContent value="envelope" data-id="2mz2p96vx" data-path="src/pages/HomePage.tsx">
            <Card data-id="1qxe6icni" data-path="src/pages/HomePage.tsx">
              <CardHeader data-id="rcsua7t93" data-path="src/pages/HomePage.tsx">
                <CardTitle className="flex items-center gap-2" data-id="1jywd78t0" data-path="src/pages/HomePage.tsx">
                  <Shield className="h-5 w-5 text-indigo-600" data-id="irqum7n25" data-path="src/pages/HomePage.tsx" />
                  الغلاف الفراغي
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4" data-id="1y5i1p7vf" data-path="src/pages/HomePage.tsx">
                <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4" data-id="u15ifasl9" data-path="src/pages/HomePage.tsx">
                  <h4 className="font-semibold text-indigo-900 mb-2" data-id="81uwo4puu" data-path="src/pages/HomePage.tsx">الغرض</h4>
                  <p className="text-indigo-800" data-id="bmo4bwj77" data-path="src/pages/HomePage.tsx">الحفاظ على فراغ عالٍ لمنع تفاعل الإلكترون مع جزيئات الغاز</p>
                </div>

                <div className="grid md:grid-cols-2 gap-4" data-id="kp1znv3z3" data-path="src/pages/HomePage.tsx">
                  <Card className="bg-yellow-50 border-yellow-200" data-id="7ttkxt4r3" data-path="src/pages/HomePage.tsx">
                    <CardHeader data-id="1a9ys2ngy" data-path="src/pages/HomePage.tsx">
                      <CardTitle className="text-sm text-yellow-800" data-id="6gr6n1evm" data-path="src/pages/HomePage.tsx">الزجاج (بيركس)</CardTitle>
                    </CardHeader>
                    <CardContent data-id="r8r9s3d78" data-path="src/pages/HomePage.tsx">
                      <ul className="text-xs text-gray-600 space-y-1" data-id="ox2f53l7h" data-path="src/pages/HomePage.tsx">
                        <li data-id="tov5bgrvv" data-path="src/pages/HomePage.tsx">• مادة تقليدية</li>
                        <li data-id="zt5hb9hey" data-path="src/pages/HomePage.tsx">• عازل كهربائي جيد</li>
                        <li data-id="dm0ebn7do" data-path="src/pages/HomePage.tsx">• سهولة التصنيع</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="bg-gray-50 border-gray-200" data-id="x5ltf401q" data-path="src/pages/HomePage.tsx">
                    <CardHeader data-id="kcmmbplp4" data-path="src/pages/HomePage.tsx">
                      <CardTitle className="text-sm text-gray-800" data-id="mr4sir90c" data-path="src/pages/HomePage.tsx">الأغلفة المعدنية الخزفية</CardTitle>
                    </CardHeader>
                    <CardContent data-id="mvpukfhr7" data-path="src/pages/HomePage.tsx">
                      <ul className="text-xs text-gray-600 space-y-1" data-id="8yfbjnr27" data-path="src/pages/HomePage.tsx">
                        <li data-id="kf9lkib10" data-path="src/pages/HomePage.tsx">• أكثر قوة ومتانة</li>
                        <li data-id="apa05cnqf" data-path="src/pages/HomePage.tsx">• تحمل أفضل للحرارة</li>
                        <li data-id="98gfvta72" data-path="src/pages/HomePage.tsx">• تقليل القوس الكهربائي</li>
                        <li data-id="xqp0xists" data-path="src/pages/HomePage.tsx">• شائعة في الأنابيب عالية الطاقة</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <Card className="bg-blue-50 border-blue-200" data-id="pscbeja64" data-path="src/pages/HomePage.tsx">
                  <CardHeader data-id="xg2tks0tu" data-path="src/pages/HomePage.tsx">
                    <CardTitle className="text-sm text-blue-800" data-id="oc7kkp5z9" data-path="src/pages/HomePage.tsx">نافذة الأشعة السينية</CardTitle>
                  </CardHeader>
                  <CardContent data-id="j39gy3vdu" data-path="src/pages/HomePage.tsx">
                    <p className="text-sm text-gray-700" data-id="jylhq2lua" data-path="src/pages/HomePage.tsx">قسم أرق من الغلاف للسماح بخروج الأشعة السينية بأقل قدر من التوهين</p>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="housing" data-id="4fk1n8ttc" data-path="src/pages/HomePage.tsx">
            <Card data-id="4s9fjdd8o" data-path="src/pages/HomePage.tsx">
              <CardHeader data-id="3awbfgy99" data-path="src/pages/HomePage.tsx">
                <CardTitle className="flex items-center gap-2" data-id="48mpp4ljo" data-path="src/pages/HomePage.tsx">
                  <Shield className="h-5 w-5 text-emerald-600" data-id="s98kjrti8" data-path="src/pages/HomePage.tsx" />
                  غلاف الأنبوب والحماية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4" data-id="7a3ip0az6" data-path="src/pages/HomePage.tsx">
                <div className="grid gap-4" data-id="a3y14x5xi" data-path="src/pages/HomePage.tsx">
                  <Card className="bg-emerald-50 border-emerald-200" data-id="48hk6fmeg" data-path="src/pages/HomePage.tsx">
                    <CardHeader data-id="3nifonr8g" data-path="src/pages/HomePage.tsx">
                      <CardTitle className="text-sm text-emerald-800" data-id="o8ibpe6x6" data-path="src/pages/HomePage.tsx">وظائف الغلاف الواقي</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3" data-id="5a5no5ag3" data-path="src/pages/HomePage.tsx">
                      <div className="grid md:grid-cols-2 gap-3" data-id="knc66gn9o" data-path="src/pages/HomePage.tsx">
                        <div className="bg-white rounded p-3" data-id="ljceza16z" data-path="src/pages/HomePage.tsx">
                          <h6 className="font-medium text-emerald-700" data-id="cxbi173no" data-path="src/pages/HomePage.tsx">الحماية من الإشعاع</h6>
                          <p className="text-xs text-gray-600" data-id="vf31k7qzt" data-path="src/pages/HomePage.tsx">بطانة من الرصاص لامتصاص الإشعاع المتسرب</p>
                        </div>
                        <div className="bg-white rounded p-3" data-id="h7111toxr" data-path="src/pages/HomePage.tsx">
                          <h6 className="font-medium text-emerald-700" data-id="eiqqgjpes" data-path="src/pages/HomePage.tsx">السلامة الكهربائية</h6>
                          <p className="text-xs text-gray-600" data-id="3kf2bdxf5" data-path="src/pages/HomePage.tsx">التأريض والعزل من الجهد العالي</p>
                        </div>
                        <div className="bg-white rounded p-3" data-id="1hsab79k9" data-path="src/pages/HomePage.tsx">
                          <h6 className="font-medium text-emerald-700" data-id="v709oy147" data-path="src/pages/HomePage.tsx">الدعم الميكانيكي</h6>
                          <p className="text-xs text-gray-600" data-id="7q8nvznpr" data-path="src/pages/HomePage.tsx">لإدخال الأنبوب</p>
                        </div>
                        <div className="bg-white rounded p-3" data-id="2pg1fkv8d" data-path="src/pages/HomePage.tsx">
                          <h6 className="font-medium text-emerald-700" data-id="fxdne2a2g" data-path="src/pages/HomePage.tsx">التبريد</h6>
                          <p className="text-xs text-gray-600" data-id="ayfl6vomt" data-path="src/pages/HomePage.tsx">زيت عازل للعزل والحمل الحراري</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <div className="grid md:grid-cols-3 gap-4" data-id="7bt3f7vdp" data-path="src/pages/HomePage.tsx">
                    <Card className="bg-blue-50 border-blue-200" data-id="yeaa5bg1h" data-path="src/pages/HomePage.tsx">
                      <CardHeader data-id="ebmt1ms9a" data-path="src/pages/HomePage.tsx">
                        <CardTitle className="text-sm text-blue-800" data-id="5wdhs12ma" data-path="src/pages/HomePage.tsx">الزيت العازل</CardTitle>
                      </CardHeader>
                      <CardContent data-id="ozjywi0cs" data-path="src/pages/HomePage.tsx">
                        <p className="text-xs text-gray-600" data-id="gw4zib6r2" data-path="src/pages/HomePage.tsx">عازل كهربائي ووسط لنقل الحرارة</p>
                      </CardContent>
                    </Card>

                    <Card className="bg-orange-50 border-orange-200" data-id="ux6gzb8iy" data-path="src/pages/HomePage.tsx">
                      <CardHeader data-id="prof8ag7i" data-path="src/pages/HomePage.tsx">
                        <CardTitle className="text-sm text-orange-800" data-id="whlrr4tfm" data-path="src/pages/HomePage.tsx">منفاخ التمدد</CardTitle>
                      </CardHeader>
                      <CardContent data-id="ph9zzmw7u" data-path="src/pages/HomePage.tsx">
                        <p className="text-xs text-gray-600" data-id="1kka5e9xg" data-path="src/pages/HomePage.tsx">يستوعب تمدد الزيت بسبب التسخين</p>
                      </CardContent>
                    </Card>

                    <Card className="bg-purple-50 border-purple-200" data-id="hbqup8oi4" data-path="src/pages/HomePage.tsx">
                      <CardHeader data-id="n2eq74xdc" data-path="src/pages/HomePage.tsx">
                        <CardTitle className="text-sm text-purple-800" data-id="monb7riiv" data-path="src/pages/HomePage.tsx">الكابلات</CardTitle>
                      </CardHeader>
                      <CardContent data-id="diny92bk7" data-path="src/pages/HomePage.tsx">
                        <p className="text-xs text-gray-600" data-id="57hu1nfad" data-path="src/pages/HomePage.tsx">مقابس وكابلات الجهد العالي</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="challenges" data-id="ptg9snlkc" data-path="src/pages/HomePage.tsx">
            <Card data-id="9t51k9svm" data-path="src/pages/HomePage.tsx">
              <CardHeader data-id="2k6qj04yj" data-path="src/pages/HomePage.tsx">
                <CardTitle className="flex items-center gap-2" data-id="yck5fxcvl" data-path="src/pages/HomePage.tsx">
                  <Settings className="h-5 w-5 text-red-600" data-id="jc214rbqh" data-path="src/pages/HomePage.tsx" />
                  التحديات والاعتبارات الهندسية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6" data-id="9itgk0rsa" data-path="src/pages/HomePage.tsx">
                <Collapsible open={openSections['thermal-management']} onOpenChange={() => toggleSection('thermal-management')} data-id="edf2nsdgj" data-path="src/pages/HomePage.tsx">
                  <CollapsibleTrigger asChild data-id="v7b966xml" data-path="src/pages/HomePage.tsx">
                    <Button variant="outline" className="w-full justify-between p-4 h-auto" data-id="0wwcmxm19" data-path="src/pages/HomePage.tsx">
                      <span className="font-semibold text-lg" data-id="5tk1ayrcu" data-path="src/pages/HomePage.tsx">إدارة الحرارة وآليات التبريد</span>
                      {openSections['thermal-management'] ? <ChevronDown className="h-4 w-4" data-id="i335g7k1p" data-path="src/pages/HomePage.tsx" /> : <ChevronRight className="h-4 w-4" data-id="6epnqqqar" data-path="src/pages/HomePage.tsx" />}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="mt-4 space-y-4" data-id="qrdt7xi2e" data-path="src/pages/HomePage.tsx">
                    <div className="grid gap-4" data-id="d5pqlqiqf" data-path="src/pages/HomePage.tsx">
                      <Card className="bg-red-50 border-red-200" data-id="402ie26y8" data-path="src/pages/HomePage.tsx">
                        <CardHeader data-id="bnrs09118" data-path="src/pages/HomePage.tsx">
                          <CardTitle className="text-sm text-red-800" data-id="aore7umxw" data-path="src/pages/HomePage.tsx">آليات نقل الحرارة</CardTitle>
                        </CardHeader>
                        <CardContent data-id="w3ddfo33u" data-path="src/pages/HomePage.tsx">
                          <div className="grid md:grid-cols-3 gap-3" data-id="p1s2kseyg" data-path="src/pages/HomePage.tsx">
                            <div className="bg-white rounded p-3 text-center" data-id="gq06zsdxq" data-path="src/pages/HomePage.tsx">
                              <h6 className="font-medium text-red-700" data-id="qshobhyp6" data-path="src/pages/HomePage.tsx">التوصيل</h6>
                            </div>
                            <div className="bg-white rounded p-3 text-center" data-id="iys5pbu0h" data-path="src/pages/HomePage.tsx">
                              <h6 className="font-medium text-red-700" data-id="0wta0gdzt" data-path="src/pages/HomePage.tsx">الحمل الحراري</h6>
                            </div>
                            <div className="bg-white rounded p-3 text-center" data-id="90sbi0chq" data-path="src/pages/HomePage.tsx">
                              <h6 className="font-medium text-red-700" data-id="nu9kjeg5j" data-path="src/pages/HomePage.tsx">الإشعاع</h6>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-blue-50 border-blue-200" data-id="zesflznb5" data-path="src/pages/HomePage.tsx">
                        <CardHeader data-id="0gpzi7wr5" data-path="src/pages/HomePage.tsx">
                          <CardTitle className="text-sm text-blue-800" data-id="exyj6lt4o" data-path="src/pages/HomePage.tsx">أنظمة التبريد</CardTitle>
                        </CardHeader>
                        <CardContent data-id="vwyeu5ak0" data-path="src/pages/HomePage.tsx">
                          <ul className="text-sm text-gray-700 space-y-2" data-id="18l3vg0dm" data-path="src/pages/HomePage.tsx">
                            <li data-id="jg5w4hc90" data-path="src/pages/HomePage.tsx">• منحنيات تبريد الأنود والحدود الحرارية</li>
                            <li data-id="nnk8zifdt" data-path="src/pages/HomePage.tsx">• المراوح للتبريد الخارجي</li>
                            <li data-id="ro5korrvf" data-path="src/pages/HomePage.tsx">• مبادلات الحرارة الزيتية المائية للأنابيب عالية الطاقة</li>
                          </ul>
                        </CardContent>
                      </Card>
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                <div className="grid md:grid-cols-2 gap-4" data-id="ook6l4rqo" data-path="src/pages/HomePage.tsx">
                  <Card className="bg-green-50 border-green-200" data-id="t8zux86ie" data-path="src/pages/HomePage.tsx">
                    <CardHeader data-id="ij525mg35" data-path="src/pages/HomePage.tsx">
                      <CardTitle className="text-sm text-green-800" data-id="tdk4l4dn9" data-path="src/pages/HomePage.tsx">سلامة الفراغ وعمر الأنبوب</CardTitle>
                    </CardHeader>
                    <CardContent data-id="937hi5p6y" data-path="src/pages/HomePage.tsx">
                      <ul className="text-xs text-gray-600 space-y-1" data-id="dv2si75zf" data-path="src/pages/HomePage.tsx">
                        <li data-id="fvn38yynu" data-path="src/pages/HomePage.tsx">• تبخر الخيوط</li>
                        <li data-id="9kwk82iur" data-path="src/pages/HomePage.tsx">• تآكل الأنود</li>
                        <li data-id="fqxndj22k" data-path="src/pages/HomePage.tsx">• تسرب الغازات</li>
                        <li data-id="1l7k91bxp" data-path="src/pages/HomePage.tsx">• القوس الكهربائي</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className="bg-yellow-50 border-yellow-200" data-id="tdby9u0mm" data-path="src/pages/HomePage.tsx">
                    <CardHeader data-id="p2u26k4w0" data-path="src/pages/HomePage.tsx">
                      <CardTitle className="text-sm text-yellow-800" data-id="ajrcqkp42" data-path="src/pages/HomePage.tsx">التحديات الإضافية</CardTitle>
                    </CardHeader>
                    <CardContent data-id="750ng6sfl" data-path="src/pages/HomePage.tsx">
                      <ul className="text-xs text-gray-600 space-y-1" data-id="rxesscjwq" data-path="src/pages/HomePage.tsx">
                        <li data-id="td4b1t917" data-path="src/pages/HomePage.tsx">• الضغوط الميكانيكية على محامل الأنود</li>
                        <li data-id="qfo2ezxlh" data-path="src/pages/HomePage.tsx">• العزل الكهربائي ومنع القوس الكهربائي</li>
                        <li data-id="w00t913x1" data-path="src/pages/HomePage.tsx">• تزهر البقعة البؤرية عند التيارات العالية</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Key Terms */}
        <Card className="mt-8" data-id="vhmicw7vc" data-path="src/pages/HomePage.tsx">
          <CardHeader data-id="jnlhghc4f" data-path="src/pages/HomePage.tsx">
            <CardTitle className="flex items-center gap-2" data-id="a7p13cg19" data-path="src/pages/HomePage.tsx">
              <Key className="h-5 w-5 text-yellow-600" data-id="qk49ood49" data-path="src/pages/HomePage.tsx" />
              المصطلحات الرئيسية
            </CardTitle>
          </CardHeader>
          <CardContent data-id="unhimsp66" data-path="src/pages/HomePage.tsx">
            <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-2" data-id="8wspl81ic" data-path="src/pages/HomePage.tsx">
              {keyTerms.map((term, index) =>
              <Badge key={index} variant="outline" className="justify-center py-2" data-id="q8qt8d38f" data-path="src/pages/HomePage.tsx">
                  {term}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default HomePage;