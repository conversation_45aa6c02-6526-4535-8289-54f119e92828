import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { Menu, Microscope, Zap, CircuitBoard, Brain, BookOpen } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import LanguageToggle from '@/components/LanguageToggle';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();
  const { t } = useLanguage();

  const navigationItems = [
  { path: '/', label: t('nav.home'), icon: Microscope },
  { path: '/concepts', label: t('nav.concepts'), icon: BookOpen },
  { path: '/xray-tube', label: t('nav.xrayTube'), icon: Zap },
  { path: '/circuits', label: t('nav.circuit'), icon: CircuitBoard },
  { path: '/ai-assistant', label: t('nav.assistant'), icon: Brain }];


  const NavItems = ({ mobile = false }) =>
  <div className={`flex ${mobile ? 'flex-col space-y-2' : 'space-x-4'}`} data-id="4aq5jottd" data-path="src/components/Navbar.tsx">
      {navigationItems.map((item) => {
      const Icon = item.icon;
      const isActive = location.pathname === item.path;

      return (
        <Link
          key={item.path}
          to={item.path}
          onClick={() => mobile && setIsOpen(false)}
          className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
          isActive ?
          'bg-blue-100 text-blue-700 font-medium' :
          'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`
          } data-id="fbqdr8w5x" data-path="src/components/Navbar.tsx">

            <Icon size={18} data-id="r9qs6ljoi" data-path="src/components/Navbar.tsx" />
            <span className="text-sm" data-id="p9p8rczt8" data-path="src/components/Navbar.tsx">{item.label}</span>
            {isActive && <Badge variant="secondary" className="text-xs" data-id="jh3uf6d1x" data-path="src/components/Navbar.tsx">Active</Badge>}
          </Link>);

    })}
    </div>;


  return (
    <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-sm" data-id="0rwpn4229" data-path="src/components/Navbar.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="k5mo2nyi3" data-path="src/components/Navbar.tsx">
        <div className="flex justify-between items-center h-16" data-id="luxe367ao" data-path="src/components/Navbar.tsx">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3" data-id="3rju5eypj" data-path="src/components/Navbar.tsx">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-lg" data-id="id31j0vfy" data-path="src/components/Navbar.tsx">
              <Microscope className="h-6 w-6 text-white" data-id="7d9dvg8uw" data-path="src/components/Navbar.tsx" />
            </div>
            <div data-id="rdjboepjj" data-path="src/components/Navbar.tsx">
              <h1 className="text-xl font-bold text-gray-900" data-id="pdc0p4qud" data-path="src/components/Navbar.tsx">X-Ray Education</h1>
              <p className="text-xs text-gray-500" data-id="zvtnlps33" data-path="src/components/Navbar.tsx">Interactive Learning Platform</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4" data-id="0uascndfz" data-path="src/components/Navbar.tsx">
            <NavItems data-id="4orbunl6t" data-path="src/components/Navbar.tsx" />
            <LanguageToggle data-id="40k35caev" data-path="src/components/Navbar.tsx" />
          </div>

          {/* Mobile Menu */}
          <div className="md:hidden flex items-center space-x-2" data-id="wtr6kk6fq" data-path="src/components/Navbar.tsx">
            <LanguageToggle data-id="464ltsrnb" data-path="src/components/Navbar.tsx" />
            <Sheet open={isOpen} onOpenChange={setIsOpen} data-id="whakej9o6" data-path="src/components/Navbar.tsx">
              <SheetTrigger asChild data-id="f0hfye4ph" data-path="src/components/Navbar.tsx">
                <Button variant="ghost" size="sm" data-id="09kr3xbf7" data-path="src/components/Navbar.tsx">
                  <Menu className="h-5 w-5" data-id="a4p7iq5yf" data-path="src/components/Navbar.tsx" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-64" data-id="g1tyvpkx2" data-path="src/components/Navbar.tsx">
                <div className="py-4" data-id="wdd8f826r" data-path="src/components/Navbar.tsx">
                  <h2 className="text-lg font-semibold mb-4" data-id="16duodlc9" data-path="src/components/Navbar.tsx">Navigation</h2>
                  <NavItems mobile data-id="dxyu918bs" data-path="src/components/Navbar.tsx" />
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>);

};

export default Navbar;