import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Cpu, BarChart3, Code, PlayCircle, CheckCircle, Settings } from 'lucide-react';
import { motion } from 'motion/react';

const Chapter7Content = () => {
  const [openSections, setOpenSections] = useState<{[key: string]: boolean;}>({});
  const [completedSections, setCompletedSections] = useState<string[]>([]);

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const markAsCompleted = (sectionId: string) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections((prev) => [...prev, sectionId]);
    }
  };

  const sections = [
  {
    id: '7.1',
    title: 'النماذج التحليلية والتجريبية لتوليد الطيف',
    icon: <BarChart3 className="w-5 h-5" data-id="a9fya0g07" data-path="src/components/Chapter7Content.tsx" />,
    subsections: [
    {
      id: '7.1.1',
      title: 'بيرش ومارشال، تاكر وآخرون (TASMIP)، SpekCalc، تقرير78',
      content: 'نماذج متعددة لمحاكاة أطياف الأشعة السينية: نموذج Birch & Marshall (1979) للأطياف المرجعية، TASMIP للحسابات السريعة، SpekCalc للتطبيقات العملية، وTASK Group 78 للمعايير الحديثة.'
    },
    {
      id: '7.1.2',
      title: 'المزايا والقيود ومعلمات الإدخال',
      content: 'النماذج التحليلية سريعة ومناسبة للحسابات الروتينية لكنها محدودة في الدقة. معلمات الإدخال تشمل: kVp، مادة الأنود، زاوية الأنود، الترشيح، وشكل موجة المولد.'
    }]

  },
  {
    id: '7.2',
    title: 'محاكاة مونت كارلو لنقل الإلكترون في الأنود',
    icon: <Cpu className="w-5 h-5" data-id="md0h2hhhu" data-path="src/components/Chapter7Content.tsx" />,
    subsections: [
    {
      id: '7.2.1',
      title: 'مبادئ تتبع الإلكترونات (التاريخ المكثف مقابل التاريخ حدثاً بحدث)',
      content: 'طريقتان لتتبع الإلكترونات: التاريخ حدثاً بحدث يتتبع كل تفاعل منفرد (دقيق لكن بطيء)، والتاريخ المكثف يجمع التفاعلات الصغيرة (أسرع لكن تقريبي). الاختيار يعتمد على الدقة المطلوبة والوقت المتاح.'
    },
    {
      id: '7.2.2',
      title: 'نمذجة إشعاع الكبح وإنتاج الفوتونات المميزة',
      content: 'المحاكاة تشمل إشعاع الكبح (عملية مستمرة) والإشعاع المميز (عملية منفصلة). يتم حساب الاحتماليات لكل نوع ومعاينة الطاقات والاتجاهات باستخدام التوزيعات الفيزيائية.'
    }]

  },
  {
    id: '7.3',
    title: 'أكواد مونت كارلو الشائعة لنمذجة المصدر',
    icon: <Code className="w-5 h-5" data-id="5srdr0gl6" data-path="src/components/Chapter7Content.tsx" />,
    subsections: [
    {
      id: '7.3.1',
      title: 'نظرة عامة على القدرات وواجهة المستخدم (MCNP، Geant4، EGSnrc، PENELOPE)',
      content: 'MCNP: شامل ومعتمد، صعب التعلم. Geant4: مرن وقوي، يتطلب برمجة C++. EGSnrc: متخصص في الفوتونات والإلكترونات، دقيق جداً. PENELOPE: مفتوح المصدر، توثيق ممتاز.'
    },
    {
      id: '7.3.2',
      title: 'تعريف هندسة المصدر والعمليات الفيزيائية',
      content: 'تعريف الهندسة يشمل: أبعاد الأنود، المواد، البقعة البؤرية، وزاوية الأنود. العمليات الفيزيائية تشمل: التبعثر المرن وغير المرن، التأين، وإنتاج الأشعة السينية.'
    }]

  },
  {
    id: '7.4',
    title: 'دمج أشكال الموجات المولدة والترشيح في عمليات المحاكاة',
    icon: <Settings className="w-5 h-5" data-id="smrrt0nr1" data-path="src/components/Chapter7Content.tsx" />,
    content: 'المحاكاة الشاملة تتطلب دمج تأثيرات المولد (أشكال الموجة، التموج) والترشيح (المتأصل والإضافي). يتم ذلك بمعاينة الطيف حسب شكل الموجة ثم تطبيق التوهين عبر المرشحات.'
  },
  {
    id: '7.5',
    title: 'التحقق من صحة الأطياف المحاكاة وقياس كفاءتها',
    icon: <CheckCircle className="w-5 h-5" data-id="bm5m61ym2" data-path="src/components/Chapter7Content.tsx" />,
    content: 'التحقق يتم بمقارنة النتائج مع: القياسات التجريبية، النماذج المرجعية، والطيوف المعايرة. مقاييس الكفاءة تشمل: الوقت/التاريخ، استهلاك الذاكرة، ودقة النتائج.'
  },
  {
    id: '7.6',
    title: 'تمرين عملي: محاكاة طيف أنود التنغستن',
    icon: <PlayCircle className="w-5 h-5" data-id="tydicn2jf" data-path="src/components/Chapter7Content.tsx" />,
    content: 'تمرين تطبيقي لمحاكاة طيف أنود التنغستن عند 120 kVp مع ترشيح 2.5 mm Al. يتضمن: إعداد الهندسة، تعريف المصدر، تشغيل المحاكاة، وتحليل النتائج.'
  }];


  const progress = completedSections.length / sections.length * 100;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6" dir="rtl" data-id="0j7art9tu" data-path="src/components/Chapter7Content.tsx">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }} data-id="v796i37cm" data-path="src/components/Chapter7Content.tsx">
        
        <Card className="mb-6" data-id="ukyspwy68" data-path="src/components/Chapter7Content.tsx">
          <CardHeader className="text-center" data-id="8b7d7ed45" data-path="src/components/Chapter7Content.tsx">
            <CardTitle className="text-2xl font-bold text-right" data-id="wc8kelpdx" data-path="src/components/Chapter7Content.tsx">
              الفصل السابع: محاكاة أطياف الأشعة السينية - التقنيات والأدوات
            </CardTitle>
            <CardDescription className="text-right" data-id="t9ssdp32j" data-path="src/components/Chapter7Content.tsx">
              دراسة شاملة لتقنيات وأدوات محاكاة أطياف الأشعة السينية
            </CardDescription>
            <div className="mt-4" data-id="n1azjaugx" data-path="src/components/Chapter7Content.tsx">
              <div className="flex justify-between items-center mb-2" data-id="cto39lt1m" data-path="src/components/Chapter7Content.tsx">
                <span className="text-sm text-muted-foreground" data-id="xltbfqqe7" data-path="src/components/Chapter7Content.tsx">التقدم</span>
                <span className="text-sm font-medium" data-id="12g7qrf0p" data-path="src/components/Chapter7Content.tsx">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" data-id="x2t7uf37x" data-path="src/components/Chapter7Content.tsx" />
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      <Tabs defaultValue="content" className="w-full" data-id="mlj90y1dx" data-path="src/components/Chapter7Content.tsx">
        <TabsList className="grid w-full grid-cols-4" data-id="49sio3paq" data-path="src/components/Chapter7Content.tsx">
          <TabsTrigger value="content" data-id="p4psuyaty" data-path="src/components/Chapter7Content.tsx">المحتوى</TabsTrigger>
          <TabsTrigger value="codes" data-id="8sqy44jes" data-path="src/components/Chapter7Content.tsx">أكواد المحاكاة</TabsTrigger>
          <TabsTrigger value="validation" data-id="5evqeu1e8" data-path="src/components/Chapter7Content.tsx">التحقق</TabsTrigger>
          <TabsTrigger value="practical" data-id="7eywuk5mj" data-path="src/components/Chapter7Content.tsx">التطبيق العملي</TabsTrigger>
        </TabsList>
        
        <TabsContent value="content" className="space-y-4" data-id="d7vrvcy6c" data-path="src/components/Chapter7Content.tsx">
          {sections.map((section, index) =>
          <motion.div
            key={section.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }} data-id="rdkxzfhr9" data-path="src/components/Chapter7Content.tsx">
              
              <Card className="overflow-hidden" data-id="c7lxjpi9n" data-path="src/components/Chapter7Content.tsx">
                <Collapsible
                open={openSections[section.id]}
                onOpenChange={() => toggleSection(section.id)} data-id="sx6czk29k" data-path="src/components/Chapter7Content.tsx">
                  
                  <CollapsibleTrigger asChild data-id="bvgadpzqt" data-path="src/components/Chapter7Content.tsx">
                    <CardHeader className="hover:bg-muted/50 cursor-pointer transition-colors" data-id="he6yaucv1" data-path="src/components/Chapter7Content.tsx">
                      <div className="flex items-center justify-between" data-id="ccvmzhu35" data-path="src/components/Chapter7Content.tsx">
                        <div className="flex items-center gap-3" data-id="bwjjwp00o" data-path="src/components/Chapter7Content.tsx">
                          <div className="flex items-center gap-2" data-id="2acwstuz8" data-path="src/components/Chapter7Content.tsx">
                            {section.icon}
                            <Badge variant="outline" data-id="8m73x3n9k" data-path="src/components/Chapter7Content.tsx">{section.id}</Badge>
                          </div>
                          <CardTitle className="text-lg text-right" data-id="0xtwoofvt" data-path="src/components/Chapter7Content.tsx">{section.title}</CardTitle>
                        </div>
                        <div className="flex items-center gap-2" data-id="idackfkrf" data-path="src/components/Chapter7Content.tsx">
                          {completedSections.includes(section.id) &&
                        <Badge variant="default" data-id="4cc21g005" data-path="src/components/Chapter7Content.tsx">مكتمل</Badge>
                        }
                          {openSections[section.id] ?
                        <ChevronDown className="w-4 h-4" data-id="5uil25dl7" data-path="src/components/Chapter7Content.tsx" /> :

                        <ChevronRight className="w-4 h-4" data-id="9y29voiix" data-path="src/components/Chapter7Content.tsx" />
                        }
                        </div>
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent data-id="nqi6lz9a0" data-path="src/components/Chapter7Content.tsx">
                    <CardContent className="pt-0" data-id="52wdb1t0t" data-path="src/components/Chapter7Content.tsx">
                      {section.subsections ?
                    <div className="space-y-4" data-id="dc3ej5l0v" data-path="src/components/Chapter7Content.tsx">
                          {section.subsections.map((subsection) =>
                      <Card key={subsection.id} className="border-l-4 border-l-primary" data-id="jt30brlqb" data-path="src/components/Chapter7Content.tsx">
                              <CardHeader className="pb-2" data-id="djas1e5an" data-path="src/components/Chapter7Content.tsx">
                                <div className="flex items-center gap-2" data-id="8kobk98a9" data-path="src/components/Chapter7Content.tsx">
                                  <Badge variant="secondary" data-id="g8rdliw8r" data-path="src/components/Chapter7Content.tsx">{subsection.id}</Badge>
                                  <CardTitle className="text-base text-right" data-id="j2t7hl4bj" data-path="src/components/Chapter7Content.tsx">
                                    {subsection.title}
                                  </CardTitle>
                                </div>
                              </CardHeader>
                              <CardContent data-id="henaasn5q" data-path="src/components/Chapter7Content.tsx">
                                <p className="text-muted-foreground text-right leading-relaxed" data-id="gtovgqg52" data-path="src/components/Chapter7Content.tsx">
                                  {subsection.content}
                                </p>
                              </CardContent>
                            </Card>
                      )}
                        </div> :

                    <p className="text-muted-foreground text-right leading-relaxed" data-id="lrrlrsg7i" data-path="src/components/Chapter7Content.tsx">
                          {section.content}
                        </p>
                    }
                      
                      <div className="mt-4 flex justify-start" data-id="4me3zarqt" data-path="src/components/Chapter7Content.tsx">
                        <Button
                        onClick={() => markAsCompleted(section.id)}
                        disabled={completedSections.includes(section.id)}
                        size="sm" data-id="7lnnx718u" data-path="src/components/Chapter7Content.tsx">
                          {completedSections.includes(section.id) ? 'مكتمل' : 'وضع علامة كمكتمل'}
                        </Button>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            </motion.div>
          )}
        </TabsContent>

        <TabsContent value="codes" className="space-y-4" data-id="irl4n1byx" data-path="src/components/Chapter7Content.tsx">
          <div className="grid md:grid-cols-2 gap-6" data-id="g95hvuz42" data-path="src/components/Chapter7Content.tsx">
            <Card data-id="d1mbe10ck" data-path="src/components/Chapter7Content.tsx">
              <CardHeader data-id="vm4x7ir5y" data-path="src/components/Chapter7Content.tsx">
                <CardTitle className="text-right" data-id="b41x5qf5k" data-path="src/components/Chapter7Content.tsx">أكواد Monte Carlo الرئيسية</CardTitle>
              </CardHeader>
              <CardContent data-id="74rkg0h6j" data-path="src/components/Chapter7Content.tsx">
                <div className="space-y-4" data-id="t21nz480g" data-path="src/components/Chapter7Content.tsx">
                  <div className="bg-blue-50 p-4 rounded-lg" data-id="lfkj3rnyb" data-path="src/components/Chapter7Content.tsx">
                    <h4 className="font-semibold text-right mb-2" data-id="cn2dztuxr" data-path="src/components/Chapter7Content.tsx">MCNP</h4>
                    <ul className="text-sm space-y-1 text-right" data-id="9x90ib4vq" data-path="src/components/Chapter7Content.tsx">
                      <li data-id="v481tbguy" data-path="src/components/Chapter7Content.tsx">• شامل ومعتمد صناعياً</li>
                      <li data-id="c0ul2idpp" data-path="src/components/Chapter7Content.tsx">• نقل النيوترونات والفوتونات</li>
                      <li data-id="8iipl7yay" data-path="src/components/Chapter7Content.tsx">• منحنى تعلم شديد</li>
                      <li data-id="vy11qkn48" data-path="src/components/Chapter7Content.tsx">• وثائق ممتازة</li>
                    </ul>
                  </div>
                  
                  <div className="bg-green-50 p-4 rounded-lg" data-id="8s2cdljij" data-path="src/components/Chapter7Content.tsx">
                    <h4 className="font-semibold text-right mb-2" data-id="t7omw3ls9" data-path="src/components/Chapter7Content.tsx">Geant4</h4>
                    <ul className="text-sm space-y-1 text-right" data-id="dtu70qais" data-path="src/components/Chapter7Content.tsx">
                      <li data-id="4hkl42ql9" data-path="src/components/Chapter7Content.tsx">• مرن وقابل للتخصيص</li>
                      <li data-id="qh4f5p1nj" data-path="src/components/Chapter7Content.tsx">• يتطلب برمجة C++</li>
                      <li data-id="ps7fegw4a" data-path="src/components/Chapter7Content.tsx">• مستخدم في CERN</li>
                      <li data-id="0c8iaki0r" data-path="src/components/Chapter7Content.tsx">• مجتمع نشط</li>
                    </ul>
                  </div>
                  
                  <div className="bg-purple-50 p-4 rounded-lg" data-id="hrfg13lkt" data-path="src/components/Chapter7Content.tsx">
                    <h4 className="font-semibold text-right mb-2" data-id="90t08368x" data-path="src/components/Chapter7Content.tsx">EGSnrc</h4>
                    <ul className="text-sm space-y-1 text-right" data-id="e1ov97b7f" data-path="src/components/Chapter7Content.tsx">
                      <li data-id="7zu85x7bh" data-path="src/components/Chapter7Content.tsx">• متخصص في الفوتونات</li>
                      <li data-id="n98f098sd" data-path="src/components/Chapter7Content.tsx">• دقة عالية جداً</li>
                      <li data-id="b9b9tgoei" data-path="src/components/Chapter7Content.tsx">• مستخدم في الفيزياء الطبية</li>
                      <li data-id="hrqt8pg5q" data-path="src/components/Chapter7Content.tsx">• مفتوح المصدر</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card data-id="vcx0yifgp" data-path="src/components/Chapter7Content.tsx">
              <CardHeader data-id="i1btkfd12" data-path="src/components/Chapter7Content.tsx">
                <CardTitle className="text-right" data-id="hqzyguor1" data-path="src/components/Chapter7Content.tsx">النماذج التحليلية</CardTitle>
              </CardHeader>
              <CardContent data-id="cxtt8ohkz" data-path="src/components/Chapter7Content.tsx">
                <div className="space-y-4" data-id="i764ep4y5" data-path="src/components/Chapter7Content.tsx">
                  <div className="bg-orange-50 p-4 rounded-lg" data-id="w3wwvl4ft" data-path="src/components/Chapter7Content.tsx">
                    <h4 className="font-semibold text-right mb-2" data-id="0yxdly1nc" data-path="src/components/Chapter7Content.tsx">SpekCalc</h4>
                    <ul className="text-sm space-y-1 text-right" data-id="3bahujl17" data-path="src/components/Chapter7Content.tsx">
                      <li data-id="45ez0k7so" data-path="src/components/Chapter7Content.tsx">• سريع وسهل الاستخدام</li>
                      <li data-id="gg9tfvjug" data-path="src/components/Chapter7Content.tsx">• واجهة ويب</li>
                      <li data-id="50l78alca" data-path="src/components/Chapter7Content.tsx">• حسابات روتينية</li>
                      <li data-id="amyl7go5u" data-path="src/components/Chapter7Content.tsx">• دقة مقبولة</li>
                    </ul>
                  </div>
                  
                  <div className="bg-red-50 p-4 rounded-lg" data-id="j581fkg7c" data-path="src/components/Chapter7Content.tsx">
                    <h4 className="font-semibold text-right mb-2" data-id="uczpo8rh8" data-path="src/components/Chapter7Content.tsx">TASMIP</h4>
                    <ul className="text-sm space-y-1 text-right" data-id="e66slq3qw" data-path="src/components/Chapter7Content.tsx">
                      <li data-id="b7qhjs9rh" data-path="src/components/Chapter7Content.tsx">• Tucker وآخرون</li>
                      <li data-id="zj5cc3iiv" data-path="src/components/Chapter7Content.tsx">• نموذج تجريبي</li>
                      <li data-id="j33cdu96j" data-path="src/components/Chapter7Content.tsx">• معادلات بسيطة</li>
                      <li data-id="3m5apt25b" data-path="src/components/Chapter7Content.tsx">• معايرة تجريبية</li>
                    </ul>
                  </div>
                  
                  <div className="bg-yellow-50 p-4 rounded-lg" data-id="0y3sddxut" data-path="src/components/Chapter7Content.tsx">
                    <h4 className="font-semibold text-right mb-2" data-id="06dpcm7ta" data-path="src/components/Chapter7Content.tsx">Task Group 78</h4>
                    <ul className="text-sm space-y-1 text-right" data-id="r5tpo4wdj" data-path="src/components/Chapter7Content.tsx">
                      <li data-id="t9m2b45cr" data-path="src/components/Chapter7Content.tsx">• معايير حديثة</li>
                      <li data-id="nwdz751pr" data-path="src/components/Chapter7Content.tsx">• توصيات AAPM</li>
                      <li data-id="yc4y7hmyr" data-path="src/components/Chapter7Content.tsx">• مرجع للصناعة</li>
                      <li data-id="1zqa45kdo" data-path="src/components/Chapter7Content.tsx">• تحديثات منتظمة</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="validation" className="space-y-4" data-id="obmgsj2e4" data-path="src/components/Chapter7Content.tsx">
          <Card data-id="hvahov1y7" data-path="src/components/Chapter7Content.tsx">
            <CardHeader data-id="t45hjbmsh" data-path="src/components/Chapter7Content.tsx">
              <CardTitle className="text-right" data-id="2avuha419" data-path="src/components/Chapter7Content.tsx">طرق التحقق من صحة النتائج</CardTitle>
            </CardHeader>
            <CardContent data-id="j6ijevus0" data-path="src/components/Chapter7Content.tsx">
              <div className="space-y-6" data-id="a8oagrcu5" data-path="src/components/Chapter7Content.tsx">
                <div className="grid md:grid-cols-2 gap-4" data-id="0kv3nfqvd" data-path="src/components/Chapter7Content.tsx">
                  <Card data-id="hzs4bisrw" data-path="src/components/Chapter7Content.tsx">
                    <CardHeader data-id="agelfvyuw" data-path="src/components/Chapter7Content.tsx">
                      <CardTitle className="text-base text-right" data-id="x3oo3e4u2" data-path="src/components/Chapter7Content.tsx">المقارنة التجريبية</CardTitle>
                    </CardHeader>
                    <CardContent data-id="q9o44u2tq" data-path="src/components/Chapter7Content.tsx">
                      <ul className="text-sm space-y-2 text-right" data-id="bac8rdet8" data-path="src/components/Chapter7Content.tsx">
                        <li data-id="xyft3g6f0" data-path="src/components/Chapter7Content.tsx">• قياسات طيفية مباشرة</li>
                        <li data-id="oiw8kcsen" data-path="src/components/Chapter7Content.tsx">• كاشفات عالية الدقة</li>
                        <li data-id="23sfnc0qb" data-path="src/components/Chapter7Content.tsx">• ظروف محكمة</li>
                        <li data-id="pozd38y4c" data-path="src/components/Chapter7Content.tsx">• تصحيحات الاستجابة</li>
                      </ul>
                    </CardContent>
                  </Card>
                  
                  <Card data-id="1p4n3ev84" data-path="src/components/Chapter7Content.tsx">
                    <CardHeader data-id="2mwt2deh0" data-path="src/components/Chapter7Content.tsx">
                      <CardTitle className="text-base text-right" data-id="somg6jqsj" data-path="src/components/Chapter7Content.tsx">المقارنة النظرية</CardTitle>
                    </CardHeader>
                    <CardContent data-id="xu9hsx8i4" data-path="src/components/Chapter7Content.tsx">
                      <ul className="text-sm space-y-2 text-right" data-id="pu6msboo1" data-path="src/components/Chapter7Content.tsx">
                        <li data-id="mcjut0qar" data-path="src/components/Chapter7Content.tsx">• النماذج المرجعية</li>
                        <li data-id="7lekguc37" data-path="src/components/Chapter7Content.tsx">• البيانات المنشورة</li>
                        <li data-id="71op52yqd" data-path="src/components/Chapter7Content.tsx">• المعايير الدولية</li>
                        <li data-id="88rbtvsd7" data-path="src/components/Chapter7Content.tsx">• المقارنة البينية</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>
                
                <Card data-id="hkx59006w" data-path="src/components/Chapter7Content.tsx">
                  <CardHeader data-id="qgdcwk0fn" data-path="src/components/Chapter7Content.tsx">
                    <CardTitle className="text-base text-right" data-id="1buxihsha" data-path="src/components/Chapter7Content.tsx">مقاييس الدقة</CardTitle>
                  </CardHeader>
                  <CardContent data-id="nxp9ec90h" data-path="src/components/Chapter7Content.tsx">
                    <div className="grid md:grid-cols-3 gap-4" data-id="b4887k8cr" data-path="src/components/Chapter7Content.tsx">
                      <div className="bg-blue-50 p-3 rounded" data-id="ko5f1f61b" data-path="src/components/Chapter7Content.tsx">
                        <h5 className="font-semibold text-right" data-id="dqcjcln7k" data-path="src/components/Chapter7Content.tsx">الانحراف المتوسط</h5>
                        <div className="font-mono text-xs mt-1 text-center" data-id="fwcwxnkq2" data-path="src/components/Chapter7Content.tsx">
                          |Icalc - Iexp|/Iexp × 100%
                        </div>
                      </div>
                      <div className="bg-green-50 p-3 rounded" data-id="w9xb0do0y" data-path="src/components/Chapter7Content.tsx">
                        <h5 className="font-semibold text-right" data-id="5lwhgspdj" data-path="src/components/Chapter7Content.tsx">معامل الارتباط</h5>
                        <div className="font-mono text-xs mt-1 text-center" data-id="k7gv0sijf" data-path="src/components/Chapter7Content.tsx">
                          R² &gt; 0.95
                        </div>
                      </div>
                      <div className="bg-orange-50 p-3 rounded" data-id="2ynb1ch4w" data-path="src/components/Chapter7Content.tsx">
                        <h5 className="font-semibold text-right" data-id="a59ha37is" data-path="src/components/Chapter7Content.tsx">اختبار Chi-squared</h5>
                        <div className="font-mono text-xs mt-1 text-center" data-id="ytp3wccwi" data-path="src/components/Chapter7Content.tsx">
                          χ²/DOF &lt; 2
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card data-id="8g2pdlyg5" data-path="src/components/Chapter7Content.tsx">
                  <CardHeader data-id="frrsvij2q" data-path="src/components/Chapter7Content.tsx">
                    <CardTitle className="text-base text-right" data-id="5v6jyxald" data-path="src/components/Chapter7Content.tsx">مؤشرات الأداء</CardTitle>
                  </CardHeader>
                  <CardContent data-id="j315mlrup" data-path="src/components/Chapter7Content.tsx">
                    <div className="bg-muted p-4 rounded-lg" data-id="3y0zqtbnw" data-path="src/components/Chapter7Content.tsx">
                      <div className="grid md:grid-cols-2 gap-4 text-sm" data-id="b0qnixfdl" data-path="src/components/Chapter7Content.tsx">
                        <div data-id="9kgf4bobw" data-path="src/components/Chapter7Content.tsx">
                          <h5 className="font-semibold text-right mb-2" data-id="1ix0h2l22" data-path="src/components/Chapter7Content.tsx">الكفاءة الحاسوبية:</h5>
                          <ul className="space-y-1 text-right" data-id="c8imxqw4h" data-path="src/components/Chapter7Content.tsx">
                            <li data-id="5snow52oa" data-path="src/components/Chapter7Content.tsx">• الوقت/التاريخ (ثانية)</li>
                            <li data-id="dy6jvjr5a" data-path="src/components/Chapter7Content.tsx">• استهلاك الذاكرة (MB)</li>
                            <li data-id="uaga9nidq" data-path="src/components/Chapter7Content.tsx">• معدل التقارب</li>
                          </ul>
                        </div>
                        <div data-id="bvz8rj2fn" data-path="src/components/Chapter7Content.tsx">
                          <h5 className="font-semibold text-right mb-2" data-id="x6ageqq5v" data-path="src/components/Chapter7Content.tsx">دقة النتائج:</h5>
                          <ul className="space-y-1 text-right" data-id="65lr1hi7m" data-path="src/components/Chapter7Content.tsx">
                            <li data-id="dqsqdx6lu" data-path="src/components/Chapter7Content.tsx">• الانحراف المعياري (&lt;1%)</li>
                            <li data-id="zw2fld62z" data-path="src/components/Chapter7Content.tsx">• الثقة الإحصائية (95%)</li>
                            <li data-id="ply1v6l8f" data-path="src/components/Chapter7Content.tsx">• الاستقرار العددي</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="practical" className="space-y-4" data-id="ebk7ugmhd" data-path="src/components/Chapter7Content.tsx">
          <Card data-id="75x7zluk8" data-path="src/components/Chapter7Content.tsx">
            <CardHeader data-id="vfokiox19" data-path="src/components/Chapter7Content.tsx">
              <CardTitle className="text-right" data-id="q9w940euq" data-path="src/components/Chapter7Content.tsx">تمرين عملي: محاكاة طيف التنغستن</CardTitle>
            </CardHeader>
            <CardContent data-id="a3v9axnst" data-path="src/components/Chapter7Content.tsx">
              <div className="space-y-6" data-id="x7vrva3hc" data-path="src/components/Chapter7Content.tsx">
                <Card data-id="ki5imhydb" data-path="src/components/Chapter7Content.tsx">
                  <CardHeader data-id="25zb2qrle" data-path="src/components/Chapter7Content.tsx">
                    <CardTitle className="text-base text-right" data-id="964tjgw0u" data-path="src/components/Chapter7Content.tsx">الخطوة 1: إعداد المحاكاة</CardTitle>
                  </CardHeader>
                  <CardContent data-id="y0ufxvzob" data-path="src/components/Chapter7Content.tsx">
                    <div className="bg-blue-50 p-4 rounded-lg" data-id="92i51qb7n" data-path="src/components/Chapter7Content.tsx">
                      <h5 className="font-semibold text-right mb-2" data-id="b3sw02ut0" data-path="src/components/Chapter7Content.tsx">المعاملات:</h5>
                      <ul className="text-sm space-y-1 text-right" data-id="jwa8pslbq" data-path="src/components/Chapter7Content.tsx">
                        <li data-id="o74nfw51s" data-path="src/components/Chapter7Content.tsx">• مادة الأنود: التنغستن (W, Z=74)</li>
                        <li data-id="2g1vquv9f" data-path="src/components/Chapter7Content.tsx">• الجهد: 120 kVp</li>
                        <li data-id="7xilngs6y" data-path="src/components/Chapter7Content.tsx">• زاوية الأنود: 17°</li>
                        <li data-id="xksah0lg9" data-path="src/components/Chapter7Content.tsx">• الترشيح: 2.5 mm Al</li>
                        <li data-id="fmvcwqu76" data-path="src/components/Chapter7Content.tsx">• البقعة البؤرية: 1×1 mm²</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
                
                <Card data-id="w34br1wxk" data-path="src/components/Chapter7Content.tsx">
                  <CardHeader data-id="mrsnyb3dd" data-path="src/components/Chapter7Content.tsx">
                    <CardTitle className="text-base text-right" data-id="v0qxpaaff" data-path="src/components/Chapter7Content.tsx">الخطوة 2: تعريف الهندسة</CardTitle>
                  </CardHeader>
                  <CardContent data-id="9xcas73zj" data-path="src/components/Chapter7Content.tsx">
                    <div className="bg-green-50 p-4 rounded-lg" data-id="269gwz8yz" data-path="src/components/Chapter7Content.tsx">
                      <div className="font-mono text-sm bg-white p-3 rounded border" data-id="lhwce52zm" data-path="src/components/Chapter7Content.tsx">
                        <div className="text-right space-y-1" data-id="9zdrhdtdt" data-path="src/components/Chapter7Content.tsx">
                          <div data-id="frd0nxfir" data-path="src/components/Chapter7Content.tsx"># تعريف الأنود</div>
                          <div data-id="ikw4vnac2" data-path="src/components/Chapter7Content.tsx">TARGET: W, 0.5 mm thick</div>
                          <div data-id="fldk3n0ye" data-path="src/components/Chapter7Content.tsx">ANGLE: 17 degrees</div>
                          <div data-id="w6807twyb" data-path="src/components/Chapter7Content.tsx"># تعريف المرشح</div>
                          <div data-id="5rin286n2" data-path="src/components/Chapter7Content.tsx">FILTER: Al, 2.5 mm</div>
                          <div data-id="n5rhm8djy" data-path="src/components/Chapter7Content.tsx"># تعريف الكاشف</div>
                          <div data-id="kwp5uzptr" data-path="src/components/Chapter7Content.tsx">DETECTOR: 1 m distance</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card data-id="jzp95n4nd" data-path="src/components/Chapter7Content.tsx">
                  <CardHeader data-id="1k9d1tpbq" data-path="src/components/Chapter7Content.tsx">
                    <CardTitle className="text-base text-right" data-id="415w3yd93" data-path="src/components/Chapter7Content.tsx">الخطوة 3: تشغيل المحاكاة</CardTitle>
                  </CardHeader>
                  <CardContent data-id="461zcihrc" data-path="src/components/Chapter7Content.tsx">
                    <div className="bg-orange-50 p-4 rounded-lg" data-id="qopj1k54g" data-path="src/components/Chapter7Content.tsx">
                      <h5 className="font-semibold text-right mb-2" data-id="mhgw9v1el" data-path="src/components/Chapter7Content.tsx">معلمات التشغيل:</h5>
                      <ul className="text-sm space-y-1 text-right" data-id="cujhek9sh" data-path="src/components/Chapter7Content.tsx">
                        <li data-id="e54wy008z" data-path="src/components/Chapter7Content.tsx">• عدد التواريخ: 10⁶</li>
                        <li data-id="heb6js5m8" data-path="src/components/Chapter7Content.tsx">• نطاق الطاقة: 1-120 keV</li>
                        <li data-id="bi7zxwu2n" data-path="src/components/Chapter7Content.tsx">• حجم القناة: 1 keV</li>
                        <li data-id="b0a2j4wpz" data-path="src/components/Chapter7Content.tsx">• الزاوية الصلبة: 10⁻⁴ sr</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
                
                <Card data-id="c64yw94r2" data-path="src/components/Chapter7Content.tsx">
                  <CardHeader data-id="rzmr24lyv" data-path="src/components/Chapter7Content.tsx">
                    <CardTitle className="text-base text-right" data-id="sqtuwv4f1" data-path="src/components/Chapter7Content.tsx">الخطوة 4: تحليل النتائج</CardTitle>
                  </CardHeader>
                  <CardContent data-id="qj2hsmxjq" data-path="src/components/Chapter7Content.tsx">
                    <div className="grid md:grid-cols-2 gap-4" data-id="xe2k83bfa" data-path="src/components/Chapter7Content.tsx">
                      <div className="bg-purple-50 p-4 rounded-lg" data-id="fhlhaz3sc" data-path="src/components/Chapter7Content.tsx">
                        <h5 className="font-semibold text-right mb-2" data-id="p4v6woq96" data-path="src/components/Chapter7Content.tsx">المؤشرات المتوقعة:</h5>
                        <ul className="text-sm space-y-1 text-right" data-id="g8luo9l9y" data-path="src/components/Chapter7Content.tsx">
                          <li data-id="58aomii6i" data-path="src/components/Chapter7Content.tsx">• الطاقة المتوسطة: ~55 keV</li>
                          <li data-id="x1kd9o4iu" data-path="src/components/Chapter7Content.tsx">• HVL: ~4.2 mm Al</li>
                          <li data-id="pl3dvuprh" data-path="src/components/Chapter7Content.tsx">• ذروات W: 59.3, 67.2 keV</li>
                          <li data-id="2on5ix27j" data-path="src/components/Chapter7Content.tsx">• الكفاءة: ~0.8%</li>
                        </ul>
                      </div>
                      <div className="bg-red-50 p-4 rounded-lg" data-id="e0g0451pt" data-path="src/components/Chapter7Content.tsx">
                        <h5 className="font-semibold text-right mb-2" data-id="0am0006v7" data-path="src/components/Chapter7Content.tsx">التحقق:</h5>
                        <ul className="text-sm space-y-1 text-right" data-id="b68esjkf0" data-path="src/components/Chapter7Content.tsx">
                          <li data-id="0cz5twvyb" data-path="src/components/Chapter7Content.tsx">• مقارنة مع SpekCalc</li>
                          <li data-id="p184chly2" data-path="src/components/Chapter7Content.tsx">• فحص التوازن الطاقوي</li>
                          <li data-id="1grgo5oh4" data-path="src/components/Chapter7Content.tsx">• تحليل الخطأ الإحصائي</li>
                          <li data-id="24hvrdycc" data-path="src/components/Chapter7Content.tsx">• مراجعة التقارب</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card data-id="xea9lg01n" data-path="src/components/Chapter7Content.tsx">
                  <CardHeader data-id="bo2sks8bd" data-path="src/components/Chapter7Content.tsx">
                    <CardTitle className="text-base text-right" data-id="18biiyoue" data-path="src/components/Chapter7Content.tsx">النتائج المتوقعة</CardTitle>
                  </CardHeader>
                  <CardContent data-id="69egd9ntc" data-path="src/components/Chapter7Content.tsx">
                    <div className="bg-gray-50 p-4 rounded-lg" data-id="6fo08oxej" data-path="src/components/Chapter7Content.tsx">
                      <div className="h-40 bg-gradient-to-r from-blue-200 via-green-200 to-red-200 rounded flex items-center justify-center" data-id="efvdsj3pk" data-path="src/components/Chapter7Content.tsx">
                        <span className="text-sm text-gray-700" data-id="r920quswu" data-path="src/components/Chapter7Content.tsx">طيف محاكاة لأنود التنغستن عند 120 kVp</span>
                      </div>
                      <p className="text-xs mt-2 text-center text-gray-600" data-id="25hcseshg" data-path="src/components/Chapter7Content.tsx">
                        طيف مستمر مع ذروات مميزة عند 59.3 و 67.2 keV
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card className="mt-8" data-id="xml369doo" data-path="src/components/Chapter7Content.tsx">
        <CardHeader data-id="8iemnlhy4" data-path="src/components/Chapter7Content.tsx">
          <CardTitle className="text-right" data-id="trolicv27" data-path="src/components/Chapter7Content.tsx">أهداف التعلم</CardTitle>
        </CardHeader>
        <CardContent data-id="165sepbnq" data-path="src/components/Chapter7Content.tsx">
          <ul className="list-disc list-inside space-y-2 text-right" data-id="6g13ralro" data-path="src/components/Chapter7Content.tsx">
            <li data-id="us9xzxx0w" data-path="src/components/Chapter7Content.tsx">فهم النماذج التحليلية والتجريبية لتوليد الأطياف</li>
            <li data-id="33fh987kf" data-path="src/components/Chapter7Content.tsx">إتقان تقنيات محاكاة Monte Carlo للأشعة السينية</li>
            <li data-id="m6t9plldl" data-path="src/components/Chapter7Content.tsx">التعرف على أكواد المحاكاة المختلفة وقدراتها</li>
            <li data-id="8rztfv4fl" data-path="src/components/Chapter7Content.tsx">تعلم طرق التحقق من صحة النتائج</li>
            <li data-id="sq3d1u87o" data-path="src/components/Chapter7Content.tsx">تطبيق المحاكاة في مشاريع عملية</li>
          </ul>
        </CardContent>
      </Card>
    </div>);

};

export default Chapter7Content;