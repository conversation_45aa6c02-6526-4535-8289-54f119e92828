import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';
import { LanguageProvider } from '@/contexts/LanguageContext';
import HomePage from '@/pages/HomePage';
import CoursePage from '@/pages/CoursePage';
import NotFound from '@/pages/NotFound';
import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000 // 10 minutes
    }
  }
});

function App() {
  return (
    <QueryClientProvider client={queryClient} data-id="wgr53gu4l" data-path="src/App.tsx">
      <LanguageProvider data-id="50aj7b7ev" data-path="src/App.tsx">
        <TooltipProvider data-id="0u6n2ldnw" data-path="src/App.tsx">
          <Router data-id="puzukrg1y" data-path="src/App.tsx">
            <Routes data-id="qy2peahqn" data-path="src/App.tsx">
              <Route path="/" element={<HomePage data-id="rmclk09fu" data-path="src/App.tsx" />} data-id="31zq02psj" data-path="src/App.tsx" />
              <Route path="/course" element={<CoursePage data-id="l23crdmjt" data-path="src/App.tsx" />} data-id="h63ifaq51" data-path="src/App.tsx" />
              <Route path="*" element={<NotFound data-id="o5cgvh94b" data-path="src/App.tsx" />} data-id="mflm6hgxg" data-path="src/App.tsx" />
            </Routes>
            <Toaster data-id="1bhxh6g4l" data-path="src/App.tsx" />
          </Router>
        </TooltipProvider>
      </LanguageProvider>
    </QueryClientProvider>);

}

export default App;