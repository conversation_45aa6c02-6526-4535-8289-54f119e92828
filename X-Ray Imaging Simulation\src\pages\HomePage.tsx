import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BookOpen, Monitor, Cpu, Zap, Users, Microscope, Search, TrendingUp, Play, ArrowLeft, ArrowRight } from 'lucide-react';
import { motion } from 'motion/react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSwitcher from '@/components/LanguageSwitcher';

const HomePage = () => {
  const { dir } = useLanguage();
  const { t } = useTranslation();

  const ArrowIcon = dir === 'rtl' ? ArrowLeft : ArrowRight;

  const features = [
  {
    icon: <Monitor className="w-8 h-8" data-id="yub5j8w81" data-path="src/pages/HomePage.tsx" />,
    title: t('home.features.3d_models.title'),
    description: t('home.features.3d_models.description')
  },
  {
    icon: <Cpu className="w-8 h-8" data-id="475hiti2b" data-path="src/pages/HomePage.tsx" />,
    title: t('home.features.monte_carlo.title'),
    description: t('home.features.monte_carlo.description')
  },
  {
    icon: <Zap className="w-8 h-8" data-id="8cw1kzwt2" data-path="src/pages/HomePage.tsx" />,
    title: t('home.features.linear_systems.title'),
    description: t('home.features.linear_systems.description')
  },
  {
    icon: <Search className="w-8 h-8" data-id="bu1l90izc" data-path="src/pages/HomePage.tsx" />,
    title: t('home.features.data_visualization.title'),
    description: t('home.features.data_visualization.description')
  }];

  const courseHighlights = [
  {
    title: t('home.part4.title'),
    chapters: [t('chapters.ch11.title'), t('chapters.ch12.title')],
    icon: <Monitor className="w-6 h-6" data-id="pu3cigo1z" data-path="src/pages/HomePage.tsx" />,
    color: 'from-blue-500 to-cyan-500'
  },
  {
    title: t('home.part5.title'),
    chapters: [
    t('chapters.ch13.title'),
    t('chapters.ch14.title'),
    t('chapters.ch15.title'),
    t('chapters.ch16.title'),
    t('chapters.ch17.title')],

    icon: <TrendingUp className="w-6 h-6" data-id="98wcxn5y5" data-path="src/pages/HomePage.tsx" />,
    color: 'from-purple-500 to-pink-500'
  }];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50" dir={dir} data-id="ilgzviw4d" data-path="src/pages/HomePage.tsx">
      {/* Language Switcher */}
      <div className="fixed top-4 right-4 z-50" data-id="706eemezn" data-path="src/pages/HomePage.tsx">
        <LanguageSwitcher data-id="satkjcugl" data-path="src/pages/HomePage.tsx" />
      </div>

      {/* Hero Section */}
      <section className="relative py-20 px-6" data-id="lvyr9oqus" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto text-center" data-id="0pwg4nb70" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }} data-id="rs69njqt4" data-path="src/pages/HomePage.tsx">

            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6" data-id="c5cm7ecfe" data-path="src/pages/HomePage.tsx">
              {t('home.hero.title')}
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto" data-id="es457c53k" data-path="src/pages/HomePage.tsx">
              {t('home.hero.subtitle')}
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12" data-id="7nvvod4qz" data-path="src/pages/HomePage.tsx">
              <Link to="/course" data-id="dhxtwugrj" data-path="src/pages/HomePage.tsx">
                <Button size="lg" className="text-lg px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700" data-id="f0ofbsoqx" data-path="src/pages/HomePage.tsx">
                  <Play className={`w-6 h-6 ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`} data-id="7xk781bb3" data-path="src/pages/HomePage.tsx" />
                  {t('home.hero.start_course')}
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="text-lg px-8 py-4" data-id="ghbtikh4z" data-path="src/pages/HomePage.tsx">
                <BookOpen className={`w-6 h-6 ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`} data-id="qllebwphi" data-path="src/pages/HomePage.tsx" />
                {t('home.hero.user_guide')}
              </Button>
            </div>

            <div className="flex flex-wrap items-center justify-center gap-4 text-sm" data-id="t2znmcrai" data-path="src/pages/HomePage.tsx">
              <Badge variant="secondary" className="px-4 py-2" data-id="9da21s1j0" data-path="src/pages/HomePage.tsx">
                <Users className={`w-4 h-4 ${dir === 'rtl' ? 'ml-1' : 'mr-1'}`} data-id="e4kaka7w7" data-path="src/pages/HomePage.tsx" />
                {t('home.badges.students_professionals')}
              </Badge>
              <Badge variant="secondary" className="px-4 py-2" data-id="kmmq9l380" data-path="src/pages/HomePage.tsx">
                <Monitor className={`w-4 h-4 ${dir === 'rtl' ? 'ml-1' : 'mr-1'}`} data-id="6wefg80wb" data-path="src/pages/HomePage.tsx" />
                {t('home.badges.interactive_content')}
              </Badge>
              <Badge variant="secondary" className="px-4 py-2" data-id="20ps0z34v" data-path="src/pages/HomePage.tsx">
                <Microscope className={`w-4 h-4 ${dir === 'rtl' ? 'ml-1' : 'mr-1'}`} data-id="509rbvjwo" data-path="src/pages/HomePage.tsx" />
                {t('home.badges.scientific_foundation')}
              </Badge>
            </div>
          </motion.div>
        </div>

        {/* Floating Animation Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none" data-id="nfly1byhu" data-path="src/pages/HomePage.tsx">
          <motion.div
            animate={{
              y: [0, -20, 0],
              rotate: [0, 5, 0]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-20 left-20 w-20 h-20 bg-blue-200 rounded-full opacity-20" data-id="wr7ws937n" data-path="src/pages/HomePage.tsx" />

          <motion.div
            animate={{
              y: [0, 15, 0],
              rotate: [0, -5, 0]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
            className="absolute top-40 right-32 w-16 h-16 bg-purple-200 rounded-full opacity-20" data-id="d9vaq7c2n" data-path="src/pages/HomePage.tsx" />

          <motion.div
            animate={{
              y: [0, -10, 0],
              rotate: [0, 3, 0]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute bottom-40 left-40 w-24 h-24 bg-pink-200 rounded-full opacity-20" data-id="7lerya4fc" data-path="src/pages/HomePage.tsx" />

        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6" data-id="oyhzj5jpl" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto" data-id="jicyvcdv1" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16" data-id="zbrrc1pj2" data-path="src/pages/HomePage.tsx">

            <h2 className="text-4xl font-bold mb-4" data-id="54cbq4dbl" data-path="src/pages/HomePage.tsx">{t('home.features.title')}</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto" data-id="y7po9iugj" data-path="src/pages/HomePage.tsx">
              {t('home.features.subtitle')}
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8" data-id="8m7w8meed" data-path="src/pages/HomePage.tsx">
            {features.map((feature, index) =>
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }} data-id="b3c2f4qjw" data-path="src/pages/HomePage.tsx">

                <Card className="h-full hover:shadow-lg transition-all duration-300 group" data-id="g26pjwa1x" data-path="src/pages/HomePage.tsx">
                  <CardHeader className="text-center" data-id="iafwm1cri" data-path="src/pages/HomePage.tsx">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white group-hover:scale-110 transition-transform" data-id="359hxvl0h" data-path="src/pages/HomePage.tsx">
                      {feature.icon}
                    </div>
                    <CardTitle data-id="rlo1xoe98" data-path="src/pages/HomePage.tsx">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent data-id="y1imecdwd" data-path="src/pages/HomePage.tsx">
                    <CardDescription data-id="fppre51g0" data-path="src/pages/HomePage.tsx">{feature.description}</CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </section>

      {/* Course Overview */}
      <section className="py-20 px-6 bg-gradient-to-r from-blue-50 to-purple-50" data-id="6o98q9uhw" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto" data-id="yc7qcnprm" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16" data-id="7meaogr2s" data-path="src/pages/HomePage.tsx">

            <h2 className="text-4xl font-bold mb-4" data-id="0gnlhbdur" data-path="src/pages/HomePage.tsx">{t('home.course_content.title')}</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto" data-id="ipl48r5d4" data-path="src/pages/HomePage.tsx">
              {t('home.course_content.subtitle')}
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12" data-id="z1bz0wm3y" data-path="src/pages/HomePage.tsx">
            {courseHighlights.map((part, index) =>
            <motion.div
              key={index}
              initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }} data-id="tqrvx2wyq" data-path="src/pages/HomePage.tsx">

                <Card className="h-full hover:shadow-xl transition-all duration-300" data-id="12qb1ri2a" data-path="src/pages/HomePage.tsx">
                  <CardHeader data-id="padr0g0e9" data-path="src/pages/HomePage.tsx">
                    <div className={`w-12 h-12 bg-gradient-to-r ${part.color} rounded-lg flex items-center justify-center text-white mb-4`} data-id="yc6t817xi" data-path="src/pages/HomePage.tsx">
                      {part.icon}
                    </div>
                    <CardTitle className="text-xl mb-4" data-id="h8z1xxwkk" data-path="src/pages/HomePage.tsx">{part.title}</CardTitle>
                  </CardHeader>
                  <CardContent data-id="6tdn0ybsg" data-path="src/pages/HomePage.tsx">
                    <div className="space-y-3" data-id="4j87vn5yj" data-path="src/pages/HomePage.tsx">
                      {part.chapters.map((chapter, chapterIndex) =>
                    <div key={chapterIndex} className={`flex items-center space-x-3 ${dir === 'rtl' ? 'justify-end rtl:space-x-reverse' : 'justify-start'}`} data-id="ajctdwm6t" data-path="src/pages/HomePage.tsx">
                          {dir === 'rtl' && <span className="text-sm" data-id="qc4v2vbzx" data-path="src/pages/HomePage.tsx">{chapter}</span>}
                          <div className="w-2 h-2 bg-blue-500 rounded-full" data-id="j8rwjkzdi" data-path="src/pages/HomePage.tsx"></div>
                          {dir === 'ltr' && <span className="text-sm" data-id="8rzd3a9rf" data-path="src/pages/HomePage.tsx">{chapter}</span>}
                        </div>
                    )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </section>

      {/* VR Experience Section */}
      <section className="py-20 px-6" data-id="a6yilcq53" data-path="src/pages/HomePage.tsx">
        <div className="max-w-7xl mx-auto" data-id="rwq66bs9d" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }} data-id="gk8bxayfw" data-path="src/pages/HomePage.tsx">

            <Card className="bg-gradient-to-r from-slate-900 to-blue-900 text-white overflow-hidden" data-id="1durmazc5" data-path="src/pages/HomePage.tsx">
              <CardContent className="p-12" data-id="eddcocblc" data-path="src/pages/HomePage.tsx">
                <div className="grid lg:grid-cols-2 gap-12 items-center" data-id="4uh9wi18f" data-path="src/pages/HomePage.tsx">
                  <div data-id="mlqa8brs7" data-path="src/pages/HomePage.tsx">
                    <h2 className="text-4xl font-bold mb-6" data-id="68vfu8upi" data-path="src/pages/HomePage.tsx">{t('home.vr.title')}</h2>
                    <p className="text-xl mb-8 text-blue-100" data-id="jac5c5a6u" data-path="src/pages/HomePage.tsx">
                      {t('home.vr.subtitle')}
                    </p>
                    
                    <div className="space-y-4 mb-8" data-id="srap07hw1" data-path="src/pages/HomePage.tsx">
                      <div className={`flex items-center space-x-3 ${dir === 'rtl' ? 'justify-end rtl:space-x-reverse' : 'justify-start'}`} data-id="hm5isylls" data-path="src/pages/HomePage.tsx">
                        {dir === 'rtl' && <span data-id="a9m39oqf6" data-path="src/pages/HomePage.tsx">{t('home.vr.feature1')}</span>}
                        <div className="w-3 h-3 bg-green-400 rounded-full" data-id="305db6hwf" data-path="src/pages/HomePage.tsx"></div>
                        {dir === 'ltr' && <span data-id="ia8zr7yt4" data-path="src/pages/HomePage.tsx">{t('home.vr.feature1')}</span>}
                      </div>
                      <div className={`flex items-center space-x-3 ${dir === 'rtl' ? 'justify-end rtl:space-x-reverse' : 'justify-start'}`} data-id="tcw2xz4km" data-path="src/pages/HomePage.tsx">
                        {dir === 'rtl' && <span data-id="lf3wpjl7s" data-path="src/pages/HomePage.tsx">{t('home.vr.feature2')}</span>}
                        <div className="w-3 h-3 bg-green-400 rounded-full" data-id="ucx8hbznu" data-path="src/pages/HomePage.tsx"></div>
                        {dir === 'ltr' && <span data-id="m8w5923y7" data-path="src/pages/HomePage.tsx">{t('home.vr.feature2')}</span>}
                      </div>
                      <div className={`flex items-center space-x-3 ${dir === 'rtl' ? 'justify-end rtl:space-x-reverse' : 'justify-start'}`} data-id="ywfhwdbds" data-path="src/pages/HomePage.tsx">
                        {dir === 'rtl' && <span data-id="pvle405ye" data-path="src/pages/HomePage.tsx">{t('home.vr.feature3')}</span>}
                        <div className="w-3 h-3 bg-green-400 rounded-full" data-id="6sxzj25py" data-path="src/pages/HomePage.tsx"></div>
                        {dir === 'ltr' && <span data-id="j6mvirter" data-path="src/pages/HomePage.tsx">{t('home.vr.feature3')}</span>}
                      </div>
                      <div className={`flex items-center space-x-3 ${dir === 'rtl' ? 'justify-end rtl:space-x-reverse' : 'justify-start'}`} data-id="e2ru4xa21" data-path="src/pages/HomePage.tsx">
                        {dir === 'rtl' && <span data-id="avh15ilzb" data-path="src/pages/HomePage.tsx">{t('home.vr.feature4')}</span>}
                        <div className="w-3 h-3 bg-green-400 rounded-full" data-id="yln863icm" data-path="src/pages/HomePage.tsx"></div>
                        {dir === 'ltr' && <span data-id="sj0fau3lg" data-path="src/pages/HomePage.tsx">{t('home.vr.feature4')}</span>}
                      </div>
                    </div>

                    <Link to="/course" data-id="6nt5r31zj" data-path="src/pages/HomePage.tsx">
                      <Button size="lg" variant="secondary" className="text-slate-900" data-id="ht99gqxjk" data-path="src/pages/HomePage.tsx">
                        <ArrowIcon className={`w-5 h-5 ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`} data-id="oybla0hfv" data-path="src/pages/HomePage.tsx" />
                        {t('home.vr.discover')}
                      </Button>
                    </Link>
                  </div>
                  
                  <div className="relative" data-id="8fyehoesa" data-path="src/pages/HomePage.tsx">
                    <motion.div
                      animate={{
                        rotateY: [0, 360]
                      }}
                      transition={{
                        duration: 20,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      className="w-80 h-80 mx-auto" data-id="ce0wxne8i" data-path="src/pages/HomePage.tsx">

                      <div className="w-full h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-6xl" data-id="r1pqmv7u9" data-path="src/pages/HomePage.tsx">
                        🥽
                      </div>
                    </motion.div>
                    
                    {/* Orbiting elements */}
                    <motion.div
                      animate={{
                        rotate: [0, 360]
                      }}
                      transition={{
                        duration: 15,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      className="absolute inset-0" data-id="wsucrddq6" data-path="src/pages/HomePage.tsx">

                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-yellow-400 rounded-full" data-id="g3dld3v47" data-path="src/pages/HomePage.tsx"></div>
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-400 rounded-full" data-id="89dt0q367" data-path="src/pages/HomePage.tsx"></div>
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-red-400 rounded-full" data-id="hqsmsdhxx" data-path="src/pages/HomePage.tsx"></div>
                      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-400 rounded-full" data-id="t4ev04wp6" data-path="src/pages/HomePage.tsx"></div>
                    </motion.div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6 bg-gradient-to-r from-blue-600 to-purple-600" data-id="adwd4sqxg" data-path="src/pages/HomePage.tsx">
        <div className="max-w-4xl mx-auto text-center text-white" data-id="bk7srfm3a" data-path="src/pages/HomePage.tsx">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }} data-id="mxuux8kxo" data-path="src/pages/HomePage.tsx">

            <h2 className="text-4xl font-bold mb-6" data-id="48gfu4qmw" data-path="src/pages/HomePage.tsx">{t('home.cta.title')}</h2>
            <p className="text-xl mb-8 text-blue-100" data-id="9gipqvbbd" data-path="src/pages/HomePage.tsx">
              {t('home.cta.subtitle')}
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4" data-id="hly3a7kan" data-path="src/pages/HomePage.tsx">
              <Link to="/course" data-id="tdon17rvy" data-path="src/pages/HomePage.tsx">
                <Button size="lg" variant="secondary" className="text-blue-600 px-8 py-4" data-id="qckzsdz92" data-path="src/pages/HomePage.tsx">
                  <Play className={`w-6 h-6 ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`} data-id="bevy92aj1" data-path="src/pages/HomePage.tsx" />
                  {t('home.cta.start_free')}
                </Button>
              </Link>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4" data-id="pfm5gdig7" data-path="src/pages/HomePage.tsx">
                <BookOpen className={`w-6 h-6 ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`} data-id="4jasnka9c" data-path="src/pages/HomePage.tsx" />
                {t('home.cta.download_curriculum')}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>);

};

export default HomePage;