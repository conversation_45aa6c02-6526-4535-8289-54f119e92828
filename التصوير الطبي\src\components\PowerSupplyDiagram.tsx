import { useState } from 'react';
import { motion } from 'motion/react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

const PowerSupplyDiagram = () => {
  const [selectedStage, setSelectedStage] = useState<string | null>(null);

  const stages = {
    input: {
      name: 'المدخل',
      description: 'تيار متردد 220V/50Hz من الشبكة الكهربائية',
      details: 'يتم استقبال التيار الكهربائي من الشبكة العامة بجهد 220V وتردد 50Hz',
      color: '#6b7280'
    },
    stepup: {
      name: 'المحول الرفع',
      description: 'رفع الجهد إلى المستوى المطلوب',
      details: 'محول تصاعدي يرفع الجهد من 220V إلى 40-150kV حسب المتطلبات',
      color: '#3b82f6'
    },
    rectification: {
      name: 'التعديل',
      description: 'تحويل التيار المتردد إلى مستمر',
      details: 'استخدام ديودات عالية الجهد لتعديل الموجة والحصول على تيار مستمر',
      color: '#ef4444'
    },
    filtering: {
      name: 'الترشيح',
      description: 'تنعيم التيار المستمر',
      details: 'استخدام مكثفات وملفات لتقليل التموج وتنعيم الإشارة',
      color: '#10b981'
    },
    output: {
      name: 'المخرج',
      description: 'تيار مستمر عالي الجهد مستقر',
      details: 'تيار مستمر نظيف ومستقر بجهد 40-150kV لتغذية أنبوب الأشعة السينية',
      color: '#8b5cf6'
    }
  };

  return (
    <div className="space-y-6" data-id="gs8kvj6a6" data-path="src/components/PowerSupplyDiagram.tsx">
      {/* Block Diagram */}
      <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-8 min-h-[300px]" data-id="c9g5xt1k1" data-path="src/components/PowerSupplyDiagram.tsx">
        <svg
          width="100%"
          height="250"
          viewBox="0 0 900 250"
          className="max-w-full" data-id="97xwoob6r" data-path="src/components/PowerSupplyDiagram.tsx">

          {/* Stage 1: Input */}
          <motion.g
            onClick={() => setSelectedStage(selectedStage === 'input' ? null : 'input')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="1cjre123p" data-path="src/components/PowerSupplyDiagram.tsx">

            <rect
              x="20"
              y="80"
              width="120"
              height="80"
              rx="10"
              fill={selectedStage === 'input' ? stages.input.color : '#f9fafb'}
              stroke={selectedStage === 'input' ? stages.input.color : '#d1d5db'}
              strokeWidth="2" data-id="h1dk10rk5" data-path="src/components/PowerSupplyDiagram.tsx" />

            <text x="80" y="115" textAnchor="middle" className="text-sm font-semibold fill-gray-700" data-id="o0zwd0lj8" data-path="src/components/PowerSupplyDiagram.tsx">
              مدخل AC
            </text>
            <text x="80" y="135" textAnchor="middle" className="text-xs fill-gray-600" data-id="xtv5m6j7z" data-path="src/components/PowerSupplyDiagram.tsx">
              220V / 50Hz
            </text>
            
            {/* AC Waveform */}
            <path
              d="M40 145 Q50 140 60 145 Q70 150 80 145 Q90 140 100 145 Q110 150 120 145"
              stroke={selectedStage === 'input' ? '#ffffff' : '#6b7280'}
              strokeWidth="2"
              fill="none" data-id="ne58s9kzn" data-path="src/components/PowerSupplyDiagram.tsx" />

          </motion.g>

          {/* Arrow 1 */}
          <defs data-id="dsczsasnt" data-path="src/components/PowerSupplyDiagram.tsx">
            <marker id="arrow1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto" data-id="728utx538" data-path="src/components/PowerSupplyDiagram.tsx">
              <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" data-id="03uw3p55h" data-path="src/components/PowerSupplyDiagram.tsx" />
            </marker>
          </defs>
          <line x1="140" y1="120" x2="180" y2="120" stroke="#6b7280" strokeWidth="2" markerEnd="url(#arrow1)" data-id="0mn1c3giq" data-path="src/components/PowerSupplyDiagram.tsx" />

          {/* Stage 2: Step-up Transformer */}
          <motion.g
            onClick={() => setSelectedStage(selectedStage === 'stepup' ? null : 'stepup')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="debxb4oa0" data-path="src/components/PowerSupplyDiagram.tsx">

            <rect
              x="180"
              y="80"
              width="120"
              height="80"
              rx="10"
              fill={selectedStage === 'stepup' ? stages.stepup.color : '#f9fafb'}
              stroke={selectedStage === 'stepup' ? stages.stepup.color : '#d1d5db'}
              strokeWidth="2" data-id="5sq56uems" data-path="src/components/PowerSupplyDiagram.tsx" />

            <text x="240" y="110" textAnchor="middle" className="text-sm font-semibold fill-gray-700" data-id="hm28u35xz" data-path="src/components/PowerSupplyDiagram.tsx">
              محول رفع
            </text>
            <text x="240" y="125" textAnchor="middle" className="text-xs fill-gray-600" data-id="w7xf1du1b" data-path="src/components/PowerSupplyDiagram.tsx">
              1:500-700
            </text>
            <text x="240" y="140" textAnchor="middle" className="text-xs fill-gray-600" data-id="rt53gxn1o" data-path="src/components/PowerSupplyDiagram.tsx">
              40-150kV
            </text>
            
            {/* Transformer symbol */}
            <g stroke={selectedStage === 'stepup' ? '#ffffff' : '#6b7280'} strokeWidth="2" fill="none" data-id="570tdnzfc" data-path="src/components/PowerSupplyDiagram.tsx">
              <circle cx="220" cy="150" r="8" data-id="3g5h2kwe8" data-path="src/components/PowerSupplyDiagram.tsx" />
              <circle cx="260" cy="150" r="8" data-id="lgun86qwt" data-path="src/components/PowerSupplyDiagram.tsx" />
              <line x1="228" y1="150" x2="252" y2="150" data-id="gauaskgi4" data-path="src/components/PowerSupplyDiagram.tsx" />
            </g>
          </motion.g>

          {/* Arrow 2 */}
          <line x1="300" y1="120" x2="340" y2="120" stroke="#6b7280" strokeWidth="2" markerEnd="url(#arrow1)" data-id="s7r5fp8xa" data-path="src/components/PowerSupplyDiagram.tsx" />

          {/* Stage 3: Rectification */}
          <motion.g
            onClick={() => setSelectedStage(selectedStage === 'rectification' ? null : 'rectification')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="oq8bsjlyl" data-path="src/components/PowerSupplyDiagram.tsx">

            <rect
              x="340"
              y="80"
              width="120"
              height="80"
              rx="10"
              fill={selectedStage === 'rectification' ? stages.rectification.color : '#f9fafb'}
              stroke={selectedStage === 'rectification' ? stages.rectification.color : '#d1d5db'}
              strokeWidth="2" data-id="4kvs9q8fx" data-path="src/components/PowerSupplyDiagram.tsx" />

            <text x="400" y="110" textAnchor="middle" className="text-sm font-semibold fill-gray-700" data-id="mki3am7ud" data-path="src/components/PowerSupplyDiagram.tsx">
              معدل
            </text>
            <text x="400" y="125" textAnchor="middle" className="text-xs fill-gray-600" data-id="kece84rit" data-path="src/components/PowerSupplyDiagram.tsx">
              AC → DC
            </text>
            
            {/* Diode symbols */}
            <g stroke={selectedStage === 'rectification' ? '#ffffff' : '#ef4444'} strokeWidth="2" data-id="20q2jvvil" data-path="src/components/PowerSupplyDiagram.tsx">
              <polygon points="380,140 390,145 380,150" fill={selectedStage === 'rectification' ? '#ffffff' : '#ef4444'} data-id="mzoi3ab3l" data-path="src/components/PowerSupplyDiagram.tsx" />
              <line x1="390" y1="140" x2="390" y2="150" data-id="q98cagf7g" data-path="src/components/PowerSupplyDiagram.tsx" />
              <polygon points="410,140 420,145 410,150" fill={selectedStage === 'rectification' ? '#ffffff' : '#ef4444'} data-id="9wusi0fhb" data-path="src/components/PowerSupplyDiagram.tsx" />
              <line x1="420" y1="140" x2="420" y2="150" data-id="kbpkuh8na" data-path="src/components/PowerSupplyDiagram.tsx" />
            </g>
          </motion.g>

          {/* Arrow 3 */}
          <line x1="460" y1="120" x2="500" y2="120" stroke="#6b7280" strokeWidth="2" markerEnd="url(#arrow1)" data-id="fsj0yq1g5" data-path="src/components/PowerSupplyDiagram.tsx" />

          {/* Stage 4: Filtering */}
          <motion.g
            onClick={() => setSelectedStage(selectedStage === 'filtering' ? null : 'filtering')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="zf0o5h5xg" data-path="src/components/PowerSupplyDiagram.tsx">

            <rect
              x="500"
              y="80"
              width="120"
              height="80"
              rx="10"
              fill={selectedStage === 'filtering' ? stages.filtering.color : '#f9fafb'}
              stroke={selectedStage === 'filtering' ? stages.filtering.color : '#d1d5db'}
              strokeWidth="2" data-id="jtqdw0swe" data-path="src/components/PowerSupplyDiagram.tsx" />

            <text x="560" y="110" textAnchor="middle" className="text-sm font-semibold fill-gray-700" data-id="soih794vh" data-path="src/components/PowerSupplyDiagram.tsx">
              مرشح
            </text>
            <text x="560" y="125" textAnchor="middle" className="text-xs fill-gray-600" data-id="55emowadd" data-path="src/components/PowerSupplyDiagram.tsx">
              تنعيم التيار
            </text>
            
            {/* Filter symbols */}
            <g stroke={selectedStage === 'filtering' ? '#ffffff' : '#10b981'} strokeWidth="2" data-id="2lofbwu1r" data-path="src/components/PowerSupplyDiagram.tsx">
              <line x1="530" y1="140" x2="530" y2="155" data-id="t649drynq" data-path="src/components/PowerSupplyDiagram.tsx" />
              <line x1="535" y1="140" x2="535" y2="155" data-id="gfg72kft3" data-path="src/components/PowerSupplyDiagram.tsx" />
              <path d="M550 140 Q560 135 570 140 Q580 145 590 140" fill="none" data-id="4jr431eky" data-path="src/components/PowerSupplyDiagram.tsx" />
            </g>
          </motion.g>

          {/* Arrow 4 */}
          <line x1="620" y1="120" x2="660" y2="120" stroke="#6b7280" strokeWidth="2" markerEnd="url(#arrow1)" data-id="o8caiqtfh" data-path="src/components/PowerSupplyDiagram.tsx" />

          {/* Stage 5: Output */}
          <motion.g
            onClick={() => setSelectedStage(selectedStage === 'output' ? null : 'output')}
            className="cursor-pointer"
            whileHover={{ scale: 1.05 }} data-id="iryxaip4a" data-path="src/components/PowerSupplyDiagram.tsx">

            <rect
              x="660"
              y="80"
              width="120"
              height="80"
              rx="10"
              fill={selectedStage === 'output' ? stages.output.color : '#f9fafb'}
              stroke={selectedStage === 'output' ? stages.output.color : '#d1d5db'}
              strokeWidth="2" data-id="ge7czoz3c" data-path="src/components/PowerSupplyDiagram.tsx" />

            <text x="720" y="110" textAnchor="middle" className="text-sm font-semibold fill-gray-700" data-id="kb34xdd81" data-path="src/components/PowerSupplyDiagram.tsx">
              مخرج DC
            </text>
            <text x="720" y="125" textAnchor="middle" className="text-xs fill-gray-600" data-id="o5u1x9n89" data-path="src/components/PowerSupplyDiagram.tsx">
              عالي الجهد
            </text>
            <text x="720" y="140" textAnchor="middle" className="text-xs fill-gray-600" data-id="254kdnum6" data-path="src/components/PowerSupplyDiagram.tsx">
              مستقر
            </text>
            
            {/* DC line */}
            <line
              x1="680"
              y1="150"
              x2="760"
              y2="150"
              stroke={selectedStage === 'output' ? '#ffffff' : '#8b5cf6'}
              strokeWidth="3" data-id="brb95t6j2" data-path="src/components/PowerSupplyDiagram.tsx" />

          </motion.g>

          {/* Title */}
          <text x="450" y="30" textAnchor="middle" className="text-lg font-bold fill-gray-800" data-id="u0r62sgf8" data-path="src/components/PowerSupplyDiagram.tsx">
            مخطط كتلي لدائرة مزود الطاقة
          </text>

          {/* Voltage Labels */}
          <text x="80" y="200" textAnchor="middle" className="text-xs fill-gray-500" data-id="5ge777tiu" data-path="src/components/PowerSupplyDiagram.tsx">220V AC</text>
          <text x="240" y="200" textAnchor="middle" className="text-xs fill-gray-500" data-id="hjvzk7n30" data-path="src/components/PowerSupplyDiagram.tsx">40-150kV AC</text>
          <text x="400" y="200" textAnchor="middle" className="text-xs fill-gray-500" data-id="5ttizng9r" data-path="src/components/PowerSupplyDiagram.tsx">40-150kV DC (متموج)</text>
          <text x="560" y="200" textAnchor="middle" className="text-xs fill-gray-500" data-id="jtjul662o" data-path="src/components/PowerSupplyDiagram.tsx">40-150kV DC (منعم)</text>
          <text x="720" y="200" textAnchor="middle" className="text-xs fill-gray-500" data-id="q854aylph" data-path="src/components/PowerSupplyDiagram.tsx">40-150kV DC (مستقر)</text>
        </svg>
      </div>

      {/* Waveform Display */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4" data-id="sjdcxnc26" data-path="src/components/PowerSupplyDiagram.tsx">
        {['input', 'rectification', 'output'].map((stage) =>
        <Card key={stage} className="hover:shadow-lg transition-shadow" data-id="n0m3dfp97" data-path="src/components/PowerSupplyDiagram.tsx">
            <CardContent className="p-4" data-id="h49jtq611" data-path="src/components/PowerSupplyDiagram.tsx">
              <h4 className="font-medium text-gray-900 mb-2" data-id="covtjg8f2" data-path="src/components/PowerSupplyDiagram.tsx">
                شكل الموجة - {stages[stage as keyof typeof stages].name}
              </h4>
              <div className="bg-gray-50 rounded p-3 h-24 flex items-center justify-center" data-id="u631cnyif" data-path="src/components/PowerSupplyDiagram.tsx">
                <svg width="150" height="60" viewBox="0 0 150 60" data-id="0pp09kwup" data-path="src/components/PowerSupplyDiagram.tsx">
                  {stage === 'input' &&
                <path
                  d="M10 30 Q25 15 40 30 Q55 45 70 30 Q85 15 100 30 Q115 45 130 30"
                  stroke="#3b82f6"
                  strokeWidth="2"
                  fill="none" data-id="m4nx62k4n" data-path="src/components/PowerSupplyDiagram.tsx" />

                }
                  {stage === 'rectification' &&
                <path
                  d="M10 30 Q25 15 40 30 L40 30 Q55 15 70 30 L70 30 Q85 15 100 30 L100 30 Q115 15 130 30"
                  stroke="#ef4444"
                  strokeWidth="2"
                  fill="none" data-id="xu8z14taf" data-path="src/components/PowerSupplyDiagram.tsx" />

                }
                  {stage === 'output' &&
                <line
                  x1="10"
                  y1="20"
                  x2="130"
                  y2="20"
                  stroke="#8b5cf6"
                  strokeWidth="3" data-id="6lptqawhr" data-path="src/components/PowerSupplyDiagram.tsx" />

                }
                </svg>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Stage Information */}
      {selectedStage &&
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }} data-id="nj4u6aq9f" data-path="src/components/PowerSupplyDiagram.tsx">

          <Card data-id="ams5vioe4" data-path="src/components/PowerSupplyDiagram.tsx">
            <CardContent className="p-6" data-id="v652wjr5z" data-path="src/components/PowerSupplyDiagram.tsx">
              <div className="flex items-start gap-4" data-id="lyvb8t3n0" data-path="src/components/PowerSupplyDiagram.tsx">
                <div
                className="w-4 h-4 rounded-full flex-shrink-0 mt-1"
                style={{ backgroundColor: stages[selectedStage as keyof typeof stages].color }} data-id="w7nbf2cwr" data-path="src/components/PowerSupplyDiagram.tsx">
              </div>
                <div className="flex-1" data-id="8x46ftgcz" data-path="src/components/PowerSupplyDiagram.tsx">
                  <div className="flex items-center gap-2 mb-2" data-id="zymgf1j0u" data-path="src/components/PowerSupplyDiagram.tsx">
                    <h3 className="text-lg font-semibold text-gray-900" data-id="p8nm3jpvm" data-path="src/components/PowerSupplyDiagram.tsx">
                      {stages[selectedStage as keyof typeof stages].name}
                    </h3>
                    <Badge variant="secondary" data-id="4g60sax7t" data-path="src/components/PowerSupplyDiagram.tsx">محدد</Badge>
                  </div>
                  <p className="text-gray-600 mb-3" data-id="q2eawctmx" data-path="src/components/PowerSupplyDiagram.tsx">
                    {stages[selectedStage as keyof typeof stages].description}
                  </p>
                  <p className="text-sm text-gray-700 leading-relaxed" data-id="edcw6rbzq" data-path="src/components/PowerSupplyDiagram.tsx">
                    {stages[selectedStage as keyof typeof stages].details}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      }

      {!selectedStage &&
      <div className="text-center text-gray-500 text-sm" data-id="84ovtcnx8" data-path="src/components/PowerSupplyDiagram.tsx">
          انقر على أي مرحلة في المخطط لعرض تفاصيلها
        </div>
      }
    </div>);

};

export default PowerSupplyDiagram;