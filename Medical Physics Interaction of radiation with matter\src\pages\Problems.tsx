
import React, { useState } from 'react';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calculator, CheckCircle, X, ChevronDown, ChevronUp, Target } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

const Problems = () => {
  const [openSections, setOpenSections] = useState<number[]>([]);

  const toggleSection = (index: number) => {
    setOpenSections((prev) =>
    prev.includes(index) ?
    prev.filter((i) => i !== index) :
    [...prev, index]
    );
  };

  const problemSets = [
  {
    category: 'التشتت المتماسك',
    categoryEn: 'Coherent Scattering',
    level: 'أساسي',
    color: 'bg-blue-50 border-blue-200',
    problems: [
    {
      id: 1,
      question: 'فوتون أشعة سينية بطاقة 30 keV يتشتت بشكل متماسك بزاوية 45°. احسب طاقة الفوتون بعد التشتت.',
      given: 'E = 30 keV, θ = 45°, تشتت متماسك',
      solution: 'في التشتت المتماسك، لا يوجد فقدان في الطاقة، لذلك E\' = E = 30 keV',
      answer: '30 keV',
      explanation: 'التشتت المتماسك يحافظ على طاقة الفوتون بغض النظر عن زاوية التشتت.'
    },
    {
      id: 2,
      question: 'مقارنة المقطع العرضي للتشتت المتماسك لعنصرين: الكربون (Z=6) والرصاص (Z=82) عند طاقة 25 keV.',
      given: 'Z₁ = 6 (كربون), Z₂ = 82 (رصاص), E = 25 keV',
      solution: 'المقطع العرضي للتشتت المتماسك يتناسب مع Z². النسبة = (82/6)² = (13.67)² ≈ 187',
      answer: 'الرصاص له مقطع عرضي أكبر بـ 187 مرة من الكربون',
      explanation: 'العناصر الثقيلة تشتت الأشعة السينية بشكل متماسك أكثر بكثير من العناصر الخفيفة.'
    }]

  },
  {
    category: 'التأثير الكهروضوئي',
    categoryEn: 'Photoelectric Effect',
    level: 'متوسط',
    color: 'bg-green-50 border-green-200',
    problems: [
    {
      id: 3,
      question: 'فوتون بطاقة 70 keV يتفاعل كهروضوئياً مع إلكترون في مدار K للرصاص (طاقة ربط K = 88 keV). هل يمكن حدوث هذا التفاعل؟',
      given: 'E = 70 keV, BE_K = 88 keV للرصاص',
      solution: 'لا يمكن حدوث التأثير الكهروضوئي لأن طاقة الفوتون (70 keV) أقل من طاقة ربط مدار K (88 keV)',
      answer: 'لا، التفاعل غير ممكن',
      explanation: 'التأثير الكهروضوئي يتطلب أن تكون طاقة الفوتون أكبر من أو تساوي طاقة ربط الإلكترون.'
    },
    {
      id: 4,
      question: 'إلكترون يطرد من مدار K للباريوم (BE_K = 37.4 keV) بواسطة فوتون طاقته 80 keV. احسب الطاقة الحركية للإلكترون المطرود.',
      given: 'E = 80 keV, BE_K = 37.4 keV',
      solution: 'الطاقة الحركية = E - BE_K = 80 - 37.4 = 42.6 keV',
      answer: '42.6 keV',
      explanation: 'الطاقة الزائدة بعد كسر الربط تتحول إلى طاقة حركية للإلكترون المطرود.'
    },
    {
      id: 5,
      question: 'قارن بين احتمالية التأثير الكهروضوئي للأنسجة الرخوة (Z_eff = 7.4) والعظام (Z_eff = 13.8) عند طاقة 30 keV.',
      given: 'Z₁ = 7.4 (أنسجة رخوة), Z₂ = 13.8 (عظام), E = 30 keV',
      solution: 'النسبة = (Z₂/Z₁)⁴·⁵ = (13.8/7.4)⁴·⁵ = (1.86)⁴·⁵ ≈ 5.2',
      answer: 'العظام لها احتمالية أكبر بـ 5.2 مرة',
      explanation: 'هذا الاختلاف الكبير في الامتصاص يخلق التباين في صور الأشعة السينية.'
    }]

  },
  {
    category: 'تشتت كومبتون',
    categoryEn: 'Compton Scattering',
    level: 'متقدم',
    color: 'bg-purple-50 border-purple-200',
    problems: [
    {
      id: 6,
      question: 'فوتون بطاقة 100 keV يتشتت كومبتون بزاوية 90°. احسب طاقة الفوتون المتشتت وطاقة الإلكترون المرتد.',
      given: 'E = 100 keV, θ = 90°, m₀c² = 511 keV',
      solution: 'E\' = E/[1 + (E/m₀c²)(1-cosθ)] = 100/[1 + (100/511)(1-0)] = 100/1.196 = 83.6 keV\nT = E - E\' = 100 - 83.6 = 16.4 keV',
      answer: 'E\' = 83.6 keV, T = 16.4 keV',
      explanation: 'في التشتت الجانبي (90°)، يفقد الفوتون جزءاً معتدلاً من طاقته للإلكترون.'
    },
    {
      id: 7,
      question: 'احسب الطول الموجي للفوتون قبل وبعد تشتت كومبتون بزاوية 180° لفوتون طاقته الأولية 60 keV.',
      given: 'E = 60 keV, θ = 180°, h = 4.136 × 10⁻¹⁵ eV·s, c = 3 × 10⁸ m/s',
      solution: 'λ = hc/E = (4.136×10⁻¹⁵ × 3×10⁸)/(60×10³) = 2.07×10⁻¹¹ m\nΔλ = (h/m₀c)(1-cosθ) = 2.43×10⁻¹² × 2 = 4.86×10⁻¹² m\nλ\' = λ + Δλ = 2.07×10⁻¹¹ + 4.86×10⁻¹² = 2.56×10⁻¹¹ m',
      answer: 'λ = 2.07×10⁻¹¹ m, λ\' = 2.56×10⁻¹¹ m',
      explanation: 'التشتت الخلفي يعطي أقصى زيادة في الطول الموجي وأقصى فقدان للطاقة.'
    }]

  },
  {
    category: 'معاملات التوهين',
    categoryEn: 'Attenuation Coefficients',
    level: 'متوسط',
    color: 'bg-red-50 border-red-200',
    problems: [
    {
      id: 8,
      question: 'حزمة أشعة سينية شدتها الأولية 1000 mR تمر عبر 5 mm من الألومينيوم (μ = 0.5 cm⁻¹). احسب الشدة النهائية.',
      given: 'I₀ = 1000 mR, x = 5 mm = 0.5 cm, μ = 0.5 cm⁻¹',
      solution: 'I = I₀ × e⁻μx = 1000 × e⁻⁽⁰·⁵ ˣ ⁰·⁵⁾ = 1000 × e⁻⁰·²⁵ = 1000 × 0.779 = 779 mR',
      answer: '779 mR',
      explanation: 'قانون بير-لامبرت يصف الانخفاض الأسي لشدة الحزمة مع سماكة المادة.'
    },
    {
      id: 9,
      question: 'إذا كان HVL للرصاص عند طاقة معينة هو 0.3 mm، احسب المعامل الخطي للتوهين.',
      given: 'HVL = 0.3 mm = 0.03 cm',
      solution: 'μ = ln(2)/HVL = 0.693/0.03 = 23.1 cm⁻¹',
      answer: 'μ = 23.1 cm⁻¹',
      explanation: 'HVL والمعامل الخطي مترابطان عكسياً - كلما قل HVL، زاد المعامل الخطي.'
    },
    {
      id: 10,
      question: 'احسب النسبة المئوية للحزمة التي تمر عبر 3 طبقات HVL من مادة ما.',
      given: 'عدد طبقات HVL = 3',
      solution: 'بعد كل HVL، تبقى 50% من الحزمة\nبعد 3 HVL: (1/2)³ = 1/8 = 0.125 = 12.5%',
      answer: '12.5%',
      explanation: 'كل طبقة HVL تقلل الحزمة إلى النصف، فثلاث طبقات تقللها إلى الثُمن.'
    }]

  },
  {
    category: 'التطبيقات المتقدمة',
    categoryEn: 'Advanced Applications',
    level: 'متقدم',
    color: 'bg-orange-50 border-orange-200',
    problems: [
    {
      id: 11,
      question: 'في فحص CT بطاقة 120 kVp (طاقة فعالة ~70 keV)، قارن بين التوهين في الأنسجة الرخوة والعظام. استخدم البيانات: μ/ρ (أنسجة رخوة) = 0.21 cm²/g، μ/ρ (عظام) = 0.48 cm²/g، كثافة الأنسجة الرخوة = 1 g/cm³، كثافة العظام = 1.85 g/cm³.',
      given: 'E_eff = 70 keV, (μ/ρ)_soft = 0.21 cm²/g, (μ/ρ)_bone = 0.48 cm²/g, ρ_soft = 1 g/cm³, ρ_bone = 1.85 g/cm³',
      solution: 'μ_soft = (μ/ρ) × ρ = 0.21 × 1 = 0.21 cm⁻¹\nμ_bone = 0.48 × 1.85 = 0.888 cm⁻¹\nالنسبة = μ_bone/μ_soft = 0.888/0.21 = 4.23',
      answer: 'العظام توهن الحزمة بـ 4.23 مرة أكثر من الأنسجة الرخوة',
      explanation: 'هذا الاختلاف في التوهين يخلق التباين الممتاز في صور CT بين العظام والأنسجة الرخوة.'
    },
    {
      id: 12,
      question: 'مريض يحتاج إلى فحص أشعة سينية للصدر. إذا كان سمك الصدر 25 cm والمعامل الخطي الفعال 0.15 cm⁻¹، احسب سماكة الرصاص المطلوبة لتوفير نفس مستوى الحماية (μ_lead = 50 cm⁻¹).',
      given: 'x_chest = 25 cm, μ_chest = 0.15 cm⁻¹, μ_lead = 50 cm⁻¹',
      solution: 'للحصول على نفس التوهين: μ_chest × x_chest = μ_lead × x_lead\n0.15 × 25 = 50 × x_lead\nx_lead = 3.75/50 = 0.075 cm = 0.75 mm',
      answer: '0.75 mm من الرصاص',
      explanation: 'كمية صغيرة من الرصاص تكافئ سماكة كبيرة من الأنسجة بسبب العدد الذري العالي للرصاص.'
    }]

  }];


  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-slate-50 to-orange-50" data-id="nigz8p3u1" data-path="src/pages/Problems.tsx">
      <Navigation
        title="المشكلات والتمارين"
        titleEn="Problems & Exercises" data-id="lot91mdth" data-path="src/pages/Problems.tsx" />

      
      <div className="container mx-auto px-4 py-8" data-id="bo7kbhgvs" data-path="src/pages/Problems.tsx">
        {/* Header */}
        <Card className="mb-8" data-id="3qoxg64ee" data-path="src/pages/Problems.tsx">
          <CardHeader data-id="7f3ab9ga8" data-path="src/pages/Problems.tsx">
            <div className="flex items-center gap-3" data-id="mza4dcd7i" data-path="src/pages/Problems.tsx">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center" data-id="rpgbgxmor" data-path="src/pages/Problems.tsx">
                <Calculator className="w-5 h-5 text-orange-600" data-id="4hitspt6q" data-path="src/pages/Problems.tsx" />
              </div>
              <div data-id="0f8cu2qz4" data-path="src/pages/Problems.tsx">
                <CardTitle className="text-2xl text-right" data-id="o0ef27d1t" data-path="src/pages/Problems.tsx">المشكلات والتمارين</CardTitle>
                <p className="text-gray-600 text-right" data-id="3iamy4w0i" data-path="src/pages/Problems.tsx">
                  تمارين تطبيقية شاملة لتعزيز فهم تفاعل الأشعة السينية مع المادة
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent data-id="l7d5v0tzv" data-path="src/pages/Problems.tsx">
            <div className="text-right leading-relaxed space-y-4" data-id="6onb35fs9" data-path="src/pages/Problems.tsx">
              <p className="text-gray-700" data-id="v9orj3kji" data-path="src/pages/Problems.tsx">
                تم تصميم هذه المجموعة من المسائل لتغطي جميع جوانب تفاعل الأشعة السينية مع المادة، 
                من المفاهيم الأساسية إلى التطبيقات المتقدمة في التصوير الطبي.
              </p>
              
              <Alert data-id="zr01npqks" data-path="src/pages/Problems.tsx">
                <Target className="h-4 w-4" data-id="wfx3qihsx" data-path="src/pages/Problems.tsx" />
                <AlertDescription className="text-right" data-id="zve51o70u" data-path="src/pages/Problems.tsx">
                  <strong data-id="9ibm94bvt" data-path="src/pages/Problems.tsx">نصيحة للدراسة:</strong> حاول حل كل مسألة بنفسك قبل النظر إلى الحل. 
                  استخدم الآلة الحاسبة واكتب جميع الخطوات بوضوح.
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>

        {/* Problem Sets */}
        <div className="space-y-6" data-id="ggld61yqk" data-path="src/pages/Problems.tsx">
          {problemSets.map((set, setIndex) =>
          <Card key={setIndex} className={set.color} data-id="a5ys8q2sa" data-path="src/pages/Problems.tsx">
              <CardHeader data-id="bdvgd1os4" data-path="src/pages/Problems.tsx">
                <Collapsible data-id="nxn9pywa0" data-path="src/pages/Problems.tsx">
                  <CollapsibleTrigger asChild data-id="63exosjqy" data-path="src/pages/Problems.tsx">
                    <div
                    className="flex items-center justify-between w-full cursor-pointer"
                    onClick={() => toggleSection(setIndex)} data-id="kiaxt9cr9" data-path="src/pages/Problems.tsx">

                      <div className="flex items-center gap-3" data-id="dymph9uci" data-path="src/pages/Problems.tsx">
                        <Badge
                        variant={set.level === 'أساسي' ? 'secondary' : set.level === 'متوسط' ? 'default' : 'destructive'} data-id="krkwmw68d" data-path="src/pages/Problems.tsx">

                          {set.level}
                        </Badge>
                        <span className="text-sm text-gray-600" data-id="7ekz0vzmw" data-path="src/pages/Problems.tsx">{set.problems.length} مسائل</span>
                        {openSections.includes(setIndex) ?
                      <ChevronUp className="w-4 h-4" data-id="13psdopfd" data-path="src/pages/Problems.tsx" /> :
                      <ChevronDown className="w-4 h-4" data-id="zfrysui8w" data-path="src/pages/Problems.tsx" />
                      }
                      </div>
                      <div className="text-right" data-id="xfc7km18u" data-path="src/pages/Problems.tsx">
                        <CardTitle className="text-xl" data-id="5iyc24vaf" data-path="src/pages/Problems.tsx">{set.category}</CardTitle>
                        <CardDescription className="italic" data-id="fproq9v0e" data-path="src/pages/Problems.tsx">{set.categoryEn}</CardDescription>
                      </div>
                    </div>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent data-id="3vm04znea" data-path="src/pages/Problems.tsx">
                    <div className="mt-6 space-y-4" data-id="q3xdvnrj3" data-path="src/pages/Problems.tsx">
                      {set.problems.map((problem, problemIndex) =>
                    <Card key={problem.id} className="bg-white border-gray-200" data-id="xth5omze0" data-path="src/pages/Problems.tsx">
                          <CardHeader data-id="2ecd2vqsy" data-path="src/pages/Problems.tsx">
                            <div className="flex justify-between items-start" data-id="4h0md1t7d" data-path="src/pages/Problems.tsx">
                              <Badge variant="outline" data-id="mucf6hawe" data-path="src/pages/Problems.tsx">المسألة {problem.id}</Badge>
                              <div className="text-right flex-1 mr-4" data-id="s3p0499on" data-path="src/pages/Problems.tsx">
                                <p className="font-medium leading-relaxed" data-id="lymm2ttpk" data-path="src/pages/Problems.tsx">
                                  {problem.question}
                                </p>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent data-id="xk90vj9l1" data-path="src/pages/Problems.tsx">
                            <Collapsible data-id="3yv648cxz" data-path="src/pages/Problems.tsx">
                              <div className="space-y-4" data-id="7lv22e3sq" data-path="src/pages/Problems.tsx">
                                <div className="bg-blue-50 p-3 rounded-lg" data-id="i457sis3n" data-path="src/pages/Problems.tsx">
                                  <p className="text-sm text-right" data-id="u9eywmxfl" data-path="src/pages/Problems.tsx">
                                    <span className="font-medium" data-id="nf4kt7qyj" data-path="src/pages/Problems.tsx">المعطيات: </span>
                                    {problem.given}
                                  </p>
                                </div>
                                
                                <CollapsibleTrigger asChild data-id="wd1wpjiuw" data-path="src/pages/Problems.tsx">
                                  <Button variant="outline" className="w-full" data-id="8py304zwf" data-path="src/pages/Problems.tsx">
                                    عرض الحل والشرح
                                  </Button>
                                </CollapsibleTrigger>
                                
                                <CollapsibleContent data-id="ruwwm0l5u" data-path="src/pages/Problems.tsx">
                                  <div className="space-y-4" data-id="xlosnk0y7" data-path="src/pages/Problems.tsx">
                                    <div className="bg-green-50 p-4 rounded-lg" data-id="l0k0tstn0" data-path="src/pages/Problems.tsx">
                                      <h4 className="font-medium mb-2 text-right flex items-center gap-2" data-id="unvnhx7pw" data-path="src/pages/Problems.tsx">
                                        <CheckCircle className="w-4 h-4 text-green-600" data-id="9l2xtokj7" data-path="src/pages/Problems.tsx" />
                                        الحل:
                                      </h4>
                                      <div className="text-sm text-right leading-relaxed whitespace-pre-line" data-id="i8c1lvbq2" data-path="src/pages/Problems.tsx">
                                        {problem.solution}
                                      </div>
                                    </div>
                                    
                                    <div className="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400" data-id="xqbx1zlwj" data-path="src/pages/Problems.tsx">
                                      <h4 className="font-medium mb-2 text-right" data-id="ufbm088pp" data-path="src/pages/Problems.tsx">الإجابة النهائية:</h4>
                                      <p className="text-lg font-semibold text-right text-yellow-800" data-id="gj5nt5ldz" data-path="src/pages/Problems.tsx">
                                        {problem.answer}
                                      </p>
                                    </div>
                                    
                                    <div className="bg-purple-50 p-4 rounded-lg" data-id="72nqrn1uz" data-path="src/pages/Problems.tsx">
                                      <h4 className="font-medium mb-2 text-right" data-id="3xqzve08f" data-path="src/pages/Problems.tsx">الشرح والتفسير:</h4>
                                      <p className="text-sm text-right leading-relaxed" data-id="3hdvbfy2m" data-path="src/pages/Problems.tsx">
                                        {problem.explanation}
                                      </p>
                                    </div>
                                  </div>
                                </CollapsibleContent>
                              </div>
                            </Collapsible>
                          </CardContent>
                        </Card>
                    )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </CardHeader>
            </Card>
          )}
        </div>

        {/* Summary and Study Tips */}
        <Card className="mt-8" data-id="vz0gn5xsl" data-path="src/pages/Problems.tsx">
          <CardHeader data-id="1jui7z9ti" data-path="src/pages/Problems.tsx">
            <CardTitle className="text-xl text-right" data-id="21shud61p" data-path="src/pages/Problems.tsx">نصائح لحل المسائل</CardTitle>
          </CardHeader>
          <CardContent data-id="nd6ahw0tp" data-path="src/pages/Problems.tsx">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6" data-id="5zuaeiefl" data-path="src/pages/Problems.tsx">
              <div className="space-y-3" data-id="dzzl2x0ay" data-path="src/pages/Problems.tsx">
                <h3 className="font-semibold text-right" data-id="w0olud7xa" data-path="src/pages/Problems.tsx">خطوات الحل المنهجي:</h3>
                <ol className="space-y-2 text-sm text-right" data-id="iiryukx2b" data-path="src/pages/Problems.tsx">
                  <li data-id="4v23jff1c" data-path="src/pages/Problems.tsx">1. اقرأ المسألة بعناية وحدد ما هو مطلوب</li>
                  <li data-id="07gffa1vh" data-path="src/pages/Problems.tsx">2. اكتب جميع المعطيات والثوابت المعروفة</li>
                  <li data-id="twnt6gwtg" data-path="src/pages/Problems.tsx">3. حدد القوانين والمعادلات المناسبة</li>
                  <li data-id="judh39d8p" data-path="src/pages/Problems.tsx">4. تحقق من وحدات القياس وحولها إذا لزم الأمر</li>
                  <li data-id="18nlxrzdb" data-path="src/pages/Problems.tsx">5. احسب النتيجة خطوة بخطوة</li>
                  <li data-id="qb1gd504p" data-path="src/pages/Problems.tsx">6. تحقق من منطقية الإجابة</li>
                </ol>
              </div>
              
              <div className="space-y-3" data-id="gqmaf3dbl" data-path="src/pages/Problems.tsx">
                <h3 className="font-semibold text-right" data-id="hbyjehjzh" data-path="src/pages/Problems.tsx">ثوابت مهمة يجب تذكرها:</h3>
                <ul className="space-y-2 text-sm text-right" data-id="tvglefago" data-path="src/pages/Problems.tsx">
                  <li data-id="q2v96rcs8" data-path="src/pages/Problems.tsx">• كتلة الإلكترون الساكن: m₀c² = 511 keV</li>
                  <li data-id="jqx7cnk7h" data-path="src/pages/Problems.tsx">• ثابت بلانك: h = 4.136 × 10⁻¹⁵ eV·s</li>
                  <li data-id="vw1vwqmye" data-path="src/pages/Problems.tsx">• سرعة الضوء: c = 3 × 10⁸ m/s</li>
                  <li data-id="gowh6a7qv" data-path="src/pages/Problems.tsx">• الطول الموجي كومبتون: λc = 2.43 pm</li>
                  <li data-id="ep9i1cxc4" data-path="src/pages/Problems.tsx">• عدد أفوجادرو: NA = 6.022 × 10²³ mol⁻¹</li>
                  <li data-id="3nrneuiqk" data-path="src/pages/Problems.tsx">• ln(2) = 0.693</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Problem Statistics */}
        <Card className="mt-8" data-id="iu2rr9zwh" data-path="src/pages/Problems.tsx">
          <CardHeader data-id="7yemse6wy" data-path="src/pages/Problems.tsx">
            <CardTitle className="text-xl text-right" data-id="ht209yzdk" data-path="src/pages/Problems.tsx">إحصائيات المسائل</CardTitle>
          </CardHeader>
          <CardContent data-id="u4c0ii0zt" data-path="src/pages/Problems.tsx">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4" data-id="j0ehg41al" data-path="src/pages/Problems.tsx">
              <div className="text-center" data-id="vv392oyk7" data-path="src/pages/Problems.tsx">
                <div className="text-2xl font-bold text-blue-600" data-id="1vfig460r" data-path="src/pages/Problems.tsx">
                  {problemSets.reduce((sum, set) => sum + set.problems.length, 0)}
                </div>
                <p className="text-sm text-gray-600" data-id="ijpzig598" data-path="src/pages/Problems.tsx">إجمالي المسائل</p>
              </div>
              <div className="text-center" data-id="8cu42zyc7" data-path="src/pages/Problems.tsx">
                <div className="text-2xl font-bold text-green-600" data-id="j2afth8em" data-path="src/pages/Problems.tsx">
                  {problemSets.filter((set) => set.level === 'أساسي').reduce((sum, set) => sum + set.problems.length, 0)}
                </div>
                <p className="text-sm text-gray-600" data-id="g0v0kpckb" data-path="src/pages/Problems.tsx">مسائل أساسية</p>
              </div>
              <div className="text-center" data-id="rm3i50uvw" data-path="src/pages/Problems.tsx">
                <div className="text-2xl font-bold text-yellow-600" data-id="pn2lhf2pu" data-path="src/pages/Problems.tsx">
                  {problemSets.filter((set) => set.level === 'متوسط').reduce((sum, set) => sum + set.problems.length, 0)}
                </div>
                <p className="text-sm text-gray-600" data-id="eadip1olt" data-path="src/pages/Problems.tsx">مسائل متوسطة</p>
              </div>
              <div className="text-center" data-id="ikh373bai" data-path="src/pages/Problems.tsx">
                <div className="text-2xl font-bold text-red-600" data-id="80ho2cat6" data-path="src/pages/Problems.tsx">
                  {problemSets.filter((set) => set.level === 'متقدم').reduce((sum, set) => sum + set.problems.length, 0)}
                </div>
                <p className="text-sm text-gray-600" data-id="qa9osk16f" data-path="src/pages/Problems.tsx">مسائل متقدمة</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default Problems;