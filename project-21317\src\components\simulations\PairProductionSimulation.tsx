import { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';

const PairProductionSimulation = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  const [photonEnergy, setPhotonEnergy] = useState([2000]); // keV
  const [atomicNumber, setAtomicNumber] = useState([82]); // Pb
  const [isRunning, setIsRunning] = useState(false);

  // Animation state
  const [photonPosition, setPhotonPosition] = useState({ x: 50, y: 250 });
  const [electronPosition, setElectronPosition] = useState({ x: 300, y: 250 });
  const [positronPosition, setPositronPosition] = useState({ x: 300, y: 250 });
  const [animationPhase, setAnimationPhase] = useState('ready'); // ready, collision, pair-creation, annihilation
  const [showAnnihilation, setShowAnnihilation] = useState(false);

  // Calculate pair production parameters
  const electronRestEnergy = 511; // keV
  const thresholdEnergy = 2 * electronRestEnergy; // 1.022 MeV
  const canOccur = photonEnergy[0] >= thresholdEnergy;
  const kineticEnergy = canOccur ? photonEnergy[0] - thresholdEnergy : 0;
  const electronKE = kineticEnergy / 2; // Assuming equal sharing
  const positronKE = kineticEnergy / 2;

  // Cross-section (simplified)
  const crossSection = canOccur ? Math.pow(atomicNumber[0], 2) * Math.log(photonEnergy[0] / thresholdEnergy) : 0;

  // Animation loop
  useEffect(() => {
    if (isRunning && canvasRef.current) {
      const animate = () => {
        drawSimulation();
        updateAnimation();
        animationRef.current = requestAnimationFrame(animate);
      };
      animate();
    } else if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRunning, photonPosition, electronPosition, positronPosition, animationPhase]);

  const updateAnimation = () => {
    if (animationPhase === 'ready' && photonPosition.x < 290) {
      setPhotonPosition((prev) => ({ ...prev, x: prev.x + 2 }));
    } else if (animationPhase === 'ready' && photonPosition.x >= 290 && canOccur) {
      setAnimationPhase('collision');
      setTimeout(() => {
        setAnimationPhase('pair-creation');

        // Animate electron (upward)
        const electronDistance = electronKE * 0.2;
        setElectronPosition({
          x: 300 + electronDistance * 0.7,
          y: 250 - electronDistance
        });

        // Animate positron (downward)
        const positronDistance = positronKE * 0.2;
        setPositronPosition({
          x: 300 + positronDistance * 0.7,
          y: 250 + positronDistance
        });

        // Show annihilation after 3 seconds
        setTimeout(() => {
          setShowAnnihilation(true);
        }, 3000);
      }, 500);
    }
  };

  const drawSimulation = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw nucleus
    ctx.fillStyle = '#1e40af';
    ctx.beginPath();
    ctx.arc(300, 250, 15, 0, 2 * Math.PI);
    ctx.fill();

    // Draw nuclear field lines
    ctx.strokeStyle = '#60a5fa';
    ctx.lineWidth = 1;
    ctx.setLineDash([2, 2]);
    for (let i = 0; i < 8; i++) {
      const angle = i * 45 * Math.PI / 180;
      ctx.beginPath();
      ctx.moveTo(300 + Math.cos(angle) * 20, 250 + Math.sin(angle) * 20);
      ctx.lineTo(300 + Math.cos(angle) * 40, 250 + Math.sin(angle) * 40);
      ctx.stroke();
    }
    ctx.setLineDash([]);

    // Draw incident photon
    if (animationPhase !== 'pair-creation' && animationPhase !== 'annihilation') {
      ctx.fillStyle = '#fbbf24';
      ctx.beginPath();
      ctx.arc(photonPosition.x, photonPosition.y, 8, 0, 2 * Math.PI);
      ctx.fill();

      // Draw photon wave
      ctx.strokeStyle = '#fbbf24';
      ctx.lineWidth = 3;
      ctx.beginPath();
      const waveLength = 20;
      const amplitude = 10;
      for (let x = 50; x < photonPosition.x; x += 2) {
        const y = 250 + amplitude * Math.sin((x - 50) / waveLength * 2 * Math.PI);
        if (x === 50) ctx.moveTo(x, y);else
        ctx.lineTo(x, y);
      }
      ctx.stroke();
    }

    // Draw collision effects
    if (animationPhase === 'collision') {
      ctx.strokeStyle = '#fbbf24';
      ctx.lineWidth = 4;
      for (let i = 0; i < 12; i++) {
        const angle = i * 30 * Math.PI / 180;
        const length = 25;
        ctx.beginPath();
        ctx.moveTo(300, 250);
        ctx.lineTo(300 + Math.cos(angle) * length, 250 + Math.sin(angle) * length);
        ctx.stroke();
      }
    }

    // Draw electron-positron pair
    if (animationPhase === 'pair-creation' && !showAnnihilation) {
      // Electron (red)
      ctx.fillStyle = '#ef4444';
      ctx.beginPath();
      ctx.arc(electronPosition.x, electronPosition.y, 6, 0, 2 * Math.PI);
      ctx.fill();

      // Electron trajectory
      ctx.strokeStyle = '#ef4444';
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.moveTo(300, 250);
      ctx.lineTo(electronPosition.x, electronPosition.y);
      ctx.stroke();
      ctx.setLineDash([]);

      // Positron (blue)
      ctx.fillStyle = '#3b82f6';
      ctx.beginPath();
      ctx.arc(positronPosition.x, positronPosition.y, 6, 0, 2 * Math.PI);
      ctx.fill();

      // Positron trajectory
      ctx.strokeStyle = '#3b82f6';
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.moveTo(300, 250);
      ctx.lineTo(positronPosition.x, positronPosition.y);
      ctx.stroke();
      ctx.setLineDash([]);

      // Add + and - signs
      ctx.fillStyle = '#fff';
      ctx.font = 'bold 10px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('-', electronPosition.x, electronPosition.y + 3);
      ctx.fillText('+', positronPosition.x, positronPosition.y + 3);
    }

    // Draw annihilation
    if (showAnnihilation) {
      // Annihilation photons
      ctx.fillStyle = '#f97316';
      ctx.beginPath();
      ctx.arc(350, 220, 5, 0, 2 * Math.PI);
      ctx.fill();

      ctx.beginPath();
      ctx.arc(350, 280, 5, 0, 2 * Math.PI);
      ctx.fill();

      // Annihilation flash
      ctx.strokeStyle = '#fbbf24';
      ctx.lineWidth = 2;
      for (let i = 0; i < 16; i++) {
        const angle = i * 22.5 * Math.PI / 180;
        const length = 30;
        ctx.beginPath();
        ctx.moveTo(340, 250);
        ctx.lineTo(340 + Math.cos(angle) * length, 250 + Math.sin(angle) * length);
        ctx.stroke();
      }
    }

    // Draw energy labels
    ctx.fillStyle = '#374151';
    ctx.font = '12px Arial';
    ctx.textAlign = 'left';

    if (animationPhase === 'ready') {
      ctx.fillText('High Energy Photon', 80, 230);
      ctx.fillText(`${photonEnergy[0]} keV`, 80, 245);
      ctx.fillText('Nucleus', 320, 280);
    }

    if (animationPhase === 'pair-creation' && !showAnnihilation) {
      ctx.fillText('Electron (e⁻)', electronPosition.x + 10, electronPosition.y - 10);
      ctx.fillText(`KE: ${electronKE.toFixed(0)} keV`, electronPosition.x + 10, electronPosition.y + 5);
      ctx.fillText('Positron (e⁺)', positronPosition.x + 10, positronPosition.y + 15);
      ctx.fillText(`KE: ${positronKE.toFixed(0)} keV`, positronPosition.x + 10, positronPosition.y + 30);
    }

    if (showAnnihilation) {
      ctx.fillText('Annihilation', 320, 200);
      ctx.fillText('2 × 511 keV photons', 320, 215);
    }

    // Warning for low energy
    if (!canOccur) {
      ctx.fillStyle = '#ef4444';
      ctx.font = 'bold 14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Energy too low!', 300, 350);
      ctx.fillText(`Minimum: ${thresholdEnergy} keV`, 300, 370);
    }
  };

  const resetAnimation = () => {
    setPhotonPosition({ x: 50, y: 250 });
    setElectronPosition({ x: 300, y: 250 });
    setPositronPosition({ x: 300, y: 250 });
    setAnimationPhase('ready');
    setShowAnnihilation(false);
    setIsRunning(false);
  };

  const toggleAnimation = () => {
    if (animationPhase === 'pair-creation' || showAnnihilation) {
      resetAnimation();
    } else {
      setIsRunning(!isRunning);
    }
  };

  return (
    <div className="space-y-6" data-id="3reo9h689" data-path="src/components/simulations/PairProductionSimulation.tsx">
      {/* Canvas */}
      <div className="relative bg-white rounded-lg border" data-id="i9gn0xrdw" data-path="src/components/simulations/PairProductionSimulation.tsx">
        <canvas
          ref={canvasRef}
          width={600}
          height={400}
          className="w-full h-80 rounded-lg" data-id="7yp2stg69" data-path="src/components/simulations/PairProductionSimulation.tsx" />

        
        {/* Control Buttons */}
        <div className="absolute top-4 right-4 flex gap-2" data-id="qdd9ireud" data-path="src/components/simulations/PairProductionSimulation.tsx">
          <Button size="sm" onClick={toggleAnimation} disabled={!canOccur} data-id="292ufhwtb" data-path="src/components/simulations/PairProductionSimulation.tsx">
            {isRunning ? <Pause className="w-4 h-4" data-id="g94xohorr" data-path="src/components/simulations/PairProductionSimulation.tsx" /> : <Play className="w-4 h-4" data-id="r6dsuvxoa" data-path="src/components/simulations/PairProductionSimulation.tsx" />}
          </Button>
          <Button size="sm" variant="outline" onClick={resetAnimation} data-id="q2js435y6" data-path="src/components/simulations/PairProductionSimulation.tsx">
            <RotateCcw className="w-4 h-4" data-id="afiojncmf" data-path="src/components/simulations/PairProductionSimulation.tsx" />
          </Button>
        </div>
      </div>

      {/* Controls */}
      <div className="grid md:grid-cols-2 gap-6" data-id="mwk7jqyl8" data-path="src/components/simulations/PairProductionSimulation.tsx">
        <Card data-id="v9vv6ilxl" data-path="src/components/simulations/PairProductionSimulation.tsx">
          <CardContent className="pt-6 space-y-4" data-id="wt9o0dzve" data-path="src/components/simulations/PairProductionSimulation.tsx">
            <div data-id="0xqud5zf9" data-path="src/components/simulations/PairProductionSimulation.tsx">
              <Label className="text-sm font-medium" data-id="z9hrvdmta" data-path="src/components/simulations/PairProductionSimulation.tsx">Photon Energy (keV)</Label>
              <Slider
                value={photonEnergy}
                onValueChange={setPhotonEnergy}
                max={10000}
                min={500}
                step={100}
                className="mt-2" data-id="lc73zakao" data-path="src/components/simulations/PairProductionSimulation.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="o1wlh1ue4" data-path="src/components/simulations/PairProductionSimulation.tsx">
                <span data-id="6cvtdnyqf" data-path="src/components/simulations/PairProductionSimulation.tsx">500</span>
                <span className="font-medium" data-id="5z4b6iuk9" data-path="src/components/simulations/PairProductionSimulation.tsx">{photonEnergy[0]} keV</span>
                <span data-id="t0rxqhs88" data-path="src/components/simulations/PairProductionSimulation.tsx">10000</span>
              </div>
            </div>

            <div data-id="3q0xpi1zo" data-path="src/components/simulations/PairProductionSimulation.tsx">
              <Label className="text-sm font-medium" data-id="sv6tmrxcu" data-path="src/components/simulations/PairProductionSimulation.tsx">Atomic Number (Z)</Label>
              <Slider
                value={atomicNumber}
                onValueChange={setAtomicNumber}
                max={92}
                min={20}
                step={1}
                className="mt-2" data-id="0q4hjugdj" data-path="src/components/simulations/PairProductionSimulation.tsx" />

              <div className="flex justify-between text-xs text-gray-500 mt-1" data-id="zgryqper2" data-path="src/components/simulations/PairProductionSimulation.tsx">
                <span data-id="d2t0bihb2" data-path="src/components/simulations/PairProductionSimulation.tsx">Ca (20)</span>
                <span className="font-medium" data-id="63z9gji24" data-path="src/components/simulations/PairProductionSimulation.tsx">Z = {atomicNumber[0]}</span>
                <span data-id="zjbapuo65" data-path="src/components/simulations/PairProductionSimulation.tsx">U (92)</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="l41ou0g1c" data-path="src/components/simulations/PairProductionSimulation.tsx">
          <CardContent className="pt-6 space-y-4" data-id="1ij9l9rbi" data-path="src/components/simulations/PairProductionSimulation.tsx">
            <div data-id="vjx13ltfc" data-path="src/components/simulations/PairProductionSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="6xdspaara" data-path="src/components/simulations/PairProductionSimulation.tsx">Threshold Energy</Label>
              <Badge variant="outline" data-id="rg64nf8gk" data-path="src/components/simulations/PairProductionSimulation.tsx">{thresholdEnergy} keV (1.022 MeV)</Badge>
            </div>

            <div data-id="92i97c4sa" data-path="src/components/simulations/PairProductionSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="hn1u2jlil" data-path="src/components/simulations/PairProductionSimulation.tsx">Available Kinetic Energy</Label>
              <Badge variant={canOccur ? "outline" : "destructive"} data-id="99q4ii10l" data-path="src/components/simulations/PairProductionSimulation.tsx">
                {kineticEnergy.toFixed(0)} keV
              </Badge>
            </div>

            <div data-id="kj18sx1dx" data-path="src/components/simulations/PairProductionSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="d5cv13cw9" data-path="src/components/simulations/PairProductionSimulation.tsx">Cross-Section (relative)</Label>
              <Badge variant="secondary" data-id="5u2511a45" data-path="src/components/simulations/PairProductionSimulation.tsx">{crossSection.toFixed(2)} units</Badge>
            </div>

            <div data-id="0fr0bdugk" data-path="src/components/simulations/PairProductionSimulation.tsx">
              <Label className="text-sm font-medium mb-2 block" data-id="3hc21mj1l" data-path="src/components/simulations/PairProductionSimulation.tsx">Process Possible</Label>
              <Badge variant={canOccur ? "default" : "destructive"} data-id="8uq6bkn3v" data-path="src/components/simulations/PairProductionSimulation.tsx">
                {canOccur ? "Yes" : "No"}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Information Panel */}
      <Card data-id="jp1okehqw" data-path="src/components/simulations/PairProductionSimulation.tsx">
        <CardContent className="pt-6" data-id="4iilgww75" data-path="src/components/simulations/PairProductionSimulation.tsx">
          <h3 className="font-semibold text-gray-900 mb-3" data-id="i43o8bhyd" data-path="src/components/simulations/PairProductionSimulation.tsx">Pair Production</h3>
          <div className="grid md:grid-cols-3 gap-4 text-sm text-gray-600" data-id="mz4i8gwa5" data-path="src/components/simulations/PairProductionSimulation.tsx">
            <div data-id="l5vosy14t" data-path="src/components/simulations/PairProductionSimulation.tsx">
              <h4 className="font-medium text-gray-900 mb-1" data-id="0fwghi4ae" data-path="src/components/simulations/PairProductionSimulation.tsx">Process</h4>
              <p data-id="haj65s9bk" data-path="src/components/simulations/PairProductionSimulation.tsx">High-energy photon converts to electron-positron pair in nuclear field. Requires E ≥ 1.022 MeV.</p>
            </div>
            <div data-id="usvretttg" data-path="src/components/simulations/PairProductionSimulation.tsx">
              <h4 className="font-medium text-gray-900 mb-1" data-id="q4xg0203s" data-path="src/components/simulations/PairProductionSimulation.tsx">Energy Conservation</h4>
              <p data-id="ji5m556ro" data-path="src/components/simulations/PairProductionSimulation.tsx">E_photon = 2m₀c² + KE_electron + KE_positron</p>
              <p className="text-xs mt-1" data-id="pfgc1tsol" data-path="src/components/simulations/PairProductionSimulation.tsx">Where 2m₀c² = 1.022 MeV</p>
            </div>
            <div data-id="kl8ofe32y" data-path="src/components/simulations/PairProductionSimulation.tsx">
              <h4 className="font-medium text-gray-900 mb-1" data-id="tl57qrxvr" data-path="src/components/simulations/PairProductionSimulation.tsx">Characteristics</h4>
              <p data-id="ejs4gfwuz" data-path="src/components/simulations/PairProductionSimulation.tsx">Dominant at high energies (&gt;10 MeV) and high-Z materials. Cross-section ∝ Z² ln(E).</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>);

};

export default PairProductionSimulation;