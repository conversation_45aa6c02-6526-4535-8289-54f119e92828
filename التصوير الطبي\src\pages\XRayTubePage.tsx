import { useState } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import {
  Zap,
  Target,
  Thermometer,
  Settings,
  Play,
  Pause,
  RotateCcw } from
'lucide-react';
import XRayTubeDiagram from '@/components/XRayTubeDiagram';
import XRaySimulator from '@/components/XRaySimulator';

const XRayTubePage = () => {
  const [activeTab, setActiveTab] = useState('structure');

  const tubeComponents = [
  {
    id: 'cathode',
    name: 'الكاثود (المهبط)',
    description: 'مصدر الإلكترونات، يحتوي على فتيل التنجستن المُسخن',
    details: [
    'فتيل التنجستن المُسخن لإنتاج الإلكترونات',
    'كوب التركيز لتوجيه الإلكترونات',
    'درجة حرارة التشغيل: 2000-2500°C',
    'عمر الفتيل: 1000-2000 ساعة'],

    color: 'bg-red-500'
  },
  {
    id: 'anode',
    name: 'الأنود (المصعد)',
    description: 'الهدف الذي تصطدم به الإلكترونات لإنتاج الأشعة السينية',
    details: [
    'مصنوع من التنجستن أو الموليبدينوم',
    'زاوية الهدف: 12-17 درجة',
    'قدرة تحمل الحرارة العالية',
    'قرص دوار لتوزيع الحرارة'],

    color: 'bg-blue-500'
  },
  {
    id: 'envelope',
    name: 'الغلاف الزجاجي',
    description: 'يحتوي على فراغ عالي لحركة الإلكترونات الحرة',
    details: [
    'زجاج بوروسيليكات مقاوم للحرارة',
    'فراغ عالي (10⁻⁶ mmHg)',
    'نافذة خروج الأشعة السينية',
    'حماية من التلوث'],

    color: 'bg-green-500'
  },
  {
    id: 'housing',
    name: 'الغلاف الواقي',
    description: 'يوفر الحماية الإشعاعية والتبريد',
    details: [
    'رصاص للحماية الإشعاعية',
    'نظام تبريد بالزيت أو الهواء',
    'عزل كهربائي عالي الجهد',
    'مؤشرات التشغيل والحرارة'],

    color: 'bg-purple-500'
  }];


  return (
    <div className="min-h-screen py-8" data-id="vy76n55zx" data-path="src/pages/XRayTubePage.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="ihlpil2ar" data-path="src/pages/XRayTubePage.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12" data-id="n7q4rayte" data-path="src/pages/XRayTubePage.tsx">

          <Badge variant="secondary" className="mb-4" data-id="8g80wtwy8" data-path="src/pages/XRayTubePage.tsx">أنبوب الأشعة السينية</Badge>
          <h1 className="text-4xl font-bold text-gray-900 mb-4" data-id="z3wmd2h1l" data-path="src/pages/XRayTubePage.tsx">
            تشريح أنبوب الأشعة السينية
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="c3g8855zd" data-path="src/pages/XRayTubePage.tsx">
            استكشف المكونات الداخلية وآلية عمل أنبوب الأشعة السينية بشكل تفاعلي
          </p>
        </motion.div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" data-id="bp7xi940b" data-path="src/pages/XRayTubePage.tsx">
          <TabsList className="grid w-full grid-cols-3 mb-8" data-id="x5bn3mw6i" data-path="src/pages/XRayTubePage.tsx">
            <TabsTrigger value="structure" data-id="vx3e4u4pd" data-path="src/pages/XRayTubePage.tsx">التركيب</TabsTrigger>
            <TabsTrigger value="operation" data-id="60uo7fozh" data-path="src/pages/XRayTubePage.tsx">آلية العمل</TabsTrigger>
            <TabsTrigger value="simulator" data-id="u6vq5dbgf" data-path="src/pages/XRayTubePage.tsx">المحاكي</TabsTrigger>
          </TabsList>

          {/* Structure Tab */}
          <TabsContent value="structure" data-id="66feap9qe" data-path="src/pages/XRayTubePage.tsx">
            <div className="grid lg:grid-cols-2 gap-8" data-id="6lh3aejjw" data-path="src/pages/XRayTubePage.tsx">
              {/* Diagram */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }} data-id="gfjo638r4" data-path="src/pages/XRayTubePage.tsx">

                <Card className="h-full" data-id="22m84wseg" data-path="src/pages/XRayTubePage.tsx">
                  <CardHeader data-id="1xm6uqfz3" data-path="src/pages/XRayTubePage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="ly9vho89k" data-path="src/pages/XRayTubePage.tsx">
                      <Target className="w-5 h-5 text-blue-600" data-id="zs8k52oz4" data-path="src/pages/XRayTubePage.tsx" />
                      المخطط التفاعلي
                    </CardTitle>
                    <CardDescription data-id="5g61zxxoa" data-path="src/pages/XRayTubePage.tsx">
                      انقر على المكونات لاستكشاف تفاصيلها
                    </CardDescription>
                  </CardHeader>
                  <CardContent data-id="h1yss17bk" data-path="src/pages/XRayTubePage.tsx">
                    <XRayTubeDiagram data-id="r85in3j7a" data-path="src/pages/XRayTubePage.tsx" />
                  </CardContent>
                </Card>
              </motion.div>

              {/* Components */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="space-y-4" data-id="f3vccu3tc" data-path="src/pages/XRayTubePage.tsx">

                {tubeComponents.map((component, index) =>
                <motion.div
                  key={component.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }} data-id="arutbiu99" data-path="src/pages/XRayTubePage.tsx">

                    <Card className="hover:shadow-lg transition-shadow" data-id="wg4emmsjx" data-path="src/pages/XRayTubePage.tsx">
                      <CardHeader className="pb-3" data-id="0rwlq73py" data-path="src/pages/XRayTubePage.tsx">
                        <div className="flex items-center gap-3" data-id="8vyua07ch" data-path="src/pages/XRayTubePage.tsx">
                          <div className={`w-4 h-4 rounded-full ${component.color}`} data-id="gy6ss5mfp" data-path="src/pages/XRayTubePage.tsx"></div>
                          <CardTitle className="text-lg" data-id="4wuxssaur" data-path="src/pages/XRayTubePage.tsx">{component.name}</CardTitle>
                        </div>
                        <CardDescription data-id="o3il9fat2" data-path="src/pages/XRayTubePage.tsx">{component.description}</CardDescription>
                      </CardHeader>
                      <CardContent data-id="zx1krs4d9" data-path="src/pages/XRayTubePage.tsx">
                        <ul className="space-y-2" data-id="bfxry221m" data-path="src/pages/XRayTubePage.tsx">
                          {component.details.map((detail, idx) =>
                        <li key={idx} className="flex items-start gap-2 text-sm text-gray-600" data-id="ubuvhsmbz" data-path="src/pages/XRayTubePage.tsx">
                              <div className="w-1.5 h-1.5 rounded-full bg-gray-400 mt-2 flex-shrink-0" data-id="sbihwx59g" data-path="src/pages/XRayTubePage.tsx"></div>
                              {detail}
                            </li>
                        )}
                        </ul>
                      </CardContent>
                    </Card>
                  </motion.div>
                )}
              </motion.div>
            </div>
          </TabsContent>

          {/* Operation Tab */}
          <TabsContent value="operation" data-id="hxob2st36" data-path="src/pages/XRayTubePage.tsx">
            <div className="grid gap-8" data-id="j59brb6iu" data-path="src/pages/XRayTubePage.tsx">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }} data-id="a8hrsc6n0" data-path="src/pages/XRayTubePage.tsx">

                <Card data-id="5r8z4g57q" data-path="src/pages/XRayTubePage.tsx">
                  <CardHeader data-id="wio941rq2" data-path="src/pages/XRayTubePage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="x7fbtddnl" data-path="src/pages/XRayTubePage.tsx">
                      <Zap className="w-5 h-5 text-yellow-600" data-id="chbtntii9" data-path="src/pages/XRayTubePage.tsx" />
                      مراحل إنتاج الأشعة السينية
                    </CardTitle>
                  </CardHeader>
                  <CardContent data-id="13xb3l76m" data-path="src/pages/XRayTubePage.tsx">
                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6" data-id="yvalyyl25" data-path="src/pages/XRayTubePage.tsx">
                      {[
                      {
                        step: '1',
                        title: 'تسخين الفتيل',
                        description: 'تمرير تيار كهربائي عبر فتيل التنجستن لتسخينه وإطلاق الإلكترونات',
                        icon: Thermometer,
                        color: 'bg-red-100 text-red-600'
                      },
                      {
                        step: '2',
                        title: 'تسريع الإلكترونات',
                        description: 'تطبيق فولتية عالية (40-150 kV) لتسريع الإلكترونات نحو الأنود',
                        icon: Zap,
                        color: 'bg-yellow-100 text-yellow-600'
                      },
                      {
                        step: '3',
                        title: 'الاصطدام والتفاعل',
                        description: 'اصطدام الإلكترونات بالأنود وتحويل الطاقة الحركية',
                        icon: Target,
                        color: 'bg-blue-100 text-blue-600'
                      },
                      {
                        step: '4',
                        title: 'إنتاج الأشعة',
                        description: 'إنتاج الأشعة السينية (1%) والحرارة (99%) من التفاعل',
                        icon: Zap,
                        color: 'bg-purple-100 text-purple-600'
                      }].
                      map((step, index) => {
                        const Icon = step.icon;
                        return (
                          <motion.div
                            key={step.step}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.2 }}
                            className="text-center" data-id="ndn08dmks" data-path="src/pages/XRayTubePage.tsx">

                            <div className={`inline-flex items-center justify-center w-16 h-16 ${step.color} rounded-full mb-4`} data-id="7a77jilvw" data-path="src/pages/XRayTubePage.tsx">
                              <Icon className="w-8 h-8" data-id="5cuaf78hs" data-path="src/pages/XRayTubePage.tsx" />
                            </div>
                            <div className="bg-gray-900 text-white text-sm font-bold w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-3" data-id="jkk9044tn" data-path="src/pages/XRayTubePage.tsx">
                              {step.step}
                            </div>
                            <h3 className="font-semibold text-gray-900 mb-2" data-id="yet12gnnq" data-path="src/pages/XRayTubePage.tsx">{step.title}</h3>
                            <p className="text-sm text-gray-600" data-id="qdpdd3620" data-path="src/pages/XRayTubePage.tsx">{step.description}</p>
                          </motion.div>);

                      })}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Technical Specifications */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }} data-id="xp77p0kkj" data-path="src/pages/XRayTubePage.tsx">

                <Card data-id="cb5z6vwo4" data-path="src/pages/XRayTubePage.tsx">
                  <CardHeader data-id="j91zao0ok" data-path="src/pages/XRayTubePage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="rj7gp4yq4" data-path="src/pages/XRayTubePage.tsx">
                      <Settings className="w-5 h-5 text-gray-600" data-id="gndikla7s" data-path="src/pages/XRayTubePage.tsx" />
                      المواصفات التقنية
                    </CardTitle>
                  </CardHeader>
                  <CardContent data-id="h9l6jzuac" data-path="src/pages/XRayTubePage.tsx">
                    <div className="grid md:grid-cols-3 gap-6" data-id="my9gpj0ws" data-path="src/pages/XRayTubePage.tsx">
                      <div className="space-y-4" data-id="yl69c96ku" data-path="src/pages/XRayTubePage.tsx">
                        <h4 className="font-semibold text-gray-900" data-id="2kqg087ez" data-path="src/pages/XRayTubePage.tsx">الجهد والتيار</h4>
                        <div className="space-y-2 text-sm" data-id="lpt9wa9uz" data-path="src/pages/XRayTubePage.tsx">
                          <div className="flex justify-between" data-id="5nl2b7a5e" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="r0ymamdsl" data-path="src/pages/XRayTubePage.tsx">جهد الأنبوب:</span>
                            <span className="font-medium" data-id="ffjpypz66" data-path="src/pages/XRayTubePage.tsx">40-150 kV</span>
                          </div>
                          <div className="flex justify-between" data-id="6jplujcci" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="wmpsumahu" data-path="src/pages/XRayTubePage.tsx">تيار الأنبوب:</span>
                            <span className="font-medium" data-id="emw3ibeq8" data-path="src/pages/XRayTubePage.tsx">50-1000 mA</span>
                          </div>
                          <div className="flex justify-between" data-id="q3imd3r8k" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="zhmls3xzp" data-path="src/pages/XRayTubePage.tsx">تيار الفتيل:</span>
                            <span className="font-medium" data-id="xj2hxwgm7" data-path="src/pages/XRayTubePage.tsx">3-5 A</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4" data-id="icu5of3ke" data-path="src/pages/XRayTubePage.tsx">
                        <h4 className="font-semibold text-gray-900" data-id="buodqasr8" data-path="src/pages/XRayTubePage.tsx">الحرارة والتبريد</h4>
                        <div className="space-y-2 text-sm" data-id="4eph57xmp" data-path="src/pages/XRayTubePage.tsx">
                          <div className="flex justify-between" data-id="3q0b5bitm" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="4esx5bwjy" data-path="src/pages/XRayTubePage.tsx">سعة الأنود الحرارية:</span>
                            <span className="font-medium" data-id="ct1rsvuet" data-path="src/pages/XRayTubePage.tsx">300-2000 kJ</span>
                          </div>
                          <div className="flex justify-between" data-id="67a4ghkyu" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="bkrr19cby" data-path="src/pages/XRayTubePage.tsx">معدل التبريد:</span>
                            <span className="font-medium" data-id="mzcapz1qh" data-path="src/pages/XRayTubePage.tsx">50-200 kW</span>
                          </div>
                          <div className="flex justify-between" data-id="o62fvggtj" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="yjyi4kng2" data-path="src/pages/XRayTubePage.tsx">درجة حرارة التشغيل:</span>
                            <span className="font-medium" data-id="2p5yafj32" data-path="src/pages/XRayTubePage.tsx">1000-2500°C</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4" data-id="x1deck3ca" data-path="src/pages/XRayTubePage.tsx">
                        <h4 className="font-semibold text-gray-900" data-id="ae95zv51e" data-path="src/pages/XRayTubePage.tsx">البنية الفيزيائية</h4>
                        <div className="space-y-2 text-sm" data-id="jsht3zr1v" data-path="src/pages/XRayTubePage.tsx">
                          <div className="flex justify-between" data-id="lpk33lcye" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="s7cujseeg" data-path="src/pages/XRayTubePage.tsx">زاوية الأنود:</span>
                            <span className="font-medium" data-id="j1cwz3whs" data-path="src/pages/XRayTubePage.tsx">12-17°</span>
                          </div>
                          <div className="flex justify-between" data-id="y322kmjgn" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="wgvti85si" data-path="src/pages/XRayTubePage.tsx">حجم البؤرة:</span>
                            <span className="font-medium" data-id="3ug5l4kyh" data-path="src/pages/XRayTubePage.tsx">0.3-2.0 mm</span>
                          </div>
                          <div className="flex justify-between" data-id="puvcxsp28" data-path="src/pages/XRayTubePage.tsx">
                            <span data-id="36tve8cs0" data-path="src/pages/XRayTubePage.tsx">مستوى الفراغ:</span>
                            <span className="font-medium" data-id="02kxwem8z" data-path="src/pages/XRayTubePage.tsx">10⁻⁶ mmHg</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </TabsContent>

          {/* Simulator Tab */}
          <TabsContent value="simulator" data-id="n5hqa1s8i" data-path="src/pages/XRayTubePage.tsx">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }} data-id="3env8w1nn" data-path="src/pages/XRayTubePage.tsx">

              <XRaySimulator data-id="0igsjiwjs" data-path="src/pages/XRayTubePage.tsx" />
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>
    </div>);

};

export default XRayTubePage;