import { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Play, Pause, RotateCcw } from 'lucide-react';

const XRaySimulation = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageCanvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  const [kVp, setKVp] = useState([80]); // kVp
  const [mAs, setMAs] = useState([100]); // mAs
  const [filtration, setFiltration] = useState([2]); // mm Al
  const [phantomType, setPhantomType] = useState('chest');
  const [isRunning, setIsRunning] = useState(false);

  // Animation state
  const [xrayBeam, setXrayBeam] = useState([]);
  const [detectorSignal, setDetectorSignal] = useState([]);

  const phantoms = {
    chest: {
      name: 'Chest Phantom',
      tissues: [
      { name: 'Air', density: 0.001, thickness: 200, mu: 0.0001 },
      { name: 'Lung', density: 0.3, thickness: 100, mu: 0.02 },
      { name: 'Heart', density: 1.0, thickness: 120, mu: 0.2 },
      { name: 'Rib', density: 1.9, thickness: 15, mu: 0.5 },
      { name: 'Muscle', density: 1.0, thickness: 30, mu: 0.18 }]

    },
    abdomen: {
      name: 'Abdomen Phantom',
      tissues: [
      { name: 'Muscle', density: 1.0, thickness: 50, mu: 0.18 },
      { name: 'Fat', density: 0.9, thickness: 20, mu: 0.15 },
      { name: 'Liver', density: 1.05, thickness: 80, mu: 0.19 },
      { name: 'Bone', density: 1.9, thickness: 20, mu: 0.5 },
      { name: 'Bowel Gas', density: 0.001, thickness: 30, mu: 0.0001 }]

    },
    skull: {
      name: 'Skull Phantom',
      tissues: [
      { name: 'Skin', density: 1.0, thickness: 5, mu: 0.18 },
      { name: 'Muscle', density: 1.0, thickness: 10, mu: 0.18 },
      { name: 'Skull Bone', density: 2.0, thickness: 8, mu: 0.6 },
      { name: 'Brain', density: 1.04, thickness: 150, mu: 0.19 },
      { name: 'CSF', density: 1.0, thickness: 5, mu: 0.18 }]

    }
  };

  // Calculate beam characteristics
  const beamIntensity = mAs[0] * Math.pow(kVp[0], 2) / 100;
  const beamHardening = 1 - Math.exp(-filtration[0] * 0.1);
  const effectiveEnergy = kVp[0] * 0.3 * (1 + beamHardening);

  // Animation loop
  useEffect(() => {
    if (isRunning && canvasRef.current) {
      const animate = () => {
        drawSimulation();
        generateImage();
        animationRef.current = requestAnimationFrame(animate);
      };
      animate();
    } else if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRunning, kVp, mAs, filtration, phantomType]);

  const drawSimulation = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw X-ray tube
    ctx.fillStyle = '#374151';
    ctx.fillRect(20, 150, 60, 40);
    ctx.fillStyle = '#fbbf24';
    ctx.beginPath();
    ctx.arc(50, 170, 8, 0, 2 * Math.PI);
    ctx.fill();

    // Draw X-ray beam
    const beamWidth = 60;
    const beamStart = 80;
    const beamEnd = 300;

    ctx.fillStyle = `rgba(251, 191, 36, ${0.3 + beamIntensity / 1000})`;
    ctx.beginPath();
    ctx.moveTo(beamStart, 170 - beamWidth / 2);
    ctx.lineTo(beamEnd, 170 - beamWidth / 2);
    ctx.lineTo(beamEnd, 170 + beamWidth / 2);
    ctx.lineTo(beamStart, 170 + beamWidth / 2);
    ctx.closePath();
    ctx.fill();

    // Draw phantom
    const phantom = phantoms[phantomType];
    let currentX = 320;

    phantom.tissues.forEach((tissue, index) => {
      const width = tissue.thickness * 0.8;
      const height = 100;

      // Color based on density
      let color;
      if (tissue.density < 0.1) color = '#1f2937'; // Air - dark
      else if (tissue.density < 0.5) color = '#374151'; // Lung - dark gray
      else if (tissue.density < 1.2) color = '#6b7280'; // Soft tissue - gray
      else color = '#f3f4f6'; // Bone - light

      ctx.fillStyle = color;
      ctx.fillRect(currentX, 120, width, height);

      // Label
      ctx.fillStyle = '#000';
      ctx.font = '10px Arial';
      ctx.save();
      ctx.translate(currentX + width / 2, 170);
      ctx.rotate(-Math.PI / 2);
      ctx.textAlign = 'center';
      ctx.fillText(tissue.name, 0, 0);
      ctx.restore();

      currentX += width + 2;
    });

    // Draw detector
    ctx.fillStyle = '#1e40af';
    ctx.fillRect(500, 120, 20, 100);
    ctx.fillStyle = '#3b82f6';
    ctx.fillRect(505, 125, 10, 90);

    // Draw beam attenuation
    let attenuatedIntensity = beamIntensity;
    phantom.tissues.forEach((tissue) => {
      const attenuation = Math.exp(-tissue.mu * tissue.thickness * 0.1);
      attenuatedIntensity *= attenuation;
    });

    // Show intensity values
    ctx.fillStyle = '#374151';
    ctx.font = '12px Arial';
    ctx.fillText(`Initial: ${beamIntensity.toFixed(0)}`, 100, 140);
    ctx.fillText(`Final: ${attenuatedIntensity.toFixed(0)}`, 450, 140);
    ctx.fillText(`Attenuation: ${((1 - attenuatedIntensity / beamIntensity) * 100).toFixed(1)}%`, 250, 260);

    // Draw beam parameters
    ctx.fillText(`${kVp[0]} kVp, ${mAs[0]} mAs`, 100, 120);
    ctx.fillText(`${filtration[0]} mm Al filter`, 100, 105);
  };

  const generateImage = () => {
    const canvas = imageCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    const phantom = phantoms[phantomType];
    const imageData = ctx.createImageData(canvas.width, canvas.height);

    // Simulate X-ray image formation
    for (let y = 0; y < canvas.height; y++) {
      for (let x = 0; x < canvas.width; x++) {
        let totalAttenuation = 0;

        // Calculate path through phantom
        const phantomX = x / canvas.width * 300;
        let currentPos = 0;

        phantom.tissues.forEach((tissue) => {
          const tissueEnd = currentPos + tissue.thickness;
          if (phantomX >= currentPos && phantomX < tissueEnd) {
            totalAttenuation += tissue.mu * tissue.thickness * 0.1;
          }
          currentPos = tissueEnd;
        });

        // Calculate transmitted intensity
        const transmittedIntensity = beamIntensity * Math.exp(-totalAttenuation);
        const pixelValue = Math.min(255, transmittedIntensity * 2);

        const index = (y * canvas.width + x) * 4;
        imageData.data[index] = pixelValue; // R
        imageData.data[index + 1] = pixelValue; // G
        imageData.data[index + 2] = pixelValue; // B
        imageData.data[index + 3] = 255; // A
      }
    }

    ctx.putImageData(imageData, 0, 0);

    // Add labels
    ctx.fillStyle = '#000';
    ctx.font = '14px Arial';
    ctx.fillText('X-ray Image', 10, 25);
    ctx.font = '10px Arial';
    ctx.fillText('(Brighter = More transmission)', 10, 40);
  };

  const toggleAnimation = () => {
    setIsRunning(!isRunning);
  };

  const resetSimulation = () => {
    setIsRunning(false);
  };

  return (
    <div className="space-y-6" data-id="p1642k1n4" data-path="src/components/simulations/XRaySimulation.tsx">
      {/* Main Simulation Canvas */}
      <div className="grid md:grid-cols-2 gap-4" data-id="rnkj9mnml" data-path="src/components/simulations/XRaySimulation.tsx">
        <div className="relative bg-white rounded-lg border p-4" data-id="h1feh68tv" data-path="src/components/simulations/XRaySimulation.tsx">
          <h3 className="text-lg font-semibold mb-2" data-id="n80fubckq" data-path="src/components/simulations/XRaySimulation.tsx">X-ray System</h3>
          <canvas
            ref={canvasRef}
            width={550}
            height={300}
            className="w-full h-64 border rounded" data-id="bgda2y2wa" data-path="src/components/simulations/XRaySimulation.tsx" />

          
          <div className="absolute top-4 right-4 flex gap-2" data-id="1bgnu4cul" data-path="src/components/simulations/XRaySimulation.tsx">
            <Button size="sm" onClick={toggleAnimation} data-id="ebzbvvzsb" data-path="src/components/simulations/XRaySimulation.tsx">
              {isRunning ? <Pause className="w-4 h-4" data-id="yo9yk85yz" data-path="src/components/simulations/XRaySimulation.tsx" /> : <Play className="w-4 h-4" data-id="rxubxolsd" data-path="src/components/simulations/XRaySimulation.tsx" />}
            </Button>
            <Button size="sm" variant="outline" onClick={resetSimulation} data-id="jmidvfxbf" data-path="src/components/simulations/XRaySimulation.tsx">
              <RotateCcw className="w-4 h-4" data-id="6wlfvjnop" data-path="src/components/simulations/XRaySimulation.tsx" />
            </Button>
          </div>
        </div>

        <div className="relative bg-white rounded-lg border p-4" data-id="3009tcauc" data-path="src/components/simulations/XRaySimulation.tsx">
          <h3 className="text-lg font-semibold mb-2" data-id="71jbman5x" data-path="src/components/simulations/XRaySimulation.tsx">Resulting Image</h3>
          <canvas
            ref={imageCanvasRef}
            width={300}
            height={200}
            className="w-full h-64 border rounded bg-black" data-id="ky8f0wxe7" data-path="src/components/simulations/XRaySimulation.tsx" />

        </div>
      </div>

      {/* Controls */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4" data-id="myebdg1kr" data-path="src/components/simulations/XRaySimulation.tsx">
        <Card data-id="030o97hw4" data-path="src/components/simulations/XRaySimulation.tsx">
          <CardContent className="pt-6" data-id="j7p5ibymp" data-path="src/components/simulations/XRaySimulation.tsx">
            <div className="space-y-3" data-id="ux620jl2m" data-path="src/components/simulations/XRaySimulation.tsx">
              <Label className="text-sm font-medium" data-id="u03iiiay1" data-path="src/components/simulations/XRaySimulation.tsx">Tube Voltage (kVp)</Label>
              <Slider
                value={kVp}
                onValueChange={setKVp}
                max={150}
                min={40}
                step={5}
                className="mt-2" data-id="1wg0umhc5" data-path="src/components/simulations/XRaySimulation.tsx" />

              <div className="flex justify-between text-xs text-gray-500" data-id="wtl7eftx0" data-path="src/components/simulations/XRaySimulation.tsx">
                <span data-id="ff5hzmti1" data-path="src/components/simulations/XRaySimulation.tsx">40</span>
                <span className="font-medium" data-id="770gr0ac1" data-path="src/components/simulations/XRaySimulation.tsx">{kVp[0]} kVp</span>
                <span data-id="lno881x7p" data-path="src/components/simulations/XRaySimulation.tsx">150</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="j0inm60d0" data-path="src/components/simulations/XRaySimulation.tsx">
          <CardContent className="pt-6" data-id="9pvjaj5f0" data-path="src/components/simulations/XRaySimulation.tsx">
            <div className="space-y-3" data-id="7z3g92ktb" data-path="src/components/simulations/XRaySimulation.tsx">
              <Label className="text-sm font-medium" data-id="zprujvdz1" data-path="src/components/simulations/XRaySimulation.tsx">Tube Current-Time (mAs)</Label>
              <Slider
                value={mAs}
                onValueChange={setMAs}
                max={500}
                min={10}
                step={10}
                className="mt-2" data-id="wp52w9pjx" data-path="src/components/simulations/XRaySimulation.tsx" />

              <div className="flex justify-between text-xs text-gray-500" data-id="m0qzoxl1b" data-path="src/components/simulations/XRaySimulation.tsx">
                <span data-id="eknpx9qq2" data-path="src/components/simulations/XRaySimulation.tsx">10</span>
                <span className="font-medium" data-id="zlpox6yh0" data-path="src/components/simulations/XRaySimulation.tsx">{mAs[0]} mAs</span>
                <span data-id="m8i44duji" data-path="src/components/simulations/XRaySimulation.tsx">500</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="h74km5m6v" data-path="src/components/simulations/XRaySimulation.tsx">
          <CardContent className="pt-6" data-id="57s115t86" data-path="src/components/simulations/XRaySimulation.tsx">
            <div className="space-y-3" data-id="o7lynb0qw" data-path="src/components/simulations/XRaySimulation.tsx">
              <Label className="text-sm font-medium" data-id="5i8gwh547" data-path="src/components/simulations/XRaySimulation.tsx">Filtration (mm Al)</Label>
              <Slider
                value={filtration}
                onValueChange={setFiltration}
                max={10}
                min={0}
                step={0.5}
                className="mt-2" data-id="22ar3x778" data-path="src/components/simulations/XRaySimulation.tsx" />

              <div className="flex justify-between text-xs text-gray-500" data-id="eaastqzbz" data-path="src/components/simulations/XRaySimulation.tsx">
                <span data-id="ox60zh5j1" data-path="src/components/simulations/XRaySimulation.tsx">0</span>
                <span className="font-medium" data-id="c0o8jvp7k" data-path="src/components/simulations/XRaySimulation.tsx">{filtration[0]} mm</span>
                <span data-id="40jagsmmn" data-path="src/components/simulations/XRaySimulation.tsx">10</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="85c29htgl" data-path="src/components/simulations/XRaySimulation.tsx">
          <CardContent className="pt-6" data-id="fyr9lsv2p" data-path="src/components/simulations/XRaySimulation.tsx">
            <div className="space-y-3" data-id="rxqbm85q0" data-path="src/components/simulations/XRaySimulation.tsx">
              <Label className="text-sm font-medium" data-id="bxq3bh8ox" data-path="src/components/simulations/XRaySimulation.tsx">Phantom Type</Label>
              <Select value={phantomType} onValueChange={setPhantomType} data-id="29as50umu" data-path="src/components/simulations/XRaySimulation.tsx">
                <SelectTrigger data-id="wkn27jojt" data-path="src/components/simulations/XRaySimulation.tsx">
                  <SelectValue data-id="o8cwmfi39" data-path="src/components/simulations/XRaySimulation.tsx" />
                </SelectTrigger>
                <SelectContent data-id="06x9dngiv" data-path="src/components/simulations/XRaySimulation.tsx">
                  <SelectItem value="chest" data-id="jqjhn7d5f" data-path="src/components/simulations/XRaySimulation.tsx">Chest</SelectItem>
                  <SelectItem value="abdomen" data-id="5sabzjiss" data-path="src/components/simulations/XRaySimulation.tsx">Abdomen</SelectItem>
                  <SelectItem value="skull" data-id="bc4tkmnyk" data-path="src/components/simulations/XRaySimulation.tsx">Skull</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Image Quality Parameters */}
      <div className="grid md:grid-cols-3 gap-6" data-id="8o9h3hx81" data-path="src/components/simulations/XRaySimulation.tsx">
        <Card data-id="puyfrykzm" data-path="src/components/simulations/XRaySimulation.tsx">
          <CardContent className="pt-6" data-id="8t7blu2h2" data-path="src/components/simulations/XRaySimulation.tsx">
            <h4 className="font-semibold text-gray-900 mb-3" data-id="heq2hmy8c" data-path="src/components/simulations/XRaySimulation.tsx">Beam Characteristics</h4>
            <div className="space-y-2" data-id="st10resic" data-path="src/components/simulations/XRaySimulation.tsx">
              <div className="flex justify-between" data-id="7ws9sguuk" data-path="src/components/simulations/XRaySimulation.tsx">
                <span className="text-sm text-gray-600" data-id="ecx5b8n4q" data-path="src/components/simulations/XRaySimulation.tsx">Intensity:</span>
                <Badge variant="outline" data-id="5ler3nf78" data-path="src/components/simulations/XRaySimulation.tsx">{beamIntensity.toFixed(0)}</Badge>
              </div>
              <div className="flex justify-between" data-id="p59qulamz" data-path="src/components/simulations/XRaySimulation.tsx">
                <span className="text-sm text-gray-600" data-id="9vjbgnyex" data-path="src/components/simulations/XRaySimulation.tsx">Effective Energy:</span>
                <Badge variant="outline" data-id="tnms7vugs" data-path="src/components/simulations/XRaySimulation.tsx">{effectiveEnergy.toFixed(1)} keV</Badge>
              </div>
              <div className="flex justify-between" data-id="h2fwxl5xw" data-path="src/components/simulations/XRaySimulation.tsx">
                <span className="text-sm text-gray-600" data-id="vcwp8yqqc" data-path="src/components/simulations/XRaySimulation.tsx">Beam Hardening:</span>
                <Badge variant="outline" data-id="2ods0ydc7" data-path="src/components/simulations/XRaySimulation.tsx">{(beamHardening * 100).toFixed(1)}%</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="kyw1pp8po" data-path="src/components/simulations/XRaySimulation.tsx">
          <CardContent className="pt-6" data-id="d4yniijf6" data-path="src/components/simulations/XRaySimulation.tsx">
            <h4 className="font-semibold text-gray-900 mb-3" data-id="r81pdr3pu" data-path="src/components/simulations/XRaySimulation.tsx">Image Quality</h4>
            <div className="space-y-2" data-id="r6bfr18el" data-path="src/components/simulations/XRaySimulation.tsx">
              <div className="flex justify-between" data-id="vbnahjb25" data-path="src/components/simulations/XRaySimulation.tsx">
                <span className="text-sm text-gray-600" data-id="kga7rb3dd" data-path="src/components/simulations/XRaySimulation.tsx">Contrast:</span>
                <Badge variant="secondary" data-id="t1g79jhpq" data-path="src/components/simulations/XRaySimulation.tsx">Good</Badge>
              </div>
              <div className="flex justify-between" data-id="ggmq3kuxp" data-path="src/components/simulations/XRaySimulation.tsx">
                <span className="text-sm text-gray-600" data-id="jk4kvpc0s" data-path="src/components/simulations/XRaySimulation.tsx">Noise Level:</span>
                <Badge variant="secondary" data-id="njjr5m24c" data-path="src/components/simulations/XRaySimulation.tsx">Low</Badge>
              </div>
              <div className="flex justify-between" data-id="a6mldz3fs" data-path="src/components/simulations/XRaySimulation.tsx">
                <span className="text-sm text-gray-600" data-id="ogk4zobh2" data-path="src/components/simulations/XRaySimulation.tsx">Resolution:</span>
                <Badge variant="secondary" data-id="r2i3vz94x" data-path="src/components/simulations/XRaySimulation.tsx">High</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card data-id="qv4mfyur9" data-path="src/components/simulations/XRaySimulation.tsx">
          <CardContent className="pt-6" data-id="u5a2x039g" data-path="src/components/simulations/XRaySimulation.tsx">
            <h4 className="font-semibold text-gray-900 mb-3" data-id="l5q8n1il6" data-path="src/components/simulations/XRaySimulation.tsx">Phantom Details</h4>
            <div className="space-y-1" data-id="eozaggds9" data-path="src/components/simulations/XRaySimulation.tsx">
              <p className="text-sm font-medium" data-id="xwxs3bc2w" data-path="src/components/simulations/XRaySimulation.tsx">{phantoms[phantomType].name}</p>
              {phantoms[phantomType].tissues.slice(0, 3).map((tissue, idx) =>
              <div key={idx} className="flex justify-between text-xs" data-id="5m77b6gfd" data-path="src/components/simulations/XRaySimulation.tsx">
                  <span className="text-gray-600" data-id="l4hl2thr6" data-path="src/components/simulations/XRaySimulation.tsx">{tissue.name}:</span>
                  <span data-id="za5co5qsq" data-path="src/components/simulations/XRaySimulation.tsx">{tissue.thickness}mm, ρ={tissue.density}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>);

};

export default XRaySimulation;