import { useState } from 'react';
import { motion } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  CircuitBoard,
  Zap,
  Battery,
  Settings,
  Info } from
'lucide-react';
import CircuitDiagram from '@/components/CircuitDiagram';
import PowerSupplyDiagram from '@/components/PowerSupplyDiagram';

const CircuitDiagramPage = () => {
  const [selectedCircuit, setSelectedCircuit] = useState('main');

  const circuitTypes = [
  {
    id: 'main',
    title: 'الدائرة الرئيسية',
    description: 'دائرة التحكم الرئيسية لجهاز الأشعة السينية',
    icon: CircuitBoard,
    color: 'bg-blue-500'
  },
  {
    id: 'power',
    title: 'دائرة مزود الطاقة',
    description: 'دائرة تحويل وتنظيم الطاقة الكهربائية',
    icon: Battery,
    color: 'bg-green-500'
  },
  {
    id: 'control',
    title: 'دائرة التحكم',
    description: 'دائرة التحكم في معاملات التشغيل',
    icon: Settings,
    color: 'bg-purple-500'
  }];


  const circuitComponents = [
  {
    name: 'المحول الرئيسي',
    function: 'رفع الجهد من 220V إلى 40-150kV',
    type: 'محول تصاعدي',
    specifications: 'نسبة التحويل: 1:500-700'
  },
  {
    name: 'المعدل',
    function: 'تحويل التيار المتردد إلى تيار مستمر',
    type: 'مجموعة ديودات',
    specifications: 'معدل موجة كاملة أو نصف موجة'
  },
  {
    name: 'المرشح',
    function: 'تنعيم التيار المستمر وتقليل التموج',
    type: 'مكثفات وملفات',
    specifications: 'تقليل التموج إلى أقل من 5%'
  },
  {
    name: 'محول الفتيل',
    function: 'توفير جهد منخفض لتسخين الفتيل',
    type: 'محول تنازلي',
    specifications: '5-12V, 3-5A'
  },
  {
    name: 'دائرة التحكم في التيار',
    function: 'ضبط تيار الأنبوب (mA)',
    type: 'مقاومة متغيرة أو مفتاح إلكتروني',
    specifications: '50-1000 mA'
  },
  {
    name: 'مؤقت التعرض',
    function: 'التحكم في زمن التعرض',
    type: 'دائرة إلكترونية رقمية',
    specifications: '0.001-10 ثانية'
  }];


  return (
    <div className="min-h-screen py-8" data-id="1nch5swh2" data-path="src/pages/CircuitDiagramPage.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-id="2uwp1nl1c" data-path="src/pages/CircuitDiagramPage.tsx">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12" data-id="40opw8bm0" data-path="src/pages/CircuitDiagramPage.tsx">

          <Badge variant="secondary" className="mb-4" data-id="8aggpasp7" data-path="src/pages/CircuitDiagramPage.tsx">الدوائر الكهربائية</Badge>
          <h1 className="text-4xl font-bold text-gray-900 mb-4" data-id="6izvwokat" data-path="src/pages/CircuitDiagramPage.tsx">
            المخططات الكهربائية لأجهزة الأشعة السينية
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="gvzslv2xg" data-path="src/pages/CircuitDiagramPage.tsx">
            فهم تفصيلي للدوائر الكهربائية ومكونات النظام الكهربائي
          </p>
        </motion.div>

        <Tabs defaultValue="diagrams" className="w-full" data-id="9lxxvzn7g" data-path="src/pages/CircuitDiagramPage.tsx">
          <TabsList className="grid w-full grid-cols-3 mb-8" data-id="7vgtymeb6" data-path="src/pages/CircuitDiagramPage.tsx">
            <TabsTrigger value="diagrams" data-id="nu6isqivo" data-path="src/pages/CircuitDiagramPage.tsx">المخططات</TabsTrigger>
            <TabsTrigger value="components" data-id="p5z7q8la3" data-path="src/pages/CircuitDiagramPage.tsx">المكونات</TabsTrigger>
            <TabsTrigger value="analysis" data-id="ybpj9awbm" data-path="src/pages/CircuitDiagramPage.tsx">التحليل</TabsTrigger>
          </TabsList>

          {/* Diagrams Tab */}
          <TabsContent value="diagrams" data-id="92os0vo7m" data-path="src/pages/CircuitDiagramPage.tsx">
            <div className="grid gap-6" data-id="xqrrrful1" data-path="src/pages/CircuitDiagramPage.tsx">
              {/* Circuit Type Selector */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="grid md:grid-cols-3 gap-4 mb-8" data-id="mhkh7h7f5" data-path="src/pages/CircuitDiagramPage.tsx">

                {circuitTypes.map((type) => {
                  const Icon = type.icon;
                  return (
                    <Card
                      key={type.id}
                      className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                      selectedCircuit === type.id ? 'ring-2 ring-blue-500 shadow-lg' : ''}`
                      }
                      onClick={() => setSelectedCircuit(type.id)} data-id="vdoam4o5d" data-path="src/pages/CircuitDiagramPage.tsx">

                      <CardHeader className="text-center pb-3" data-id="isxj1l440" data-path="src/pages/CircuitDiagramPage.tsx">
                        <div className={`inline-flex items-center justify-center w-12 h-12 ${type.color} rounded-lg mb-3 mx-auto`} data-id="ham8x96bj" data-path="src/pages/CircuitDiagramPage.tsx">
                          <Icon className="w-6 h-6 text-white" data-id="p407gzcnl" data-path="src/pages/CircuitDiagramPage.tsx" />
                        </div>
                        <CardTitle className="text-lg" data-id="s8y73s3ii" data-path="src/pages/CircuitDiagramPage.tsx">{type.title}</CardTitle>
                        <CardDescription className="text-sm" data-id="v15l3vpnm" data-path="src/pages/CircuitDiagramPage.tsx">
                          {type.description}
                        </CardDescription>
                      </CardHeader>
                    </Card>);

                })}
              </motion.div>

              {/* Selected Circuit Display */}
              <motion.div
                key={selectedCircuit}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }} data-id="0am3j5gqk" data-path="src/pages/CircuitDiagramPage.tsx">

                <Card className="overflow-hidden" data-id="x1z5gprjn" data-path="src/pages/CircuitDiagramPage.tsx">
                  <CardHeader data-id="0dhcovtnz" data-path="src/pages/CircuitDiagramPage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="42w6ws3v2" data-path="src/pages/CircuitDiagramPage.tsx">
                      <CircuitBoard className="w-5 h-5 text-blue-600" data-id="twbwntxt1" data-path="src/pages/CircuitDiagramPage.tsx" />
                      {circuitTypes.find((t) => t.id === selectedCircuit)?.title}
                    </CardTitle>
                    <CardDescription data-id="tvz87lmqi" data-path="src/pages/CircuitDiagramPage.tsx">
                      انقر على المكونات لاستكشاف وظائفها
                    </CardDescription>
                  </CardHeader>
                  <CardContent data-id="144t2jh9b" data-path="src/pages/CircuitDiagramPage.tsx">
                    {selectedCircuit === 'main' && <CircuitDiagram data-id="sk62x7zym" data-path="src/pages/CircuitDiagramPage.tsx" />}
                    {selectedCircuit === 'power' && <PowerSupplyDiagram data-id="jra8jz6we" data-path="src/pages/CircuitDiagramPage.tsx" />}
                    {selectedCircuit === 'control' &&
                    <div className="text-center py-12 text-gray-500" data-id="51a0nory7" data-path="src/pages/CircuitDiagramPage.tsx">
                        مخطط دائرة التحكم - قيد التطوير
                      </div>
                    }
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </TabsContent>

          {/* Components Tab */}
          <TabsContent value="components" data-id="v0sjql5vw" data-path="src/pages/CircuitDiagramPage.tsx">
            <div className="grid gap-4" data-id="2r5zc7a7s" data-path="src/pages/CircuitDiagramPage.tsx">
              {circuitComponents.map((component, index) =>
              <motion.div
                key={component.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }} data-id="fytwlfe1x" data-path="src/pages/CircuitDiagramPage.tsx">

                  <Card className="hover:shadow-lg transition-shadow" data-id="2qrzoae2e" data-path="src/pages/CircuitDiagramPage.tsx">
                    <CardHeader data-id="4k2l2w2jg" data-path="src/pages/CircuitDiagramPage.tsx">
                      <div className="flex items-start justify-between" data-id="b7d9pnbho" data-path="src/pages/CircuitDiagramPage.tsx">
                        <div data-id="q2ypofb4k" data-path="src/pages/CircuitDiagramPage.tsx">
                          <CardTitle className="text-lg" data-id="cf97aw7cw" data-path="src/pages/CircuitDiagramPage.tsx">{component.name}</CardTitle>
                          <CardDescription className="mt-1" data-id="slm0ug3sm" data-path="src/pages/CircuitDiagramPage.tsx">
                            {component.function}
                          </CardDescription>
                        </div>
                        <Badge variant="outline" data-id="egt5vhydh" data-path="src/pages/CircuitDiagramPage.tsx">{component.type}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent data-id="79wlk3n34" data-path="src/pages/CircuitDiagramPage.tsx">
                      <div className="bg-gray-50 p-3 rounded-lg" data-id="gh16a09un" data-path="src/pages/CircuitDiagramPage.tsx">
                        <p className="text-sm text-gray-700" data-id="hx386vpwy" data-path="src/pages/CircuitDiagramPage.tsx">
                          <strong data-id="xn68dvitg" data-path="src/pages/CircuitDiagramPage.tsx">المواصفات:</strong> {component.specifications}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </div>
          </TabsContent>

          {/* Analysis Tab */}
          <TabsContent value="analysis" data-id="r0099u2yf" data-path="src/pages/CircuitDiagramPage.tsx">
            <div className="grid lg:grid-cols-2 gap-8" data-id="saqqwl7ap" data-path="src/pages/CircuitDiagramPage.tsx">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }} data-id="lctbisko2" data-path="src/pages/CircuitDiagramPage.tsx">

                <Card data-id="t80woscrc" data-path="src/pages/CircuitDiagramPage.tsx">
                  <CardHeader data-id="kts9qpg3m" data-path="src/pages/CircuitDiagramPage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="twzfkjcaz" data-path="src/pages/CircuitDiagramPage.tsx">
                      <Zap className="w-5 h-5 text-yellow-600" data-id="je65ak3fc" data-path="src/pages/CircuitDiagramPage.tsx" />
                      تحليل الطاقة
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4" data-id="796cge32l" data-path="src/pages/CircuitDiagramPage.tsx">
                    <div className="space-y-3" data-id="w2lt6zctv" data-path="src/pages/CircuitDiagramPage.tsx">
                      <h4 className="font-semibold" data-id="m6n5no6q0" data-path="src/pages/CircuitDiagramPage.tsx">كفاءة النظام</h4>
                      <div className="space-y-2" data-id="el7045qdi" data-path="src/pages/CircuitDiagramPage.tsx">
                        <div className="flex justify-between text-sm" data-id="vk8wazdlk" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="5a4p8uvor" data-path="src/pages/CircuitDiagramPage.tsx">كفاءة المحول:</span>
                          <span className="font-medium" data-id="axmt3ayki" data-path="src/pages/CircuitDiagramPage.tsx">95-98%</span>
                        </div>
                        <div className="flex justify-between text-sm" data-id="rdc2mjkka" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="crqjdlnd3" data-path="src/pages/CircuitDiagramPage.tsx">فقدان في المعدل:</span>
                          <span className="font-medium" data-id="ldkrb8swk" data-path="src/pages/CircuitDiagramPage.tsx">2-3%</span>
                        </div>
                        <div className="flex justify-between text-sm" data-id="lf81e15ck" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="e2zqrilja" data-path="src/pages/CircuitDiagramPage.tsx">فقدان في الكابلات:</span>
                          <span className="font-medium" data-id="hu277ygo0" data-path="src/pages/CircuitDiagramPage.tsx">1-2%</span>
                        </div>
                        <hr className="my-2" data-id="1ljkiyx3q" data-path="src/pages/CircuitDiagramPage.tsx" />
                        <div className="flex justify-between text-sm font-semibold" data-id="ts6k635rk" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="a4yfdocft" data-path="src/pages/CircuitDiagramPage.tsx">الكفاءة الإجمالية:</span>
                          <span data-id="1gqglj6gu" data-path="src/pages/CircuitDiagramPage.tsx">92-95%</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3" data-id="emzoju6v0" data-path="src/pages/CircuitDiagramPage.tsx">
                      <h4 className="font-semibold" data-id="b9a8gdp3t" data-path="src/pages/CircuitDiagramPage.tsx">توزيع الطاقة</h4>
                      <div className="space-y-2" data-id="en8qjql41" data-path="src/pages/CircuitDiagramPage.tsx">
                        <div className="flex justify-between text-sm" data-id="itwbun04y" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="ljc910mc3" data-path="src/pages/CircuitDiagramPage.tsx">أشعة سينية:</span>
                          <span className="font-medium text-blue-600" data-id="2iwnhp7vh" data-path="src/pages/CircuitDiagramPage.tsx">1%</span>
                        </div>
                        <div className="flex justify-between text-sm" data-id="qlc77m2f1" data-path="src/pages/CircuitDiagramPage.tsx">
                          <span data-id="v79q986jw" data-path="src/pages/CircuitDiagramPage.tsx">حرارة:</span>
                          <span className="font-medium text-red-600" data-id="58swnisv0" data-path="src/pages/CircuitDiagramPage.tsx">99%</span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-4" data-id="qiph6cyrj" data-path="src/pages/CircuitDiagramPage.tsx">
                        <div className="bg-blue-500 h-4 rounded-l-full" style={{ width: '1%' }} data-id="movz428c9" data-path="src/pages/CircuitDiagramPage.tsx"></div>
                        <div className="bg-red-500 h-4 rounded-r-full ml-0" style={{ width: '99%', marginTop: '-16px', marginLeft: '1%' }} data-id="czjfo14a8" data-path="src/pages/CircuitDiagramPage.tsx"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }} data-id="71f5sltnw" data-path="src/pages/CircuitDiagramPage.tsx">

                <Card data-id="8w87r002e" data-path="src/pages/CircuitDiagramPage.tsx">
                  <CardHeader data-id="qd247ial0" data-path="src/pages/CircuitDiagramPage.tsx">
                    <CardTitle className="flex items-center gap-2" data-id="mriej5y9c" data-path="src/pages/CircuitDiagramPage.tsx">
                      <Info className="w-5 h-5 text-blue-600" data-id="hnhrzzux2" data-path="src/pages/CircuitDiagramPage.tsx" />
                      معادلات مهمة
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4" data-id="bvc4yggx9" data-path="src/pages/CircuitDiagramPage.tsx">
                    <div className="space-y-3" data-id="3t90gngkf" data-path="src/pages/CircuitDiagramPage.tsx">
                      <div className="bg-blue-50 p-3 rounded-lg" data-id="143hgtq3v" data-path="src/pages/CircuitDiagramPage.tsx">
                        <h5 className="font-semibold text-blue-900 mb-2" data-id="s4jtfuh5e" data-path="src/pages/CircuitDiagramPage.tsx">قانون أوم</h5>
                        <div className="text-center font-mono text-lg text-blue-800" data-id="3e1wrz6hg" data-path="src/pages/CircuitDiagramPage.tsx">
                          V = I × R
                        </div>
                        <p className="text-xs text-blue-600 mt-1" data-id="14en15rds" data-path="src/pages/CircuitDiagramPage.tsx">
                          الجهد = التيار × المقاومة
                        </p>
                      </div>

                      <div className="bg-green-50 p-3 rounded-lg" data-id="twmk3ehpd" data-path="src/pages/CircuitDiagramPage.tsx">
                        <h5 className="font-semibold text-green-900 mb-2" data-id="jp4v8jsv4" data-path="src/pages/CircuitDiagramPage.tsx">القدرة الكهربائية</h5>
                        <div className="text-center font-mono text-lg text-green-800" data-id="3nq1e8zo6" data-path="src/pages/CircuitDiagramPage.tsx">
                          P = V × I
                        </div>
                        <p className="text-xs text-green-600 mt-1" data-id="71gp0u7kd" data-path="src/pages/CircuitDiagramPage.tsx">
                          القدرة = الجهد × التيار
                        </p>
                      </div>

                      <div className="bg-purple-50 p-3 rounded-lg" data-id="ffc8t6vde" data-path="src/pages/CircuitDiagramPage.tsx">
                        <h5 className="font-semibold text-purple-900 mb-2" data-id="vgpttpnwj" data-path="src/pages/CircuitDiagramPage.tsx">نسبة المحول</h5>
                        <div className="text-center font-mono text-lg text-purple-800" data-id="uiy3g1um0" data-path="src/pages/CircuitDiagramPage.tsx">
                          V₂/V₁ = N₂/N₁
                        </div>
                        <p className="text-xs text-purple-600 mt-1" data-id="25z1dswwh" data-path="src/pages/CircuitDiagramPage.tsx">
                          نسبة الجهد = نسبة عدد اللفات
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>);

};

export default CircuitDiagramPage;