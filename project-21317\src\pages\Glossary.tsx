import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BookOpen, Search, ArrowRight } from 'lucide-react';

const Glossary = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const glossaryTerms = [
  {
    term: "Absorbed Dose",
    definition: "The energy deposited by ionizing radiation per unit mass of material. Measured in Gray (Gy), where 1 Gy = 1 J/kg.",
    category: "dosimetry",
    relatedTerms: ["Equivalent Dose", "Effective Dose", "Gray"]
  },
  {
    term: "ALARA",
    definition: "As Low As Reasonably Achievable - fundamental principle of radiation protection that aims to minimize radiation exposure while maintaining diagnostic quality.",
    category: "protection",
    relatedTerms: ["Radiation Protection", "Dose Optimization", "Justification"]
  },
  {
    term: "Attenuation",
    definition: "The reduction in intensity of a radiation beam as it passes through matter due to absorption and scattering interactions.",
    category: "physics",
    relatedTerms: ["Linear Attenuation Coefficient", "Half-Value Layer", "Beer-Lambert Law"]
  },
  {
    term: "Beam Hardening",
    definition: "The preferential absorption of low-energy photons from a polychromatic X-ray beam, resulting in an effective increase in average beam energy.",
    category: "imaging",
    relatedTerms: ["Polychromatic Beam", "Filtration", "Cupping Artifact"]
  },
  {
    term: "Compton Scattering",
    definition: "Inelastic scattering of photons with loosely bound electrons, resulting in photon energy loss and change in direction. Dominant interaction in soft tissue at diagnostic energies.",
    category: "physics",
    relatedTerms: ["Photoelectric Effect", "Pair Production", "Klein-Nishina Formula"]
  },
  {
    term: "Contrast-to-Noise Ratio (CNR)",
    definition: "A measure of image quality that quantifies the ability to distinguish between different structures in the presence of noise.",
    category: "quality",
    relatedTerms: ["Signal-to-Noise Ratio", "Image Quality", "Contrast Resolution"]
  },
  {
    term: "Effective Dose",
    definition: "A weighted average of equivalent doses to different organs and tissues, accounting for their varying radiosensitivity. Measured in Sieverts (Sv).",
    category: "dosimetry",
    relatedTerms: ["Equivalent Dose", "Tissue Weighting Factor", "Sievert"]
  },
  {
    term: "Half-Value Layer (HVL)",
    definition: "The thickness of material required to reduce the intensity of a radiation beam to half its original value.",
    category: "physics",
    relatedTerms: ["Linear Attenuation Coefficient", "Tenth-Value Layer", "Beam Quality"]
  },
  {
    term: "Inverse Square Law",
    definition: "Physical law stating that radiation intensity decreases with the square of the distance from a point source: I₂ = I₁(r₁/r₂)²",
    category: "physics",
    relatedTerms: ["Distance", "Radiation Protection", "Source Geometry"]
  },
  {
    term: "kVp",
    definition: "Kilovolt peak - the maximum energy of X-ray photons in a polychromatic beam, controlling penetration and contrast.",
    category: "imaging",
    relatedTerms: ["X-ray Tube", "Beam Quality", "Penetration"]
  },
  {
    term: "Linear Attenuation Coefficient (μ)",
    definition: "A measure of how easily a material can be penetrated by X-rays or gamma rays, expressed in units of inverse length (cm⁻¹).",
    category: "physics",
    relatedTerms: ["Attenuation", "Half-Value Layer", "Mass Attenuation Coefficient"]
  },
  {
    term: "mAs",
    definition: "Milliampere-seconds - the product of tube current and exposure time, controlling the quantity of X-ray photons produced.",
    category: "imaging",
    relatedTerms: ["X-ray Tube", "Beam Quantity", "Patient Dose"]
  },
  {
    term: "Modulation Transfer Function (MTF)",
    definition: "A quantitative measure of spatial resolution performance as a function of spatial frequency, describing how well fine details are reproduced.",
    category: "quality",
    relatedTerms: ["Spatial Resolution", "Point Spread Function", "Image Quality"]
  },
  {
    term: "Noise",
    definition: "Random variations in pixel values that degrade image quality, arising from quantum (statistical) and electronic sources.",
    category: "quality",
    relatedTerms: ["Signal-to-Noise Ratio", "Quantum Mottle", "Image Quality"]
  },
  {
    term: "Pair Production",
    definition: "Interaction where a high-energy photon (>1.022 MeV) converts to an electron-positron pair in the vicinity of an atomic nucleus.",
    category: "physics",
    relatedTerms: ["Photoelectric Effect", "Compton Scattering", "Annihilation Radiation"]
  },
  {
    term: "Photoelectric Effect",
    definition: "Complete absorption of a photon by an inner shell electron, which is ejected as a photoelectron. Dominant at low energies and high atomic numbers.",
    category: "physics",
    relatedTerms: ["Compton Scattering", "Pair Production", "K-edge"]
  },
  {
    term: "Pixel",
    definition: "Picture element - the smallest unit of a digital image, containing intensity information for that spatial location.",
    category: "imaging",
    relatedTerms: ["Voxel", "Matrix Size", "Spatial Resolution"]
  },
  {
    term: "Quality Assurance (QA)",
    definition: "Systematic procedures to ensure that imaging equipment performs consistently and produces images of adequate quality for diagnosis.",
    category: "quality",
    relatedTerms: ["Quality Control", "Performance Testing", "Phantoms"]
  },
  {
    term: "Radiation Weighting Factor (wR)",
    definition: "Dimensionless factor used to convert absorbed dose to equivalent dose, accounting for the biological effectiveness of different radiation types.",
    category: "dosimetry",
    relatedTerms: ["Equivalent Dose", "Relative Biological Effectiveness", "Linear Energy Transfer"]
  },
  {
    term: "Scatter Radiation",
    definition: "Radiation that has been deflected from its original direction by interaction with matter, contributing to image degradation and patient dose.",
    category: "imaging",
    relatedTerms: ["Compton Scattering", "Grid", "Collimation"]
  },
  {
    term: "Sievert (Sv)",
    definition: "SI unit of equivalent dose and effective dose, representing the biological effect of ionizing radiation. 1 Sv = 1 J/kg × radiation weighting factor.",
    category: "dosimetry",
    relatedTerms: ["Gray", "Radiation Weighting Factor", "Dose Equivalent"]
  },
  {
    term: "Signal-to-Noise Ratio (SNR)",
    definition: "Ratio of signal strength to noise level, used as a measure of image quality. Higher SNR indicates better image quality.",
    category: "quality",
    relatedTerms: ["Contrast-to-Noise Ratio", "Image Quality", "Noise"]
  },
  {
    term: "Spatial Resolution",
    definition: "The ability of an imaging system to distinguish between closely spaced objects, typically measured in line pairs per millimeter.",
    category: "quality",
    relatedTerms: ["Modulation Transfer Function", "Point Spread Function", "Pixel Size"]
  },
  {
    term: "Tissue Weighting Factor (wT)",
    definition: "Factor representing the relative contribution of different organs/tissues to the overall radiation detriment when the whole body is irradiated uniformly.",
    category: "dosimetry",
    relatedTerms: ["Effective Dose", "Organ Dose", "Radiosensitivity"]
  },
  {
    term: "Voxel",
    definition: "Volume element - the three-dimensional equivalent of a pixel, representing the smallest distinguishable box-shaped part of a 3D image.",
    category: "imaging",
    relatedTerms: ["Pixel", "Slice Thickness", "Spatial Resolution"]
  },
  {
    term: "Window/Level",
    definition: "Image display parameters that map the full range of pixel values to the display range for optimal visualization of specific tissue types.",
    category: "imaging",
    relatedTerms: ["Contrast", "Display", "Hounsfield Units"]
  }];


  const categories = [
  { id: 'all', name: 'All Terms', count: glossaryTerms.length },
  { id: 'physics', name: 'Physics', count: glossaryTerms.filter((t) => t.category === 'physics').length },
  { id: 'imaging', name: 'Imaging', count: glossaryTerms.filter((t) => t.category === 'imaging').length },
  { id: 'dosimetry', name: 'Dosimetry', count: glossaryTerms.filter((t) => t.category === 'dosimetry').length },
  { id: 'protection', name: 'Protection', count: glossaryTerms.filter((t) => t.category === 'protection').length },
  { id: 'quality', name: 'Quality', count: glossaryTerms.filter((t) => t.category === 'quality').length }];


  const filteredTerms = glossaryTerms.filter((term) => {
    const matchesSearch = term.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
    term.definition.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || term.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-teal-50" data-id="0rxb1w5oc" data-path="src/pages/Glossary.tsx">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" data-id="grhyvju87" data-path="src/pages/Glossary.tsx">
        {/* Header */}
        <div className="text-center mb-12" data-id="zyyle3o8p" data-path="src/pages/Glossary.tsx">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4" data-id="g4bdnrik7" data-path="src/pages/Glossary.tsx">
            Medical Physics Glossary
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto" data-id="uil2wdz9d" data-path="src/pages/Glossary.tsx">
            Comprehensive dictionary of medical physics terms, concepts, and definitions
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-8" data-id="n5u5utp5g" data-path="src/pages/Glossary.tsx">
          <div className="flex flex-col lg:flex-row gap-4 mb-6" data-id="5fn6ndu44" data-path="src/pages/Glossary.tsx">
            <div className="relative flex-1" data-id="n4o3rlqfw" data-path="src/pages/Glossary.tsx">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" data-id="zbvtfzc77" data-path="src/pages/Glossary.tsx" />
              <Input
                placeholder="Search terms or definitions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10" data-id="6nwhk5iyc" data-path="src/pages/Glossary.tsx" />

            </div>
          </div>

          <div className="flex flex-wrap gap-2" data-id="q1h8830cj" data-path="src/pages/Glossary.tsx">
            {categories.map((category) =>
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="flex items-center gap-2" data-id="zlscpj87a" data-path="src/pages/Glossary.tsx">

                {category.name}
                <Badge variant="secondary" className="ml-1" data-id="o1ajeqxic" data-path="src/pages/Glossary.tsx">
                  {category.count}
                </Badge>
              </Button>
            )}
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6" data-id="auhq7qmpg" data-path="src/pages/Glossary.tsx">
          <p className="text-gray-600" data-id="ssl88v3tk" data-path="src/pages/Glossary.tsx">
            Showing {filteredTerms.length} of {glossaryTerms.length} terms
          </p>
        </div>

        {/* Glossary Terms */}
        <div className="grid gap-6" data-id="7pywuxpgs" data-path="src/pages/Glossary.tsx">
          {filteredTerms.map((term, index) =>
          <Card key={index} className="hover:shadow-md transition-shadow" data-id="noly5bv1p" data-path="src/pages/Glossary.tsx">
              <CardHeader data-id="qecrzmvnj" data-path="src/pages/Glossary.tsx">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2" data-id="sglrnwccm" data-path="src/pages/Glossary.tsx">
                  <CardTitle className="text-xl text-blue-800" data-id="rsu7w9p4x" data-path="src/pages/Glossary.tsx">
                    {term.term}
                  </CardTitle>
                  <Badge variant="outline" className="w-fit" data-id="aya5zh7bf" data-path="src/pages/Glossary.tsx">
                    {term.category}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent data-id="ys712gxuy" data-path="src/pages/Glossary.tsx">
                <CardDescription className="text-base text-gray-700 leading-relaxed mb-4" data-id="2c0t5fo6m" data-path="src/pages/Glossary.tsx">
                  {term.definition}
                </CardDescription>
                
                {term.relatedTerms && term.relatedTerms.length > 0 &&
              <div data-id="sp8v5qx6u" data-path="src/pages/Glossary.tsx">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center gap-2" data-id="t0j7n0g13" data-path="src/pages/Glossary.tsx">
                      <ArrowRight className="w-4 h-4" data-id="jmkvja3za" data-path="src/pages/Glossary.tsx" />
                      Related Terms
                    </h4>
                    <div className="flex flex-wrap gap-2" data-id="lm4m6mxka" data-path="src/pages/Glossary.tsx">
                      {term.relatedTerms.map((relatedTerm, idx) =>
                  <Badge
                    key={idx}
                    variant="secondary"
                    className="text-xs cursor-pointer hover:bg-blue-100"
                    onClick={() => setSearchTerm(relatedTerm)} data-id="zp7dt3iao" data-path="src/pages/Glossary.tsx">

                          {relatedTerm}
                        </Badge>
                  )}
                    </div>
                  </div>
              }
              </CardContent>
            </Card>
          )}
        </div>

        {filteredTerms.length === 0 &&
        <div className="text-center py-12" data-id="0al6k2f65" data-path="src/pages/Glossary.tsx">
            <BookOpen className="w-16 h-16 mx-auto text-gray-400 mb-4" data-id="z3rs3gd8v" data-path="src/pages/Glossary.tsx" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2" data-id="cim97iat3" data-path="src/pages/Glossary.tsx">No terms found</h3>
            <p className="text-gray-500" data-id="jx0hbavi3" data-path="src/pages/Glossary.tsx">
              Try adjusting your search terms or category filter
            </p>
          </div>
        }

        {/* Additional Resources */}
        <div className="mt-16 grid md:grid-cols-3 gap-8" data-id="jjwtswbsh" data-path="src/pages/Glossary.tsx">
          <Card data-id="d7bhgie41" data-path="src/pages/Glossary.tsx">
            <CardHeader data-id="ew2oy0qv0" data-path="src/pages/Glossary.tsx">
              <CardTitle className="flex items-center gap-2" data-id="os22ukuyf" data-path="src/pages/Glossary.tsx">
                <BookOpen className="w-5 h-5" data-id="f1yu5dj6t" data-path="src/pages/Glossary.tsx" />
                References
              </CardTitle>
            </CardHeader>
            <CardContent data-id="b6vgep0l6" data-path="src/pages/Glossary.tsx">
              <ul className="space-y-2 text-sm text-gray-600" data-id="bi68i0k5g" data-path="src/pages/Glossary.tsx">
                <li data-id="uo1eyiuo8" data-path="src/pages/Glossary.tsx">• ICRP Publications</li>
                <li data-id="1grtn2o0j" data-path="src/pages/Glossary.tsx">• NCRP Reports</li>
                <li data-id="kqzjjn5ne" data-path="src/pages/Glossary.tsx">• AAPM Task Group Reports</li>
                <li data-id="n84t1poxl" data-path="src/pages/Glossary.tsx">• Medical Physics Journals</li>
                <li data-id="li67rl59p" data-path="src/pages/Glossary.tsx">• Radiation Protection Standards</li>
              </ul>
            </CardContent>
          </Card>

          <Card data-id="ls2ru6dbk" data-path="src/pages/Glossary.tsx">
            <CardHeader data-id="xhq2mj4f0" data-path="src/pages/Glossary.tsx">
              <CardTitle data-id="4m5sy4z5n" data-path="src/pages/Glossary.tsx">Professional Organizations</CardTitle>
            </CardHeader>
            <CardContent data-id="rt2g3jdwu" data-path="src/pages/Glossary.tsx">
              <ul className="space-y-2 text-sm text-gray-600" data-id="9o3oyh8u4" data-path="src/pages/Glossary.tsx">
                <li data-id="pt2da94sn" data-path="src/pages/Glossary.tsx">• AAPM (American Association of Physicists in Medicine)</li>
                <li data-id="dbpfxpqyl" data-path="src/pages/Glossary.tsx">• ICRP (International Commission on Radiological Protection)</li>
                <li data-id="c8rqfo5kb" data-path="src/pages/Glossary.tsx">• NCRP (National Council on Radiation Protection)</li>
                <li data-id="x8d6caj2g" data-path="src/pages/Glossary.tsx">• IAEA (International Atomic Energy Agency)</li>
              </ul>
            </CardContent>
          </Card>

          <Card data-id="4usqfe5bv" data-path="src/pages/Glossary.tsx">
            <CardHeader data-id="8lmadb37w" data-path="src/pages/Glossary.tsx">
              <CardTitle data-id="opwwr0sh1" data-path="src/pages/Glossary.tsx">Units & Constants</CardTitle>
            </CardHeader>
            <CardContent data-id="iitrqejt0" data-path="src/pages/Glossary.tsx">
              <div className="space-y-2 text-sm" data-id="npp56g01q" data-path="src/pages/Glossary.tsx">
                <div className="flex justify-between" data-id="u6i8rxj0p" data-path="src/pages/Glossary.tsx">
                  <span data-id="rhweih7mc" data-path="src/pages/Glossary.tsx">Electron rest mass energy:</span>
                  <span data-id="72f14bljw" data-path="src/pages/Glossary.tsx">511 keV</span>
                </div>
                <div className="flex justify-between" data-id="3p18uegd8" data-path="src/pages/Glossary.tsx">
                  <span data-id="4ic7pa47f" data-path="src/pages/Glossary.tsx">Speed of light:</span>
                  <span data-id="jnsh4xhr6" data-path="src/pages/Glossary.tsx">3×10⁸ m/s</span>
                </div>
                <div className="flex justify-between" data-id="8dyycedtb" data-path="src/pages/Glossary.tsx">
                  <span data-id="gfs1fmo0d" data-path="src/pages/Glossary.tsx">Planck constant:</span>
                  <span data-id="13pe3f8xd" data-path="src/pages/Glossary.tsx">6.626×10⁻³⁴ J·s</span>
                </div>
                <div className="flex justify-between" data-id="5ms0vma90" data-path="src/pages/Glossary.tsx">
                  <span data-id="9htvy4244" data-path="src/pages/Glossary.tsx">Avogadro number:</span>
                  <span data-id="nfxc5fog1" data-path="src/pages/Glossary.tsx">6.022×10²³ mol⁻¹</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>);

};

export default Glossary;